<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="Frm_rpt_ListOfKeyWords_withSlug.aspx.vb" Inherits="Frm_rpt_ListOfKeyWords"
    Title="Other Reports > Q 9. How Can I View Keyword Wise Report with Urdu Slug?" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > How Can I View Keyword Vs Program/Slug wise report with Urdu Slug? "
                        Width="712px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <asp:ScriptManager ID="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                      
                    <table style="width: 1362px">
                        <tr>
                            <td style="width: 92px">
                    <table style="width: 444px; border-left-color: #6699cc; border-bottom-color: #6699cc; border-top-style: groove; border-top-color: #6699cc; border-right-style: groove; border-left-style: groove; border-right-color: #6699cc; border-bottom-style: groove;">
                        <tbody>
                            <tr>
                                <td align="center" class="mytext" colspan="3" style="height: 21px; background-color: lightsteelblue; border-top-width: thin; border-left-width: thin; border-left-color: #9999cc; border-bottom-width: thin; border-bottom-color: #9999cc; border-top-color: #9999cc; border-right-width: thin; border-right-color: #9999cc;">
                                    <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Calibri"
                                        Font-Size="Large" ForeColor="Maroon" Text=":  : Entertainment :  : "></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 265px; height: 21px" class="mytext">
                                    Entertainment Keyword &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox ID="chk_EntKW" runat="server" Text="Ignore"
                                        Checked="True"></asp:CheckBox></td>
                                <td style="width: 255px; height: 21px" class="mytext">
                                    Program Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                    &nbsp; &nbsp;&nbsp;
                                </td>
                            <td class="mytext" style="width: 255px; height: 21px">
                                    Station &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                    <asp:CheckBox ID="chkStation" runat="server" AutoPostBack="True" Checked="True" Text="Ignore" /></td>
                        </tr>
                            <tr>
                                <td style="width: 265px; height: 4px" class="mytext" valign="top">
                                    <asp:TextBox ID="txtEntKeywords" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                                <td style="width: 100px; height: 4px" class="mytext" valign="top">
                                    <asp:TextBox ID="txtProgramName" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                                <td class="mytext" style="width: 255px; height: 4px" valign="top">
                                    <asp:DropDownList ID="ddlBaseStation" runat="server"
                                        Width="120px" CssClass="mytext">
                                    </asp:DropDownList></td>
                            </tr>
                            <tr>
                                <td class="mytext" style="width: 265px; height: 5px" valign="top">
                                    From Date &nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp;<asp:CheckBox ID="chkIgnoredate" runat="server" Text="Ignore Dates" Checked="True" /></td>
                                <td class="mytext" style="width: 100px; height: 5px" valign="top">
                                    To Date</td>
                                <td class="mytext" style="width: 255px; height: 5px" valign="top">
                                </td>
                            </tr>
                            <tr>
                                <td class="mytext" style="width: 265px; height: 4px" valign="top">
                                    <asp:TextBox ID="txtFromdate" runat="server" CssClass="mytext" Width="199px"></asp:TextBox></td>
                                <td class="mytext" style="width: 100px; height: 4px" valign="top">
                                    <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                                <td class="mytext" style="width: 255px; height: 4px" valign="top">
                                </td>
                            </tr>
                            <tr>
                                <td class="mytext" style="width: 265px; height: 22px">
                    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Ent_KW1" runat="server"
                        TargetControlID="txtEntKeywords" ServicePath="AutoComplete.asmx" ServiceMethod="EntKeywords"
                        EnableCaching="true" CompletionSetCount="12" CompletionInterval="1">
                    </cc1:AutoCompleteExtender>
                                    <cc1:AutoCompleteExtender ID="AutoCompleteExtender6" runat="server" CompletionInterval="1"
                                        CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="Program"
                                        ServicePath="AutoComplete.asmx" TargetControlID="txtProgramName">
                                    </cc1:AutoCompleteExtender>
                    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_NewsKeywords_1" runat="server" TargetControlID="txtNewKeywords"
                        ServicePath="AutoComplete.asmx" ServiceMethod="GetNewKeywords" EnableCaching="true"
                        CompletionSetCount="12" CompletionInterval="1">
                    </cc1:AutoCompleteExtender>
                                    &nbsp;&nbsp;
                                </td>
                                <td class="mytext" style="width: 100px; height: 22px">
                                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="MyCalendar"
                                        Format="dd-MMM-yyyy" TargetControlID="txtFromdate" PopupPosition="TopRight">
                                    </cc1:CalendarExtender>
                                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" CssClass="MyCalendar"
                                        Format="dd-MMM-yyyy" TargetControlID="txtToDate" PopupPosition="TopRight">
                                    </cc1:CalendarExtender>
                                </td>
                                <td class="mytext" style="width: 255px; height: 22px">
                                </td>
                            </tr>
                            <tr>
                                <td align="center" class="bottomMain" colspan="3" style="height: 29px">
                                    <asp:Button ID="bttnEntReport" runat="server" CssClass="buttonA" Text="View Entertainment Report"
                                    Width="204px" Font-Bold="True" Font-Size="Small" /></td>
                            </tr>
                        </tbody>
                    </table>
                            </td>
                            <td style="width: 50px">
                            </td>
                            <td style="width: 100px" valign="top"><table style="width: 444px; border-right: #6699cc thin groove; border-top: #6699cc thin groove; border-left: #6699cc thin groove; border-bottom: #6699cc thin groove;">
                                <tbody>
                                    <tr>
                                        <td align="center" class="mytext" colspan="3" style="height: 21px; background-color: lightsteelblue; border-top-width: thin; border-left-width: thin; border-left-color: #9999cc; border-bottom-width: thin; border-bottom-color: #9999cc; border-top-color: #9999cc; border-right-width: thin; border-right-color: #9999cc;">
                                            &nbsp;<asp:Label ID="Label4" runat="server" Font-Bold="True" Font-Names="Calibri"
                                                Font-Size="Large" ForeColor="Maroon" Text=":  : News :  : "></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 265px; height: 21px" class="mytext">
                                    News Keyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp;&nbsp;<asp:CheckBox ID="chk_NewKW" runat="server" Text="Ignore" Checked="True"></asp:CheckBox></td>
                                        <td style="width: 434px; height: 21px" class="mytext">
                                            &nbsp;Report Slug</td>
                                        <td class="mytext" style="width: 255px; height: 21px">
                                            Station &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                            <asp:CheckBox ID="CheckBox2" runat="server" AutoPostBack="True" Checked="True" Text="Ignore" /></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 265px; height: 22px" class="mytext" valign="top">
                                    <asp:TextBox ID="txtNewKeywords" runat="server"
                                        Width="202px" CssClass="mytext"></asp:TextBox>&nbsp;
                    </td>
                                        <td style="width: 434px; height: 22px" class="mytext" valign="top">
                                            <asp:TextBox ID="txtReportSlug" runat="server" CssClass="mytext" Width="200px"></asp:TextBox>
                                            <asp:Label ID="Label3" runat="server" Font-Italic="False" ForeColor="Red" Text="** Slug must be separated by +" Font-Names="Calibri" Font-Size="9pt"></asp:Label></td>
                                        <td class="mytext" style="width: 255px; height: 22px" valign="top">
                                            <asp:DropDownList ID="ddlBaseStation_News" runat="server"
                                        Width="120px" CssClass="mytext">
                                            </asp:DropDownList></td>
                                    </tr>
                                    <tr>
                                        <td class="mytext" style="width: 265px; height: 22px">
                                            From Date &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 
                                            <asp:CheckBox ID="chkIgnoredate_News" runat="server" Text="Ignore Dates" Checked="True" /></td>
                                        <td class="mytext" style="width: 434px; height: 22px">
                                            To Date &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;
                                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp; &nbsp;&nbsp;</td>
                                        <td class="mytext" style="width: 255px; height: 22px">
                                            Footage Type</td>
                                    </tr>
                                    <tr>
                                        <td class="mytext" style="width: 265px; height: 22px">
                                            <asp:TextBox ID="txtFromdate_News" runat="server" CssClass="mytext" Width="202px"></asp:TextBox></td>
                                        <td class="mytext" style="width: 434px; height: 22px">
                                            <asp:TextBox ID="txtToDate_News" runat="server" CssClass="mytext" Width="199px"></asp:TextBox></td>
                                        <td class="mytext" style="width: 255px; height: 22px">
                                            <asp:TextBox ID="txt_NewsKeyTypes_1" runat="server" CssClass="mytext" TabIndex="6"
                                                Width="190px"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td class="mytext" style="width: 265px; height: 22px">
                                            <cc1:CalendarExtender ID="CalendarExtender3" runat="server" CssClass="MyCalendar"
                                        Format="dd-MMM-yyyy" TargetControlID="txtFromdate_News" PopupPosition="TopRight">
                                            </cc1:CalendarExtender>
                                            <cc1:CalendarExtender ID="CalendarExtender4" runat="server" CssClass="MyCalendar"
                                        Format="dd-MMM-yyyy" TargetControlID="txtToDate_News" PopupPosition="TopRight">
                                            </cc1:CalendarExtender>
                                        </td>
                                        <td class="mytext" style="width: 434px; height: 22px">
                                        </td>
                                        <td class="mytext" style="width: 255px; height: 22px">
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_NewsKeyTypes_1" runat="server"
                                                CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="txt_NewsKeyTypes_1">
                                            </cc1:AutoCompleteExtender>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" class="bottomMain" colspan="3" style="height: 29px">
                                            <asp:Button ID="bttnNewsReport" runat="server" CssClass="buttonA" Text="View News Report"
                                    Width="204px" Font-Bold="True" Font-Size="Small" /></td>
                                    </tr>
                                </tbody>
                            </table>
                            </td>
                        </tr>
                    </table>
                    
                    &nbsp;
                </asp:Panel>
            </td>
        </tr>
    </table>
    <iframe style="position: relative; left: 0px;" runat="server" id="test" border="0px"
        height="800" width="100%"></iframe>
    <cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server" CollapseControlID="TitlePanel"
        Collapsed="false" CollapsedImage="~/Images/Collapse.gif" CollapsedText="-- Show Parameter Form (How Can I View Keyword wise report with Urdu Slug ?) --"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" ExpandedText="-- Hide Parameter Form (How Can I View Keyword wise report with Urdu Slug ?) --"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>
