Imports System.Data
Partial Class SearchEngine_SearchNews
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim dt_Keyword As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                txtUrduScript.Attributes.Add("onkeypress", "search()")
                txtUrduScript.Attributes.Add("Dir", "Rtl")

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                If Not Request.QueryString("TapeNumber") Is Nothing Then
                    txt_TapeNo_1.Text = Request.QueryString("TapeNumber")
                End If

                If Not Request.QueryString("Tapetype") Is Nothing Then
                    txtTapeType1.Text = Request.QueryString("Tapetype")
                End If

                If Not Request.QueryString("ReporterSlug") Is Nothing Then
                    AllReporterSlug.Text = Request.QueryString("ReporterSlug")
                End If

                If Not Request.QueryString("ProposedSlug") Is Nothing Then
                    AllProposedSlug.Text = Request.QueryString("ProposedSlug")
                End If

                If Not Request.QueryString("EnglishScript") Is Nothing Then
                    AllEnglishScript.Text = Request.QueryString("EnglishScript")
                End If

                If Not Request.QueryString("NewsKeyword") Is Nothing Then
                    txt_NewsKeyword_1.Text = Request.QueryString("NewsKeyword")
                End If

                If Not Request.QueryString("FootageType") Is Nothing Then
                    txt_NewsKeyTypes_1.Text = Request.QueryString("FootageType")
                End If

                If Not Request.QueryString("Reporter") Is Nothing Then
                    txtReporterName.Text = Request.QueryString("Reporter")
                End If

                ddlBaseStation.SelectedValue = CInt(Request.Cookies("userinfo")("BaseStationID"))

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Function IsUrduSearchOnly() As Integer
        If (txt_TapeNo_1.Text = "") And (txtTapeType1.Text = "") And (AllReporterSlug.Text = "") _
        And (AllEnglishScript.Text = "") And (txt_NewsKeyword_1.Text = "") And (txt_NewsKeyword_2.Text = "") _
        And (txt_NewsKeyTypes_1.Text = "") And (txt_NewsKeyTypes_2.Text = "") And (txtEntryDate.Text = "") _
        And (txtToDate.Text = "") And (txtReporterName.Text = "") Then
            Return 1
        Else
            Return 0

        End If
    End Function

    Function IsAnySearch() As Integer
        If (txt_TapeNo_1.Text = "") And (txtTapeType1.Text = "") And (AllReporterSlug.Text = "") _
        And (AllEnglishScript.Text = "") And (txt_NewsKeyword_1.Text = "") And (txt_NewsKeyword_2.Text = "") _
        And (txt_NewsKeyTypes_1.Text = "") And (txt_NewsKeyTypes_2.Text = "") And (txtEntryDate.Text = "") _
        And (txtToDate.Text = "") And (txtReporterName.Text = "") And (txtUrduScript.Text = "") Then

            Return 0

        Else

            Return 1

        End If
    End Function


    Protected Sub bttnNext_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnNext.Click
        lblErr.Text = ""
        Dim AnySearch As Integer = IsAnySearch()
        If AnySearch = 0 Then
            lblErr.Text = "Attention: No Searching criteria are enterted.."
            Exit Sub
        End If

        Dim UrduSearchOnly As Integer = IsUrduSearchOnly()

        If UrduSearchOnly = 1 Then
            GetSearch_ByUrduScript()
        Else
            FillControls()
            Try
                ''******************************************************************'
                ''************************** Tape Number **************************''
                ''******************************************************************'

                Dim IgnoreTape As String
                If txt_TapeNo_1.Text = "" And txt_TapeNo_2.Text = "" And txt_TapeNo_3.Text = "" And txt_TapeNo_4.Text = "" And txt_TapeNo_5.Text = "" Then
                    IgnoreTape = "Yes"
                Else
                    IgnoreTape = "No"
                End If
                Dim arr1 As Array = Split(txt_TapeNo_1.Text, "#")
                If arr1.Length = 2 Then
                    txt_TapeNo_1.Text = arr1(1)
                End If
                Dim Tape1 As String
                If Trim(txt_TapeNo_1.Text) = "" Then
                    Tape1 = ""
                Else
                    Tape1 = txt_TapeNo_1.Text
                    Tape1 = Trim(Tape1)
                End If
                Dim arr2 As Array = Split(txt_TapeNo_2.Text, "#")
                If arr2.Length = 2 Then
                    txt_TapeNo_2.Text = arr2(1)
                End If
                Dim Tape2 As String
                If Trim(txt_TapeNo_2.Text) = "" Then
                    Tape2 = ""
                Else
                    Tape2 = txt_TapeNo_2.Text
                    Tape2 = Trim(Tape2)
                End If


                Dim arr3 As Array = Split(txt_TapeNo_3.Text, "#")
                If arr3.Length = 2 Then
                    txt_TapeNo_3.Text = arr3(1)
                End If
                Dim Tape3 As String
                If Trim(txt_TapeNo_3.Text) = "" Then
                    Tape3 = ""
                Else
                    Tape3 = txt_TapeNo_3.Text
                    Tape3 = Trim(Tape3)
                End If

                Dim arr4 As Array = Split(txt_TapeNo_4.Text, "#")
                If arr4.Length = 2 Then
                    txt_TapeNo_4.Text = arr4(1)
                End If
                Dim Tape4 As String
                If Trim(txt_TapeNo_4.Text) = "" Then
                    Tape4 = ""
                Else
                    Tape4 = txt_TapeNo_4.Text
                    Tape4 = Trim(Tape4)
                End If

                Dim arr5 As Array = Split(txt_TapeNo_5.Text, "#")
                If arr5.Length = 2 Then
                    txt_TapeNo_5.Text = arr5(1)
                End If
                Dim Tape5 As String
                If Trim(txt_TapeNo_5.Text) = "" Then
                    Tape5 = ""
                Else
                    Tape5 = txt_TapeNo_5.Text
                    Tape5 = Trim(Tape5)
                End If

                Dim Operator_Tape1_2 As String = Tape_option_2.SelectedItem.Text
                Dim Operator_Tape2_3 As String = Tape_option_2.SelectedItem.Text
                Dim Operator_Tape3_4 As String = Tape_option_3.SelectedItem.Text
                Dim Operator_Tape4_5 As String = Tape_option_4.SelectedItem.Text

                ''******************************************************************'
                ''************************** Report Slug **************************''
                ''******************************************************************'

                Dim IngnoreReporterSlug As String
                If txt_ReporterSlug_1.Text = "" And txt_ReporterSlug_2.Text = "" And txt_ReporterSlug_3.Text = "" And txt_ReporterSlug_4.Text = "" And txt_ReporterSlug_5.Text = "" Then
                    IngnoreReporterSlug = "Yes"
                Else
                    IngnoreReporterSlug = "No"
                End If

                Dim RptSlug1 As String
                If Trim(txt_ReporterSlug_1.Text) = "" Then
                    RptSlug1 = ""
                Else
                    RptSlug1 = txt_ReporterSlug_1.Text
                    RptSlug1 = FormatText(RptSlug1)
                    RptSlug1 = Trim(RptSlug1)
                    RptSlug1 = RS(RptSlug1)
                End If

                Dim RptSlug2 As String
                If Trim(txt_ReporterSlug_2.Text) = "" Then
                    RptSlug2 = ""
                Else
                    RptSlug2 = txt_ReporterSlug_2.Text
                    RptSlug2 = FormatText(RptSlug2)
                    RptSlug2 = Trim(RptSlug2)
                    RptSlug2 = RS(RptSlug2)
                End If

                Dim RptSlug3 As String
                If Trim(txt_ReporterSlug_3.Text) = "" Then
                    RptSlug3 = ""
                Else
                    RptSlug3 = txt_ReporterSlug_3.Text
                    RptSlug3 = FormatText(RptSlug3)
                    RptSlug3 = Trim(RptSlug3)
                    RptSlug3 = RS(RptSlug3)
                End If

                Dim RptSlug4 As String
                If Trim(txt_ReporterSlug_4.Text) = "" Then
                    RptSlug4 = ""
                Else
                    RptSlug4 = txt_ReporterSlug_4.Text
                    RptSlug4 = FormatText(RptSlug4)
                    RptSlug4 = Trim(RptSlug4)
                    RptSlug4 = RS(RptSlug4)
                End If

                Dim RptSlug5 As String
                If Trim(txt_ReporterSlug_5.Text) = "" Then
                    RptSlug5 = ""
                Else
                    RptSlug5 = txt_ReporterSlug_5.Text
                    RptSlug5 = FormatText(RptSlug5)
                    RptSlug5 = Trim(RptSlug5)
                    RptSlug5 = RS(RptSlug5)
                End If

                Dim Operator_RptSlug1_2 As String = ReporterSlug_Option_1.SelectedItem.Text
                Dim Operator_RptSlug2_3 As String = ReporterSlug_Option_2.SelectedItem.Text
                Dim Operator_RptSlug3_4 As String = ReporterSlug_Option_3.SelectedItem.Text
                Dim Operator_RptSlug4_5 As String = ReporterSlug_Option_4.SelectedItem.Text


                ''******************************************************************'
                ''************************ Proposed Slug **************************''
                ''******************************************************************'

                Dim IngnoreProposedSlug As String
                If txt_ProposedSlug_1.Text = "" And txt_ProposedSlug_2.Text = "" And txt_ProposedSlug_3.Text = "" And txt_ProposedSlug_4.Text = "" And txt_ProposedSlug_5.Text = "" Then
                    IngnoreProposedSlug = "Yes"
                Else
                    IngnoreProposedSlug = "No"
                End If

                Dim ProposedSlug1 As String
                If Trim(txt_ProposedSlug_1.Text) = "" Then
                    ProposedSlug1 = ""
                Else
                    ProposedSlug1 = txt_ProposedSlug_1.Text
                    ProposedSlug1 = FormatText(ProposedSlug1)
                    ProposedSlug1 = Trim(ProposedSlug1)
                    ProposedSlug1 = PS(ProposedSlug1)

                End If

                Dim ProposedSlug2 As String
                If Trim(txt_ProposedSlug_2.Text) = "" Then
                    ProposedSlug2 = ""
                Else
                    ProposedSlug2 = txt_ProposedSlug_2.Text
                    ProposedSlug2 = FormatText(ProposedSlug2)
                    ProposedSlug2 = Trim(ProposedSlug2)
                    ProposedSlug2 = PS(ProposedSlug2)
                End If

                Dim ProposedSlug3 As String
                If Trim(txt_ProposedSlug_3.Text) = "" Then
                    ProposedSlug3 = ""
                Else
                    ProposedSlug3 = txt_ProposedSlug_3.Text
                    ProposedSlug3 = FormatText(ProposedSlug3)
                    ProposedSlug3 = Trim(ProposedSlug3)
                    ProposedSlug3 = PS(ProposedSlug3)
                End If

                Dim ProposedSlug4 As String
                If Trim(txt_ProposedSlug_4.Text) = "" Then
                    ProposedSlug4 = ""
                Else
                    ProposedSlug4 = txt_ProposedSlug_4.Text
                    ProposedSlug4 = FormatText(ProposedSlug4)
                    ProposedSlug4 = Trim(ProposedSlug4)
                    ProposedSlug4 = PS(ProposedSlug4)
                End If

                Dim ProposedSlug5 As String
                If Trim(txt_ProposedSlug_5.Text) = "" Then
                    ProposedSlug5 = ""
                Else
                    ProposedSlug5 = txt_ProposedSlug_5.Text
                    ProposedSlug5 = FormatText(ProposedSlug5)
                    ProposedSlug5 = Trim(ProposedSlug5)
                    ProposedSlug5 = PS(ProposedSlug5)
                End If

                Dim Operator_ProposedSlug1_2 As String = ProposedSlug_Option_1.SelectedItem.Text
                Dim Operator_ProposedSlug2_3 As String = ProposedSlug_Option_2.SelectedItem.Text
                Dim Operator_ProposedSlug3_4 As String = ProposedSlug_Option_3.SelectedItem.Text
                Dim Operator_ProposedSlug4_5 As String = ProposedSlug_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Key Word  ***************************''
                ''******************************************************************'

                Dim IngnoreKeyWords As String
                If txt_NewsKeyword_1.Text = "" And txt_NewsKeyword_2.Text = "" And txt_NewsKeyword_3.Text = "" And txt_NewsKeyword_4.Text = "" And txt_NewsKeyword_5.Text = "" Then
                    IngnoreKeyWords = "Yes"
                Else
                    IngnoreKeyWords = "No"
                End If

                Dim KeyWord1 As String
                If Trim(txt_NewsKeyword_1.Text) = "" Then
                    KeyWord1 = ""
                Else
                    KeyWord1 = txt_NewsKeyword_1.Text
                    KeyWord1 = Trim(KeyWord1)
                    KeyWord1 = KW(KeyWord1)
                End If

                Dim KeyWord2 As String
                If Trim(txt_NewsKeyword_2.Text) = "" Then
                    KeyWord2 = ""
                Else
                    KeyWord2 = txt_NewsKeyword_2.Text
                    KeyWord2 = FormatText(KeyWord2)
                    KeyWord2 = Trim(KeyWord2)
                    KeyWord2 = KW(KeyWord2)
                End If

                Dim KeyWord3 As String
                If Trim(txt_NewsKeyword_3.Text) = "" Then
                    KeyWord3 = ""
                Else
                    KeyWord3 = txt_NewsKeyword_3.Text
                    KeyWord3 = FormatText(KeyWord3)
                    KeyWord3 = Trim(KeyWord3)
                    KeyWord3 = KW(KeyWord3)
                End If

                Dim KeyWord4 As String
                If Trim(txt_NewsKeyword_4.Text) = "" Then
                    KeyWord4 = ""
                Else
                    KeyWord4 = txt_NewsKeyword_4.Text
                    KeyWord4 = FormatText(KeyWord4)
                    KeyWord4 = Trim(KeyWord4)
                    KeyWord4 = KW(KeyWord4)
                End If

                Dim KeyWord5 As String
                If Trim(txt_NewsKeyword_5.Text) = "" Then
                    KeyWord5 = ""
                Else
                    KeyWord5 = txt_NewsKeyword_5.Text
                    KeyWord5 = FormatText(KeyWord5)
                    KeyWord5 = Trim(KeyWord5)
                    KeyWord5 = KW(KeyWord5)
                End If

                Dim Operator_KeyWord1_2 As String = KW_Option_1.SelectedItem.Text
                Dim Operator_KeyWord2_3 As String = KW_Option_2.SelectedItem.Text
                Dim Operator_KeyWord3_4 As String = KW_Option_3.SelectedItem.Text
                Dim Operator_KeyWord4_5 As String = KW_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Key Types  **************************''
                ''******************************************************************'

                Dim IngnoreKeyTypes As String

                If txt_NewsKeyTypes_1.Text = "" And txt_NewsKeyTypes_2.Text = "" And txt_NewsKeyTypes_3.Text = "" And txt_NewsKeyTypes_4.Text = "" And txt_NewsKeyTypes_5.Text = "" Then
                    IngnoreKeyTypes = "Yes"
                Else
                    IngnoreKeyTypes = "No"
                End If

                Dim KeyType1 As String
                If Trim(txt_NewsKeyTypes_1.Text) = "" Then
                    KeyType1 = ""
                Else
                    KeyType1 = txt_NewsKeyTypes_1.Text
                    KeyType1 = Trim(KeyType1)
                End If

                Dim KeyType2 As String
                If Trim(txt_NewsKeyTypes_2.Text) = "" Then
                    KeyType2 = ""
                Else
                    KeyType2 = txt_NewsKeyTypes_2.Text
                    KeyType2 = FormatText(KeyType2)
                    KeyType2 = Trim(KeyType2)
                End If

                Dim KeyType3 As String
                If Trim(txt_NewsKeyTypes_3.Text) = "" Then
                    KeyType3 = ""
                Else
                    KeyType3 = txt_NewsKeyTypes_3.Text
                    KeyType3 = FormatText(KeyType3)
                    KeyType3 = Trim(KeyType3)
                End If

                Dim KeyType4 As String
                If Trim(txt_NewsKeyTypes_4.Text) = "" Then
                    KeyType4 = ""
                Else
                    KeyType4 = txt_NewsKeyTypes_4.Text
                    KeyType4 = FormatText(KeyType4)
                    KeyType4 = Trim(KeyType4)
                End If

                Dim KeyType5 As String
                If Trim(txt_NewsKeyTypes_5.Text) = "" Then
                    KeyType5 = ""
                Else
                    KeyType5 = txt_NewsKeyTypes_5.Text
                    KeyType5 = FormatText(KeyType5)
                    KeyType5 = Trim(KeyType5)
                End If

                Dim Operator_KeyType1_2 As String = KT_Option_1.SelectedItem.Text
                Dim Operator_KeyType2_3 As String = KT_Option_2.SelectedItem.Text
                Dim Operator_KeyType3_4 As String = KT_Option_3.SelectedItem.Text
                Dim Operator_KeyType4_5 As String = KT_Option_4.SelectedItem.Text


                ''******************************************************************'
                ''************************* English Script  ***********************''
                ''******************************************************************'

                Dim IgnoreEnglishScript As String

                If txtEnglishScript1.Text = "" And txtEnglishScript2.Text = "" And txtEnglishScript3.Text = "" And txtEnglishScript4.Text = "" And txtEnglishScript5.Text = "" Then
                    IgnoreEnglishScript = "Yes"
                Else
                    IgnoreEnglishScript = "No"
                End If

                Dim EnglishScript1 As String
                If Trim(txtEnglishScript1.Text) = "" Then
                    EnglishScript1 = ""
                Else
                    EnglishScript1 = txtEnglishScript1.Text
                    EnglishScript1 = FormatText(EnglishScript1)
                    EnglishScript1 = Trim(EnglishScript1)
                End If

                Dim EnglishScript2 As String
                If Trim(txtEnglishScript2.Text) = "" Then
                    EnglishScript2 = ""
                Else
                    EnglishScript2 = txtEnglishScript2.Text
                    EnglishScript2 = FormatText(EnglishScript2)
                    EnglishScript2 = Trim(EnglishScript2)
                End If

                Dim EnglishScript3 As String
                If Trim(txtEnglishScript3.Text) = "" Then
                    EnglishScript3 = ""
                Else
                    EnglishScript3 = txtEnglishScript3.Text
                    EnglishScript3 = FormatText(EnglishScript3)
                    EnglishScript3 = Trim(EnglishScript3)
                End If

                Dim EnglishScript4 As String
                If Trim(txtEnglishScript4.Text) = "" Then
                    EnglishScript4 = ""
                Else
                    EnglishScript4 = txtEnglishScript4.Text
                    EnglishScript4 = FormatText(EnglishScript4)
                    EnglishScript4 = Trim(EnglishScript4)
                End If

                Dim EnglishScript5 As String
                If Trim(txtEnglishScript5.Text) = "" Then
                    EnglishScript5 = ""
                Else
                    EnglishScript5 = txtEnglishScript5.Text
                    EnglishScript5 = FormatText(EnglishScript5)
                    EnglishScript5 = Trim(EnglishScript5)
                End If


                Dim Operator_EnglishScript1_2 As String = EnglishScript_Option_1.SelectedItem.Text
                Dim Operator_EnglishScript2_3 As String = EnglishScript_Option_2.SelectedItem.Text
                Dim Operator_EnglishScript3_4 As String = EnglishScript_Option_3.SelectedItem.Text
                Dim Operator_EnglishScript4_5 As String = EnglishScript_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** To Date *****************************''
                ''******************************************************************'

                Dim IgnoreDate As String
                If txtEntryDate.Text = "" Then
                    IgnoreDate = "Yes"
                Else
                    IgnoreDate = "No"
                End If

                Dim EntryDate As String
                EntryDate = txtEntryDate.Text

                Dim ToDate As String
                If txtToDate.Text = "" And txtEntryDate.Text <> "" Then
                    ToDate = txtEntryDate.Text
                Else
                    ToDate = txtToDate.Text
                End If


                ''******************************************************************'
                ''*********************** Reporter Name ***************************''
                ''******************************************************************'

                Dim IgnoreReporter As String
                If txtReporterName.Text = "" Then
                    IgnoreReporter = "Yes"
                Else
                    IgnoreReporter = "No"
                End If

                Dim ReporterName As String
                ReporterName = txtReporterName.Text

                ReporterName = Trim(ReporterName)

                ''*****************************************************************'
                ''*************************** Tape Type  *************************''
                ''*****************************************************************'

                Dim IgnoreTapeType As String
                If txtTapeType1.Text = "" Then
                    IgnoreTapeType = "Yes"
                Else
                    IgnoreTapeType = "No"
                End If

                Dim TapeType1 As String
                If Trim(txtTapeType1.Text) = "" Then
                    TapeType1 = ""
                Else
                    TapeType1 = txtTapeType1.Text
                    TapeType1 = Trim(TapeType1)
                End If

                ''******************************************************************'

                Dim Qry As String
                Qry = "@ContentType=29&@IngnoreTape=" + IgnoreTape + "" & _
                                    "&@Tape1=" + Tape1 & _
                                    "&@Tape2=" + Tape2 & _
                                    "&@Tape3=" + Tape3 & _
                                    "&@Tape4=" + Tape4 & _
                                    "&@Tape5=" + Tape5 & _
                                    "&@Operator_Tape1_2=" + Operator_Tape1_2 & _
                                    "&@Operator_Tape2_3=" + Operator_Tape2_3 & _
                                    "&@Operator_Tape3_4=" + Operator_Tape3_4 & _
                                    "&@Operator_Tape4_5=" + Operator_Tape4_5 & _
                                    "&@IngnoreReporterSlug=" + IngnoreReporterSlug & _
                                    "&@RptSlug1=" + RptSlug1 & _
                                    "&@RptSlug2=" + RptSlug2 & _
                                    "&@RptSlug3=" + RptSlug3 & _
                                    "&@RptSlug4=" + RptSlug4 & _
                                    "&@RptSlug5=" + RptSlug5 & _
                                    "&@Operator_RptSlug1_2=" + Operator_RptSlug1_2 & _
                                    "&@Operator_RptSlug2_3=" + Operator_RptSlug2_3 & _
                                    "&@Operator_RptSlug3_4=" + Operator_RptSlug3_4 & _
                                    "&@Operator_RptSlug4_5=" + Operator_RptSlug4_5 & _
                                    "&@IngnoreProposedSlug=" + IngnoreProposedSlug & _
                                    "&@ProposedSlug1=" + ProposedSlug1 & _
                                    "&@ProposedSlug2=" + ProposedSlug2 & _
                                    "&@ProposedSlug3=" + ProposedSlug3 & _
                                    "&@ProposedSlug4=" + ProposedSlug4 & _
                                    "&@ProposedSlug5=" + ProposedSlug5 & _
                                    "&@Operator_ProposedSlug1_2=" + Operator_ProposedSlug1_2 & _
                                    "&@Operator_ProposedSlug2_3=" + Operator_ProposedSlug2_3 & _
                                    "&@Operator_ProposedSlug3_4=" + Operator_ProposedSlug3_4 & _
                                    "&@Operator_ProposedSlug4_5=" + Operator_ProposedSlug4_5 & _
                                    "&@IngnoreKeyWords=" + IngnoreKeyWords & _
                                    "&@KeyWord1=" + KeyWord1 & _
                                    "&@KeyWord2=" + KeyWord2 & _
                                    "&@KeyWord3=" + KeyWord3 & _
                                    "&@KeyWord4=" + KeyWord4 & _
                                    "&@KeyWord5=" + KeyWord5 & _
                                    "&@Operator_KeyWord1_2=" + Operator_KeyWord1_2 & _
                                    "&@Operator_KeyWord2_3=" + Operator_KeyWord2_3 & _
                                    "&@Operator_KeyWord3_4=" + Operator_KeyWord3_4 & _
                                    "&@Operator_KeyWord4_5=" + Operator_KeyWord4_5 & _
                                    "&@IngnoreKeyTypes=" + IngnoreKeyTypes & _
                                    "&@KeyType1=" + KeyType1 & _
                                    "&@KeyType2=" + KeyType2 & _
                                    "&@KeyType3=" + KeyType3 & _
                                    "&@KeyType4=" + KeyType4 & _
                                    "&@KeyType5=" + KeyType5 & _
                                    "&@Operator_KeyType1_2=" + Operator_KeyType1_2 & _
                                    "&@Operator_KeyType2_3=" + Operator_KeyType2_3 & _
                                    "&@Operator_KeyType3_4=" + Operator_KeyType3_4 & _
                                    "&@Operator_KeyType4_5=" + Operator_KeyType4_5 & _
                                    "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue & _
                                    "&@IgnoreDate=" + IgnoreDate & _
                                    "&@EntryDate=" + EntryDate & _
                                    "&@IgnoreReporter=" + IgnoreReporter & _
                                    "&@ReporterName=" + ReporterName & _
                                    "&@IgnoreEnglishScript=" + IgnoreEnglishScript & _
                                    "&@EnglishScript1=" + EnglishScript1 & _
                                    "&@EnglishScript2=" + EnglishScript2 & _
                                    "&@EnglishScript3=" + EnglishScript3 & _
                                    "&@EnglishScript4=" + EnglishScript4 & _
                                    "&@EnglishScript5=" + EnglishScript5 & _
                                    "&@Operator_EnglishScript1_2=" + Operator_EnglishScript1_2 & _
                                    "&@Operator_EnglishScript2_3=" + Operator_EnglishScript2_3 & _
                                    "&@Operator_EnglishScript3_4=" + Operator_EnglishScript3_4 & _
                                    "&@Operator_EnglishScript4_5=" + Operator_EnglishScript4_5 & _
                                    "&@IgnoreTapeType=" + IgnoreTapeType & _
                                    "&@TapeType1=" + TapeType1 & _
                                    "&@ToDate=" + ToDate & _
                                    "&@BaseStationID=" + ddlBaseStation.SelectedValue


                Dim Arr As Array = Split(txtUrduScript.Text, "+")
                If Arr.Length = 5 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(3) + "%&@UrduScript5=%" + Arr(4) + "%"
                ElseIf Arr.Length = 4 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(3) + "%&@UrduScript5=%" + Arr(3) + "%"
                ElseIf Arr.Length = 3 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(2) + "%&@UrduScript5=%" + Arr(2) + "%"
                ElseIf Arr.Length = 2 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(1) + "%&@UrduScript4=%" + Arr(1) + "%&@UrduScript5=%" + Arr(1) + "%"
                ElseIf Arr.Length = 1 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(0) + "%&@UrduScript3=%" + Arr(0) + "%&@UrduScript4=%" + Arr(0) + "%&@UrduScript5=%" + Arr(0) + "%"
                End If

                Qry += "&@IsUrduSearchOnly=0"


                Response.Write("<script type='text/javascript'>detailedresults=window.open('News_Results.aspx?" & Qry & "');</script>")

                MostSearchElements()

                SetLableSize()

                Try
                    Me.Session.Add("rowindex", -1)
                Catch ex As Exception
                End Try

            Catch ex As Exception
                Throw
            End Try
        End If
       
    End Sub

    Private Sub SetLableSize()
        Label7.Font.Size = 8
        Label8.Font.Size = 8
        Label9.Font.Size = 8
        Label10.Font.Size = 8
        Label11.Font.Size = 8
        Label12.Font.Size = 8
        Label13.Font.Size = 8
        Label14.Font.Size = 8
        Label15.Font.Size = 8
        Label16.Font.Size = 8
        Label17.Font.Size = 8
        Label18.Font.Size = 8
        Label19.Font.Size = 8
    End Sub

    Private Function KW(ByVal KW1 As String) As String
        Dim arr As Array = Split(KW1, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = KW1
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            KW1 = a

        Else
            Dim b As String
            b = KW1
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            KW1 = b

        End If
        Return KW1
    End Function

    Private Function RS(ByVal RS1 As String) As String
        Dim arr As Array = Split(RS1, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = RS1
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            RS1 = a

        Else
            Dim b As String
            b = RS1
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            RS1 = b

        End If
        Return RS1
    End Function

    Private Function PS(ByVal PS1 As String) As String
        Dim arr As Array = Split(PS1, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = PS1
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            PS1 = a

        Else
            Dim b As String
            b = PS1
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            PS1 = b

        End If
        Return PS1
    End Function

    Private Function FormatText(ByVal Text As String) As String
        Try
            Dim A As String = Text
            A = A.Replace("_", " ")
            A = A.Replace("!", " ")
            A = A.Replace("@", " ")
            A = A.Replace("""", " ")
            A = A.Replace("#", " ")
            A = A.Replace("$", " ")
            A = A.Replace("%", " ")
            A = A.Replace("^", " ")
            A = A.Replace("&", " ")
            A = A.Replace("(", " ")
            A = A.Replace(")", " ")
            A = A.Replace("+", " ")
            A = A.Replace("|", " ")
            A = A.Replace("~", " ")
            A = A.Replace("`", " ")
            A = A.Replace("-", " ")
            A = A.Replace("=", " ")
            A = A.Replace(",", " ")
            A = A.Replace(".", " ")
            A = A.Replace("/", " ")
            A = A.Replace("?", " ")
            A = A.Replace(">", " ")
            A = A.Replace("<", " ")
            A = A.Replace(";", " ")
            A = A.Replace(":", " ")
            A = A.Replace("'", " ")
            A = A.Replace("[", " ")
            A = A.Replace("]", " ")
            A = A.Replace("{", " ")
            A = A.Replace("}", " ")
            A = A.Replace("    ", " ")
            A = A.Replace("   ", " ")
            A = A.Replace("  ", " ")
            A = A.Replace("  ", " ")
            Text = A
            Return Text
        Catch ex As Exception
            Throw
        End Try


    End Function

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click

        ''*****************************''
        ''******** Tape Number ********''
        ''*****************************''

        txt_TapeNo_1.Text = String.Empty
        txt_TapeNo_2.Text = String.Empty
        txt_TapeNo_3.Text = String.Empty
        txt_TapeNo_4.Text = String.Empty
        txt_TapeNo_5.Text = String.Empty

        ''*****************************''
        ''******** Report Slug ********''
        ''*****************************''

        txt_ReporterSlug_1.Text = String.Empty
        txt_ReporterSlug_2.Text = String.Empty
        txt_ReporterSlug_3.Text = String.Empty
        txt_ReporterSlug_4.Text = String.Empty
        txt_ReporterSlug_5.Text = String.Empty

        ''*****************************''
        ''******* Proposed Slug *******''
        ''*****************************''

        txt_ProposedSlug_1.Text = String.Empty
        txt_ProposedSlug_2.Text = String.Empty
        txt_ProposedSlug_3.Text = String.Empty
        txt_ProposedSlug_4.Text = String.Empty
        txt_ProposedSlug_5.Text = String.Empty

        ''*****************************''
        ''******** News KeyWord *******''
        ''*****************************''

        txt_NewsKeyword_1.Text = String.Empty
        txt_NewsKeyword_2.Text = String.Empty
        txt_NewsKeyword_3.Text = String.Empty
        txt_NewsKeyword_4.Text = String.Empty
        txt_NewsKeyword_5.Text = String.Empty


        ''*****************************''
        ''*** Footages ( KeyTypes ) ***''
        ''*****************************''

        txt_NewsKeyTypes_1.Text = String.Empty
        txt_NewsKeyTypes_2.Text = String.Empty
        txt_NewsKeyTypes_3.Text = String.Empty
        txt_NewsKeyTypes_4.Text = String.Empty
        txt_NewsKeyTypes_5.Text = String.Empty

        ''*****************************''
        ''******* English Script ******''
        ''*****************************''

        txtEnglishScript1.Text = String.Empty
        txtEnglishScript2.Text = String.Empty
        txtEnglishScript3.Text = String.Empty
        txtEnglishScript4.Text = String.Empty
        txtEnglishScript5.Text = String.Empty

        ''*****************************''
        ''********* Entry Date ********''
        ''*****************************''

        txtEntryDate.Text = String.Empty
        txtToDate.Text = String.Empty

        ''*****************************''
        ''******** Reporter Name ******''
        ''*****************************''

        txtReporterName.Text = String.Empty

        ''*****************************''
        ''****** Result per Page ******''
        ''*****************************''

        ddl_ResultPerPage.SelectedIndex = 0

        ''*****************************''
        ''*********** All txt *********''
        ''*****************************''

        AllReporterSlug.Text = String.Empty
        AllProposedSlug.Text = String.Empty
        AllEnglishScript.Text = String.Empty
        txtTapeType1.Text = String.Empty

    End Sub

    Private Sub FillControls()
        ''*****************************''
        ''******** Report Slug ********''
        ''*****************************''

        Dim Description As String = AllReporterSlug.Text

        txt_ReporterSlug_1.Text = ""
        txt_ReporterSlug_2.Text = ""
        txt_ReporterSlug_3.Text = ""
        txt_ReporterSlug_4.Text = ""
        txt_ReporterSlug_5.Text = ""


        Dim stringItems() As String = Description.Split("~")
        Dim myArrayList As New ArrayList
        Dim k As Integer
        k = stringItems.Length

        If AllReporterSlug.Text = "" Then
            txt_ReporterSlug_1.Text = ""
            txt_ReporterSlug_2.Text = ""
            txt_ReporterSlug_3.Text = ""
            txt_ReporterSlug_4.Text = ""
            txt_ReporterSlug_5.Text = ""
        End If

        If k <> 0 Then
            txt_ReporterSlug_1.Text = stringItems(0).ToString
        End If


        If k = 1 Then
            txt_ReporterSlug_2.Text = ""
            txt_ReporterSlug_3.Text = ""
            txt_ReporterSlug_4.Text = ""
            txt_ReporterSlug_5.Text = ""
        End If

        If k > 1 And k < 3 Then
            txt_ReporterSlug_2.Text = stringItems(1).ToString
            txt_ReporterSlug_3.Text = ""
            txt_ReporterSlug_4.Text = ""
            txt_ReporterSlug_5.Text = ""

        End If
        If k > 2 And k < 4 Then
            txt_ReporterSlug_2.Text = stringItems(1).ToString
            txt_ReporterSlug_3.Text = stringItems(2).ToString
            txt_ReporterSlug_4.Text = ""
            txt_ReporterSlug_5.Text = ""
        End If

        If k > 3 And k < 5 Then
            txt_ReporterSlug_2.Text = stringItems(1).ToString
            txt_ReporterSlug_3.Text = stringItems(2).ToString
            txt_ReporterSlug_4.Text = stringItems(3).ToString
            txt_ReporterSlug_5.Text = ""
        End If

        If k > 4 And k < 6 Then
            txt_ReporterSlug_2.Text = stringItems(1).ToString
            txt_ReporterSlug_3.Text = stringItems(2).ToString
            txt_ReporterSlug_4.Text = stringItems(3).ToString
            txt_ReporterSlug_5.Text = stringItems(4).ToString
        End If

        ''''''''''''''''''''''''''''''''''''

        ''*******************************''
        ''******** Proposed Slug ********''
        ''*******************************''

        Dim ProposedSlug As String = AllProposedSlug.Text

        txt_ProposedSlug_1.Text = ""
        txt_ProposedSlug_2.Text = ""
        txt_ProposedSlug_3.Text = ""
        txt_ProposedSlug_4.Text = ""
        txt_ProposedSlug_5.Text = ""


        Dim Proposed_stringItems() As String = ProposedSlug.Split("~")
        Dim Proposed_myArrayList As New ArrayList
        Dim k1 As Integer
        k1 = Proposed_stringItems.Length

        If AllProposedSlug.Text = "" Then
            txt_ProposedSlug_1.Text = ""
            txt_ProposedSlug_2.Text = ""
            txt_ProposedSlug_3.Text = ""
            txt_ProposedSlug_4.Text = ""
            txt_ProposedSlug_5.Text = ""
        End If

        If k1 <> 0 Then
            txt_ProposedSlug_1.Text = Proposed_stringItems(0).ToString
        End If


        If k1 = 1 Then
            txt_ProposedSlug_2.Text = ""
            txt_ProposedSlug_3.Text = ""
            txt_ProposedSlug_4.Text = ""
            txt_ProposedSlug_5.Text = ""
        End If

        If k1 > 1 And k1 < 3 Then
            txt_ProposedSlug_2.Text = Proposed_stringItems(1).ToString
            txt_ProposedSlug_3.Text = ""
            txt_ProposedSlug_4.Text = ""
            txt_ProposedSlug_5.Text = ""

        End If
        If k1 > 2 And k1 < 4 Then
            txt_ProposedSlug_2.Text = Proposed_stringItems(1).ToString
            txt_ProposedSlug_3.Text = Proposed_stringItems(2).ToString
            txt_ProposedSlug_4.Text = ""
            txt_ProposedSlug_5.Text = ""
        End If

        If k1 > 3 And k1 < 5 Then
            txt_ProposedSlug_2.Text = Proposed_stringItems(1).ToString
            txt_ProposedSlug_3.Text = Proposed_stringItems(2).ToString
            txt_ProposedSlug_4.Text = Proposed_stringItems(3).ToString
            txt_ProposedSlug_5.Text = ""
        End If

        If k1 > 4 And k1 < 6 Then
            txt_ProposedSlug_2.Text = Proposed_stringItems(1).ToString
            txt_ProposedSlug_3.Text = Proposed_stringItems(2).ToString
            txt_ProposedSlug_4.Text = Proposed_stringItems(3).ToString
            txt_ProposedSlug_5.Text = Proposed_stringItems(4).ToString
        End If

        ''''''''''''''''''''''''''''''''''''

        ''*************************************''
        ''******** English Script Slug ********''
        ''*************************************''

        Dim EngScript As String = AllEnglishScript.Text

        txtEnglishScript1.Text = ""
        txtEnglishScript2.Text = ""
        txtEnglishScript3.Text = ""
        txtEnglishScript4.Text = ""
        txtEnglishScript5.Text = ""


        Dim EngScript_stringItems() As String = EngScript.Split("~")
        Dim EngScript_myArrayList As New ArrayList
        Dim k2 As Integer
        k2 = EngScript_stringItems.Length

        If AllEnglishScript.Text = "" Then
            txtEnglishScript1.Text = ""
            txtEnglishScript2.Text = ""
            txtEnglishScript3.Text = ""
            txtEnglishScript4.Text = ""
            txtEnglishScript5.Text = ""
        End If

        If k2 <> 0 Then
            txtEnglishScript1.Text = EngScript_stringItems(0).ToString
        End If


        If k2 = 1 Then
            txtEnglishScript2.Text = ""
            txtEnglishScript3.Text = ""
            txtEnglishScript4.Text = ""
            txtEnglishScript5.Text = ""
        End If

        If k2 > 1 And k2 < 3 Then
            txtEnglishScript2.Text = EngScript_stringItems(1).ToString
            txtEnglishScript3.Text = ""
            txtEnglishScript4.Text = ""
            txtEnglishScript5.Text = ""

        End If
        If k2 > 2 And k2 < 4 Then
            txtEnglishScript2.Text = EngScript_stringItems(1).ToString
            txtEnglishScript3.Text = EngScript_stringItems(2).ToString
            txtEnglishScript4.Text = ""
            txtEnglishScript5.Text = ""
        End If

        If k2 > 3 And k2 < 5 Then
            txtEnglishScript2.Text = EngScript_stringItems(1).ToString
            txtEnglishScript3.Text = EngScript_stringItems(2).ToString
            txtEnglishScript4.Text = EngScript_stringItems(3).ToString
            txtEnglishScript5.Text = ""
        End If

        If k2 > 4 And k2 < 6 Then
            txtEnglishScript2.Text = EngScript_stringItems(1).ToString
            txtEnglishScript3.Text = EngScript_stringItems(2).ToString
            txtEnglishScript4.Text = EngScript_stringItems(3).ToString
            txtEnglishScript5.Text = EngScript_stringItems(4).ToString
        End If

    End Sub

    Private Sub MostSearchElements()
        Try
            Dim ObjSave As New BusinessFacade.Reports()

            ''********** Tape Number **********''
            If txt_TapeNo_1.Text <> "" Then
                ObjSave.SearchType = "TapeNumber"
                ObjSave.SearchElement = txt_TapeNo_1.Text
                ObjSave.SearchTextBox = "txt_TapeNo_1.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''*********** Tape Type ***********''
            If txtTapeType1.Text <> "" Then
                ObjSave.SearchType = "Tapetype"
                ObjSave.SearchElement = txtTapeType1.Text
                ObjSave.SearchTextBox = "txtTapeType1.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ ReporterSlug ***********''
            If AllReporterSlug.Text <> "" Then
                ObjSave.SearchType = "ReporterSlug"
                ObjSave.SearchElement = AllReporterSlug.Text
                ObjSave.SearchTextBox = "AllReporterSlug.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ ProposedSlug ***********''
            If AllProposedSlug.Text <> "" Then
                ObjSave.SearchType = "ProposedSlug"
                ObjSave.SearchElement = AllProposedSlug.Text
                ObjSave.SearchTextBox = "AllProposedSlug.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ ProposedSlug ***********''
            If AllEnglishScript.Text <> "" Then
                ObjSave.SearchType = "EnglishScript"
                ObjSave.SearchElement = AllEnglishScript.Text
                ObjSave.SearchTextBox = "AllEnglishScript.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ NewsKeyword ***********''
            If txt_NewsKeyword_1.Text <> "" Then
                ObjSave.SearchType = "NewsKeyword"
                ObjSave.SearchElement = txt_NewsKeyword_1.Text
                ObjSave.SearchTextBox = "txt_NewsKeyword_1.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ FootageType ***********''
            If txt_NewsKeyTypes_1.Text <> "" Then
                ObjSave.SearchType = "FootageType"
                ObjSave.SearchElement = txt_NewsKeyTypes_1.Text
                ObjSave.SearchTextBox = "txt_NewsKeyTypes_1.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If


            ''************ FootageType ***********''
            If txtReporterName.Text <> "" Then
                ObjSave.SearchType = "Reporter"
                ObjSave.SearchElement = txtReporterName.Text
                ObjSave.SearchTextBox = "txtReporterName.Text"
                ObjSave.ContentType = "News"
                ObjSave.Insert_MostSearchedElements()
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetSearch_ByUrduScript()

        Dim Arr As Array = Split(txtUrduScript.Text, "+")

        If Arr.Length < 6 Then
            Dim Qry As String = ""
            If Arr.Length = 5 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(3) + "&@UrduScript5=" + Arr(4) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 4 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(3) + "&@UrduScript5=" + Arr(3) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 3 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(2) + "&@UrduScript5=" + Arr(2) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 2 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(1) + "&@UrduScript4=" + Arr(1) + "&@UrduScript5=" + Arr(1) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 1 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(0) + "&@UrduScript3=" + Arr(0) + "&@UrduScript4=" + Arr(0) + "&@UrduScript5=" + Arr(0) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            End If

            Qry += "&@IsUrduSearchOnly=1"
            Qry += "&@BaseStationID=" + ddlBaseStation.SelectedValue.ToString()

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('News_Results.aspx?" + Qry + "', 'mywindow','width=1000, height=760, left=50, top=30, menubar=yes, status=yes, location=yes, toolbar=yes, scrollbars=yes, resizable=yes'); "
            script = script + "}</script>"
            Page.RegisterClientScriptBlock("test", script)

        End If

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If Button1.Text = "<< Show Keyboard >>" Then
            Image1.Visible = True
            Button1.Text = "<< Hide Keyboard >>"
        Else
            Image1.Visible = False
            Button1.Text = "<< Show Keyboard >>"
        End If
    End Sub


    Protected Sub btnView_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnView.Click
        Response.Write("<script type='text/javascript'>detailedresults=window.open('frmTapeAddforIssue.aspx','_blank','toolbar=yes, scrollbars=yes, resizable=yes, top=100, left=100, width=400, height=400');</script>")
    End Sub

End Class
