<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="TapeReturn.aspx.vb" Inherits="TapeReturn_TapeReturn" Title="Home > Tape Management >Blank Tape Return" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table width="100%">
        <tr>
            <td style="width: 100px">
                <asp:ScriptManager ID="ScriptManager1" runat="server">
                </asp:ScriptManager>
            </td>
        </tr>
        <tr>
            <td style="width: 100%; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Blank Tape Return</td>
        </tr>
        <tr>
            <td style="height: 9px; text-decoration: underline;" class="labelheading"></td>
        </tr>
        <tr>
            <td style="width: 100%; height: 0px;" valign="top">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue">
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Font-Bold="True" Text="(Show Form) - Bulk Return Form"
                        Width="448px" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label><br />
                </asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <table width="100%">
                        <tr>
                            <td valign="top" style="width: 718px">
                                <table>
                                    <tr class="mytext">
                                        <td colspan="6"></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 221px; height: 26px">Employee Name
                                <asp:Image ID="Err_Emp_Bulk" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                        <td style="width: 221px; height: 26px">Department Name</td>
                                        <td style="width: 41px; height: 26px"></td>
                                        <td style="height: 26px">Entry Date</td>
                                        <td style="height: 26px"></td>
                                        <td style="width: 3px; height: 26px">
                                            <asp:DropDownList CssClass="mytext" ID="ddl_Department" runat="server" AutoPostBack="True" Width="72px" Visible="False">
                                            </asp:DropDownList></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 221px; height: 26px;">
                                            <asp:TextBox ID="txt_Employee" runat="server" Width="208px" CssClass="mytext"></asp:TextBox>
                                        </td>
                                        <td style="width: 221px; height: 26px">
                                            <asp:TextBox ID="txtDepartmentName" runat="server" CssClass="mytext" Width="208px"></asp:TextBox></td>
                                        <td style="width: 41px; height: 26px">
                                            <asp:LinkButton ID="lnkSearch_BulkTapeReturn" runat="server">Search</asp:LinkButton></td>
                                        <td style="height: 26px">
                                            <asp:TextBox ID="txtBulkEntryDate" runat="server" CssClass="mytext" Width="112px"></asp:TextBox></td>
                                        <td style="height: 26px">
                                            <asp:TextBox ID="TextBox1" runat="server" Visible="False" Width="40px"></asp:TextBox>&nbsp;</td>
                                        <td style="width: 3px; height: 26px;">
                                            <asp:DropDownList CssClass="mytext" ID="ddl_Employee" runat="server" AutoPostBack="True" Width="80px" Visible="False">
                                            </asp:DropDownList></td>
                                    </tr>
                                </table>
                                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="432px" CssClass="mytext"></asp:Label></td>
                        </tr>
                        <tr class="mytext">
                            <td style="width: 718px; height: 29px" class="bottomMain" valign="middle">&nbsp;<asp:Button CssClass="buttonA" ID="bttnReturnBulkTape" runat="server" Font-Bold="True"
                                Text="Return" Width="80px" />
                                <asp:Button CssClass="buttonA" ID="bttnCancel_BulkReturn" runat="server" Text="Cancel" Width="80px" Font-Bold="True" />
                                <asp:Button ID="Button2" runat="server" Text="Bulk Return Email" Visible="False" /></td>
                        </tr>
                    </table>
                    <asp:GridView ID="dg_BulkReturn" runat="server" AutoGenerateColumns="False"
                        Width="100%" CssClass="gridContent" AllowPaging="True" PageSize="15">
                        <Columns>
                            <asp:TemplateField HeaderText="S.No">
                                <ItemTemplate>
                                    <%#Container.DataItemIndex + 1%>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" />
                            <asp:BoundField DataField="IssuedToDepartmentID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="DepartmentName" HeaderText="Department" />
                            <asp:BoundField DataField="TapeTypeID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                            <asp:BoundField DataField="Quantity" HeaderText="Issued Qty" />
                            <asp:BoundField DataField="Returned" HeaderText="Return Qty" />
                            <asp:BoundField DataField="RemainingQty" HeaderText="Remaining Qty" />
                            <asp:TemplateField HeaderText="Quantity">
                                <ItemStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:TextBox ID="Qty" runat="server" Width="48px"></asp:TextBox>
                                    <cc1:FilteredTextBoxExtender ID="FilteredTextBoxExtender1" runat="server"
                                        TargetControlID="Qty" ValidChars="1234567890">
                                    </cc1:FilteredTextBoxExtender>
                                </ItemTemplate>
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="IssuedToEmployeeID" HeaderText="IssuedToEmployeeID" />
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                </asp:Panel>
            </td>
        </tr>
    </table>
    <asp:Panel ID="TitlePanel_1" runat="server" BackColor="LightSteelBlue" Width="100%">
        <asp:Image ID="Image2" runat="server" />
        <asp:Label ID="Label2" runat="server" Font-Bold="True" Text="(Show Form) - One By One Tape Return"
            Width="480px" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label><br />
    </asp:Panel>
    <asp:Panel ID="ContentPanel_2" runat="server" Height="50px" Width="100%">
        <table style="width: 100%">
            <tr>
                <td style="height: 83px">
                    <table>
                        <tr class="mytext">
                            <td style="height: 13px"></td>
                            <td style="width: 135px; height: 13px"></td>
                            <td style="width: 68px; height: 13px"></td>
                            <td style="width: 68px; height: 13px"></td>
                            <td style="width: 157px; height: 13px"></td>
                            <td style="width: 157px; height: 13px"></td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 13px">Employee Name &nbsp;&nbsp;
                                <asp:Image ID="Err_Emp_1By1" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                            <td style="width: 135px; height: 13px">Tape Number &nbsp;&nbsp;
                                <asp:Image ID="Err_TapeNo_1By1" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                            <td style="width: 68px; height: 13px"></td>
                            <td style="width: 68px; height: 13px">Entry Date</td>
                            <td style="width: 157px; height: 13px"></td>
                            <td style="width: 157px; height: 13px">
                                <asp:DropDownList ID="ddl_Emp_1By1" runat="server" AutoPostBack="True" CssClass="mytext"
                                    Width="160px" Visible="False">
                                </asp:DropDownList></td>
                        </tr>
                        <tr>
                            <td class="mytext">
                                <asp:TextBox ID="txt_EmpName_1by1" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                            <td style="width: 135px" class="mytext">
                                <asp:TextBox ID="txt_TapeNumber_Return" runat="server" CssClass="mytext"></asp:TextBox></td>
                            <td style="width: 68px" class="mytext">
                                <asp:LinkButton ID="lnkSearch" runat="server" Height="16px" Width="40px">Search</asp:LinkButton></td>
                            <td class="mytext" style="width: 68px">
                                <asp:TextBox ID="txtEntryDate_1by1Return" runat="server" CssClass="mytext" Width="112px"></asp:TextBox></td>
                            <td class="mytext" style="width: 157px">
                                <asp:TextBox ID="txtIsEmployeeSearch" runat="server" BackColor="#FF8080" BorderStyle="None"
                                    Width="46px" Visible="False"></asp:TextBox></td>
                            <td class="mytext" style="width: 157px">
                                <asp:DropDownList ID="ddl_TapeNumber" runat="server" CssClass="mytext" Width="160px" Visible="False">
                                </asp:DropDownList></td>
                        </tr>
                        <tr>
                            <td class="mytext"></td>
                            <td class="mytext" style="width: 135px"></td>
                            <td class="mytext" style="width: 68px"></td>
                            <td class="mytext" style="width: 68px"></td>
                            <td class="mytext" style="width: 157px"></td>
                            <td class="mytext" style="width: 157px"></td>
                        </tr>
                    </table>
                    <asp:Label ID="lblErr2" runat="server" Font-Bold="True" ForeColor="Red"
                        Width="432px" Font-Names="Arial" Font-Size="9pt"></asp:Label></td>
            </tr>
            <tr class="mytext">
                <td class="bottomMain" style="width: 100%; height: 29px">&nbsp; &nbsp;<asp:Button ID="bttnSave_Return" runat="server" CssClass="buttonA" Text="Return ( Contains Data )"
                    Width="181px" Font-Bold="True" />&nbsp; &nbsp;<asp:Button ID="bttnCancel_Return" runat="server" CssClass="buttonA" Text="Cancel"
                        Width="108px" Font-Bold="True" />&nbsp;
                    &nbsp;<asp:Button ID="bttnSave_Return_EmptyData" runat="server" CssClass="buttonA" Text="Return ( Empty Tapes ) "
                        Width="207px" Font-Bold="True" />
                    &nbsp; &nbsp;
                         <asp:Button ID="Button1" runat="server" CssClass="buttonA" Text="Email"
                             Width="132px" BackColor="Yellow" Font-Bold="True" Visible="False" />
                    <asp:Button ID="bttnOneByOneReturn" runat="server" CssClass="buttonA" Text="Return (old)"
                        Width="80px" Visible="False" />&nbsp;
                    <asp:Button ID="bttnCancel_1By1" runat="server" CssClass="buttonA" Text="Cancel (old)"
                        Width="80px" Visible="False" />
                    <asp:Button ID="bttn_Add" runat="server" CssClass="buttonA" Text="Add" Width="80px" Font-Bold="True" Visible="False" /></td>
            </tr>
            <tr class="mytext">
                <td align="center" style="width: 100%" valign="middle">
                    <asp:Label ID="lblRecordCount" runat="server" BackColor="#FFE0C0" Font-Bold="True"
                        Font-Size="10pt" ForeColor="Maroon" Height="17px" Width="100%" Visible="False"></asp:Label></td>
            </tr>
            <tr>
                <td abbr="GO" style="width: 100px; height: 21px">
                    <asp:GridView ID="dg_OneByOne" runat="server" AutoGenerateColumns="False" CssClass="gridContent" Width="848px">
                        <Columns>
                            <asp:TemplateField HeaderText="S.No">
                                <ItemTemplate>
                                    <%#Container.DataItemIndex + 1%>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="TapeIssuanceID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="1px" />
                                <HeaderStyle Width="1px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeLibraryID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="1px" />
                                <HeaderStyle Width="1px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name">
                                <ItemStyle Width="165px" />
                            </asp:BoundField>
                            <asp:TemplateField>
                                <ItemTemplate>
                                    <asp:CheckBox ID="Chk" onclick="Check_Click(this)" runat="server" />
                                </ItemTemplate>
                                <HeaderTemplate>
                                    All
                                                      <asp:CheckBox ID="checkAll" runat="server" onclick="checkAll(this);" />
                                </HeaderTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="EmployeeID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeIssuanceID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeIssuanceDate" DataFormatString="{0:dd-MMM-yyyy}" HeaderText="Tape Issuance Date"
                                HtmlEncode="False">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                            <asp:BoundField DataField="ProgramChildName" HeaderText="Program Child">
                                <ItemStyle Width="225px" />
                            </asp:BoundField>
                            <asp:TemplateField ShowHeader="False">
                                <ItemTemplate>
                                    <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Delete"
                                        OnClientClick="return confirm('Are you Sure you want to Delete ?')" Text="Delete"></asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                        <AlternatingRowStyle BackColor="#E0E0E0" />
                    </asp:GridView>
                    <br />
                    <asp:GridView ID="dg_OneByOne_New" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                        Width="560px">
                        <Columns>
                            <asp:BoundField DataField="TapeLibraryID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:BoundField DataField="EmployeeName" HeaderText="EmployeeName" />
                            <asp:BoundField DataField="EmployeeID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="Check">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckBox1" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="TapeIssuanceID" Visible="False">
                                <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeIssuanceDate" DataFormatString="{0:dd-MMM-yyyy}" HeaderText="Tape Issuance Date"
                                HtmlEncode="False">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:BoundField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                </td>
            </tr>
        </table>
    </asp:Panel>

    <cc1:CollapsiblePanelExtender
        ID="CollapsiblePanelExtender1"
        runat="server"
        CollapseControlID="TitlePanel"
        Collapsed="true"
        CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Parameter Form (Bulk Tape Return) --"
        ExpandControlID="TitlePanel"
        ExpandedImage="~/Images/expand.gif"
        ExpandedText="-- Hide Parameter Form (Bulk Tape Return) --"
        ImageControlID="Image1"
        SuppressPostBack="true"
        TextLabelID="Label1"
        TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_Emp_1By1">
    </cc1:ListSearchExtender>
    <cc1:CollapsiblePanelExtender
        ID="CollapsiblePanelExtender2"
        runat="server"
        CollapseControlID="TitlePanel_1"
        Collapsed="true"
        CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Parameter Form (One - By - One Tape Return) --"
        ExpandControlID="TitlePanel_1"
        ExpandedImage="~/Images/expand.gif"
        ExpandedText="-- Hide Parameter Form (One - By - One Tape Return) --"
        ImageControlID="Image2"
        SuppressPostBack="true"
        TextLabelID="Label2"
        TargetControlID="ContentPanel_2">
    </cc1:CollapsiblePanelExtender>
    <cc1:AutoCompleteExtender
        ID="AutoCompleteExtender_TapeNo_onebyoneReturn"
        runat="server"
        CompletionInterval="1"
        CompletionSetCount="12"
        EnableCaching="true"
        MinimumPrefixLength="3"
        ServiceMethod="GetTapeNumebrs_TapeReturn"
        ServicePath="AutoComplete.asmx"
        TargetControlID="txt_TapeNumber_Return">
    </cc1:AutoCompleteExtender>
    <cc1:AutoCompleteExtender
        ID="AutoCompleteExtender_EmpName_TapeReturn1By1"
        runat="server"
        CompletionInterval="1"
        CompletionSetCount="12"
        EnableCaching="true"
        MinimumPrefixLength="3"
        ServiceMethod="GetEmployeeRecord_TapeReturn_1by1"
        ServicePath="AutoComplete.asmx"
        TargetControlID="txt_EmpName_1by1">
    </cc1:AutoCompleteExtender>
    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtEntryDate_1by1Return" Format="dd-MMM-yyyy" CssClass="MyCalendar">
    </cc1:CalendarExtender>
    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtBulkEntryDate" Format="dd-MMM-yyyy" CssClass="MyCalendar">
    </cc1:CalendarExtender>
    <cc1:AutoCompleteExtender
        ID="AutoCompleteExtender1"
        runat="server"
        CompletionInterval="1"
        CompletionSetCount="12"
        EnableCaching="true"
        MinimumPrefixLength="1"
        ServiceMethod="GetDepartment"
        ServicePath="AutoComplete.asmx"
        TargetControlID="txtDepartmentName">
    </cc1:AutoCompleteExtender>
    <cc1:AutoCompleteExtender
        ID="AutoCompleteExtender_Employee_BulkReturn"
        runat="server"
        CompletionInterval="1"
        CompletionSetCount="12"
        EnableCaching="true"
        MinimumPrefixLength="1"
        ServiceMethod="GetBulkTapeIssuedEmployee"
        ServicePath="AutoComplete.asmx"
        TargetControlID="txt_Employee">
    </cc1:AutoCompleteExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_Employee">
    </cc1:ListSearchExtender>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>