<%@ Page Language="VB" AutoEventWireup="false" MasterPageFile="~/MasterPage.master" CodeFile="IssueSelectedTape.aspx.vb"
    Inherits="SearchEngine_IssueSelectedTape" Title="Home > Archive Tape Setup > Archive Tape Issue " %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    
    <table width="100%">
        <tr>
            <td style="width: 100%; height: 21px; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Archive Tape Setup &gt; Archive Tape Issue </td>
        </tr>

        <tr>
            <td style="width: 100%; height: 20px">
                <asp:Panel ID="TitlePanel" runat="server" Width="99%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Medium"
                        Text="Archival Tape Issuance" Width="528px" CssClass="heading1"></asp:Label>
                </asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="99.5%">
                    <table width="50%" border="0">
                        <tr class="mytext">
                            <td>Issue To Employee</td>
                            <td style="height: 4px">Entry Date&nbsp;</td>
                            <td style="height: 4px"></td>
                            <td style="width: 145px">
                                <asp:DropDownList CssClass="mytext" ID="ddl_IssueToEmp" runat="server" Width="144px" Visible="False">
                                </asp:DropDownList>
                                <asp:CheckBox ID="Chk_Program" runat="server" Text="Ignore" Visible="False" />
                                <asp:CheckBox ID="Chk_TapeNo" runat="server" Text="Ignore" Visible="False" /></td>
                        </tr>
                        <tr class="mytext">
                            <td>
                                <asp:TextBox ID="txt_IssueToEmp" runat="server" CssClass="mytext" EnableViewState="False"
                                    Width="192px"></asp:TextBox>
                                <asp:Image ID="Err_Employee_Issue" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                            <td style="height: 13px">
                                <asp:TextBox ID="txtEntryDate_Issue" runat="server" CssClass="mytext" EnableViewState="False"
                                    Width="128px"></asp:TextBox></td>
                            <td>
                                <asp:DropDownList CssClass="mytext" ID="ddl_ContentType" runat="server" Width="136px" AutoPostBack="True" Visible="False">
                                </asp:DropDownList>
                            </td>
                            <td>
                                <asp:TextBox CssClass="mytext" ID="txt_TapeNo" runat="server" EnableViewState="False" Width="72px" Visible="False"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <table style="width: 673px">
                                    <tr>
                                        <td style="width: 50%; height: 20px;" align="center" valign="middle">
                                            <asp:Label ID="lblEntTapes" runat="server" BackColor="LightSkyBlue" Font-Bold="True" Font-Names="Arial"
                                                Font-Size="10pt" Text="Entertainment Tapes" Width="100%" BorderColor="DodgerBlue" BorderStyle="Solid" BorderWidth="1px"></asp:Label></td>
                                        <td align="center" valign="middle" style="width: 50%; height: 20px;">
                                            <asp:Label ID="lblNewsTapes" runat="server" BackColor="LightSkyBlue" Font-Bold="True" Font-Names="Arial"
                                                Font-Size="10pt" Text="News Tapes" Width="100%" BorderColor="DodgerBlue" BorderStyle="Solid" BorderWidth="1px"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%" valign="top">
                                            <asp:GridView ID="dg_TapeIssue" runat="server" AutoGenerateColumns="False"
                                                PageSize="20" Width="336px" CssClass="gridContent" BorderColor="Transparent">
                                                <Columns>
                                                    <asp:TemplateField HeaderText="S.No">
                                                        <ItemTemplate>
                                                            <%#Container.DataItemIndex + 1%>
                                                        </ItemTemplate>
                                                        <ItemStyle HorizontalAlign="Center" />
                                                    </asp:TemplateField>

                                                    <asp:TemplateField HeaderText="Issue">
                                                        <ItemStyle HorizontalAlign="Center" />
                                                        <ItemTemplate>
                                                            <asp:CheckBox ID="Chk_Issue" onclick="Check_Click(this)" runat="server" />
                                                        </ItemTemplate>
                                                        <HeaderTemplate>
                                                            All
                                                      <asp:CheckBox ID="checkAll" runat="server" onclick="checkAll(this);" />
                                                        </HeaderTemplate>
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="TapeNumber" HeaderText="Tape No">
                                                        <HeaderStyle HorizontalAlign="Left" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="TapeLibraryID" Visible="true">
                                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                        <HeaderStyle Width="0px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="StationName" HeaderText="Station Name" />
                                                    <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                                                    <asp:BoundField DataField="IsAvailable" HeaderText="IsAvailable" />
                                                </Columns>
                                                <HeaderStyle CssClass="gridheader" />
                                                <AlternatingRowStyle CssClass="AlternateRows" BackColor="#E0E0E0" />
                                            </asp:GridView>
                                        </td>
                                        <td style="width: 346px" valign="top">
                                            <asp:GridView ID="dg_TapeIssuance_SlugWise" runat="server" AutoGenerateColumns="False"
                                                PageSize="20" Width="328px" CssClass="gridContent" BorderColor="Transparent">
                                                <Columns>
                                                    <asp:TemplateField HeaderText="S.No">
                                                        <ItemTemplate>
                                                            <%#Container.DataItemIndex + 1%>
                                                        </ItemTemplate>
                                                        <ItemStyle HorizontalAlign="Center" />
                                                    </asp:TemplateField>

                                                    <asp:TemplateField HeaderText="Issue">
                                                        <ItemStyle HorizontalAlign="Center" />
                                                        <ItemTemplate>
                                                            <asp:CheckBox ID="Chk_Issue" onclick="Check_Click(this)" runat="server" />
                                                        </ItemTemplate>
                                                        <HeaderTemplate>
                                                            All
                                                      <asp:CheckBox ID="checkAll" runat="server" onclick="checkAll(this);" />
                                                        </HeaderTemplate>
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="TapeNumber" HeaderText="Tape No">
                                                        <HeaderStyle HorizontalAlign="Left" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="TapeLibraryID">
                                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="StationName" HeaderText="Station Name" />
                                                    <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                                                    <asp:BoundField DataField="IsAvailable" HeaderText="IsAvailable" />
                                                </Columns>
                                                <HeaderStyle CssClass="gridheader" />
                                                <AlternatingRowStyle CssClass="AlternateRows" BackColor="#E0E0E0" />
                                            </asp:GridView>
                                        </td>
                                    </tr>
                                </table>
                                <asp:Image ID="imgWait" runat="server" BorderColor="Navy" BorderStyle="Solid" BorderWidth="3px"
                                    ImageUrl="~/Images/wait.gif" Visible="False" /></td>
                        </tr>
                        <tr class="mytext">
                            <td class="bottomMain" colspan="4" style="height: 29px">&nbsp;&nbsp;
                                        <asp:Button CssClass="buttonA" ID="bttnIssue" runat="server" Text="Issue" Width="97px" Font-Bold="True" />
                                &nbsp; &nbsp;&nbsp;
                                        <asp:Button CssClass="buttonA" ID="btnDelete" runat="server" Text="Delete" Width="97px" Font-Bold="True" />
                                &nbsp;&nbsp; &nbsp;
                                        <asp:Button
                                            CssClass="buttonA" ID="bttnClearSel" runat="server" Text="Reset" Width="97px" Font-Bold="True" />&nbsp;
                                        &nbsp;<asp:Button
                                            CssClass="buttonA" ID="Button2" runat="server" Text="Email" Width="104px" Visible="False" />
                            </td>
                        </tr>
                    </table>
                    <table>
                        <tr>
                            <td style="width: 100%; height: 20px">
                                <asp:Label ID="lblErr_Issue" runat="server" Font-Bold="True" ForeColor="Red" Width="100%" Font-Size="Small" Font-Names="Arial"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server"
                    ConfirmText="Are you sure, you want to delete the record ?" Enabled="True" TargetControlID="btnDelete">
                </cc1:ConfirmButtonExtender>
                <cc1:ConfirmButtonExtender ID="lkDelete_ConfirmButtonExtender" runat="server"
                    ConfirmText="Are you sure, you want to issue these tapes ?" Enabled="True" TargetControlID="bttnIssue">
                </cc1:ConfirmButtonExtender>


            </td>
        </tr>
        <tr>
            <td style="width: 100%; height: 20px"></td>
        </tr>
        <tr>
            <td style="width: 100%; height: 21px"></td>
        </tr>
    </table>
    <asp:ScriptManager ID="ScriptManager1" runat="server">
        <Services>
            <asp:ServiceReference Path="AutoComplete.asmx" />
        </Services>
    </asp:ScriptManager>
    &nbsp;
                        <cc1:AutoCompleteExtender
                            runat="server"
                            ID="AutoCompleteExtender1"
                            TargetControlID="txt_TapeNo"
                            ServicePath="AutoComplete.asmx"
                            ServiceMethod="Guest"
                            MinimumPrefixLength="1"
                            CompletionInterval="25"
                            EnableCaching="true"
                            CompletionSetCount="12">
                        </cc1:AutoCompleteExtender>
    &nbsp;
    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_ContentType">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_ProgramName">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender5" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_IssueToEmp">
    </cc1:ListSearchExtender>
    <cc1:CollapsiblePanelExtender
        ID="CollapsiblePanelExtender1"
        runat="server"
        CollapseControlID="TitlePanel"
        Collapsed="false"
        CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Parameter Form (Archival Tape Issuance) --"
        ExpandControlID="TitlePanel"
        ExpandedImage="~/Images/expand.gif"
        ExpandedText="-- Hide Parameter Form (Archival Tape Issuance) --"
        ImageControlID="Image1"
        SuppressPostBack="true"
        TextLabelID="Label2"
        TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
    &nbsp; &nbsp;
                                        <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="ddl_TapeNumber">
                                        </cc1:ListSearchExtender>
    &nbsp; &nbsp;
                                         <cc1:AutoCompleteExtender
                                             ID="AutoCompleteExtender_Employee"
                                             runat="server"
                                             CompletionInterval="1"
                                             CompletionSetCount="12"
                                             EnableCaching="true"
                                             MinimumPrefixLength="3"
                                             ServiceMethod="GetEmployee"
                                             ServicePath="AutoComplete.asmx"
                                             TargetControlID="txt_IssueToEmp">
                                         </cc1:AutoCompleteExtender>
    &nbsp;
                                         <cc1:CalendarExtender
                                             ID="CalendarExtenderIssue"
                                             runat="server" TargetControlID="txtEntryDate_Issue" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                                         </cc1:CalendarExtender>
    &nbsp;
                                        <asp:DropDownList CssClass="mytext" ID="ddl_TapeNumber" runat="server" Width="208px" Visible="False">
                                        </asp:DropDownList>
    <asp:DropDownList CssClass="mytext" ID="ddl_ProgramName" runat="server" Width="192px" Visible="False">
    </asp:DropDownList>
    <asp:Button CssClass="buttonA" ID="bttnSearch" runat="server" Font-Bold="False" Text="Search" Width="64px" Visible="False" />
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
 <script type="text/javascript">
     // var txt = document.getElementById("ctl00$ContentPlaceHolder1$txt_IssueToEmp")
     // txt.focus()
     window.onload = function () {
         document.getElementById("ctl00_ContentPlaceHolder1_txt_IssueToEmp").focus();

     };
    </script>    
</asp:Content>
