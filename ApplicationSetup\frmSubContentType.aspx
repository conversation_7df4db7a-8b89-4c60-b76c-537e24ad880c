<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmSubContentType.aspx.vb" Inherits="ApplicationSetup_frmSubContentType" title="Home > Sub Content Type > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w16" CssClass="labelheading">Home</asp:LinkButton> &gt; Sub Content Type &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD>Sub Content Type Name</TD><TD><asp:TextBox id="txt_SubContentTypeName" runat="server" CssClass="mytext"></asp:TextBox></TD><TD>Content Type</TD><TD><asp:DropDownList id="ddl_ContentType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="408px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_SubContentType" runat="server" CssClass="gridContent" Width="648px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="SubContentTypeID" HeaderText="SubContentTypeID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="SubContentTypeName" HeaderText="SubContent Type Name" />
                        <asp:BoundField DataField="ContentTypeID" HeaderText="ContentTypeID" />
                        <asp:BoundField DataField="ContentTypeName" HeaderText="Content Type Name" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD><asp:TextBox id="txt_SubContentTypeID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w7" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

