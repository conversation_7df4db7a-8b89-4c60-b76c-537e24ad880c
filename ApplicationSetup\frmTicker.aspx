<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="frmTicker.aspx.vb" Inherits="frmTicker" Title="Home > Ticker > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>

 


<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
 <script language="javascript" type="text/javascript">
        function ShowAlert() {
            alert('Status has been Updated Successfully!');
        }

    </script>
    <asp:ScriptManager id="ScriptManager_City" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR class="mytext"><TD style="WIDTH: 3px; HEIGHT: 15px"></TD><TD style="TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LinkButton1" onclick="LinkButton1_Click" runat="server" __designer:wfdid="w16" CssClass="labelheading">Home</asp:LinkButton>&nbsp;&gt;&nbsp;&nbsp;Ticker &gt; Add New</TD></TR><TR><TD style="WIDTH: 3px"></TD><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD>Ticker</TD><TD><asp:TextBox id="txt_Ticker" runat="server" CssClass="mytext" Width="447px" TextMode="MultiLine" Height="97px"></asp:TextBox></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 29px"></TD><TD style="WIDTH: 100%; HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD></TD><TD vAlign=top><asp:GridView id="dg" runat="server" __designer:wfdid="w1" CssClass="gridContent" Width="96%" AutoGenerateColumns="False" AutoGenerateSelectButton="True" Font-Size="Small">
<RowStyle CssClass="GridItemStyle"></RowStyle>
<Columns>
<asp:BoundField DataField="NewsID" HeaderText="NewsID"></asp:BoundField>
<asp:BoundField DataField="NewsHeader" HeaderText="Ticker Detail">
<ItemStyle Width="80%"></ItemStyle>
</asp:BoundField>
<asp:TemplateField><HeaderTemplate>
                                Status
                            
</HeaderTemplate>
<ItemTemplate>
                                <asp:CheckBox ID="ChkStatus" runat="server" Checked='<% # Eval("IsActive") %>' />
                            
</ItemTemplate>

<ItemStyle HorizontalAlign="Center"></ItemStyle>
</asp:TemplateField>
<asp:TemplateField><HeaderTemplate>
                                Update Status
                            
</HeaderTemplate>
<ItemTemplate>
                                <asp:LinkButton ID="lnkStatus" runat="server" CommandName="Status">Update Status</asp:LinkButton>
                            
</ItemTemplate>

<ItemStyle HorizontalAlign="Center"></ItemStyle>
</asp:TemplateField>
</Columns>

<SelectedRowStyle BackColor="#FFFFC0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>

<AlternatingRowStyle BackColor="#E0E0E0"></AlternatingRowStyle>
</asp:GridView> </TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 12px"></TD><TD style="HEIGHT: 12px"><asp:TextBox id="txt_TickerID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox>&nbsp; <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w3" Visible="False"></asp:Label></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w9" ConfirmText="Do you want to Delete !" TargetControlID="BttnDelete"></cc1:ConfirmButtonExtender>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>
