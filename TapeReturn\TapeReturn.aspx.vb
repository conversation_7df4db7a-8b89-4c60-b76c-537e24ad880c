Imports System.Data

Partial Class TapeReturn_TapeReturn
    Inherits System.Web.UI.Page
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)
    Dim connStr As String = ConfigurationSettings.AppSettings("ConnectionString")

    Dim dt_Tape As New DataTable
    Dim dtReturn As DataTable
    Dim dtReturnNew As DataTable
    Dim strCommand As String
    Sub BlankReturn(ByVal IsEmpty As Integer)
        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        If dg_OneByOne_New.Rows.Count = 0 Then

            Dim Count As Integer
            Dim Q As Integer
            Dim DateError As Integer
            For Q = 0 To dg_OneByOne.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(Q).Cells(5).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then
                    Count = Count + 1
                    If Convert.ToDateTime(dg_OneByOne.Rows(Q).Cells(8).Text).ToString("dd-MMM-yyyy") > Convert.ToDateTime(txtEntryDate_1by1Return.Text) Then
                        DateError = DateError + 1
                    End If
                End If
            Next

            ''''''''''''''''''''''''''''''''''''''''''''''''
            If Count > 0 And txtEntryDate_1by1Return.Text <> "" And DateError = 0 Then
                Dim P As Integer
                Dim IssuanceID As Integer
                Dim IsTapeChecked As Integer = 0

                Try
                    For P = 0 To dg_OneByOne.Rows.Count - 1
                        Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(5).Controls(1), CheckBox)
                        If MyCheckBox.Checked = True Then

                            ''******************************''
                            ''*** BaseStation Validation ***''
                            ''******************************''

                            Dim objValidation As New BusinessFacade.NewTapeNumber()
                            objValidation.TapeLibraryID = dg_OneByOne.Rows(P).Cells(2).Text
                            Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                            If BaseStationID = CokieBaseStationID Then
                                IssuanceID = Convert.ToInt32(dg_OneByOne.Rows(P).Cells(7).Text.ToString)
                                Dim ObjReturn1by1 As New BusinessFacade.TapeReturn()
                                ObjReturn1by1.TapeIssuanceID = IssuanceID
                                ObjReturn1by1.Emp_1By1 = dg_OneByOne.Rows(P).Cells(6).Text
                                ObjReturn1by1.UserID = UserID
                                ObjReturn1by1.TapeLibraryID = dg_OneByOne.Rows(P).Cells(2).Text
                                ObjReturn1by1.EntryDate = txtEntryDate_1by1Return.Text
                                ObjReturn1by1.IsReturnEmpty = IsEmpty
                                ObjReturn1by1.OneByOneTapeReturn_saveRecord_IsReturnEmpty()
                                'ObjReturn1by1.OneByOneTapeReturn_saveRecord()
                                lblErr2.Text = "Tape has been Return Successfully!!"
                                IsTapeChecked = 1
                            Else
                                lblErr2.Text = "You are not allowed to Return this Tape!!"
                            End If
                            ''************ End *************''
                            ''******************************''

                        End If
                    Next

                    Try
                        If IsTapeChecked = 1 Then

                            Dim ObjReminder As New BusinessFacade.ReminderService()
                            ObjReminder.EmployeeName = dg_OneByOne.Rows(0).Cells(4).Text.ToString
                            Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                            If EmailAddress <> "N/A" Then
                                Dim Arr As Array = Split(EmailAddress, "@")
                                If Arr.Length = 2 Then
                                    'sendmail(EmailAddress)
                                    sendmail_Multiple()
                                Else
                                    lblErr2.Text += "But Email Not send due to Invalid Email Address!"
                                End If
                            Else
                                lblErr2.Text += "But Email Not send b/c Email Address not Exists!"
                                lblErr.Text = "Email Address not Exists"
                            End If

                        End If
                    Catch ex As Exception

                    End Try

                    'FillGrid()

                    If txt_EmpName_1by1.Text <> "" Then
                        FillGrid_MultipleSearch()
                    Else
                        ViewState("dtReturnNew") = Nothing
                        dtReturnNew = ViewState("dtReturnNew")
                        dg_OneByOne.DataSource = Nothing
                        dg_OneByOne.DataBind()
                    End If

                    txtEntryDate_1by1Return.Text = Date.Now().ToString("dd-MMM-yyyy")
                    txt_TapeNumber_Return.Text = String.Empty
                Catch ex As Exception
                    Throw
                End Try
            ElseIf txtEntryDate_1by1Return.Text = "" Then
                lblErr2.Text = "Please select Date !!"
            ElseIf Count = 0 Then
                lblErr2.Text = "Please select Tape First"
            Else
                lblErr2.Text = "Attention: Return Date Can't be less than Issue Date!"
            End If
        Else
            Dim CountChk As Integer
            Dim Q As Integer
            For Q = 0 To dg_OneByOne_New.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_OneByOne_New.Rows(Q).Cells(4).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then
                    CountChk = CountChk + 1
                End If
            Next

            ''''''''''''''''''''''''''''''''''''''''''''''''
            If CountChk > 0 And txtEntryDate_1by1Return.Text <> "" Then
                Dim P As Integer
                Dim IssuanceID As Integer
                Try
                    For P = 0 To dg_OneByOne_New.Rows.Count - 1
                        Dim MyCheckBox As CheckBox = CType(dg_OneByOne_New.Rows(P).Cells(4).Controls(1), CheckBox)
                        If MyCheckBox.Checked = True Then

                            ''******************************''
                            ''*** BaseStation Validation ***''
                            ''******************************''

                            Dim objValidation As New BusinessFacade.NewTapeNumber()
                            objValidation.TapeLibraryID = dg_OneByOne.Rows(P).Cells(1).Text
                            Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))

                            If BaseStationID = CokieBaseStationID Then
                                IssuanceID = Convert.ToInt32(dg_OneByOne_New.Rows(P).Cells(5).Text.ToString)
                                Dim ObjReturn1by1 As New BusinessFacade.TapeReturn()
                                ObjReturn1by1.TapeIssuanceID = IssuanceID
                                ObjReturn1by1.Emp_1By1 = dg_OneByOne_New.Rows(P).Cells(3).Text
                                ObjReturn1by1.UserID = UserID
                                ObjReturn1by1.TapeLibraryID = dg_OneByOne_New.Rows(P).Cells(0).Text
                                ObjReturn1by1.EntryDate = txtEntryDate_1by1Return.Text
                                ObjReturn1by1.IsReturnEmpty = IsEmpty
                                'ObjReturn1by1.OneByOneTapeReturn_saveRecord()
                                ObjReturn1by1.OneByOneTapeReturn_saveRecord_IsReturnEmpty()
                                lblErr2.Text = "Tape has been Return Successfully!!"
                            Else
                                lblErr2.Text = "You are not allowed to Return this Tape!!"
                            End If
                            ''************ End *************''
                            ''******************************''

                        End If
                    Next
                    dt_Tape.Clear()
                    ViewState("dt_Tape") = Nothing
                    Bind_NewGrid()
                    dg_OneByOne.DataSource = Nothing
                    dg_OneByOne.DataBind()
                    txtEntryDate_1by1Return.Text = Date.Now().ToString("dd-MMM-yyyy")
                    txt_TapeNumber_Return.Text = String.Empty
                Catch ex As Exception
                    Throw
                End Try
            ElseIf txtEntryDate_1by1Return.Text = "" Then
                lblErr2.Text = "Please select Date !!"
            ElseIf CountChk = 0 Then
                lblErr2.Text = "Please select Tape First"
            End If
        End If

    End Sub

    Sub FillGrid_MultipleSearch()

        dg_OneByOne.Visible = True

        ''*********************************************************''
        ''***************** TapeNumber Wise Search ****************''
        ''*********************************************************''

        If txt_TapeNumber_Return.Text.Length <> 0 Then

            Dim arr As Array = Split(txt_TapeNumber_Return.Text, "#")
            If arr.Length = 2 Then
                txt_TapeNumber_Return.Text = arr(1)
            End If

            ''*******************************************''
            ''*********** Get TapeLibraryID *************''
            ''*******************************************''

            Dim LibraryID As Integer
            Dim objLibID As New BusinessFacade.TapeIssuance()
            objLibID.TapeNumber = txt_TapeNumber_Return.Text
            LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

            If LibraryID = 0 Then
                Err_TapeNo_1By1.Visible = True
            Else

                If (dg_OneByOne.Rows.Count = 0) Or (txtIsEmployeeSearch.Text = "1") Then

                    Err_TapeNo_1By1.Visible = False
                    Dim ObjSearch As New BusinessFacade.TapeReturn()
                    ObjSearch.TapeNumber = txt_TapeNumber_Return.Text
                    dtReturnNew = ObjSearch.TapeReturnGetRecord_TapeNumberWise_Double()

                    Dim IsOtherStation As Integer = 0
                    For T As Integer = 0 To dtReturnNew.Rows.Count - 1
                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dtReturnNew.Rows(T)("TapeLibraryID").ToString()
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID <> CokieBaseStationID Then
                            IsOtherStation = 1
                        End If
                    Next

                    If IsOtherStation = 0 Then

                        dtReturnNew.DefaultView.Sort = "EmployeeName asc"

                        dg_OneByOne.DataSource = dtReturnNew
                        dg_OneByOne.Columns(1).Visible = True
                        dg_OneByOne.Columns(2).Visible = True
                        dg_OneByOne.Columns(6).Visible = True
                        dg_OneByOne.Columns(7).Visible = True
                        dg_OneByOne.DataBind()
                        dg_OneByOne.Columns(1).Visible = False
                        dg_OneByOne.Columns(2).Visible = False
                        dg_OneByOne.Columns(6).Visible = False
                        dg_OneByOne.Columns(7).Visible = False

                        SelectedNewTape(txt_TapeNumber_Return.Text)
                        txt_TapeNumber_Return.Text = ""

                        ViewState("dtReturnNew") = dtReturnNew

                        txtIsEmployeeSearch.Text = 0

                    End If

                    If dtReturnNew.Rows.Count = 0 Then
                        lblErr2.Text = "There is no Record against Tape mentioned !!"
                    End If
                Else

                    ''******************** Already Tapes Added ***********************''
                    Err_TapeNo_1By1.Visible = False
                    Dim dtTemp_Return As DataTable = Nothing

                    dtReturnNew = ViewState("dtReturnNew")

                    Dim ObjSearch As New BusinessFacade.TapeReturn()
                    ObjSearch.TapeNumber = txt_TapeNumber_Return.Text
                    dtTemp_Return = ObjSearch.TapeReturnGetRecord_TapeNumberWise_Double()

                    For J As Integer = 0 To dtTemp_Return.Rows.Count - 1
                        Dim AlreadyExists As Integer = 0
                        For K As Integer = 0 To dg_OneByOne.Rows.Count - 1
                            If dg_OneByOne.Rows(K).Cells(2).Text = dtTemp_Return.Rows(J)("TapeLibraryID").ToString() Then
                                AlreadyExists = 1
                            End If
                        Next

                        If AlreadyExists = "0" Then
                            Dim dt As DataTable = dtReturnNew

                            Dim objValidation As New BusinessFacade.NewTapeNumber()
                            objValidation.TapeLibraryID = dtTemp_Return.Rows(J)("TapeLibraryID").ToString
                            Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                            If BaseStationID = CokieBaseStationID Then
                                Dim Drow As DataRow = dtReturnNew.NewRow()
                                Drow(0) = dtTemp_Return.Rows(J)("EmployeeID").ToString()
                                Drow(1) = dtTemp_Return.Rows(J)("EmployeeName").ToString
                                Drow(2) = dtTemp_Return.Rows(J)("TapeIssuanceID").ToString
                                Drow(3) = dtTemp_Return.Rows(J)("TapeNumber").ToString
                                Drow(4) = dtTemp_Return.Rows(J)("TapeLibraryID").ToString
                                Drow(5) = dtTemp_Return.Rows(J)("TapeIssuanceDate").ToString
                                Drow(6) = dtTemp_Return.Rows(J)("Rank").ToString
                                Drow(7) = dtTemp_Return.Rows(J)("TapeType").ToString
                                Drow(8) = dtTemp_Return.Rows(J)("ProgramChildName").ToString
                                dtReturnNew.Rows.Add(Drow)
                            Else
                                lblErr2.Text = "You are not allowed to Issue this Tape!"
                            End If
                        End If
                    Next

                    dtReturnNew.DefaultView.Sort = "EmployeeName asc"
                    dg_OneByOne.DataSource = dtReturnNew
                    dg_OneByOne.Columns(1).Visible = True
                    dg_OneByOne.Columns(2).Visible = True
                    dg_OneByOne.Columns(6).Visible = True
                    dg_OneByOne.Columns(7).Visible = True
                    dg_OneByOne.DataBind()
                    dg_OneByOne.Columns(1).Visible = False
                    dg_OneByOne.Columns(2).Visible = False
                    dg_OneByOne.Columns(6).Visible = False
                    dg_OneByOne.Columns(7).Visible = False

                    SelectedNewTape(txt_TapeNumber_Return.Text)
                    txt_TapeNumber_Return.Text = ""

                    ViewState("dtReturnNew") = dtReturnNew
                    ''**************************** End *******************************''

                End If

            End If
        End If

        ''*********************************************************''
        ''***************** Employee Wise Search ****************''
        ''*********************************************************''

        If txt_EmpName_1by1.Text.Length <> 0 And txt_TapeNumber_Return.Text.Length = 0 Then

            ViewState("dtReturnNew") = Nothing
            dtReturnNew = ViewState("dtReturnNew")
            dg_OneByOne.DataSource = Nothing
            dg_OneByOne.DataBind()

            Dim arr2 As Array = Split(txt_EmpName_1by1.Text, "#")
            If arr2.Length = 2 Then
                txt_EmpName_1by1.Text = arr2(1)
            End If

            ''****************************************''
            ''*********** Get EmployeeID *************''
            ''****************************************''

            Dim EmployeeID As Integer
            Dim objEmployeeID As New BusinessFacade.TapeIssuance()
            objEmployeeID.EmployeeName = txt_EmpName_1by1.Text
            EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

            ''****************************************''
            If EmployeeID = 0 Then
                Err_Emp_1By1.Visible = True
            Else
                Err_Emp_1By1.Visible = False
                Dim ObjUser As New BusinessFacade.TapeReturn()
                ObjUser.EmployeeID = EmployeeID
                dg_OneByOne.DataSource = ObjUser.OneByOneTapeRetrun_GetRecord(ObjUser.EmployeeID)
                dg_OneByOne.Columns(1).Visible = True
                dg_OneByOne.Columns(2).Visible = True
                dg_OneByOne.Columns(6).Visible = True
                dg_OneByOne.Columns(7).Visible = True
                dg_OneByOne.DataBind()
                dg_OneByOne.Columns(1).Visible = False
                dg_OneByOne.Columns(2).Visible = False
                dg_OneByOne.Columns(6).Visible = False
                dg_OneByOne.Columns(7).Visible = False

                txtIsEmployeeSearch.Text = 1

            End If
        End If

        If dg_OneByOne.Rows.Count > 0 Then
            lblRecordCount.Visible = True
            lblRecordCount.Text = "No of Records : " + CStr(dg_OneByOne.Rows.Count)
        Else
            lblRecordCount.Visible = False
            lblRecordCount.Text = String.Empty
        End If
    End Sub

    Public Sub returnTape()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Try
            Dim P As Integer
            For P = 0 To dg_BulkReturn.Rows.Count - 1
                Dim MyTextBox As TextBox = CType(dg_BulkReturn.Rows(P).Cells(9).Controls(1), TextBox)
                If MyTextBox.Text.Length > 0 Then
                    If (Convert.ToInt32(dg_BulkReturn.Rows(P).Cells(8).Text) - Convert.ToInt32(MyTextBox.Text.ToString) > -1) = True Then
                        Dim ObjReturn As New BusinessFacade.TapeReturn()
                        ObjReturn.EmployeeID = dg_BulkReturn.Rows(P).Cells(10).Text
                        ObjReturn.DepartmentID = dg_BulkReturn.Rows(P).Cells(2).Text
                        ObjReturn.TapeTypeID = dg_BulkReturn.Rows(P).Cells(4).Text
                        ObjReturn.Qty = Convert.ToInt32(MyTextBox.Text.ToString)
                        ObjReturn.UserID = UserID
                        ObjReturn.EntryDate = txtBulkEntryDate.Text
                        ObjReturn.BulkTapeReturn_SaveRecord_New()
                        lblErr.Text = "Tape has been Returned Successfully!!"
                    Else
                        lblErr.Text = "Received qty cannot exceed issued qty."
                    End If
                End If
            Next
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Sub SelectedNewTape(ByVal TapeNumber As String)

        For K As Integer = 0 To dg_OneByOne.Rows.Count - 1
            If dg_OneByOne.Rows(K).Cells(3).Text.ToLower() = TapeNumber.ToLower() Then
                dg_OneByOne.Rows(K).BackColor = Drawing.Color.Pink
            End If
        Next

    End Sub

    Public Sub sendBulkreturnmail()
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been returned in bulk to Archive Department on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Quantity</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Department</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmailAddress As String = ""

        Dim ToAddress As String = ""

        Dim P As Integer
        Dim Count As Integer = 0
        Dim empname As String = ""
        Dim oldempname As String = ""
        Dim Sno As Integer = 0
        For P = 0 To dg_BulkReturn.Rows.Count - 1
            Dim MyTextBox As TextBox = CType(dg_BulkReturn.Rows(P).Cells(9).Controls(1), TextBox)
            If MyTextBox.Text.Length > 0 Then

                empname = dg_BulkReturn.Rows(P).Cells(1).Text.ToString

                headermessage = headermessage.Replace("ReturnedDate", txtBulkEntryDate.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))

                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = dg_BulkReturn.Rows(P).Cells(1).Text.ToString
                EmailAddress = ObjReminder.GetEmailAddress()

                If ToAddress <> "N/A" Then
                    If ToAddress.Length > 0 Then
                        If ToAddress <> EmailAddress Then
                            sendreturnmail(headermessage, fullheader, ToAddress, oldempname)
                            ToAddress = EmailAddress
                            fullheader = ""
                            Sno = 0

                        End If

                    End If
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If
                Else
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If
                End If

                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >quantity</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Department</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_BulkReturn.Rows(P).Cells(5).Text.ToString)
                iterativemessage = iterativemessage.Replace("quantity", MyTextBox.Text)
                iterativemessage = iterativemessage.Replace("Department", dg_BulkReturn.Rows(P).Cells(3).Text.ToString)

                If ToAddress <> "N/A" Then

                    fullheader = fullheader + iterativemessage
                    Sno = Sno + 1
                End If

            End If
            ''************ End *************''
            ''******************************''

        Next

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        headermessage = headermessage.Replace("EmpName", empname)

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage & fullheader & footermessage

        If EmailAddress = "N/A" Then
            'lblErr2.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If

        Dim Mail As New Email
        ToAddress = ToAddress

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Mail.sendemail(ToAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Public Sub sendmail(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been returned to Archive Department on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Program</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Issued Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;  font-size:small; "" >Returned by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(5).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = dg_OneByOne.Rows(P).Cells(4).Text.ToString

                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1by1Return.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))

                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = dg_OneByOne.Rows(P).Cells(4).Text.ToString
                EmailAddress = ObjReminder.GetEmailAddress()

                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_OneByOne.Rows(P).Cells(3).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", dg_OneByOne.Rows(P).Cells(8).Text.ToString)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_OneByOne.Rows(P).Cells(9).Text.ToString)
                iterativemessage = iterativemessage.Replace("Program", dg_OneByOne.Rows(P).Cells(10).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
                iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)
                'iterativemessage = iterativemessage.Replace("Returnedby", empname.Substring(0, empname.IndexOf("-") - 1))

                fullheader = fullheader + iterativemessage

                Sno = Sno + 1

            End If
            ''************ End *************''
            ''******************************''

        Next

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage & fullheader & footermessage

        If EmailAddress = "N/A" Then
            lblErr2.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Public Sub sendmail_Multiple()
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been returned to Archive Department on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Program</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Issued Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;  font-size:small; "" >Returned by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim EmailAddress As String = ""

        Dim ToAddress As String = ""
        Dim oldempname As String = ""
        Dim empname As String = ""

        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(5).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                empname = dg_OneByOne.Rows(P).Cells(4).Text.ToString

                'headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1by1Return.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))

                ' Get Email

                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = dg_OneByOne.Rows(P).Cells(4).Text.ToString
                EmailAddress = ObjReminder.GetEmailAddress()

                If ToAddress <> "N/A" Then
                    If ToAddress.Length > 0 Then
                        If ToAddress <> EmailAddress Then
                            headermessage = headermessage.Replace("EmpName", oldempname)
                            sendreturnmail(headermessage, fullheader, ToAddress, oldempname)
                            headermessage = headermessage.Replace(oldempname, "EmpName")
                            ToAddress = EmailAddress
                            fullheader = ""
                            Sno = 0

                        End If

                    End If
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If
                Else
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If
                End If

                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_OneByOne.Rows(P).Cells(3).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", dg_OneByOne.Rows(P).Cells(8).Text.ToString)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_OneByOne.Rows(P).Cells(9).Text.ToString)
                iterativemessage = iterativemessage.Replace("Program", dg_OneByOne.Rows(P).Cells(10).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
                iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname"))

                If ToAddress <> "N/A" Then
                    fullheader = fullheader + iterativemessage
                    Sno = Sno + 1
                End If

            End If
            ''************ End *************''
            ''******************************''
        Next

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        headermessage = headermessage.Replace("EmpName", empname)

        message = message & headermessage & middlemessage & fullheader & footermessage

        If EmailAddress = "N/A" Then
            Return
        End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim Mail As New Email
        Mail.sendemail(ToAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Public Sub sendreturnmail(ByVal headermessage As String, ByVal fullheader As String, ByVal emailaddress As String, ByVal empname As String)
        '   Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        '  Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        ' Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Quantity</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Department</td></tr>"
        'Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"

        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Program</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Issued Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;  font-size:small; "" >Returned by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        headermessage = headermessage.Replace("EmpName", empname)

        Dim ToAddress As String = ""

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage & fullheader & footermessage

        If emailaddress = "N/A" Then
            'lblErr.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If

        Dim Mail As New Email

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        ''' add EmailAddress variable in To
        Mail.sendemail(emailaddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Protected Sub bttn_Add_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttn_Add.Click

        If ViewState("dt_Tape") Is Nothing Then
            Dim col1 As DataColumn = New DataColumn("EmployeeName")
            col1.DataType = System.Type.GetType("System.String")
            dt_Tape.Columns.Add(col1)

            Dim col2 As DataColumn = New DataColumn("EmployeeID")
            col2.DataType = System.Type.GetType("System.Int32")
            dt_Tape.Columns.Add(col2)

            Dim col3 As DataColumn = New DataColumn("TapeNumber")
            col3.DataType = System.Type.GetType("System.String")
            dt_Tape.Columns.Add(col3)

            Dim col4 As DataColumn = New DataColumn("TapelibraryID")
            col4.DataType = System.Type.GetType("System.Int32")
            dt_Tape.Columns.Add(col4)

            Dim col5 As DataColumn = New DataColumn("TapeIssuanceID")
            col5.DataType = System.Type.GetType("System.Int32")
            dt_Tape.Columns.Add(col5)
        Else
            dt_Tape = ViewState("dt_Tape")
        End If

        ''*************************************''
        ''******** Remove Duplicate Record ****''
        ''*************************************''
        Dim Cnt As Integer
        Dim Cnt1 As Integer
        Dim Count As Integer = 0
        For Cnt = 0 To dg_OneByOne_New.Rows.Count - 1
            For Cnt1 = 0 To dg_OneByOne.Rows.Count - 1
                If dg_OneByOne.Rows(Cnt1).Cells(2).Text = dg_OneByOne_New.Rows(Cnt).Cells(1).Text Then
                    Count = Count + 1
                End If
            Next

        Next

        ''*************************************''

        If dg_OneByOne.Rows.Count = 1 Then
            Dim Rw As DataRow
            Rw = dt_Tape.NewRow()
            Rw.Item("EmployeeName") = dg_OneByOne.Rows(0).Cells(3).Text
            Rw.Item("EmployeeID") = dg_OneByOne.Rows(0).Cells(5).Text
            Rw.Item("TapeNumber") = dg_OneByOne.Rows(0).Cells(2).Text
            Rw.Item("TapeLibraryID") = dg_OneByOne.Rows(0).Cells(1).Text
            Rw.Item("TapeIssuanceID") = dg_OneByOne.Rows(0).Cells(6).Text
            dt_Tape.Rows.Add(Rw)

            Dim ObjSearch As New BusinessFacade.TapeReturn()
            ObjSearch.TapeLibraryID = 0
            dg_OneByOne.DataSource = ObjSearch.TapeReturnGetRecord_TapeNumberWise()
            dg_BulkReturn.Columns(0).Visible = True
            dg_BulkReturn.Columns(3).Visible = True
            dg_BulkReturn.Columns(5).Visible = True
            dg_BulkReturn.DataBind()
            dg_BulkReturn.Columns(0).Visible = False
            dg_BulkReturn.Columns(3).Visible = False
            dg_BulkReturn.Columns(5).Visible = False

            lblErr2.Text = "Record has been Added! - - Now Add More Tapes or Click Return!! "
        Else
            Dim L As Integer
            For L = 0 To dg_OneByOne.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(L).Cells(4).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then
                    Dim Rw As DataRow
                    Rw = dt_Tape.NewRow()
                    Rw.Item("EmployeeName") = dg_OneByOne.Rows(L).Cells(3).Text
                    Rw.Item("EmployeeID") = dg_OneByOne.Rows(L).Cells(5).Text
                    Rw.Item("TapeNumber") = dg_OneByOne.Rows(L).Cells(2).Text
                    Rw.Item("TapeLibraryID") = dg_OneByOne.Rows(L).Cells(1).Text
                    Rw.Item("TapeIssuanceID") = dg_OneByOne.Rows(0).Cells(6).Text
                    dt_Tape.Rows.Add(Rw)
                End If
            Next
        End If

        ViewState("dt_Tape") = dt_Tape
        Bind_NewGrid()
        If dg_OneByOne.Rows.Count = 1 Then
            dg_OneByOne.Visible = False
        ElseIf dg_OneByOne.Rows.Count = dg_OneByOne_New.Rows.Count Then
            dg_OneByOne.Visible = False
        End If

    End Sub

    Protected Sub bttnCancel_1By1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_1By1.Click
        Clrscr_2()
        ddl_Emp_1By1.SelectedIndex = "0"
        lblErr2.Text = String.Empty
    End Sub

    Protected Sub bttnCancel_BulkReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_BulkReturn.Click
        Clrscr()
        lblErr.Text = String.Empty
        dg_BulkReturn.DataSource = Nothing
        dg_BulkReturn.DataBind()

    End Sub

    Protected Sub bttnCancel_Return_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_Return.Click
        txt_TapeNumber_Return.Text = String.Empty
        txt_EmpName_1by1.Text = String.Empty
        txtEntryDate_1by1Return.Text = Date.Now().ToString("dd-MMM-yyyy")
        lblErr2.Text = String.Empty
        dt_Tape.Clear()
        dt_Tape = Nothing
        dg_OneByOne.DataSource = Nothing
        dg_OneByOne.DataBind()
        dg_OneByOne_New.DataSource = Nothing
        dg_OneByOne_New.DataBind()
        lblRecordCount.Text = String.Empty
        lblRecordCount.Visible = False
    End Sub

    Protected Sub bttnOneByOneReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnOneByOneReturn.Click
        Dim CountChk As Integer
        Dim Q As Integer
        For Q = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(Q).Cells(3).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                CountChk = CountChk + 1
            End If
        Next

        If CountChk > 0 Then
            If ddl_Emp_1By1.SelectedIndex = "0" Then
                lblErr2.Text = "Please Select Employee!!"
            ElseIf CountChk = 0 Then
                lblErr2.Text = "Please Select CheckBox!!"
            Else

                '''''''''''''' Save Detail Record ''''''''''''''''''
                Dim P As Integer
                Dim IssuanceID As Integer
                Try
                    For P = 0 To dg_OneByOne.Rows.Count - 1
                        Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(3).Controls(1), CheckBox)
                        If MyCheckBox.Checked = True Then
                            IssuanceID = Convert.ToInt32(dg_OneByOne.Rows(P).Cells(0).Text.ToString)
                            Dim ObjReturn1by1 As New BusinessFacade.TapeReturn()
                            ObjReturn1by1.TapeIssuanceID = IssuanceID
                            ObjReturn1by1.Emp_1By1 = ddl_Emp_1By1.SelectedValue
                            ObjReturn1by1.TapeLibraryID = dg_OneByOne.Rows(P).Cells(1).Text
                            ObjReturn1by1.OneByOneTapeReturn_saveRecord()

                            Dim ObjUpdateTapeLibrary As New BusinessFacade.NewTapeNumber()
                            ObjUpdateTapeLibrary.IsAvailable = 1
                            ObjUpdateTapeLibrary.TapeLibraryID = dg_OneByOne.Rows(P).Cells(1).Text
                            ObjUpdateTapeLibrary.TapeIsAvailable_Update()
                        End If
                    Next
                    lblErr2.Text = "Record has been Saved!!"
                    Clrscr_2()
                    ' BindGrid()
                Catch ex As Exception
                    Throw
                End Try
            End If
        Else
            lblErr2.Text = "Please Select Tape First !!"
        End If

    End Sub

    Protected Sub bttnReturnBulkTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReturnBulkTape.Click

        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        If CokieBaseStationID = 1 Then
            Dim K As Integer
            Dim Count As Integer
            For K = 0 To dg_BulkReturn.Rows.Count - 1
                Dim MyTextBox As TextBox = CType(dg_BulkReturn.Rows(K).Cells(9).Controls(1), TextBox)
                If MyTextBox.Text.Length > 0 Then
                    Count = Count + 1
                End If
            Next

            Try

                ''*****************************************''
                ''************* Get EmployeeID ************''
                ''*****************************************''

                Dim EmployeeID As Integer
                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                objEmployeeID.EmployeeName = txt_Employee.Text
                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

                ''*****************************************''

                If Count > 0 And txtBulkEntryDate.Text <> "" Then

                    '''''''''''''' Save Detail Record ''''''''''''''''''
                    returnTape()

                    Try
                        sendBulkreturnmail()
                    Catch ex As Exception

                    End Try

                    dg_BulkReturn.DataBind()
                    BindGrid_BulkReturn()
                    Err_Emp_Bulk.Visible = False
                    txtBulkEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")

                ElseIf txtBulkEntryDate.Text = "" Then
                    lblErr.Text = "Please Select Date !!"
                ElseIf Count = 0 Then
                    lblErr.Text = "Please Insert Quantity!!"
                End If
            Catch ex As Exception
                Throw
            End Try
        Else
            lblErr.Text = "You are not allowed to Return Bulk Tapes!!"
        End If

    End Sub

    Protected Sub bttnSave_Return_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_Return.Click
        BlankReturn(0)
    End Sub

    Protected Sub bttnSave_Return_EmptyData_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_Return_EmptyData.Click
        BlankReturn(1)
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        sendmail_Multiple()
    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        sendBulkreturnmail()
    End Sub

    Protected Sub ddl_Employee_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_Employee.SelectedIndexChanged
        lblErr.Text = String.Empty
        If ddl_Employee.SelectedIndex <> "0" Then
            ddl_Department.DataTextField = "DepartmentName"
            ddl_Department.DataValueField = "DepartmentID"
            Dim ObjUser As New BusinessFacade.Employee()
            ObjUser.EmployeeID = ddl_Employee.SelectedValue
            ddl_Department.DataSource = ObjUser.GetDepartmentByEmployee(ObjUser.EmployeeID)
            ddl_Department.DataBind()
            ddl_Department.Items.Insert(0, "--Select--")
            BindGrid_BulkReturn()
        Else
            lblErr.Text = "Please Select Employee!!"
        End If
    End Sub

    Protected Sub dg_BulkReturn_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_BulkReturn.PageIndexChanging
        dg_BulkReturn.PageIndex = e.NewPageIndex()
        BindGrid_BulkReturn()
    End Sub

    Protected Sub dg_OneByOne_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_OneByOne.RowDeleting

        Dim RowId As Integer
        RowId = e.RowIndex

        ''******************************''
        ''*** BaseStation Validation ***''
        ''******************************''

        Dim objValidation As New BusinessFacade.NewTapeNumber()
        objValidation.TapeLibraryID = dg_OneByOne.Rows(RowId).Cells(2).Text
        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        If BaseStationID = CokieBaseStationID Then
            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''

            Dim Objdelete As New BusinessFacade.TapeIssuance()
            Objdelete.TapeIssuanceID = dg_OneByOne.Rows(RowId).Cells(1).Text
            Objdelete.UserID = UserID
            Objdelete.DeleteRecord()

            'FillGrid()

            If txt_EmpName_1by1.Text <> "" Then
                FillGrid_MultipleSearch()
            Else
                dtReturnNew = ViewState("dtReturnNew")

                If Not dtReturnNew Is Nothing Then
                    If dtReturnNew.Rows.Count > 0 Then

                        For I As Integer = 0 To dtReturnNew.Rows.Count - 1
                            If dtReturnNew.Rows(I)("TapeIssuanceID").ToString() = dg_OneByOne.Rows(RowId).Cells(1).Text Then
                                dtReturnNew.Rows(I).Delete()
                                dtReturnNew.AcceptChanges()
                                Exit For
                            End If
                        Next

                        ViewState("dtReturnNew") = dtReturnNew

                        dtReturnNew.DefaultView.Sort = "EmployeeName asc"

                        dg_OneByOne.DataSource = dtReturnNew
                        dg_OneByOne.Columns(1).Visible = True
                        dg_OneByOne.Columns(2).Visible = True
                        dg_OneByOne.Columns(6).Visible = True
                        dg_OneByOne.Columns(7).Visible = True
                        dg_OneByOne.DataBind()
                        dg_OneByOne.Columns(1).Visible = False
                        dg_OneByOne.Columns(2).Visible = False
                        dg_OneByOne.Columns(6).Visible = False
                        dg_OneByOne.Columns(7).Visible = False
                    End If
                End If
            End If
        Else
            lblErr2.Text = "You are not allowed to Delete this Record!!"
        End If
        ''************ End *************''
        ''******************************''

    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    'End Sub
    Protected Sub lnkSearch_BulkTapeReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSearch_BulkTapeReturn.Click

        ''*******************************************''
        ''************ Get EmployeeID ***************''
        ''*******************************************''

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_Employee.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''********************************************''
        ''************ Get DepartmentID **************''
        ''********************************************''

        Dim DepartmentID As Integer
        Dim objDepartmentID As New BusinessFacade.TapeIssuance()
        objEmployeeID.DepartmentName = txtDepartmentName.Text
        DepartmentID = objEmployeeID.GetDepartmentID_byDepartmentName(objEmployeeID.DepartmentName)

        ''*******************************************''

        If EmployeeID <> 0 And Trim(txtDepartmentName.Text) = "" Then
            lblErr.Text = String.Empty
            Err_Emp_Bulk.Visible = False
            BindGrid_BulkReturn()
        ElseIf DepartmentID = 0 Then
            lblErr.Text = "Please Select Employee!!"
            Err_Emp_Bulk.Visible = True

        End If

        If DepartmentID <> 0 Then
            lblErr.Text = String.Empty
            'Err_Emp_Bulk.Visible = False
            BindGrid_BulkReturn()
        End If

    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSearch.Click
        lblErr2.Text = String.Empty
        ' FillGrid()
        FillGrid_MultipleSearch()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                txtBulkEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
                txtEntryDate_1by1Return.Text = Date.Now().ToString("dd-MMM-yyyy")

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub
    Private Sub Bind_NewGrid()
        dg_OneByOne_New.DataSource = dt_Tape
        dg_OneByOne_New.Columns(0).Visible = True
        dg_OneByOne_New.Columns(3).Visible = True
        dg_OneByOne_New.Columns(5).Visible = True
        dg_OneByOne_New.DataBind()
        dg_OneByOne_New.Columns(0).Visible = False
        dg_OneByOne_New.Columns(3).Visible = False
        dg_OneByOne_New.Columns(5).Visible = False

    End Sub

    Private Sub BindGrid_BulkReturn()

        ''******************************************''
        ''************ Get EmployeeID **************''
        ''******************************************''

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_Employee.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''********************************************''
        ''************ Get DepartmentID **************''
        ''********************************************''

        Dim DepartmentID As Integer
        Dim objDepartmentID As New BusinessFacade.TapeIssuance()
        objEmployeeID.DepartmentName = txtDepartmentName.Text
        DepartmentID = objEmployeeID.GetDepartmentID_byDepartmentName(objEmployeeID.DepartmentName)

        Dim ObjGrid As New BusinessFacade.TapeReturn()
        ObjGrid.EmployeeID = EmployeeID
        ObjGrid.DepartmentID = DepartmentID
        dg_BulkReturn.DataSource = ObjGrid.BulkTapeRetrun_GetRecord_New(ObjGrid.EmployeeID, ObjGrid.DepartmentID)
        dg_BulkReturn.Columns(2).Visible = True
        dg_BulkReturn.Columns(4).Visible = True
        dg_BulkReturn.Columns(10).Visible = True
        dg_BulkReturn.DataBind()
        dg_BulkReturn.Columns(2).Visible = False
        dg_BulkReturn.Columns(4).Visible = False
        dg_BulkReturn.Columns(10).Visible = False

        If dg_BulkReturn.Rows.Count = 0 Then
            lblErr.Text = "There is no Record Available!!"
            txt_Employee.Text = String.Empty
            txtDepartmentName.Text = String.Empty
        End If
    End Sub
    Private Sub Clrscr()
        dg_BulkReturn.DataSource = Nothing
        dg_BulkReturn.DataBind()
        txt_Employee.Text = String.Empty
        txtDepartmentName.Text = String.Empty
        txtBulkEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
    End Sub
    Private Sub Clrscr_2()
        Dim R As Integer
        For R = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(R).Cells(3).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

    End Sub
    'Private Sub FillGrid()
    '    dg_OneByOne.Visible = True

    '    ''*********************************************************''
    '    ''***************** TapeNumber Wise Search ****************''
    '    ''*********************************************************''

    '    If txt_TapeNumber_Return.Text.Length <> 0 Then
    '        Dim arr As Array = Split(txt_TapeNumber_Return.Text, "#")
    '        If arr.Length = 2 Then
    '            txt_TapeNumber_Return.Text = arr(1)
    '        End If

    '        ''*******************************************''
    '        ''*********** Get TapeLibraryID *************''
    '        ''*******************************************''

    '        Dim LibraryID As Integer
    '        Dim objLibID As New BusinessFacade.TapeIssuance()
    '        objLibID.TapeNumber = txt_TapeNumber_Return.Text
    '        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

    '        If LibraryID = 0 Then
    '            Err_TapeNo_1By1.Visible = True
    '        Else
    '            Err_TapeNo_1By1.Visible = False
    '            Dim ObjSearch As New BusinessFacade.TapeReturn()
    '            'ObjSearch.TapeLibraryID = LibraryID
    '            ObjSearch.TapeNumber = txt_TapeNumber_Return.Text
    '            dg_OneByOne.DataSource = ObjSearch.TapeReturnGetRecord_TapeNumberWise_Double()
    '            dg_OneByOne.Columns(1).Visible = True
    '            dg_OneByOne.Columns(2).Visible = True
    '            dg_OneByOne.Columns(6).Visible = True
    '            dg_OneByOne.Columns(7).Visible = True
    '            dg_OneByOne.DataBind()
    '            dg_OneByOne.Columns(1).Visible = False
    '            dg_OneByOne.Columns(2).Visible = False
    '            dg_OneByOne.Columns(6).Visible = False
    '            dg_OneByOne.Columns(7).Visible = False

    '        End If

    '    End If

    '    ''*********************************************************''
    '    ''***************** Employee Wise Search ****************''
    '    ''*********************************************************''

    '    If txt_EmpName_1by1.Text.Length <> 0 And txt_TapeNumber_Return.Text.Length = 0 Then
    '        Dim arr2 As Array = Split(txt_EmpName_1by1.Text, "#")
    '        If arr2.Length = 2 Then
    '            txt_EmpName_1by1.Text = arr2(1)
    '        End If

    '        ''****************************************''
    '        ''*********** Get EmployeeID *************''
    '        ''****************************************''

    '        Dim EmployeeID As Integer
    '        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
    '        objEmployeeID.EmployeeName = txt_EmpName_1by1.Text
    '        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

    '        ''****************************************''
    '        If EmployeeID = 0 Then
    '            Err_Emp_1By1.Visible = True
    '        Else
    '            Err_Emp_1By1.Visible = False
    '            Dim ObjUser As New BusinessFacade.TapeReturn()
    '            'ObjUser.EmployeeID = ddl_Emp_1By1.SelectedValue
    '            ObjUser.EmployeeID = EmployeeID
    '            dg_OneByOne.DataSource = ObjUser.OneByOneTapeRetrun_GetRecord(ObjUser.EmployeeID)
    '            dg_OneByOne.Columns(1).Visible = True
    '            dg_OneByOne.Columns(2).Visible = True
    '            dg_OneByOne.Columns(6).Visible = True
    '            dg_OneByOne.Columns(7).Visible = True
    '            dg_OneByOne.DataBind()
    '            dg_OneByOne.Columns(1).Visible = False
    '            dg_OneByOne.Columns(2).Visible = False
    '            dg_OneByOne.Columns(6).Visible = False
    '            dg_OneByOne.Columns(7).Visible = False
    '        End If
    '    End If

    '    If dg_OneByOne.Rows.Count > 0 Then
    '        lblRecordCount.Visible = True
    '        lblRecordCount.Text = "No of Records : " + CStr(dg_OneByOne.Rows.Count)
    '    Else
    '        lblRecordCount.Visible = False
    '        lblRecordCount.Text = String.Empty
    '    End If
End Class