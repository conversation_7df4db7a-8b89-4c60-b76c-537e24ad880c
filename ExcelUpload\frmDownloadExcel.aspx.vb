Imports System.Xml
Imports System.Xml.Xsl
Imports System.Data
Imports System.Data.SqlClient

Partial Class ExcelUpload_frmDownloadExcel
    Inherits System.Web.UI.Page

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim ds_Excel As New DataSet
        Dim dtNK_Excel, dtRP_Excel As New DataTable
        ds_Excel = New BusinessFacade.ExcelUpload().SetupList_ExeclUpload()

        dtNK_Excel = ds_Excel.Tables(0)
        dtRP_Excel = ds_Excel.Tables(0)




        If dtNK_Excel.Rows.Count > 0 Then
            '   ExportGridToExcel(dtNK_Excel)
            ExportGridToExcel_Complete(ds_Excel)

        Else
            MsgBox("Data does not exists is system!", MsgBoxStyle.Exclamation)
        End If
    End Sub

    Private Sub ExportGridToExcel(ByVal Dt As DataTable)

        Dim Excel As Object = CreateObject("Excel.Application")

        If Excel Is Nothing Then
            MsgBox("It appears that Excel is not installed on this machine. This operation requires MS Excel to be installed on this machine.", MsgBoxStyle.Critical)
            Return
        End If

        'Make Excel visible
        Excel.Visible = True

        'Initialize Excel Sheet
        With Excel
            .SheetsInNewWorkbook = 1
            .Workbooks.Add()
            .Worksheets(1).Select()


            ''***************************************************************''
            '' Get Excel Columns Name
            Dim ColumnNo As Integer = 1
            For K As Integer = 0 To Dt.Columns.Count - 1
                .Cells(1, ColumnNo).Value = Dt.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            Dim RowNo As Integer = 2
            For J As Integer = 0 To Dt.Rows.Count - 1
                For L As Integer = 0 To Dt.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = Dt.Rows(J)(L).ToString()
                Next
            Next
        End With

        'Excel.Quit()
        System.Runtime.InteropServices.Marshal.ReleaseComObject(Excel)
        Excel = Nothing
        'MsgBox("Export to Excel Complete", MsgBoxStyle.Information)
    End Sub

    Private Sub ExportGridToExcel_Complete(ByVal Ds As DataSet)

        Dim Excel As Object = CreateObject("Excel.Application")

        If Excel Is Nothing Then
            MsgBox("It appears that Excel is not installed on this machine. This operation requires MS Excel to be installed on this machine.", MsgBoxStyle.Critical)
            Return
        End If

        'Make Excel visible
        Excel.Visible = True

        Dim dt As New DataTable
        dt = Ds.Tables(0)
        Dim dt2 As New DataTable
        dt2 = Ds.Tables(1)
        Dim dt3 As New DataTable
        dt3 = Ds.Tables(2)

        'Initialize Excel Sheet
        With Excel
            .SheetsInNewWorkbook = 3
            .Workbooks.Add()

            Dim ColumnNo As Integer = 1
            Dim RowNo As Integer = 2


            .Worksheets(2).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt2.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt2.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt2.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt2.Rows(J)(L).ToString()
                Next
            Next



            .Worksheets(3).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt3.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt3.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt3.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt3.Rows(J)(L).ToString()
                Next
            Next



            .Worksheets(1).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt.Rows.Count - 1
                For L As Integer = 0 To dt.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt.Rows(J)(L).ToString()
                Next
            Next

        End With

        'Excel.Quit()
        System.Runtime.InteropServices.Marshal.ReleaseComObject(Excel)
        Excel = Nothing
        'MsgBox("Export to Excel Complete", MsgBoxStyle.Information)
    End Sub
End Class
