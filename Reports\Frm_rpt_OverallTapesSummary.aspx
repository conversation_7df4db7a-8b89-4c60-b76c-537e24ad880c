<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="Frm_rpt_OverallTapesSummary.aspx.vb" Inherits="Frm_rpt_OverallTapesSummary"
    Title="Home > Reports > Blank Tapes > Q 14. How Can I View Overall Tapes Issue And Return?" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%; height: 30px;">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Blank Tapes Reports  >  How Can I View Overall Tapes Issue And Return?"
                        Width="744px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                     <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
                    <table cellspacing="3">
                        <tbody>
                            <tr class="mytext">
                                <td valign="middle">
                                    &nbsp;</td>
                                <td valign="middle">
                                </td>
                                <td style="font-size: 8pt; font-family: Arial" valign="middle">
                                </td>
                                <td valign="middle">
                                </td>
                                <td valign="middle">
                                </td>
                                <td valign="middle">
                                </td>
                            </tr>
                            <tr class="mytext">
                                <td valign="bottom">
                                    Department Name</td>
                                <td valign="bottom">
                                    Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                    <asp:CheckBox ID="chkEmployee" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana"
                                        Font-Bold="False" __designer:wfdid="w1" OnCheckedChanged="chkEmployee_CheckedChanged1"
                                        AutoPostBack="true"></asp:CheckBox></td>
                                <td style="font-size: 8pt; font-family: Arial" valign="bottom">
                                    From Date &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                    <asp:CheckBox ID="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Size="X-Small"
                                        Font-Names="Verdana" Font-Bold="False" __designer:wfdid="w2"></asp:CheckBox></td>
                                <td valign="bottom">
                                    To Date</td>
                                <td valign="bottom">
                                    Station</td>
                                <td valign="bottom">
                                    Pendings</td>
                            </tr>
                            <tr class="mytext">
                                <td style="height: 17px" valign="top">
                                    <asp:DropDownList ID="ddlDepartment" runat="server" Width="200px" CssClass="mytext"
                                        __designer:wfdid="w3" AutoPostBack="True" DataValueField="DepartmentID" DataTextField="DepartmentName"
                                        DataSourceID="dsDepartment">
                                    </asp:DropDownList><asp:SqlDataSource ID="dsDepartment" runat="server" __designer:wfdid="w4"
                                        SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;"
                                        ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>"></asp:SqlDataSource>
                                </td>
                                <td style="height: 17px" valign="top">
                                    <asp:TextBox ID="TxtEmployee" runat="server" Width="192px" CssClass="mytext" __designer:wfdid="w5"
                                        EnableViewState="False"></asp:TextBox></td>
                                <td style="height: 17px" valign="top">
                                    <asp:TextBox ID="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w6"></asp:TextBox></td>
                                <td style="height: 17px" valign="top">
                                    <asp:TextBox ID="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w7"></asp:TextBox>&nbsp;
                                </td>
                                <td style="height: 17px" valign="top">
                                    <asp:DropDownList ID="ddlBaseStation" runat="server" Width="88px" CssClass="mytext"
                                        __designer:wfdid="w8">
                                    </asp:DropDownList>
                                    <asp:Label ID="Label3" runat="server" Text="Export to" Width="48px" __designer:wfdid="w9"
                                        Visible="False"></asp:Label></td>
                                <td style="height: 17px" valign="top">
                                    <asp:DropDownList ID="ddlPendings" runat="server" Width="64px" CssClass="mytext"
                                        __designer:wfdid="w10">
                                        <asp:ListItem>Ignore</asp:ListItem>
                                        <asp:ListItem>Yes</asp:ListItem>
                                        <asp:ListItem>No</asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:DropDownList ID="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w11"
                                        Visible="False">
                                        <asp:ListItem>PDF</asp:ListItem>
                                        <asp:ListItem>EXCEL</asp:ListItem>
                                    </asp:DropDownList></td>
                            </tr>
                        </tbody>
                    </table>
                    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" __designer:wfdid="w12"
                        PromptPosition="Bottom" PromptText TargetControlID="ddlDepartment">
                    </cc1:ListSearchExtender>
                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="MyCalendar"
                        __designer:wfdid="w13" TargetControlID="txtFromdate" Format="dd-MMM-yyyy">
                    </cc1:CalendarExtender>
                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" CssClass="MyCalendar"
                        __designer:wfdid="w14" TargetControlID="txtToDate" Format="dd-MMM-yyyy">
                    </cc1:CalendarExtender>
                    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Employee" runat="server" __designer:wfdid="w15"
                        TargetControlID="TxtEmployee" CompletionSetCount="12" EnableCaching="true" CompletionInterval="1"
                        MinimumPrefixLength="3" ServiceMethod="GetEmployee" ServicePath="AutoComplete.asmx">
                    </cc1:AutoCompleteExtender>
                    </contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 460px">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                </asp:Panel>
                &nbsp;
            </td>
        </tr>
    </table>
    <iframe style="position: relative; left: 0px; height: 344px;" runat="server" id="test"
        border="0px" height="800" width="100%" visible="false"></iframe>
    <cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server" CollapseControlID="TitlePanel"
        Collapsed="false" CollapsedImage="~/Images/Collapse.gif" CollapsedText="-- Show Form (Blank Tapes Reports  >  How Can I View Overall Tapes Issue And Return?) --"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" ExpandedText="-- Hide Form (Blank Tapes Reports  >  How Can I View Overall Tapes Issue And Return?) --"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>
