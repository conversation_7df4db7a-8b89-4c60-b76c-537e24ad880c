Imports System.Collections.Generic
Imports System.Web
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Data
Imports System.Net
Imports System.Net.Mail
Imports Microsoft.VisualBasic

Public Class Email

    Public Sub sendemail(ByVal sendto As String, ByVal messagebody As String, ByVal sendFrom As String, ByVal msgdate As String, ByVal mailtype As String)
        Try
            Dim Mail As New MailMessage()
            Mail.From = New MailAddress(sendFrom)
            Mail.CC.Add(sendFrom)
            If sendto.Contains(";") Then
                Dim al As ArrayList = New ArrayList(sendto.Split(";"))

                Dim P As Integer
                For P = 0 To al.Count - 1
                    If al(P).ToString.Length > 0 Then
                        Mail.To.Add(al(P).ToString)
                    End If
                Next
            Else
                Mail.To.Add(sendto)
            End If

            If mailtype = "return" Then
                Mail.Subject = "Tape Return Acknowledgement (" & msgdate & ")"
            End If
            If mailtype = "issue" Then
                Mail.Subject = "Tape Issue Acknowledgement (" & msgdate & ")"
            End If

            Mail.Body = messagebody
            Mail.IsBodyHtml = True
            'Dim smtp As New SmtpClient("***********")
            Dim smtp As New SmtpClient("mail.geo.tv")

            smtp.Send(Mail)

        Catch Exp As Exception

        End Try


    End Sub

End Class
