<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmEmployeeCategory.aspx.vb" Inherits="ApplicationSetup_frmEmployeeCategory" title="Home > Employee Category > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server"></asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w6" CssClass="labelheading">Home</asp:LinkButton> &gt; Employee Category &gt; Add New</TD></TR><TR><TD style="WIDTH: 326px; HEIGHT: 82px" vAlign=top><TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 200px">Employee Category </TD><TD><asp:TextBox id="txt_EmpCategory" runat="server" CssClass="mytext"></asp:TextBox></TD><TD></TD></TR><TR class="mytext"><TD style="WIDTH: 200px">Description</TD><TD style="WIDTH: 284px"><asp:TextBox id="txt_Description" runat="server" CssClass="mytext" Height="32px" TextMode="MultiLine" Width="208px"></asp:TextBox></TD><TD style="WIDTH: 125px"></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="304px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 326px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD style="WIDTH: 100%"><asp:GridView id="dg_EmpCategory" runat="server" CssClass="gridContent" Width="100%" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25"><Columns>
<asp:BoundField DataField="EmployeeCategoryID" HeaderText="Employee Category ID" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="EmployeeCategory" HeaderText="Employee Category" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="Description" HeaderText="Description">
<HeaderStyle Width="550px"></HeaderStyle>
</asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w15" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender></TD></TR><TR><TD style="WIDTH: 326px"><asp:TextBox id="txt_EmpCategoryID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

