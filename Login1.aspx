<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Login1.aspx.vb" Inherits="Login1" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body style="background-image: url(Images/menuLogin.jpg)">
    <form id="form1" runat="server">
        <table style="width: 100%; height: 100%;">
            <tr>
                <td style="width: 10%; height: 104px">
                </td>
                <td style="background-image: url(Images/headerLogin.jpg); width: 833px; height: 104px; background-repeat: no-repeat; font-weight: bold; color: #000000; font-family: Arial, Fantasy;">
                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp; &nbsp; &nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br />
                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp; Digital Archive Management System</td>
                <td style="width: 10%; height: 104px">
                </td>
            </tr>
            <tr>
                <td style="width: 10%; height: 600px">
                </td>
                <td align="center" style="left: 30%; width: 833px;
                    height: 402px" valign="middle">
                    <div style="background-attachment: fixed; background-repeat: no-repeat; text-align: center">
                        <table>
                            <tr>
                                <td style="background-attachment: fixed; width: 533px; background-repeat: no-repeat;
                                    height: 507px">
                                    <table style="width: 100%; height: 100%">
                                        <tr>
                                            <td style="width: 100px">
                                            </td>
                                            <td style="width: 100px">
                                Login ID</td>
                                            <td style="width: 100px">
                                <asp:TextBox ID="txtLoginID" runat="server" Width="150px"></asp:TextBox></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 100px">
                                            </td>
                                            <td style="width: 100px">
                                Password</td>
                                            <td style="width: 100px">
                                <asp:TextBox ID="txtpassword" runat="server" TextMode="Password" Width="150px"></asp:TextBox></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 100px">
                                            </td>
                                            <td style="width: 100px">
                                            </td>
                                            <td style="width: 200px">
                                <asp:Button ID="btnLogin" runat="server" CssClass="buttonA" Height="24px" Text="Login"
                                    Width="72px" />
                                <asp:Button ID="bttnCancel" runat="server" CssClass="buttonA" Height="24px" Text="Cancel"
                                    Width="64px" /></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 100px">
                                            </td>
                                            <td id="TD1" runat="server" colspan="2">
                                <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Small"
                                    ForeColor="Red" Width="288px"></asp:Label></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
                <td style="width: 10%; height: 402px">
                </td>
            </tr>
            <tr>
                <td style="height: 21px">
                </td>
                <td align="center" style="color: yellow; background-color: midnightblue; height: 21px; width: 833px;" valign="middle">
                    &nbsp; Copyright Geo Tv. All Rights Reserved</td>
                <td style="height: 21px">
                </td>
            </tr>
        </table>
  
    </form>
</body>
</html>
