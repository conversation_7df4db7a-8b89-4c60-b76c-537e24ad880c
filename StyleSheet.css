﻿body {
}

table
{
	font-family:Arial;
	font-size:11px;
}

.<PERSON><PERSON>
{
	font-family:Arial;
	font-size:11px;
}

.LabelHeader
{
	font-family:Arial;
	font-size:11px;
}

.Panel
{
	background-color:#b5ccfa;
	font-family:Arial;
	font-size:11px;
}

.PanelHeader
{
	background-color:#b5ccfa;
	font-family:Arial;
	font-size:11px;
}

.PanelHeaderExpand
{
	background-color:#b5ccfa;
	font-family:Arial;
	font-size:11px;
}

.AlternateRows
{
	background-color:#E0E0E0;
}
.GridHeader
{
	background-color: #006699;
	color: infobackground;
}
.Grid
{
	font-family:Arial;
	font-size:11px;
}

.buttonA
{
background-color:#EBEBD1;
}


.watermarked {
	height:18px;
	width:150px;
	padding:2px 0 0 2px;
	border:1px solid #BEBEBE;
	background-color:#F0F8FF;
	color:gray;
}	
.FooterLink
{
	font-family: Arial;
	font-size: 8pt;
	font-weight: normal;
	font-style: normal;
	color: #000000;
	background-color: #D1D1D1;
}
/*------------ Marquee --------------*/

.An<PERSON><PERSON>arquee
{
	font-family: Arial, Helvetica, sans-serif;
	font-size: x-small;
	text-decoration: none;
	color: #003366;
}