
Partial Class ApplicationSetup_frmSlug
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

        End If
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub bttnChange_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Try
            Dim OriginalString As String = txtOldWord.Text
            Dim NewString As String = txtNewWord.Text
            Dim Reslut As String
            Dim I As Integer
            For I = 0 To dg.Rows.Count - 1
                Dim SourceString As String = dg.Rows(I).Cells(1).Text
                Reslut = ReplaceString(SourceString, OriginalString, NewString)

                Dim ObjUpdate As New BusinessFacade.TapeSlug()
                ObjUpdate.TapeSlugID = dg.Rows(I).Cells(0).Text
                ObjUpdate.TapeSlug = Reslut
                ObjUpdate.UpdateRecord()
            Next
            GetRecords()
        Catch ex As Exception
            Throw
        End Try
     
    End Sub

    Function ReplaceString(ByVal SourceString As String, ByVal OriginalString As String, ByVal NewString As String) As String

        'Recursive function ReplaceString searches Source string and replaces ALL OCCURRENCES of OriginalString with NewString.
        'If a value for NewString is omitted (or IsEmpty), then all occurrences of OriginalString are removed from the SourceString!

        Dim Position As Integer

        If SourceString = "" Or (SourceString) Is Nothing Then
            ReplaceString = SourceString
        Else
            Position = InStr(1, SourceString, OriginalString)
            If Position > 0 Then
                ReplaceString = (Mid$(SourceString, 1, Position - 1) & NewString & ReplaceString(Mid(SourceString, Position + Len(OriginalString)), OriginalString, NewString))
            Else
                ReplaceString = SourceString
            End If
        End If

    End Function

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Try
            GetRecords()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetRecords()
        Try
            Dim ObjSearch As New BusinessFacade.TapeSlug()
            ObjSearch.TapeSlug = txtSearch.Text
            Dim dt As Data.DataTable
            dt = ObjSearch.GetRecords()
            dg.DataSource = dt
            dg.Columns(0).Visible = True
            dg.DataBind()
            dg.Columns(0).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub dg_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs)
        Try
            dg.PageIndex = e.NewPageIndex()
            GetRecords()
        Catch ex As Exception
            Throw
        End Try
    End Sub
End Class
