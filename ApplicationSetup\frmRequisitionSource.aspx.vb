
Partial Class ApplicationSetup_frmRequisitionSource
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_RequisitionSourceID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_RequisitionSource.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_RequisitionSource.Text = "" Then
            lblErr.Text = "Please Insert Requisition Source!!"
        Else
            Dim ObjRequisitionSource As New BusinessFacade.RequisitionSource()
            ObjRequisitionSource.RequisitionSource = txt_RequisitionSource.Text
            ObjRequisitionSource.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjRequisitionSource As New BusinessFacade.RequisitionSource()
        ObjRequisitionSource.RequisitionSourceID = txt_RequisitionSourceID.Text
        ObjRequisitionSource.RequisitionSource = txt_RequisitionSource.Text
        ObjRequisitionSource.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_RequisitionSource.DataSource() = New BusinessFacade.RequisitionSource().GetRecords()
        dg_RequisitionSource.DataBind()
        'dg_RequisitionSource.Columns(0).Visible = False
    End Sub

    Protected Sub dg_RequisitionSource_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_RequisitionSource.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_RequisitionSource.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_RequisitionSource.SelectedIndex.ToString
        txt_RequisitionSourceID.Text = Convert.ToInt32(dg_RequisitionSource.Rows(I).Cells(1).Text)
        txt_RequisitionSource.Text = dg_RequisitionSource.Rows(I).Cells(2).Text
        dg_RequisitionSource.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        If txt_RequisitionSourceID.Text = "" Then
            lblErr.Text = "Please select Requisition Source First!!"
        Else
            Dim ObjRequisitionSource As New BusinessFacade.RequisitionSource()
            ObjRequisitionSource.RequisitionSourceID = txt_RequisitionSourceID.Text
            ObjRequisitionSource.DeleteRecord(ObjRequisitionSource.RequisitionSourceID)
            FillGrid()
            clrscr()
            lblErr.Text = "Record has been Deleted!!"
            dg_RequisitionSource.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End If
    End Sub

    Private Sub clrscr()
        txt_RequisitionSource.Text = String.Empty
        txt_RequisitionSourceID.Text = String.Empty
    End Sub

    'Protected Sub dg_RequisitionSource_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_RequisitionSource.PageIndexChanging
    '    dg_RequisitionSource.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_RequisitionSource.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
