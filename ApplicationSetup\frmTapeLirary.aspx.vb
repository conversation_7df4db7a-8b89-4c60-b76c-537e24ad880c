
Partial Class frmTapeLirary
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If
            FillDropDowns()
        End If
    End Sub

    Private Sub FillDropDowns()
        Dim ObjTapeType As New BusinessFacade.TapeType()
        ddl_TapeType.DataSource = ObjTapeType.GetRecords()
        ddl_TapeType.DataTextField = "TapeType"
        ddl_TapeType.DataValueField = "TapeTypeID"
        ddl_TapeType.DataBind()
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        If Trim(txtTapeNumber.Text) = "" Then
            lblErr.Text = "Please Enter Tape Number!"
        Else
            UpdateRecord()
            dg.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            txtTapeNumber.Text = ""

        End If

    End Sub

    Private Sub UpdateRecord()
        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim ObjTapeNumber As New BusinessFacade.NewTapeNumber()
        ObjTapeNumber.TapeNumber = txtTapeNumber.Text
        ObjTapeNumber.TapeTypeID = ddl_TapeType.SelectedValue
        ObjTapeNumber.TapeType = ddl_TapeType.SelectedItem.ToString
        ObjTapeNumber.UserID = UserID
        ObjTapeNumber.UpdateTapeType_inTapeLibrary()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
        clrscr()
    End Sub

    Private Sub FillGrid()
        Dim ObjTape As New BusinessFacade.NewTapeNumber()
        ObjTape.TapeNumber = txtTapeNumber.Text
        dg.DataSource() = ObjTape.GetTapeNumberDetail()
        dg.Columns(2).Visible = True
        dg.DataBind()
        dg.Columns(2).Visible = False

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg.SelectedIndex.ToString
        txtTapeNumber.Text = dg.Rows(I).Cells(1).Text
        ddl_TapeType.SelectedValue = dg.Rows(I).Cells(3).Text
        dg.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Private Sub clrscr()
        txtTapeNumber.Text = String.Empty
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        lblErr.Text = String.Empty
        clrscr()
        txt_Search.Text = String.Empty
        dg.DataSource = Nothing
        dg.DataBind()
        ddl_TapeType.SelectedIndex = 0
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        lblErr.Text = String.Empty
        clrscr()

        If Trim(txt_Search.Text) <> "" Then
            Dim ObjSearch As New BusinessFacade.NewTapeNumber()
            ObjSearch.TapeNumber = txt_Search.Text
            dg.DataSource = ObjSearch.GetTapeNumberDetail()
            dg.Columns(2).Visible = True
            dg.DataBind()
            dg.Columns(2).Visible = False
        Else
            lblErr.Text = "Please Enter Tape Number!"
        End If
    End Sub
End Class
