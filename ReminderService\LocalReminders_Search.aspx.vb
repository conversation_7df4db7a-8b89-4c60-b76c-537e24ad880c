
Imports System
Imports System.Data
Imports Microsoft.VisualBasic
Imports System.Web
Imports System.Web.Mail
Imports System.IO

Public Class ReminderService_LocalReminders_Search
    Inherits System.Web.UI.Page

    Dim strFrom As String = ""
    Dim strTo As String = ""
    Dim strCC As String = ""
    Dim strBCC As String = ""
    Dim strSubject As String = ""
    Dim strBody As String = ""
    ''Dim strConnection As String = "server=KHI-CCS-SRV\TRM;database=DAMS_NewDB;uid=sa;pwd=**********;connection timeout=0"
    Dim strConnection As String = "Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********"

    Dim objConnection As New Data.SqlClient.SqlConnection(strConnection)


    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Not Page.IsPostBack Then
            strFrom = ""
            strTo = ""
            strCC = ""
            strBCC = ""
            strSubject = ""
            strBody = ""




            Dim dt As New DataTable
            dt = Microsoft.ApplicationBlocks.Data.SqlHelper.ExecuteDataset(objConnection, "GetEmail", Request("EmailID")).Tables(0)

            Me.txtBody.Text = dt.Rows(0)(0)
            Me.txtTapeDetail.Text = dt.Rows(0)(1)

            ''************************************************************************''
            Dim Arr As Array = Split(txtBody.Text, "^")
            txtExtensions.Text = Arr(Arr.Length - 1).ToString()
            txtDepartmentLogo.Text = Arr(Arr.Length - 2).ToString()
            ''************************************************************************''


            Me.txtFrom.Text = Request.QueryString("From")
            Me.txtTo.Text = Request.QueryString("To")
            Me.txtCC.Text = Request.QueryString("CC")
            Me.txtBCC.Text = Request.QueryString("BCC")
            Me.txtSubject.Text = Request.QueryString("Subject")
            Dim Body As String
            Body = Me.txtBody.Text
            Me.txtTestBody.Text = Me.txtBody.Text
            Body = Body.Replace("^", vbNewLine)
            Body = Body.Replace("!", " ")
            Body = Body.Replace("bbbccc", "")
            Body = Body.Replace("dddeee", "")
            Body = Body.Replace("~", "      ")

            strBody = Body
            Me.txtBody.Text = strBody

            'Dim TestBody As String
            'TestBody = Me.txtTestBody.Text
            'TestBody = TestBody.Replace("^", "<br>")

        End If

    End Sub

    Private Sub btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn.Click

        'Dim NewEmployeeName As String = Request.QueryString.Get("EmployeeName")
        'NewEmployeeName = LTrim(NewEmployeeName)
        'NewEmployeeName = RTrim(NewEmployeeName)
        'NewEmployeeName = Replace(NewEmployeeName, " - ", "-")
        'NewEmployeeName = Replace(NewEmployeeName, "- ", "-")
        'NewEmployeeName = Replace(NewEmployeeName, " -", "-")
        'Dim ArrAsmat As Array = Split(NewEmployeeName, "P-")


        ''Dim EmployeeName As String = "Dear " + CStr(Request.QueryString.Get("EmployeeName"))


        ''Dim EmployeeName As String = "Dear " + NewEmployeeName
        ''Dim EmployeeName As String = "Dear " + RTrim(LTrim(ArrAsmat(0).ToString()))
        Dim EmployeeName As String = "Dear User"

        Dim TestBody As String = ""


        Try
            TestBody = TestBody + _
            "<HTML>" & _
                        "<HEAD>" & _
                            "" & _
                            "</HEAD>" & _
                            "<BODY MS_POSITIONING=""GridLayout"">" & _
                            "<table border=""0"" cellpadding=""0"" cellspacing=""0""" & _
                                "HEIGHT: 990px" & _
                                "font-family: verdana" & _
                                "BACKGROUND-COLOR: #4396ca"" width=""100%"">" & _
                    "<tr>" & _
                    "<td colspan=""8"" style=""font-weight: bold; font-size: 20pt; font-family: verdana; color: window; HEIGHT: 45px; BACKGROUND-COLOR: #88B3C4"" valign=""bottom"" align=""left"">  &nbsp;&nbsp;Friendly Reminder</td>" & _
                    "</tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & EmployeeName & _
                    "</td></tr><tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">The following tapes are outstanding against you: </td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                        "<tr style=""font-weight: bold; font-size: 11pt; font-family: verdana; width: 100%; color: #990000""><b><u>" & _
                        "<td style=""width: 5%"">S.No" & _
                        "</td>" & _
                        "<td style=""width: 10%"">Tape No." & _
                        "</td>" & _
                        "<td style=""width: 10%"">Tape Type" & _
                        "</td>" & _
                        "<td style=""width: 10%"">Issue Date" & _
                        "</td>" & _
                        "<td style=""width: 10%"">Due Date" & _
                        "</td>" & _
                        "<td style=""width: 10%"">Remarks" & _
                        "</td>" & _
                        "<td style=""width: 10%"">Total Reminders" & _
                        "</td>" & _
                        "<td style=""width: 30%"">Program / Slug" & _
                        "</td></b></u>" & _
                        "</tr>" & _
                        "<tr><td colspan=""7"">&nbsp;</td></tr>"



            ''********************************************************''
            Dim TapeDetail As String
            TapeDetail = Me.txtTapeDetail.Text
            Dim UserName As String = Request.QueryString.Get("UserName")

            Dim SNo, TapeType, TapeNo, IssueDate, DueDate, Remarks, TotalReminders, ProgramSlug As String

            ''**************************************************************************************************************''
            ''********************************************** Start New Working *********************************************''
            ''**************************************************************************************************************''

            'Dim dtReminderDetails As New DataTable
            'dtReminderDetails = Microsoft.ApplicationBlocks.Data.SqlHelper.ExecuteDataset(objConnection, "GetReminderDetails", Request("EmailID")).Tables(0)

            'If dtReminderDetails.Rows.Count > 0 Then
            '    For I As Integer = 0 To dtReminderDetails.Rows.Count - 1
            '        SNo = dtReminderDetails.Rows(I)(0).ToString()
            '        TapeType = dtReminderDetails.Rows(I)(1).ToString()
            '        TapeNo = dtReminderDetails.Rows(I)(2).ToString()
            '        IssueDate = dtReminderDetails.Rows(I)(3).ToString()
            '        DueDate = dtReminderDetails.Rows(I)(4).ToString()
            '        If dtReminderDetails.Rows(I)(5).ToString() = "Archive" Then
            '            Remarks = "Archival"
            '        Else
            '            Remarks = dtReminderDetails.Rows(I)(5).ToString()
            '        End If
            '        TotalReminders = dtReminderDetails.Rows(I)(6).ToString()
            '        ProgramSlug = dtReminderDetails.Rows(I)(7).ToString()

            '        TestBody = TestBody + "<tr>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & SNo & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & TapeType & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & TapeNo & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & IssueDate & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & DueDate & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & Remarks & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & TotalReminders & "" & _
            '         "</td>" & _
            '         "<td style=""font-size: 10pt; font-family: verdana"">" & ProgramSlug & "" & _
            '         "</td>" & _
            '         "</tr>" & _
            '         "<tr><td colspan=""6"">&nbsp;</td></tr>"

            '    Next
            'End If






            ''************************************************************************************************************''
            ''********************************************** End New Working *********************************************''
            ''************************************************************************************************************''



            ''**************************************************************************************************************''
            ''********************************************** Start Old Working *********************************************''
            ''**************************************************************************************************************''

            Dim ArrMaster As Array = Split(TapeDetail, "$$")
            Dim K As Integer
            For K = 0 To ArrMaster.Length - 2
                Dim ArrChild As Array = Split(ArrMaster(K), "-+")
                SNo = ArrChild(0)
                TapeType = ArrChild(1)
                TapeNo = ArrChild(2)
                IssueDate = ArrChild(3)
                DueDate = ArrChild(4)
                If ArrChild(5) = "Archive" Then
                    Remarks = "Archival"
                Else
                    Remarks = ArrChild(5)
                End If
                TotalReminders = ArrChild(6)
                ProgramSlug = ArrChild(7)

                TestBody = TestBody + "<tr>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & SNo & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & TapeType & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & TapeNo & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & IssueDate & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & DueDate & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & Remarks & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & TotalReminders & "" & _
                            "</td>" & _
                            "<td style=""font-size: 10pt; font-family: verdana"">" & ProgramSlug & "" & _
                            "</td>" & _
                            "</tr>" & _
                "<tr><td colspan=""6"">&nbsp;</td></tr>"
            Next


            ''************************************************************************************************************''
            ''********************************************** End Old Working *********************************************''
            ''************************************************************************************************************''


            TestBody = TestBody + "<tr><td colspan=""8"" style=""font-size: 11pt; font-family: verdana"">&nbsp;</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us. </td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">Regards,</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & UserName & "</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & txtDepartmentLogo.Text & "</td></tr>" & _
                        "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & txtExtensions.Text & "</td></tr>" & _
                    "</table>" & _
                            "</BODY>" & _
                            "</HTML>"

            Dim SMTPServer As String = "mail.geo.tv"
            Dim mailNew As MailMessage = New MailMessage
            mailNew.Cc = Me.txtCC.Text
            mailNew.Bcc = Me.txtBCC.Text
            mailNew.To = Me.txtTo.Text
            mailNew.From = Me.txtFrom.Text

            mailNew.Subject = Me.txtSubject.Text & " to " & Request.QueryString("EmployeeName")
            mailNew.BodyFormat = System.Web.Mail.MailFormat.Html
            SmtpMail.SmtpServer = SMTPServer

            mailNew.Body = TestBody
            SmtpMail.Send(mailNew)
            Me.lbMessage.Visible = True
            DisableControls()

        Catch ex As Exception
            'Throw ex

            Me.lbMessage.Text = ex.Message
            Me.lbMessage.Visible = True

        End Try
    End Sub

    Function GetUserName(ByVal UserName As String) As String
        Dim User As String = ""
        Dim strConn As String = "server=ccs-server\roshni;database=Security_Manager;uid=appuser;pwd=**********;connection timeout=0"
        Dim objConn As New Data.SqlClient.SqlConnection(strConn)
        User = Microsoft.ApplicationBlocks.Data.SqlHelper.ExecuteScalar(objConn, "GetUserNamebyLoginID", UserName).ToString()
        Return User
    End Function

    Sub DisableControls()
        Me.txtBCC.Visible = False
        Me.txtBody.Visible = False
        Me.txtCC.Visible = False
        Me.txtFrom.Visible = False
        Me.txtSubject.Visible = False
        Me.txtTo.Visible = False
        Me.btn.Visible = False
        Me.Label1.Visible = False
        Me.Label2.Visible = False
        Me.Label3.Visible = False
        Me.Label4.Visible = False
        Me.Label5.Visible = False
    End Sub


End Class
