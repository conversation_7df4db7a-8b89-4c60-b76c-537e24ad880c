<%@ Page Language="VB" AutoEventWireup="false" CodeFile="NewsTapeNumberDetails.aspx.vb" Inherits="SearchEngine_NewsTapeNumberDetails" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
     <link href="main.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <div>
    
    </div>
        <table style="font-weight: bold; background-color: lightblue;" width="100%">
            <tr>
                <td style="width: 100px; height: 21px">
                    Tape Details</td>
                <td style="width: 100px; height: 21px">
                </td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px">
                </td>
                <td style="width: 100px; height: 5px">
                </td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Tape No:</td>
                <td style="width: 100px; height: 21px;">
                    <asp:Label ID="lblTapeNumber" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Keyword:
                </td>
                <td style="width: 100px">
                    <asp:Label ID="lblKeyword" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Sub Closet:</td>
                <td style="width: 100px; height: 21px;">
                    <asp:Label ID="lblSubCloset" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Reporter Slug:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblReportSlug" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Proposed Slug:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblProposedSlug" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Reporter Name:</td>
                <td style="width: 100px; height: 21px;">
                    <asp:Label ID="lblReporter" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Camera Man:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblCameraMan" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px">
                    Footage type:</td>
                <td style="width: 100px; height: 21px">
                    <asp:Label ID="lblFootageTypes" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 23px;">
                    Is Available:</td>
                <td style="width: 100px; height: 23px;">
                    <asp:Label ID="lblisAvailable" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Entry Date:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblEntryDate" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Tape Type:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblTapeType" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Tape Status:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblTapeStatus" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    Strat Time:</td>
                <td style="width: 100px">
                    <asp:Label ID="lblStartTime" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px;">
                    End Time:</td>
                <td style="width: 100px; height: 21px;">
                    <asp:Label ID="lblEndTime" runat="server" Width="472px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100px; height: 21px">
                </td>
                <td style="width: 100px; height: 21px">
                    <asp:Label ID="lblRecordID" runat="server" Visible="False" Width="136px" Font-Bold="False"></asp:Label></td>
            </tr>
            <tr>
                <td style="height: 29px" colspan="2" class="bottomMain">
                    &nbsp;<asp:Button ID="Button1" runat="server" OnClientClick="window.print()" Text="<< Print >>" Font-Bold="True" Width="128px" CssClass="buttonA" />
                    <asp:Button ID="GetEnglishScript" runat="server" CssClass="buttonA" Font-Bold="True"
                        Text="View English Script" Width="144px" Visible="False" /></td>
            </tr>
        </table>
        <table style="width: 100%">
            <tr>
                <td class="mytext" style="font-weight: bold; width: 100px; text-decoration: underline">
                    English Script :-</td>
            </tr>
            <tr>
                <td style="width: 100%">
                    <asp:TextBox ID="lblEngScript" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                        Height="300px" TextMode="MultiLine" Width="100%"></asp:TextBox></td>
            </tr>
        </table>
    </form>
</body>
</html>
