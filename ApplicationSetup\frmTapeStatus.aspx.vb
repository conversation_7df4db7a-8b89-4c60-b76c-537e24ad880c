
Partial Class ApplicationSetup_frmTapeStatus
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_TapeStatusID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_TapeStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_TapeStatus.Text = "" Then
            lblErr.Text = "Please insert Tape Status!!"
        Else
            Dim ObjTapeStatus As New BusinessFacade.TapeStatus()
            ObjTapeStatus.TapeStatus = txt_TapeStatus.Text
            ObjTapeStatus.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjTapeStatus As New BusinessFacade.TapeStatus()
        ObjTapeStatus.TapeStatusID = txt_TapeStatusID.Text
        ObjTapeStatus.TapeStatus = txt_TapeStatus.Text
        ObjTapeStatus.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_TapeStatus.DataSource() = New BusinessFacade.TapeStatus().GetRecords()
        dg_TapeStatus.DataBind()
        '  dg_TapeStatus.Columns(0).Visible = False
    End Sub

    Protected Sub dg_TapeStatus_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_TapeStatus.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_TapeStatus.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_TapeStatus.SelectedIndex.ToString
        txt_TapeStatusID.Text = Convert.ToInt32(dg_TapeStatus.Rows(I).Cells(1).Text)
        txt_TapeStatus.Text = dg_TapeStatus.Rows(I).Cells(2).Text
        dg_TapeStatus.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_TapeStatusID.Text = "" Then
                lblErr.Text = "Please Select Tape Status!!"
            Else
                Dim ObjTapeStatus As New BusinessFacade.TapeStatus()
                ObjTapeStatus.TapeStatusID = txt_TapeStatusID.Text
                ObjTapeStatus.DeleteRecord(ObjTapeStatus.TapeStatusID)
                FillGrid()
                Clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_TapeStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Tape Status is Already in Used !"
            Clrscr()
            dg_TapeStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub Clrscr()
        txt_TapeStatus.Text = String.Empty
        txt_TapeStatusID.Text = String.Empty
    End Sub

    'Protected Sub dg_TapeStatus_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_TapeStatus.PageIndexChanging
    '    dg_TapeStatus.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_TapeStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
