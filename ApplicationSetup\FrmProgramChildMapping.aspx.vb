Imports System
Imports System.Data
Imports System.Data.SqlClient

Partial Class ApplicationSetup_FrmProgramChildMapping
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")
            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)
        End If


        'If (lbl_UserName.Text.ToLower <> "turab.ali") Or (lbl_UserName.Text.ToLower <> "sobia.aziz") Then
        '    bttnMappEmployee.Enabled = False
        '    lblErr.Text = "You are not Authorize for Employee Mapping !"
        'End If

        If Not Page.IsPostBack = True Then
            bttnMappEmployee.Enabled = False
            lblErr.Text = "You are not Authorize for Program Child Mapping !"

            'If (lbl_UserName.Text.ToLower = "turab.ali") Or (lbl_UserName.Text.ToLower = "sobia.aziz") Or (lbl_UserName.Text.ToLower = "muhammad.shariq") Then
            '    bttnMappEmployee.Enabled = True
            '    lblErr.Text = ""
            'End If

            ''***************************''
            Dim objUsers As New BusinessFacade.Employee
            Dim IsValid As Integer = objUsers.GetMappingUsers("Program", lbl_UserName.Text.ToLower)
            If IsValid = 1 Then
                bttnMappEmployee.Enabled = True
                lblErr.Text = ""
            Else
                bttnMappEmployee.Enabled = False
                lblErr.Text = "You are not Authorize for Program Child Mapping !"
            End If

            ''***************************''

        End If

        Dim connStr As String
        connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")

    End Sub

    Protected Sub bttnMappEmployee_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappEmployee.Click
        Try

            ''**********************************************************''
            ''******************Get Old Program ChildID ****************''
            ''**********************************************************''

            Dim OldProgramChildID As Integer
            Dim ObjOldProgramChildID As New BusinessFacade.ProgramChild()
            ObjOldProgramChildID.ProgramChildName = txtOldProgramChild.Text
            OldProgramChildID = ObjOldProgramChildID.GetProgramChildID_AutoComplete(ObjOldProgramChildID.ProgramChildName)


            ''**********************************************************''
            ''******************Get New Program ChildID ****************''
            ''**********************************************************''

            Dim NewProgramChildID As Integer
            Dim ObjNewProgramChildID As New BusinessFacade.ProgramChild()
            ObjNewProgramChildID.ProgramChildName = txtNewProgramChild.Text
            NewProgramChildID = ObjNewProgramChildID.GetProgramChildID_AutoComplete(ObjNewProgramChildID.ProgramChildName)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If (OldProgramChildID <> "0") And (NewProgramChildID <> "0") And (OldProgramChildID <> NewProgramChildID) Then

                ''****************************************************''
                ''*********** Insert in Employee Mapping *************''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "Proc_ProgramChildMapping " & OldProgramChildID & "," & NewProgramChildID & "," & UserID
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr.Text = "Program Child has been Mapped Successfully."

                'bttnMappEmployee.Enabled = False
                If Not Page.IsPostBack Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmProgramChildMapping.aspx" + ";"""
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                End If
                txtNewProgramChild.Text = String.Empty
                txtOldProgramChild.Text = String.Empty
            ElseIf (OldProgramChildID = "0") Or (NewProgramChildID = "0") Then
                lblErr.Text = "Please Select a Valid Program Child"
            ElseIf OldProgramChildID = NewProgramChildID Then
                lblErr.Text = "Old and New Program Child must be different from each other!"
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmEmployeeMapping.aspx" + ";"""
        'script = script + "}</script>"
        'Page.RegisterClientScriptBlock("test", script)
        txtNewProgramChild.Text = String.Empty
        txtOldProgramChild.Text = String.Empty
        lblErr.Text = String.Empty

    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onbeforeunload = function() {"
        script = script + "return ""Closing the page now may result in data loss."";"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)
    End Sub
End Class
