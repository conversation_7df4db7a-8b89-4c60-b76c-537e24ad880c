<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ListOfPackages.aspx.vb" Inherits="Frm_rpt_ListOfPackages" title="Home > Other Reports > Q 1. How Can I View Slugs Contain Specific Word?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table width="100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports >  How Can I View Slugs Contain Specific Word?" Width="704px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="WIDTH: 164px; HEIGHT: 13px">&nbsp; </TD><TD style="WIDTH: 192px; HEIGHT: 13px"></TD><TD style="WIDTH: 191px; HEIGHT: 13px"></TD><TD style="WIDTH: 110px; HEIGHT: 13px"></TD><TD style="WIDTH: 120px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 21px">Reporter Name &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkReporter" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" AutoPostBack="True" __designer:wfdid="w35"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 192px; FONT-FAMILY: Arial">Reporter Slug &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkSlug" runat="server" Text="Ignore" AutoPostBack="True" __designer:wfdid="w36"></asp:CheckBox></TD><TD style="WIDTH: 191px">Proposed Slug&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <asp:CheckBox id="ChkPropSlug" runat="server" Text="Ignore" AutoPostBack="True" __designer:wfdid="w37"></asp:CheckBox></TD><TD style="WIDTH: 110px">Station <asp:CheckBox id="chkStation" runat="server" Text="Ignore" __designer:dtid="1970324836974612" AutoPostBack="True" __designer:wfdid="w6" Checked="True"></asp:CheckBox></TD><TD style="WIDTH: 120px">From Date &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore" __designer:wfdid="w38"></asp:CheckBox></TD><TD style="WIDTH: 107px">To Date</TD><TD style="WIDTH: 107px"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w46"></asp:Label></TD></TR><TR class="mytext"><TD style="WIDTH: 164px" vAlign=top><asp:DropDownList id="ddlReporter" runat="server" Width="208px" CssClass="mytext" __designer:wfdid="w39" DataSourceID="dsReporter" DataTextField="EmployeeName" DataValueField="EmployeeID"></asp:DropDownList><asp:SqlDataSource id="dsReporter" runat="server" __designer:wfdid="w40" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommand="Reporter_GetRecords" SelectCommandType="StoredProcedure"></asp:SqlDataSource> </TD><TD style="WIDTH: 192px" vAlign=top><asp:TextBox id="txtReporterSlug" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w41" TextMode="MultiLine"></asp:TextBox></TD><TD style="WIDTH: 191px" vAlign=top><asp:TextBox id="txtPropSlug" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w42" TextMode="MultiLine"></asp:TextBox></TD><TD style="WIDTH: 110px" vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w7">
                                </asp:DropDownList></TD><TD style="WIDTH: 120px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="104px" CssClass="mytext" __designer:wfdid="w43"></asp:TextBox>&nbsp; </TD><TD style="WIDTH: 107px" vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="96px" CssClass="mytext" __designer:wfdid="w44"></asp:TextBox>&nbsp; </TD><TD style="WIDTH: 107px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w47"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR><TR class="mytext"><TD vAlign=top colSpan=6><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w48" PromptPosition="Bottom" PromptText TargetControlID="ddlReporter"></cc1:ListSearchExtender><cc1:CalendarExtender id="CalendarExtender1" runat="server" __designer:dtid="281474976710670" CssClass="MyCalendar" __designer:wfdid="w49" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender><cc1:CalendarExtender id="CalendarExtender2" runat="server" __designer:dtid="281474976710671" CssClass="MyCalendar" __designer:wfdid="w50" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender></TD><TD vAlign=top colSpan=1></TD></TR></TBODY></TABLE>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain">
                                &nbsp;
                                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" />
                                <asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" Visible="False" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;&nbsp;
                                       
            </td>
        </tr>
    </table>
    &nbsp;<cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form (Other Reports > How Can I View Slugs Contain Specific Word?) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form (Other Reports > How Can I View Slugs Contain Specific Word?) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

