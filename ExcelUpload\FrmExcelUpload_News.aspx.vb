Imports System.Data
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.IO
Imports System.Xml
Imports System.Xml.Xsl



Partial Class ExcelUpload_FrmExcelUpload_News
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            lblErr.Text = "First Check Excel Sheet Type it extension must be '.xlxs'"

            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else

                    Master.FooterText = Request.Cookies("userinfo")("username")
                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                    BindCombo()
                    GetSpecificStations()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    ddlStation.SelectedValue = CokieBaseStationID
                    If CokieBaseStationID = 1 Then
                        ddlStation.Enabled = True
                    Else
                        ddlStation.Enabled = False
                    End If
                End If
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Sub BindCombo()
        Dim objTapeType As New TapeType
        ddl_TapeType.DataSource = objTapeType.GetRecords()
        ddl_TapeType.DataTextField = "TapeType"
        ddl_TapeType.DataValueField = "TapeTypeID"
        ddl_TapeType.DataBind()
    End Sub

    Private Sub GetSpecificStations()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        Dim ObjStation As New BusinessFacade.NewTapeNumber
        ddlStation.DataTextField = "StationName"
        ddlStation.DataValueField = "StationID"
        ddlStation.DataSource = ObjStation.GetSpecificStations(CokieBaseStationID)
        ddlStation.DataBind()
    End Sub

    Protected Sub bttnLoadExcel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnLoadExcel.Click
        bttnSave.Enabled = True

        Dim fs As FileStream = Nothing
        Dim strFile As String = FileUpload.PostedFile.FileName

        If strFile = "" Then
            lblErr.Text = "Invalid File!"
            Exit Sub
        End If
        Dim strFile2 As String() = Split(strFile, "\")
        Dim FileNameTemp As String = strFile2(strFile2.Length - 1)
        Dim strFileType As String() = Split(FileNameTemp, ".")
        Dim FileType As String = strFileType(strFileType.Length - 1)
        Dim FileName As String = strFileType(strFileType.Length - 2)
        Dim filepath As String = Server.MapPath("Upload").ToString & "\" & FileNameTemp
        FileUpload.PostedFile.SaveAs(filepath)

        ''***********************************************''
        lblFileName.Text = FileNameTemp
        lblFilePath.Text = filepath '"c:\Websites\Roshni3\GAPAnalysis\Web\Upload\Upload\" & FileNameTemp'

        ''***********************************************''

        dgExcel.DataSource = Nothing
        dgExcel.DataBind()


        fillGrid()
        CheckNewKeywords()
        CheckReporters()
        CheckTapeDetails()
        CheckFootageType()

        CheckStartTime()

    End Sub

    Sub CheckStartTime()

        For I As Integer = 0 To dgExcel.Rows.Count - 1

            Dim StartTime As String = dgExcel.Rows(I).Cells(5).Text
            Dim EndTime As String = dgExcel.Rows(I).Cells(6).Text
            Dim ArrStartTime As Array = StartTime.Split(":")
            Dim ArrEndTime As Array = EndTime.Split(":")
            
            Dim IsInvalidStartTime As Integer = 0
            For K As Integer = 0 To ArrStartTime.Length - 1
                If Not IsNumeric(ArrStartTime(K).ToString()) Then
                    IsInvalidStartTime = 1
                    dgExcel.Rows(I).Cells(5).BackColor = Drawing.Color.Red
                    bttnSave.Enabled = False
                End If
            Next

            Dim IsInvalidEndTime As Integer = 0
            For J As Integer = 0 To ArrEndTime.Length - 1
                If Not IsNumeric(ArrEndTime(J).ToString()) Then
                    IsInvalidStartTime = 1
                    dgExcel.Rows(I).Cells(6).BackColor = Drawing.Color.Red
                    bttnSave.Enabled = False
                End If
            Next
        Next

    End Sub
    Private Sub fillGrid3()
        Dim connstring As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=C:\Users\<USER>\Desktop\9693.xlsx;Extended Properties=""Excel 12.0 Xml;HDR=YES;IMEX=1"""
        Dim con As OleDbConnection = New OleDbConnection(connstring)
        Dim conn As New OleDbConnection(connstring)
        Dim cmd As New OleDbCommand()
        Dim Sheet As String = "Select * from [Sheet1$]"
        Dim adapter As New OleDbDataAdapter(Sheet, conn)
        Dim ds As New DataSet()
        adapter.Fill(ds)
    End Sub
    Private Sub fillGrid()

        Dim connstring As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" & lblFilePath.Text & ";Extended Properties=""Excel 12.0 Xml;HDR=YES;IMEX=1"""

        Dim conn As New OleDbConnection(connstring)
        Dim cmd As New OleDbCommand()
        Dim Sheet As String = "Select * from [Sheet1$]"
        Dim adapter As New OleDbDataAdapter(Sheet, conn)
        Dim ds As New DataSet()

        ' Open the connection
        conn.Open()

        ' Fill schema to get column information
        adapter.FillSchema(ds, SchemaType.Source)

        ' Specify column data types explicitly
        For Each column As DataColumn In ds.Tables(0).Columns
            If column.DataType Is GetType(String) Then
                ' Change the length to a value that suits your data
                Dim colname As String = column.ColumnName
                If colname = "Urdu Slug" Then
                    Console.WriteLine(colname)
                End If
                column.MaxLength = 64 * 1024 ' Adjust the maximum length as needed
            End If
        Next

        ' Fill the DataSet with data
        adapter.Fill(ds)

        ' Close the connection
        conn.Close()

        dgExcel.DataSource = ds.Tables(0).DefaultView
        dgExcel.DataBind()
    End Sub
    Private Sub fillGrid_255()
        Try

            '''Dim connstring As String = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" & lblFilePath.Text & ";Extended Properties=Excel 8.0;"
            'Dim connstring As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" & lblFilePath.Text & ";Extended Properties=Excel 12.0 Xml;HDR=YES;IMEX=1"
            Dim connstring As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" & lblFilePath.Text & ";Extended Properties=""Excel 12.0 Xml;HDR=YES;IMEX=1"""
            'Dim constring  As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=C:\\Users\\<USER>\\Desktop\\9693.xlsx;Extended Properties=\"Excel 12.0 Xml;HDR=YES;IMEX=1\""
            'Dim connstring As String = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=C:\Users\<USER>\Desktop\9693.xlsx;Extended Properties=Excel 12.0 Xml;HDR=YES;IMEX=1\"
            ' Urdu Slug

            Dim conn As New OleDbConnection(connstring)
            Dim cmd As New OleDbCommand()
            Dim Sheet As String = "Select * from [Sheet1$]"
            Dim adapter As New OleDbDataAdapter(Sheet, conn)
            Dim ds As New DataSet()
            adapter.Fill(ds)

            dgExcel.DataSource = ds.Tables(0).DefaultView
            dgExcel.DataBind()
        Catch ex As Exception
            Throw ex
        End Try
    End Sub

    Private Sub CheckNewKeywords()

        Dim dt As DataTable = New BusinessFacade.NewsKeyword().GetNewKeywords()
        For I As Integer = 0 To dgExcel.Rows.Count - 1
            Dim IsExists As Integer = 0
            For J As Integer = 0 To dt.Rows.Count - 1
                If dgExcel.Rows(I).Cells(7).Text.ToUpper() <> "&NBSP;" Then
                    If dgExcel.Rows(I).Cells(7).Text.ToUpper() = dt.Rows(J)(1).ToString() Then
                        IsExists = IsExists + 1
                    End If
                Else
                    IsExists = 1
                End If
            Next
            If IsExists = 0 Then
                dgExcel.Rows(I).Cells(7).BackColor = Drawing.Color.Red
                bttnSave.Enabled = False
            End If
        Next
    End Sub

    Private Sub CheckFootageType()

        Dim dt As DataTable = New BusinessFacade.FootageType().GetRecords()
        For I As Integer = 0 To dgExcel.Rows.Count - 1
            Dim IsExists As Integer = 0
            For J As Integer = 0 To dt.Rows.Count - 1
                If dgExcel.Rows(I).Cells(11).Text.ToUpper() <> "&NBSP;" Then
                    If dgExcel.Rows(I).Cells(11).Text.ToUpper() = dt.Rows(J)(1).ToString() Then
                        IsExists = IsExists + 1
                    End If
                Else
                    IsExists = 1
                End If
            Next
            If IsExists = 0 Then
                dgExcel.Rows(I).Cells(11).BackColor = Drawing.Color.Red
                bttnSave.Enabled = False
            End If
        Next

    End Sub

    Sub CheckReporters()
        Dim dt As DataTable = New BusinessFacade.Employee().GetReporter()
        For I As Integer = 0 To dgExcel.Rows.Count - 1
            Dim IsExists As Integer = 0
            For J As Integer = 0 To dt.Rows.Count - 1
                If dgExcel.Rows(I).Cells(8).Text.ToUpper() <> "&NBSP;" Then
                    If dgExcel.Rows(I).Cells(8).Text.ToUpper() = dt.Rows(J)(1).ToString() Then
                        IsExists = IsExists + 1
                    End If
                Else
                    IsExists = 1
                End If
            Next
            If IsExists = 0 Then
                dgExcel.Rows(I).Cells(8).BackColor = Drawing.Color.Red
                bttnSave.Enabled = False
            End If
        Next
    End Sub

    Sub CheckTapeDetails()
        Dim TapeNumber As String = dgExcel.Rows(0).Cells(3).Text()
        Dim MorethanOneTapes As Integer = 0
        For I As Integer = 1 To dgExcel.Rows.Count - 1
            If dgExcel.Rows(I).Cells(3).Text <> "&NBSP;" Then
                If dgExcel.Rows(I).Cells(3).Text <> TapeNumber Then
                    MorethanOneTapes = MorethanOneTapes + 1
                    lblErr.Text = "You are not allow to add more than one tapes!"
                    dgExcel.Columns(3).ControlStyle.BackColor = Drawing.Color.Red
                    bttnSave.Enabled = False
                    Exit Sub
                End If
            End If
        Next

        Dim ds As DataSet = New BusinessFacade.Employee().ExcelUpload_GetTapeDetails(TapeNumber)
        If ds.Tables(0).Rows(0)(0).ToString = "-1" Then
            lblErr.Text = "Tape Not Exists"
            bttnSave.Enabled = False
            Exit Sub
        ElseIf ds.Tables(0).Rows(0)(0).ToString = "1" Then
            lblErr.Text = "Tape Already Archived"
            bttnSave.Enabled = False
            Exit Sub
        End If

        Dim TapeType As String = dgExcel.Rows(0).Cells(4).Text()
        If ds.Tables(1).Rows(0)(0).ToString <> "Tape Not Exists" Then
            For I As Integer = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(4).Text <> "&NBSP;" Then
                    If dgExcel.Rows(I).Cells(4).Text.ToLower() <> ds.Tables(1).Rows(0)(0).ToString.ToLower() Then
                        dgExcel.Rows(I).Cells(4).BackColor = Drawing.Color.Red
                        bttnSave.Enabled = False
                        lblErr.Text = "Tape Type is not valid"
                    End If
                End If

            Next

        End If

        Dim EmployeeName As String = "0"
        If TapeNumber <> "" Then
            Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
            EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber(TapeNumber)

            If EmployeeName <> "0" Then
                lblErr.Text = "Tape has been already issued to " & EmployeeName
                bttnSave.Enabled = False
            End If
        End If

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Try
            lblErr.Text = String.Empty

            If dgExcel.Rows.Count > 0 Then
                UploadData()

                ClearControls_UploadArea()
                ClearControls_TapeNumber()

            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub UploadData()
        Dim Arr As New ArrayList

        If dgExcel.Rows.Count > 0 Then

            ''***************************************''
            ''********* Check Column Exists *********''
            ''***************************************''

            'Dim EntryDateExists As Integer = 0
            Dim ReporterSlugExists As Integer = 0
            Dim TapeNumberExists As Integer = 0
            Dim TapeTypeExists As Integer = 0
            Dim StartTimeExists As Integer = 0
            Dim EndTimeExists As Integer = 0
            Dim KeywordExists As Integer = 0
            Dim ReporterExists As Integer = 0
            Dim UrduSlugExists As Integer = 0
            Dim EnglishSlugExists As Integer = 0
            Dim SlugDateExists As Integer = 0
            Dim FootageTypeExists As Integer = 0


            Dim K As Integer
            For K = 0 To dgExcel.Columns.Count - 1

                'If dgExcel.Columns(K).HeaderText = "Entry Date" Then
                '    EntryDateExists = EntryDateExists + 1
                'End If

                If dgExcel.Columns(K).HeaderText = "Reporter Slug" Then
                    ReporterSlugExists = ReporterSlugExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Tape Number" Then
                    TapeNumberExists = TapeNumberExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Tape Type" Then
                    TapeTypeExists = TapeTypeExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Start Time" Then
                    StartTimeExists = StartTimeExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "End Time" Then
                    EndTimeExists = EndTimeExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Keyword" Then
                    KeywordExists = KeywordExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Reporter" Then
                    ReporterExists = ReporterExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Urdu Slug" Then
                    UrduSlugExists = UrduSlugExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "English Script" Then
                    EnglishSlugExists = EnglishSlugExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Slug Date" Then
                    SlugDateExists = SlugDateExists + 1
                End If

                If dgExcel.Columns(K).HeaderText = "Footage Type" Then
                    FootageTypeExists = FootageTypeExists + 1
                End If

            Next

            'If EntryDateExists = 0 Then
            '    lblErr.Text = "Column 'Entry Date' does not Exists!"
            '    Exit Sub
            'End If

            If ReporterSlugExists = 0 Then
                lblErr.Text = "Column 'Reporter Slug' does not Exists!"
                Exit Sub
            End If

            If TapeNumberExists = 0 Then
                lblErr.Text = "Column 'Tape Number' does not Exists!"
                Exit Sub
            End If

            If TapeTypeExists = 0 Then
                lblErr.Text = "Column 'Tape Type' does not Exists!"
                Exit Sub
            End If

            If StartTimeExists = 0 Then
                lblErr.Text = "Column 'Start Time' does not Exists!"
                Exit Sub
            End If

            If EndTimeExists = 0 Then
                lblErr.Text = "Column 'End Time' does not Exists!"
                Exit Sub
            End If

            If KeywordExists = 0 Then
                lblErr.Text = "Column 'Keyword' does not Exists!"
                Exit Sub
            End If

            If ReporterExists = 0 Then
                lblErr.Text = "Column 'Reporter' does not Exists!"
                Exit Sub
            End If

            If UrduSlugExists = 0 Then
                lblErr.Text = "Column 'Urdu Slug' does not Exists!"
                Exit Sub
            End If

            If EnglishSlugExists = 0 Then
                lblErr.Text = "Column 'English Script' does not Exists!"
                Exit Sub
            End If

            If SlugDateExists = 0 Then
                lblErr.Text = "Column 'Slug Date' does not Exists!"
                Exit Sub
            End If

            If FootageTypeExists = 0 Then
                lblErr.Text = "Column 'Footage Type' does not Exists!"
                Exit Sub
            End If

            ''***************************************************************************************************''
            ''*************************************Check Null Values ********************************************''
            ''***************************************************************************************************''

            '1.
            Dim NullExists As Integer = 0
            Dim I As Integer = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(1).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in Slug Date does not allowed!"
                Exit Sub
            End If

            '2.
            NullExists = 0
            I = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(2).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in Report Slug does not allowed!"
                Exit Sub
            End If

            '3.
            NullExists = 0
            I = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(3).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in Tape Number does not allowed!"
                Exit Sub
            End If

            '4.
            NullExists = 0
            I = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(4).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in Tape Type does not allowed!"
                Exit Sub
            End If


            '5.
            NullExists = 0
            I = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(5).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in Start Time does not allowed!"
                Exit Sub
            End If

            '6.
            NullExists = 0
            I = 0
            For I = 0 To dgExcel.Rows.Count - 1
                If dgExcel.Rows(I).Cells(6).Text = "&nbsp;" Then
                    NullExists = NullExists + 1
                End If
            Next

            If NullExists <> 0 Then
                lblErr.Text = "Null Values in End Time does not allowed!"
                Exit Sub
            End If

            ''***************************************************************************************************''


            ''*************************************''
            ''******** Already Exists *************''
            ''*************************************''
            Dim ObjAlready As New BusinessFacade.ExcelUpload()
            Dim isBlank As Integer
            isBlank = ObjAlready.AreadyArchived(dgExcel.Rows(0).Cells(3).Text)

            If isBlank = 0 Then
                lblErr.Text = "This Tape is already Archived!"
                Exit Sub
            End If
            ''*************************************''

            If ReporterSlugExists > 0 And TapeNumberExists > 0 And TapeTypeExists > 0 And StartTimeExists > 0 And EndTimeExists > 0 And KeywordExists > 0 And ReporterExists > 0 And UrduSlugExists > 0 And EnglishSlugExists > 0 Then
                Dim H As Integer
                For H = 0 To dgExcel.Rows.Count - 1
                    Dim ObjSave As New BusinessFacade.ExcelUpload()
                    Dim TapeNumber As String = dgExcel.Rows(H).Cells(3).Text
                    Dim Tapetype As String = dgExcel.Rows(H).Cells(4).Text
                    Dim TapeSlug As String = dgExcel.Rows(H).Cells(2).Text + ", Date: " + CStr(dgExcel.Rows(H).Cells(1).Text)
                    Dim EntryDate As String = dgExcel.Rows(H).Cells(1).Text

                    Dim StartTime As String = dgExcel.Rows(H).Cells(5).Text
                    Dim EndTime As String = dgExcel.Rows(H).Cells(6).Text
                    Dim ArrStartTime As Array = StartTime.Split(":")
                    Dim ArrEndTime As Array = StartTime.Split(":")
                    Dim Duration As String
                    If (ArrStartTime.Length <> 4) Or (ArrStartTime.Length <> 4) Then
                        StartTime = "00:00:00:00"
                        EndTime = "00:00:00:00"
                        Duration = "00:00:00:00"
                    Else
                        Duration = Calculate(EndTime, StartTime)
                    End If

                    'Dim Duration As String = dgExcel.Rows(H).Cells(5).ToString()
                    Dim Keyword As String = IIf(dgExcel.Rows(H).Cells(7).Text = "&nbsp;", 0, dgExcel.Rows(H).Cells(7).Text)
                    Dim Reporter As String = IIf(dgExcel.Rows(H).Cells(8).Text = "&nbsp;", 0, dgExcel.Rows(H).Cells(8).Text)

                    Dim FootageType As String = IIf(dgExcel.Rows(H).Cells(11).Text = "&nbsp;", 0, dgExcel.Rows(H).Cells(11).Text)

                    ''**********************************************************''
                    ''******************** Get Employee ID *********************''
                    ''**********************************************************''

                    '   Dim arr_UserID As Array = Split(Request.Cookies("userinfo")("username"), ",")
                    '  Dim UserName As String = arr_UserID(1)

                    Dim UserID As Integer
                    Dim objUserID As New BusinessFacade.Employee()
                    objUserID.SM_LoginID = Request.Cookies("userinfo")("username")
                    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



                    '  Dim UserID As Integer = 3 'Request.Cookies("GAP")("username")


                    Dim TapeSlugID As Integer = ObjSave.InsertExcelUpload(TapeNumber, Tapetype, TapeSlug, EntryDate, StartTime, EndTime, Duration, Keyword, Reporter, UserID, FootageType)
                    Arr.Add(TapeSlugID)

                    ''*****************************************************************''
                    ''********************** Insert Urdu Slug *************************''
                    ''*****************************************************************''

                    Dim Con As System.Data.SqlClient.SqlConnection
                    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                    Dim cmd As New System.Data.SqlClient.SqlCommand
                    cmd.CommandType = Data.CommandType.StoredProcedure
                    cmd.CommandText = "Insert_UrduScript_ExcelUpload"
                    cmd.Connection = Con

                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                    p1.Value = TapeSlugID

                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                    p2.Value = IIf(dgExcel.Rows(H).Cells(9).Text = "&nbsp;", "N/A", dgExcel.Rows(H).Cells(9).Text)

                    Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishSlug", Data.SqlDbType.NText)
                    p3.Value = IIf(dgExcel.Rows(H).Cells(10).Text = "&nbsp;", "N/A", dgExcel.Rows(H).Cells(10).Text)

                    If Con.State = ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd.CommandTimeout = 0
                    cmd.Connection = Con
                    cmd.ExecuteNonQuery()
                    cmd.Connection.Close()

                    ''*****************************************************************''

                Next

                Dim objFinalSave As New BusinessFacade.ExcelUpload()
                objFinalSave.ExcelUplaod_FinalSave(dgExcel.Rows(0).Cells(3).Text)


                ''******************************************''
                ''**** update Tape Slug for SearchEngine ***''
                ''******************************************''

                Dim arrIndex As Integer
                For arrIndex = 0 To Arr.Count - 1
                    Dim Con As System.Data.SqlClient.SqlConnection
                    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                    Dim cmd As New System.Data.SqlClient.SqlCommand
                    cmd.CommandType = Data.CommandType.StoredProcedure
                    cmd.CommandText = "Insert_UrduScript_SearchEngine_ExcelUpload"
                    cmd.Connection = Con

                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                    p1.Value = Arr.Item(arrIndex).ToString()

                    If Con.State = ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd.CommandTimeout = 0
                    cmd.Connection = Con
                    cmd.ExecuteNonQuery()
                    cmd.Connection.Close()

                Next
                ''******************************************''


                lblErr.Text = CStr(dgExcel.Rows.Count) + " Rows Inserted Successfully! "

            End If

        End If
    End Sub

    Private Function Calculate(ByVal StartTime As String, ByVal EndTime As String) As String

        Dim Duration As String




        lblErr.Text = String.Empty

        Dim DUR_1, DUR_2, DUR_3, DUR_4 As String

        Dim time1 As String
        time1 = StartTime
        Dim str As Array = time1.Split(":")

        Dim time2 As String
        time2 = EndTime
        Dim str2 As Array = time2.Split(":")

        Dim Min1 As Integer
        If str(0) <> "" And str(1) <> "" And str(2) <> "" Then
            Min1 = (str(0) * 60 * 60) + (str(1) * 60) + (str(2))
        End If

        Dim Min2 As Integer
        If str2(0) <> "" And str2(1) <> "" And str2(2) <> "" Then
            Min2 = (str2(0) * 60 * 60) + (str2(1) * 60) + (str2(2))
        End If

        Dim Diff As String = ""
        If Min1 >= Min2 Then
            Dim sec As Integer = Min1 - Min2
            If str(3) < str2(3) Then

                Dim a As Integer
                a = str(2)
                Dim b As Integer
                b = str2(2)
                If a = b Then
                    sec = sec - 1
                End If

            End If

            Diff = Format(Int(sec / 3600), "00") & ":" & Format(Int((sec - (Int(sec / 3600) * 3600)) / 60), "00") & ":" & Format(((sec Mod 60)), "00")


            Dim A1 As String = StartTime
            Dim A2 As String = EndTime
            Dim B1 As String = ""
            Dim B2 As String = ""

            B1 = Left(A1, 2) / 24 + Mid(A1, 4, 2) / (24 * 60) + Mid(A1, 7, 2) / (24 * 60 * 60) + Right(A1, 2) / (24 * 60 * 60 * 24)

            B2 = Left(A2, 2) / 24 + Mid(A2, 4, 2) / (24 * 60) + Mid(A2, 7, 2) / (24 * 60 * 60) + Right(A2, 2) / (24 * 60 * 60 * 24)

            Dim B3 As String = ""
            B3 = B1 - B2


            Dim B4 As String = ""
            B4 = Format((B3 * 24 * 60 * 60 - Int(B3 * 24 * 60 * 60)) * 24, "00")
            If B4 = "24" Then
                B4 = "00"
            End If

            Dim Convert As String = ""
            Convert = Diff & ":" & B4

            Dim Dur As String
            Dur = Convert

            Dim arr As Array = Split(Dur, ":")

            DUR_1 = arr(0).ToString
            DUR_2 = arr(1).ToString
            DUR_3 = arr(2).ToString
            DUR_4 = arr(3).ToString

        Else
            DUR_1 = "00"
            DUR_2 = "00"
            DUR_3 = "00"
            DUR_4 = "00"

        End If

        Duration = DUR_1 & ":" & DUR_2 & ":" & DUR_3 & ":" & DUR_4
        Return Duration
    End Function

    Protected Sub lnkSample_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSample.Click
        Dim startInfo As System.Diagnostics.ProcessStartInfo
        Dim pStart As New System.Diagnostics.Process

        'startInfo = New System.Diagnostics.ProcessStartInfo("\\finosys\IMC MIS\AdminPortal.exe")

        startInfo = New System.Diagnostics.ProcessStartInfo("C:\Websites\Roshni3\DAMS\DAMS\ExcelUpload\Sample.xls")


        pStart.StartInfo = startInfo
        pStart.Start()
        pStart.WaitForExit()
    End Sub

    Protected Sub bttnTapeNumber_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnTapeNumber.Click
        If Trim(txt_TapeNumber.Text) = "" Then
            lblErr.Text = "Please Enter Tape Number"
            Exit Sub
        End If

        Dim obj As New BusinessFacade.ExcelUpload()
        Dim TapeLibraryID As Integer = obj.InsertTapeNumber_ExcelUpload(txt_TapeNumber.Text, ddl_TapeType.SelectedValue, ddlStation.SelectedValue)
        If TapeLibraryID = 0 Then
            lblErr.Text = "This Tape Number Alreay Exists!"
            Exit Sub
        Else
            lblErr.Text = "Tape Number has been Saved Sucessfully!"
            Exit Sub
        End If
    End Sub

    Protected Sub bttnClear_TapeNumber_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear_TapeNumber.Click
        ClearControls_TapeNumber()
        lblErr.Text = ""
    End Sub

    Sub ClearControls_TapeNumber()
        txt_TapeNumber.Text = ""
        ddl_TapeType.SelectedIndex = -1
        ddlStation.SelectedIndex = -1

    End Sub

    Protected Sub bttnClear_ExcelArea_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear_ExcelArea.Click
        ClearControls_UploadArea()
    End Sub

    Sub ClearControls_UploadArea()
        dgExcel.DataSource = Nothing
        dgExcel.DataBind()

        lblFilePath.Text = ""
        lblFileName.Text = ""
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub bttnDownload_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDownload.Click
        Dim ds_Excel As New DataSet
        Dim dtNK_Excel, dtRP_Excel As New DataTable
        ds_Excel = New BusinessFacade.ExcelUpload().SetupList_ExeclUpload()

        dtNK_Excel = ds_Excel.Tables(0)
        dtRP_Excel = ds_Excel.Tables(0)




        If dtNK_Excel.Rows.Count > 0 Then
            '   ExportGridToExcel(dtNK_Excel)
            ExportGridToExcel_Complete(ds_Excel)

        Else
            MsgBox("Data does not exists is system!", MsgBoxStyle.Exclamation)
        End If
    End Sub

    Private Sub ExportGridToExcel_Complete(ByVal Ds As DataSet)

        Dim Excel As Object = CreateObject("Excel.Application")

        If Excel Is Nothing Then
            MsgBox("It appears that Excel is not installed on this machine. This operation requires MS Excel to be installed on this machine.", MsgBoxStyle.Critical)
            Return
        End If

        'Make Excel visible
        Excel.Visible = True

        Dim dt As New DataTable
        dt = Ds.Tables(0)
        Dim dt2 As New DataTable
        dt2 = Ds.Tables(1)
        Dim dt3 As New DataTable
        dt3 = Ds.Tables(2)

        'Initialize Excel Sheet
        With Excel
            .SheetsInNewWorkbook = 3
            .Workbooks.Add()

            Dim ColumnNo As Integer = 1
            Dim RowNo As Integer = 2


            .Worksheets(2).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt2.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt2.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt2.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt2.Rows(J)(L).ToString()
                Next
            Next



            .Worksheets(3).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt3.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt3.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt3.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt3.Rows(J)(L).ToString()
                Next
            Next



            .Worksheets(1).Select()


            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt.Rows.Count - 1
                For L As Integer = 0 To dt.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt.Rows(J)(L).ToString()
                Next
            Next

        End With

        'Excel.Quit()
        System.Runtime.InteropServices.Marshal.ReleaseComObject(Excel)
        Excel = Nothing
        'MsgBox("Export to Excel Complete", MsgBoxStyle.Information)
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('../ExcelUpload/FrmExcelDownload.aspx" + "', 'mywindow'); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)
    End Sub

End Class


