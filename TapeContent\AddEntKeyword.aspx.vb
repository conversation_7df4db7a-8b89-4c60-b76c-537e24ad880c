
Partial Class TapeContent_AddEntKeyword
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()

        ddl_SubContentType.DataSource = New BusinessFacade.SubContentType().GetRecords()
        ddl_SubContentType.DataTextField = "SubContentTypeName"
        ddl_SubContentType.DataValueField = "SubContentTypeID"
        ddl_SubContentType.DataBind()
        ddl_SubContentType.Items.Insert(0, "--Select--")

        '''''''''''''''''''''''''''''''''''''''''''''''''''
        lstKeyType.DataTextField = "KeyType"
        lstKeyType.DataValueField = "KeyTypeID"
        lstKeyType.DataSource = New BusinessFacade.KeyType().KeyType_Ent_GetRecords()
        lstKeyType.DataBind()


    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        lblErr.Text = String.Empty

        ''******************************************''
        ''************** Get Keyword  **************''
        ''******************************************''

        Dim KeywordID As Integer
        Dim objKeywordID As New BusinessFacade.EntertainmentKeyword()
        objKeywordID.EntertainmentKeyword = txt_EntertainmentKeyword.Text
        KeywordID = objKeywordID.IsExists_EntKeyword(objKeywordID.EntertainmentKeyword)

        If KeywordID <> "0" Then
            lblErr.Text = "This Keyword already Exists !!"
        Else
            SaveRecord()
        End If
        '  clrscr()

    End Sub

    Private Sub SaveRecord()

        If txt_EntertainmentKeyword.Text = "" Then
            lblErr.Text = "Please Insert Entertainment Keyword!!"
        ElseIf ddl_SubContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select SubContent Type!!"
        Else
            Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
            objEntKeyword.EntertainmentKeyword = txt_EntertainmentKeyword.Text
            objEntKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objEntKeyword.TKeytype = lstKeyType.SelectedItem.ToString
            objEntKeyword.SaveRecord()

            FillGrid()
            lblErr.Text = "Record has been Saved !!"
        End If

    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        Dim ObjGrid As New BusinessFacade.EntertainmentKeyword()
        ObjGrid.EntertainmentKeyword = txt_EntertainmentKeyword.Text
        dt = ObjGrid.ShowEntertainmentKeyword(ObjGrid.EntertainmentKeyword)
        dg_EntKeyword.DataSource() = dt
        dg_EntKeyword.Columns(0).Visible = True
        dg_EntKeyword.Columns(1).Visible = True
        dg_EntKeyword.DataBind()
        dg_EntKeyword.Columns(0).Visible = False
        dg_EntKeyword.Columns(1).Visible = False
    End Sub

    Private Sub clrscr()
        txt_EntertainmentKeyword.Text = String.Empty
        txt_EntKeywordID.Text = String.Empty
        ddl_SubContentType.SelectedIndex = 0
        txt_TKeytype.Text = String.Empty
        lstKeyType.ClearSelection()

    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

End Class
