<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Store_StockRegister.aspx.vb" Inherits="StoreManagement_Store_StockRegister" title="Home > Store Management > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<%@ Register Assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebSchedule" TagPrefix="igsch" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<table width="100%">
    <tr>
        <td style="height: 21px; text-decoration: underline;" class="labelheading">
            <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
            &gt;
            <asp:LinkButton ID="LnkStoreManagement" runat="server" CssClass="labelheading">Store Management</asp:LinkButton>
            &gt; Add New</td>
    </tr>
            
            <tr class="mytext">
                <td valign="top" >
                    <table >
                        <tr class="mytext">
                            <td style="height: 13px" >
                                PR No.</td>
                            <td style="height: 13px" >
                                SIR No</td>
                            <td style="height: 13px" >
                                City</td>
                            <td style="height: 13px" >
                                Stock Status</td>
                            <td style="width: 149px; height: 13px;">
                                Requisition Source</td>
                            <td style="width: 133px; height: 13px;">
                                Remarks</td>
                            <td style="width: 100px; height: 13px;" >
                                Entry Date</td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 22px" valign="top" >
                                <asp:TextBox CssClass="mytext" ID="txt_PRNo" runat="server" Width="100px"></asp:TextBox></td>
                            <td style="height: 22px" valign="top" >
                                <asp:TextBox CssClass="mytext" ID="txt_SIRNO" runat="server" Width="100px"></asp:TextBox></td>
                            <td style="height: 22px" valign="top" >
                                <asp:DropDownList CssClass="mytext" ID="ddl_City_Issued" runat="server" Width="152px">
                                </asp:DropDownList></td>
                            <td style="height: 22px" valign="top" >
                                <asp:DropDownList CssClass="mytext" ID="ddl_StockStatus" runat="server" Width="152px">
                                </asp:DropDownList></td>
                            <td style="width: 149px; height: 22px" valign="top">
                                <asp:DropDownList CssClass="mytext" ID="ddl_RequisitionSource" runat="server" Width="144px">
                                </asp:DropDownList>
                                &nbsp; &nbsp; &nbsp; &nbsp;
                            </td>
                            <td style="width: 133px; height: 22px" valign="top">
                                <asp:TextBox CssClass="mytext" ID="txtRemarks" runat="server" Height="24px" TextMode="MultiLine" Width="120px"></asp:TextBox></td>
                            <td style="width: 100px;" valign="top" >
                                <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" Width="100px"></asp:TextBox></td>
                        </tr>
                    </table>
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="488px"></asp:Label></td>
            </tr>
    <tr cssclass="mytext">
        <td class="bottomMain" style="height: 29px">
            &nbsp;<asp:Button CssClass="buttonA" ID="bttnSave" runat="server" Text="Save" Width="64px" />
            <asp:Button
                                        CssClass="buttonA" ID="bttnCencel" runat="server" Text="Cancel" /></td>
    </tr>
            <tr>
                <td>
                    <asp:GridView ID="dg" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                        CssClass="gridContent" PageSize="25" Width="424px">
                        <Columns>
                            <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" />
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" >
                                <ItemStyle Width="250px" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="Quantity">
                                <ItemTemplate>
                                    <asp:TextBox ID="txt_Quantity" runat="server" Width="32px" Text='<%# Eval("Qty") %>'></asp:TextBox>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="StockRegisterDetailID" HeaderText="StockRegisterDetailID" />
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                        <AlternatingRowStyle CssClass="AlternateRows" />
                    </asp:GridView>
                </td>
            </tr>
    <tr>
        <td>
        </td>
    </tr>
    <tr>
        <td class="labelheading">
            <asp:Label ID="lblAuditHistory" runat="server" Visible="False">Audit History - Store Management</asp:Label></td>
    </tr>
    <tr>
        <td>
            <asp:GridView ID="dgAuditHistory" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                PageSize="25" Width="100%">
                <Columns>
                    <asp:BoundField DataField="AddedBy" HeaderText="Added By" />
                    <asp:BoundField DataField="AddedDate" HeaderText="Added Date" />
                    <asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy" />
                    <asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate" />
                </Columns>
                <SelectedRowStyle BackColor="#FFE0C0" />
                <HeaderStyle CssClass="gridheader" />
            </asp:GridView>
        </td>
    </tr>
        </table>
         <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
        </cc1:ToolkitScriptManager>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" Format="dd-MMM-yyyy"
        TargetControlID="txtEntryDate" CssClass="MyCalendar">
    </cc1:CalendarExtender>
                                <asp:TextBox CssClass="mytext" ID="txt_MasterID" runat="server" Width="64px" Visible="False"></asp:TextBox>
</asp:Content>

