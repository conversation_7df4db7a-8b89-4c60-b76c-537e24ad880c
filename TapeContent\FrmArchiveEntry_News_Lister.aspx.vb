Imports System.Data
Imports System.Data.SqlClient

Partial Class TapeContent_FrmArchiveEntry_News_Lister
    Inherits System.Web.UI.Page
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    'Private strConnection As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                '    lbl_UserName.Text = Master.FooterText

                '    Dim Arr_UserID As Array = Split(lbl_UserName.Text, ",")
                '    lbl_UserName.Text = Arr_UserID(1)
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                Chk_TapeNumber.Checked = True
                Chk_SubCloset.Checked = True
                Chk_ReporterSlug.Checked = True
                Chk_ProposedSlug.Checked = True
                Chk_Reporter.Checked = True
                Chk_CameraMan.Checked = True
                Chk_EnglishScript.Checked = True
                Chk_UrduScript.Checked = True
                Chk_Loc.Checked = True
                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        'ddl_Reporter.DataTextField = "EmployeeName"
        'ddl_Reporter.DataValueField = "EmployeeID"
        'ddl_Reporter.DataSource = New BusinessFacade.TapeContent_News().Reporter_GetRecords()
        'ddl_Reporter.DataBind()

        'ddl_CameraMan.DataTextField = "EmployeeName"
        'ddl_CameraMan.DataValueField = "EmployeeID"
        'ddl_CameraMan.DataSource = New BusinessFacade.TapeContent_News().CameraMan_GetRecords()
        'ddl_CameraMan.DataBind()

        'ddl_TapeNumber.DataTextField = "TapeNumber"
        'ddl_TapeNumber.DataValueField = "TapeLibraryID"
        'ddl_TapeNumber.DataSource = New BusinessFacade.TapeContentLister().TapeContent_News_TapeNo_GetRecords()
        'ddl_TapeNumber.DataBind()

        ddl_SubCloset.DataTextField = "SubClosetName"
        ddl_SubCloset.DataValueField = "SubClostID"
        ddl_SubCloset.DataSource = New BusinessFacade.SubCloset().GetRecords()
        ddl_SubCloset.DataBind()

    End Sub

    Protected Sub bttnAdd_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAdd.Click
        Response.Redirect("FrmArchiveEntry_News.aspx")
    End Sub

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch.Click
        FillGrid()
    End Sub

    Private Sub FillGrid()
        ''******************************************''
        ''********** Get Reporterer ID *************''
        ''******************************************''

        Dim RptID As Integer
        Dim objRptID As New BusinessFacade.TapeIssuance()
        objRptID.EmployeeName = txt_ReporterName.Text
        RptID = objRptID.GetEmployeeID_byEmployeeName(objRptID.EmployeeName)

        ''******************************************''
        ''********** Get Camera Man ID *************''
        ''******************************************''

        Dim CMID As Integer
        Dim objCMID As New BusinessFacade.TapeIssuance()
        objCMID.EmployeeName = txt_CameraMan.Text
        CMID = objCMID.GetEmployeeID_byEmployeeName(objCMID.EmployeeName)


        Dim arr As Array = Split(txt_TapeNo.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNo.Text = arr(1)
        End If

        '''''''''' Get TapeLibraryID '''''''''''''''''
        Dim TapeID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNo.Text
        TapeID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        Try

            Dim Qry As String
            Dim cmd As New SqlCommand()
            cmd.Connection = objConnection
            'cmd.CommandText = "TapeContent_News_GetRecords_New"
            cmd.CommandText = "TapeContent_News_GetRecords_New2"
            cmd.CommandType = Data.CommandType.StoredProcedure


            Dim ad As New SqlDataAdapter(cmd)
            Dim ds As New Data.DataSet()

            ''**************************************************''

            Dim A1 As Integer
            If Chk_TapeNumber.Checked = True Then
                A1 = -1
            Else
                A1 = TapeID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@TapeLibraryID", A1)

            Dim C1 As Integer
            If Chk_Reporter.Checked = True Then
                C1 = -1
            Else
                C1 = RptID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ReporterID", C1)

            Dim D1 As Integer
            If Chk_CameraMan.Checked = True Then
                D1 = -1
            Else
                D1 = CMID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@CameraManID", D1)

            Dim Var11 As String
            If Chk_ReporterSlug.Checked = True Then
                Var11 = "-1"
            Else
                Var11 = txt_ReporterSlug.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ReporterSlug", Var11)

            Dim Var21 As String
            If Chk_ProposedSlug.Checked = True Then
                Var21 = "-1"
            Else
                Var21 = txt_ProposedSlug.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ProposedSlug", Var21)



            Dim Var31 As String
            If Chk_Loc.Checked = True Then
                Var31 = "-1"
            Else
                Var31 = txt_Loc.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@LocationCode", Var31)

            ''**************************************************************''

            ad.Fill(ds, "TapeContent")
            Qry = ad.SelectCommand.CommandText
            'ad.SelectCommand.CommandText = "TapeContentDetail_News_GetRecords_New"
            ad.SelectCommand.CommandText = "TapeContentDetail_News_GetRecords_New2"
            ad.SelectCommand.Parameters.Clear()

            Dim A As Integer
            If Chk_TapeNumber.Checked = True Then
                A = -1
            Else
                A = TapeID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@TapeLibraryID", A)

            Dim C As Integer
            If Chk_Reporter.Checked = True Then
                C = -1
            Else
                C = RptID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ReporterID", C)

            Dim D As Integer
            If Chk_CameraMan.Checked = True Then
                D = -1
            Else
                D = CMID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@CameraManID", D)

            Dim Var1 As String
            If Chk_ReporterSlug.Checked = True Then
                Var1 = "-1"
            Else
                Var1 = txt_ReporterSlug.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ReporterSlug", Var1)

            Dim Var2 As String
            If Chk_ProposedSlug.Checked = True Then
                Var2 = "-1"
            Else
                Var2 = txt_ProposedSlug.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ProposedSlug", Var2)

            Dim Var3a1 As String
            If Chk_Loc.Checked = True Then
                Var3a1 = "-1"
            Else
                Var3a1 = txt_Loc.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@LocationCode", Var3a1)

            ad.Fill(ds, "TapeContentDetail_News")
            ds.Relations.Add(ds.Tables("TapeContent").Columns("TapeContentNewsID"), ds.Tables("TapeContentDetail_News").Columns("TapeContentID"))
            dg_search.DataSource = ds.Tables("TapeContent").DefaultView
            dg_search.DataBind()
            If ds.Tables(0).Rows.Count > 0 Then
                dg_search.DataSource = ds.Tables(0)
                dg_search.DataBind()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub dg_search_SelectedRowsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedRowsEventArgs) Handles dg_search.SelectedRowsChange
        txt_TapeContentID.Text = dg_search.DisplayLayout.SelectedRows.Item(0).Cells(0).ToString
    End Sub

    Protected Sub bttnEdit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEdit.Click
        If txt_TapeContentID.Text <> "" Then

            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.TapeContent()
            objValidation.TapeContentID = txt_TapeContentID.Text
            Dim BaseStationID As Integer = objValidation.BaseStationValidation_ArchivalNews()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then
                Response.Redirect("FrmArchiveEntry_News.aspx?TapeContentID=" & txt_TapeContentID.Text)
            Else
                lblErr.Text = "You are not allowed to Edit this Record!!"
            End If
            ''************ End *************''
            ''******************************''

        End If
    End Sub

    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click
        txt_TapeContentID.Text = String.Empty
        txt_EnglishScript.Text = String.Empty
        txt_UrduScript.Text = String.Empty
        txt_ReporterSlug.Text = String.Empty
        txt_ProposedSlug.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub bttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDelete.Click
        If txt_TapeContentID.Text <> "" Then

            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.TapeContent()
            objValidation.TapeContentID = txt_TapeContentID.Text
            Dim BaseStationID As Integer = objValidation.BaseStationValidation_ArchivalNews()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then

                ''******************************************************''
                ''******************************************************''
                ''******************************************************''

                Dim ObjIsIssued As New BusinessFacade.TapeContent()
                ObjIsIssued.TapeContentID = txt_TapeContentID.Text
                Dim IsAvailable As String
                IsAvailable = ObjIsIssued.Proc_IsAvailableNewsArchivalTapes()

                If IsAvailable = 0 Then
                    lblErr.Text = "This Tape Is Not Available, You are not allowed to Delete this Record!!"
                Else
                    ''******************************************************''
                    ''******************** Get User ID *********************''
                    ''******************************************************''

                    Dim UserID As Integer
                    Dim objUserID As New BusinessFacade.Employee()
                    objUserID.SM_LoginID = lbl_UserName.Text
                    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                    Dim objDelete As New BusinessFacade.TapeContent_News()
                    objDelete.userID = UserID
                    objDelete.TapeContentID = txt_TapeContentID.Text
                    objDelete.DeleteRecord()

                    txt_TapeContentID.Text = ""

                    FillGrid()
                End If
            Else
                lblErr.Text = "You are not allowed to Delete this Record!!"
            End If
                ''************ End *************''
                ''******************************''
            End If

    End Sub

    Protected Sub dg_search_PageIndexChanged(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.PageEventArgs) Handles dg_search.PageIndexChanged
        dg_search.DisplayLayout.Pager.CurrentPageIndex = e.NewPageIndex()
        FillGrid()
    End Sub
End Class
