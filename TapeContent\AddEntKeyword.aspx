<%@ Page Language="VB" AutoEventWireup="false" CodeFile="AddEntKeyword.aspx.vb" Inherits="TapeContent_AddEntKeyword" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<script language="javascript" type="text/ecmascript">
 function CloseNewKeyword()
    {
         window.opener.location.href="FrmArchiveEntry_Ent.aspx";
//        RefreshParent_3();
        self.close();
    }
</script>

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
    </head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:ScriptManager ID="ScriptManager1" runat="server">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <table width="100%">
                    <tr>
                        <td class="labelheading" style="height: 21px; text-decoration: underline" bgcolor="#000033">
                            <asp:Label ID="Label4" runat="server" Font-Bold="True" Font-Names="Arial Rounded MT Bold"
                                Font-Size="Large" Font-Strikeout="False" ForeColor="Yellow" Text="Entertainment Keyword > Add New"></asp:Label></td>
                    </tr>
                    <tr>
                        <td valign="top" bgcolor="#add8e6">
                            <table>
                                <tr class="mytext">
                                    <td valign="top" bgcolor="#add8e6">
                                        <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="Entertainment Keyword"></asp:Label></td>
                                    <td valign="top">
                                        <asp:TextBox ID="txt_EntertainmentKeyword" runat="server" CssClass="mytext" TextMode="MultiLine"
                                            Width="120px" Font-Size="Small" Height="40px"></asp:TextBox></td>
                                    <td valign="top">
                                        <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="Subcontent Type"></asp:Label></td>
                                    <td style="height: 36px" valign="top">
                                        <asp:DropDownList ID="ddl_SubContentType" runat="server" CssClass="mytext" Width="152px">
                                        </asp:DropDownList></td>
                                    <td style="height: 36px" valign="top">
                                        <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="Key type"></asp:Label></td>
                                    <td style="height: 36px" valign="top">
                                        <asp:ListBox ID="lstKeyType" runat="server" CssClass="mytext" Height="64px" SelectionMode="Multiple"
                                            Width="168px" Font-Size="Small"></asp:ListBox></td>
                                    <td style="height: 36px" valign="top">
                                        <asp:TextBox ID="txt_TKeytype" runat="server" CssClass="mytext" TextMode="MultiLine"
                                            Visible="False" Width="40px"></asp:TextBox></td>
                                </tr>
                            </table>
                            &nbsp;<asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="504px"></asp:Label></td>
                    </tr>
                    <tr>
                        <td class="bottomMain" style="height: 26px" bgcolor="#add8e6">
                            &nbsp;<asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Text="Save" Width="64px" />
                            <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Text="Clear" Width="64px" />&nbsp;<asp:Button
                                ID="bttnClose" runat="server" CssClass="buttonA" OnClientClick="javascript:CloseNewKeyword();"
                                Text="Close" Width="64px" /></td>
                    </tr>
                    <tr>
                        <td valign="top" style="height: 154px">
                            <asp:GridView ID="dg_EntKeyword" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                                PageSize="25" Width="904px" BackColor="#FFE0C0" Font-Size="Small">
                                <Columns>
                                    <asp:BoundField ApplyFormatInEditMode="True" DataField="EntertainmentKeywordID" Visible="False">
                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                        <HeaderStyle Width="0px" />
                                    </asp:BoundField>
                                    <asp:BoundField ApplyFormatInEditMode="True" DataField="SubContentTypeID" Visible="False">
                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                        <HeaderStyle Width="0px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SubContentTypeName" HeaderText="Sub-Content Type" />
                                    <asp:BoundField DataField="EntertainmentKeyword" HeaderText="Ent.Keyword">
                                        <HeaderStyle Width="250px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="TKeytype" HeaderText="Key Type">
                                        <ItemStyle Width="350px" />
                                    </asp:BoundField>
                                </Columns>
                                <SelectedRowStyle BackColor="#FFE0C0" />
                                <HeaderStyle CssClass="gridheader" BackColor="#FFFF80" />
                            </asp:GridView>
                        </td>
                    </tr>
                    <tr>
                        <td style="height: 21px" bgcolor="#ffffff">
                            <asp:TextBox ID="txt_EntKeywordID" runat="server" CssClass="mytext" Visible="False"
                                Width="48px"></asp:TextBox></td>
                    </tr>
                </table>
            </ContentTemplate>
        </asp:UpdatePanel>
    
    </div>
    </form>
</body>
</html>
