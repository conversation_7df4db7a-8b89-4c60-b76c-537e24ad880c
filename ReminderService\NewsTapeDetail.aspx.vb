Imports System.Data
Partial Class SearchEngine_NewsTapeDetail
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim DS1 As DataSet
    Dim DS2 As DataSet

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            Dim TapeNumber As String
            TapeNumber = Request.QueryString("TapeNumber")

            Dim Program As String
            Program = Request.QueryString("ReportSlug")

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "GetNewsSearchEngine_Keywords"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo.Value = TapeNumber

            Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReportSlug", Data.SqlDbType.Text)
            Prg.Value = Program

            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS, "Table0")

            If DS.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS.Tables("Table0").Rows.Count - 1 Then
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + ",</b><br>"
                    Else
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + "</b><br>"
                    End If
                Next
            End If

            ''********************************************''
            ''*************** Footages *******************''
            ''********************************************''

            Dim cmd1 As New System.Data.SqlClient.SqlCommand
            cmd1.CommandType = Data.CommandType.StoredProcedure
            cmd1.CommandText = "GetNewsSearchEngine_Footages"
            cmd1.Connection = Con
            cmd1.CommandTimeout = 0

            Dim TapeNo1 As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo1.Value = TapeNumber

            Dim Prg1 As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@ReportSlug", Data.SqlDbType.Text)
            Prg1.Value = Program

            Dim da1 As New System.Data.SqlClient.SqlDataAdapter
            DS1 = New DataSet

            da1.SelectCommand = cmd1
            da1.Fill(DS1, "Table0")

            If DS1.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS1.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS1.Tables("Table0").Rows.Count - 1 Then
                        lblFootages.Text = lblFootages.Text + "<b>" + J + " : " + DS1.Tables("Table0").Rows(I)(0).ToString + ",</b><br>"
                    Else
                        lblFootages.Text = lblFootages.Text + "<b>" + J + " : " + DS1.Tables("Table0").Rows(I)(0).ToString + "</b><br>"
                    End If
                Next
            End If

            ''********************************************''
            ''*************** Eng Script *******************''
            ''********************************************''

            Dim cmd2 As New System.Data.SqlClient.SqlCommand
            cmd2.CommandType = Data.CommandType.StoredProcedure
            cmd2.CommandText = "GetEnglishScript"
            cmd2.Connection = Con
            cmd2.CommandTimeout = 0

            Dim TapeNo2 As Data.SqlClient.SqlParameter = cmd2.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo2.Value = TapeNumber

            Dim Prg2 As Data.SqlClient.SqlParameter = cmd2.Parameters.Add("@ReportSlug", Data.SqlDbType.Text)
            Prg2.Value = Program


            Dim da2 As New System.Data.SqlClient.SqlDataAdapter
            DS2 = New DataSet

            da2.SelectCommand = cmd2
            da2.Fill(DS2, "Table0")
            'lblEngScript.Text = DS.Tables(0).Rows(0)(0)
            lblEngScript.Text = DS2.Tables("Table0").Rows(0)(0)

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub GetEnglishScript_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles GetEnglishScript.Click
        'lblEngScript.Visible = True
        ''*********************** Store Procedure **********************'

        'Dim cmd As New System.Data.SqlClient.SqlCommand
        'cmd.CommandType = Data.CommandType.StoredProcedure
        'cmd.CommandText = "GetEnglishScript"
        'cmd.Connection = Con
        'cmd.CommandTimeout = 0

        'Dim RecordID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RecordID", Data.SqlDbType.Int)
        'RecordID.Value = Request.QueryString("RecordID")

        'Dim da As New System.Data.SqlClient.SqlDataAdapter
        'DS = New DataSet

        'da.SelectCommand = cmd
        'da.Fill(DS)
        ''lblEngScript.Text = DS.Tables(0).Rows(0)(0)
        'lblEngScript.Text = DS.Tables(0).Rows(0)(0)

    End Sub

End Class
