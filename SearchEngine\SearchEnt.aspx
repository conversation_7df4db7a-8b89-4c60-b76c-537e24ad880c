<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="SearchEnt.aspx.vb" Inherits="SearchEngine_SearchEnt" title="Home > Search Engine > News" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Search" runat="server">
    </asp:ScriptManager>
    <table style="width: 100%">
        <tr>
            <td class="labelheading" style="width: 100%; text-decoration: underline">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading" OnClick="lnkHomePage_Click">Home</asp:LinkButton>
                &gt; Advanced Search &gt; Entertainment<asp:TextBox ID="TextBox1" runat="server"
                    Visible="False" Width="24px"></asp:TextBox></td>
        </tr>
        <tr>
            <td class="mytext" style="width: 100%; height: 25px;">
                Find Record that have....
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Names="Calibri" Font-Size="Medium"
                    ForeColor="Red"></asp:Label></td>
        </tr>
        <tr>
            <td class="mytext" style="width: 100%;">
                <table>
                    <tr>
                        <td style="width: 100px">
                            <asp:Label ID="Label7" runat="server" Font-Overline="False" Font-Size="X-Small" ForeColor="MidnightBlue"
                                Text="Tape Number"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_TapeNo_1" runat="server" Width="176px" CssClass="mytext" Font-Size="X-Small"></asp:TextBox></td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                            <asp:Label ID="Label14" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Tape Type"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txtTapeType1" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:Label ID="Label8" runat="server" Font-Overline="False" Font-Size="X-Small" ForeColor="MidnightBlue"
                                Text="Program Name"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_Program_1" runat="server" CssClass="mytext" Width="176px" Height="40px" TextMode="MultiLine" Font-Size="X-Small"></asp:TextBox></td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                            &nbsp;<asp:Label ID="Label15" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Abstract"></asp:Label></td>
                        <td>
                            <asp:TextBox ID="AllAbstract" runat="server" Height="40px" TextMode="MultiLine" Width="176px"></asp:TextBox>&nbsp;
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:Label ID="Label9" runat="server" Font-Overline="False" Font-Size="X-Small" ForeColor="MidnightBlue"
                                Text="Note Area"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="AllNoteArea" runat="server" Height="40px" TextMode="MultiLine" Width="176px"></asp:TextBox></td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                            <asp:Label ID="Label16" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Key Type"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_KeyType_1" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px; height: 22px;">
                            <asp:Label ID="Label10" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="1st Key Word"></asp:Label></td>
                        <td style="width: 100px; height: 22px;">
                            <asp:TextBox ID="txt_keyword_1" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></td>
                        <td style="width: 50px; height: 22px">
                        </td>
                        <td style="width: 100px; height: 22px;" align="left">
                            <asp:Label ID="Label17" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Episode Number"></asp:Label></td>
                        <td style="height: 22px">
                            <asp:TextBox ID="txtEpisodeNo" runat="server" CssClass="mytext" Width="176px"></asp:TextBox>
                            <asp:CheckBox ID="chkEpisode" runat="server" Text="Ignore" Checked="True" Visible="False" /></td>
                    </tr>
                    <tr>
                        <td style="width: 100px; height: 13px;">
                        </td>
                        <td style="width: 180px; height: 13px;">
                        </td>
                        <td style="width: 50px; height: 13px">
                        </td>
                        <td style="width: 100px; height: 13px;" align="left">
                        </td>
                        <td style="width: 100px; height: 13px;">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:Label ID="Label11" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="2nd Key Word"></asp:Label></td>
                        <td style="width: 180px">
                            <asp:TextBox ID="txt_keyword_2" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                            <asp:Label ID="Label18" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="From Date"></asp:Label></td>
                        <td>
                            <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" Width="176px"></asp:TextBox>
                            <asp:CheckBox ID="chkDate" runat="server" Text="Ignore" Checked="True" Visible="False" /></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:Label ID="Label12" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Base Station"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddlBaseStation" runat="server" CssClass="mytext" Width="88px">
                                <asp:ListItem Value="-1">IGNORE</asp:ListItem>
                                <asp:ListItem Value="1">KARACHI</asp:ListItem>
                                <asp:ListItem Value="3">LAHORE</asp:ListItem>
                                <asp:ListItem Value="4">ISLAMABAD</asp:ListItem>
                                <asp:ListItem Value="5">Peshawar</asp:ListItem>
                                <asp:ListItem Value="6">DUBAI</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 50px">
                        </td>
                        <td style="width: 100px" align="left">
                            <asp:Label ID="Label19" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="To Date"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 50px">
                        </td>
                        <td align="left" style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px" valign="top">
                            <asp:Label ID="Label13" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Result Per Page"></asp:Label></td>
                        <td style="width: 100px" valign="top">
                            <asp:DropDownList ID="ddl_ResultPerPage" runat="server" CssClass="mytext" Width="56px">
                                <asp:ListItem>2000</asp:ListItem>
                                <asp:ListItem>50</asp:ListItem>
                                <asp:ListItem>100</asp:ListItem>
                                <asp:ListItem>200</asp:ListItem>
                                <asp:ListItem>300</asp:ListItem>
                                <asp:ListItem>400</asp:ListItem>
                                <asp:ListItem>500</asp:ListItem>
                                <asp:ListItem>600</asp:ListItem>
                                <asp:ListItem>700</asp:ListItem>
                                <asp:ListItem>800</asp:ListItem>
                                <asp:ListItem>900</asp:ListItem>
                                <asp:ListItem>1000</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 50px">
                        </td>
                        <td align="left" style="width: 100px" valign="top">
                            <asp:Label ID="Label20" runat="server" Font-Overline="False" Font-Size="X-Small"
                                ForeColor="MidnightBlue" Text="Urdu Script"></asp:Label></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txtUrduScript" runat="server" Height="40px" TextMode="MultiLine"
                                Width="176px"></asp:TextBox></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="width: 100%;">
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtenderTapeNo_1" 
                    runat="server" 
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="TapeContentEnt_TapeNumber_GetRecords_2"
                     MinimumPrefixLength="3" 
                    ServicePath="AutoComplete.asmx" TargetControlID="txt_TapeNo_1" >
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_5">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender6" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Program_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender7" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Program_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender8" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Program_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender9" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    MinimumPrefixLength="1" 
                    EnableCaching="true" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Program_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender10" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    MinimumPrefixLength="1" 
                    EnableCaching="true" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Program_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Note_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="NoteArea"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NoreArea_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Note_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="NoteArea"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NoreArea_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Note_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="NoteArea"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NoreArea_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Note_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="NoteArea"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NoreArea_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Note_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    ServiceMethod="NoteArea"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NoreArea_5">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Ent_KW1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_keyword_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Ent_KW2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_keyword_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Ent_KW3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_keyword_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Ent_KW4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_keyword_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Ent_KW5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_keyword_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_KeyType_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetKeyTypes"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_KeyType_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_KeyType_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetKeyTypes"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_KeyType_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_KeyType_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetKeyTypes"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_KeyType_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_KeyType_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetKeyTypes"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_KeyType_4">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_KeyType_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetKeyTypes"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_KeyType_5">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeType" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetTapeType"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtTapeType1">
                </cc1:AutoCompleteExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_Tape_1" runat="server"
                    TargetControlID="txt_TapeNo_1" WatermarkCssClass="watermarked" WatermarkText="Enter Tape No !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_Prog_1" runat="server"
                    TargetControlID="txt_Program_1" WatermarkCssClass="watermarked2" WatermarkText="Enter Program !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_Note_1" runat="server"
                    TargetControlID="AllNoteArea" WatermarkCssClass="watermarked2" WatermarkText="Enter Note !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KW_1" runat="server" TargetControlID="txt_keyword_1"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keyword !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderKW2" runat="server" TargetControlID="txt_keyword_2"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keyword !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderAbs" runat="server" TargetControlID="AllAbstract"
                    WatermarkCssClass="watermarked2" WatermarkText="Enter Abstract !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KT_1" runat="server" TargetControlID="txt_KeyType_1"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keytype !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderTapeType" runat="server"
                    TargetControlID="txtTapeType1" WatermarkCssClass="watermarked" WatermarkText="Enter TapeType !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderDate" runat="server" TargetControlID="txtEntryDate"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Date !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderEp" runat="server" TargetControlID="txtEpisodeNo"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Episodes !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderToDate" runat="server"
                    TargetControlID="txtToDate" WatermarkCssClass="watermarked" WatermarkText="Enter To Date !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:CalendarExtender ID="CalendarExtenderFromDate" runat="server" TargetControlID="txtEntryDate" Format="dd MMM yyyy" CssClass="MyCalendar">
                </cc1:CalendarExtender>
                <cc1:CalendarExtender ID="CalendarExtenderToDate" runat="server" Format="dd-MMM-yyyy"
                    TargetControlID="txtToDate" CssClass="MyCalendar">
                </cc1:CalendarExtender>
                &nbsp; &nbsp;&nbsp;
                            <asp:CheckBox ID="Chk_TapeNumber" runat="server" Height="16px" Text="Ignore" Width="32px" Visible="False" /><asp:CheckBox ID="Chk_Program" runat="server" Height="16px" Text="Ignore" Width="32px" Visible="False" /><asp:CheckBox ID="Chk_NoteArea" runat="server" Height="16px" Text="Ignore" Width="32px" Visible="False" /><asp:CheckBox ID="Chk_KW" runat="server" Height="16px" Text="Ignore" Width="32px" Visible="False" /><asp:CheckBox ID="Chk_KT" runat="server" Height="16px" Text="Ignore" Width="32px" Visible="False" /></td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 29px">
                &nbsp;&nbsp;
                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Search Records >>"
                    Width="160px" />
                <asp:Button ID="Button2" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Clear >>"
                    Width="88px" />
                &nbsp;<asp:Button ID="bttnSearch" runat="server" CssClass="buttonA" Font-Bold="True" Text="Search Records"
                    Width="112px" Visible="False" />&nbsp;
                <asp:Button ID="btnView" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< View Tapes >>"
                    Width="130px" TabIndex="13" Visible="False" />&nbsp;</td>
        </tr>
        <tr>
            <td>
                <table class="mytext" style="width: 1064px">
                    <tr>
                        <td style="width: 267px; height: 26px">
                            </td>
                        <td style="width: 830px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label1" runat="server" Text="Tape No" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 26px">
                            </td>
                        <td style="width: 30px; height: 26px">
                            <asp:DropDownList ID="Tape_option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_2" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 26px; height: 26px">
                            <asp:DropDownList ID="Tape_option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="Tape_option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="Tape_option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 267px; height: 25px">
                            </td>
                        <td style="width: 830px; height: 25px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label2" runat="server" Text="Program" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 25px">
                            </td>
                        <td style="width: 30px; height: 25px">
                            <asp:DropDownList ID="Program_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 25px">
                            <asp:TextBox ID="txt_Program_2" runat="server" CssClass="mytext" Width="128px" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 26px; height: 25px">
                            <asp:DropDownList ID="Program_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 25px">
                            <asp:TextBox ID="txt_Program_3" runat="server" CssClass="mytext" Width="128px" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 25px">
                            <asp:DropDownList ID="Program_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 25px">
                            <asp:TextBox ID="txt_Program_4" runat="server" CssClass="mytext" Width="128px" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 25px">
                            <asp:DropDownList ID="Program_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 25px">
                            <asp:TextBox ID="txt_Program_5" runat="server" CssClass="mytext" Width="128px" TextMode="Password" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 267px; height: 26px">
                            </td>
                        <td style="width: 830px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label4" runat="server" Text="Note Area" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 26px">
                            <asp:TextBox ID="txt_NoreArea_1" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 30px; height: 26px">
                            <asp:DropDownList ID="Note_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_NoreArea_2" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 26px; height: 26px">
                            <asp:DropDownList ID="Note_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_NoreArea_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="Note_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_NoreArea_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="Note_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_NoreArea_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 267px; height: 26px">
                            </td>
                        <td style="width: 830px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label3" runat="server" Text="Key Words" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 26px">
                            </td>
                        <td style="width: 30px; height: 26px">
                            <asp:DropDownList ID="KW_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>Or</asp:ListItem>
                                <asp:ListItem>And</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            </td>
                        <td style="width: 26px; height: 26px">
                            <asp:DropDownList ID="KW_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_keyword_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="KW_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_keyword_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="KW_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_keyword_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 267px; height: 26px">
                            </td>
                        <td style="width: 830px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label5" runat="server" Text="Key Types" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 26px">
                            </td>
                        <td style="width: 30px; height: 26px">
                            <asp:DropDownList ID="KT_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_KeyType_2" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 26px; height: 26px">
                            <asp:DropDownList ID="KT_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_KeyType_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="KT_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_KeyType_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="KT_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_KeyType_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 267px; height: 26px">
                        </td>
                        <td style="font-weight: bold; width: 830px; height: 26px; text-decoration: underline">
                            <asp:Label ID="Label6" runat="server" Text="Abstract" Visible="False"></asp:Label></td>
                        <td style="width: 58px; height: 26px">
                            <asp:TextBox ID="txt_Abstract1" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 30px; height: 26px"><asp:DropDownList ID="Abstract_Option_1" runat="server" CssClass="mytext" Visible="False">
                            <asp:ListItem>And</asp:ListItem>
                            <asp:ListItem>Or</asp:ListItem>
                        </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_Abstract2" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 26px; height: 26px"><asp:DropDownList ID="Abstract_Option_2" runat="server" CssClass="mytext" Visible="False">
                            <asp:ListItem>And</asp:ListItem>
                            <asp:ListItem>Or</asp:ListItem>
                        </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_Abstract3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px"><asp:DropDownList ID="Abstract_Option_3" runat="server" CssClass="mytext" Visible="False">
                            <asp:ListItem>And</asp:ListItem>
                            <asp:ListItem>Or</asp:ListItem>
                        </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_Abstract4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px"><asp:DropDownList ID="Abstract_Option_4" runat="server" CssClass="mytext" Visible="False">
                            <asp:ListItem>And</asp:ListItem>
                            <asp:ListItem>Or</asp:ListItem>
                        </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_Abstract5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="width: 100%; height: 29px">
                            <asp:DropDownList ID="ddlDay" runat="server" CssClass="myddl" Width="40px" Visible="False">
                            </asp:DropDownList><asp:DropDownList ID="ddlMonth" runat="server" CssClass="myddl" Width="48px" Visible="False">
                            </asp:DropDownList><asp:DropDownList ID="ddlYear" runat="server" CssClass="myddl" Visible="False">
                            </asp:DropDownList><asp:CheckBox ID="ChkFromDate" runat="server" Text="Ignore" Visible="False" /><asp:DropDownList ID="ddlToDay" runat="server" CssClass="myddl" Width="40px" Visible="False">
                            </asp:DropDownList><asp:DropDownList ID="ddlToMonth" runat="server" CssClass="myddl" Width="48px" Visible="False">
                            </asp:DropDownList><asp:DropDownList ID="ddlToYear" runat="server" CssClass="myddl" Visible="False">
                            </asp:DropDownList><asp:CheckBox ID="ChkToDate" runat="server" Text="Ignore" Visible="False" /></td>
        </tr>
    </table>
</asp:Content>

