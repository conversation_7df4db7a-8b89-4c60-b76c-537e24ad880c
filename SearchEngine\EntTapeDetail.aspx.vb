Imports System.Data
Imports Microsoft.VisualBasic
'Imports System.Net.Mail
'Imports System.Web.Mail



Partial Class EntTapeDetail
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim DS1 As DataSet

    
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try

            txtUrduScript.Attributes.Add("onkeypress", "search()")
            txtUrduScript.Attributes.Add("Dir", "Rtl")

            Dim TapeNumber As String
            TapeNumber = Request.QueryString("TapeNumber")

            Dim Program As String
            Program = Request.QueryString("ProgramChildName")

            'Dim EpisodeNo As Integer
            'EpisodeNo = Convert.ToInt32(Request.QueryString("EpisodeNo"))
            Dim EpisodeNo As String
            EpisodeNo = Request.QueryString("EpisodeNo")

            Dim PartNo As String
            PartNo = Request.QueryString("PartNo")

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            'cmd.CommandText = "GetEntSearchEngine_Keywords"
            cmd.CommandText = "GetEntSearchEngine_Keywords2"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo.Value = TapeNumber

            Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Program", Data.SqlDbType.Text)
            Prg.Value = Program

            Dim Epi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EpisodeNo", Data.SqlDbType.Text)
            Epi.Value = EpisodeNo

            Dim PNi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@PartNo", Data.SqlDbType.Text)
            PNi.Value = PartNo

            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS, "Table0")

            If DS.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS.Tables("Table0").Rows.Count - 1 Then
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + ",</b><br>"
                    Else
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + "</b><br>"
                    End If
                Next
            End If


            ''***************************************''
            Dim cmd1 As New System.Data.SqlClient.SqlCommand
            cmd1.CommandType = Data.CommandType.StoredProcedure
            '  cmd1.CommandText = "GetEmployeeName_by_TapeNumber_SearchEngine_Ent"
            cmd1.CommandText = "GetEmployeeName_by_TapeNumber_SearchEngine_Ent_Test"
            cmd1.Connection = Con
            cmd1.CommandTimeout = 0

            Dim TapeNo1 As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo1.Value = TapeNumber

            Dim da1 As New System.Data.SqlClient.SqlDataAdapter
            DS1 = New DataSet

            da1.SelectCommand = cmd1
            da1.Fill(DS1)

            lblIsAvailable.Text = DS1.Tables(0).Rows(0)(0).ToString
            If DS1.Tables.Count > 1 Then
                If DS1.Tables(1).Rows.Count = 1 Then
                    lblIsAvailable2.Text = DS1.Tables(1).Rows(0)(0).ToString
                End If
            End If

            If DS1.Tables.Count > 2 Then
                If DS1.Tables(2).Rows.Count = 1 Then
                    lblIsAvailable3.Text = DS1.Tables(2).Rows(0)(0).ToString
                End If
            End If

            If DS1.Tables.Count > 3 Then
                If DS1.Tables(3).Rows.Count = 1 Then
                    lblIsAvailable4.Text = DS1.Tables(3).Rows(0)(0).ToString
                End If
            End If

            If DS1.Tables.Count > 4 Then
                If DS1.Tables(4).Rows.Count = 1 Then
                    lblIsAvailable5.Text = DS1.Tables(4).Rows(0)(0).ToString
                End If
            End If

            GetIsDamage()

            GetIssuedEmployeeID()

            GetUrduScript()

            GetAddedBy()

            GetTapeContentID(TapeNumber)

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Sub GetAddedBy()
        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS5 As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetEntertainment_AddedBy"
        cmd.Connection = Con

        Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
        TapeNo.Value = Request.QueryString("TapeNumber")

        Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Program", Data.SqlDbType.Text)
        Prg.Value = Request.QueryString("ProgramChildName")

        Dim Epi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EpisodeNo", Data.SqlDbType.Int)
        Epi.Value = CInt(Request.QueryString("EpisodeNo"))

        Dim PNi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@PartNo", Data.SqlDbType.Text)
        PNi.Value = CStr(Request.QueryString("PartNo"))


        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS5 = New DataSet
        da.SelectCommand = cmd
        da.Fill(DS5, "Master")
        If DS5.Tables("Master").Rows.Count > 0 Then
            lblAddedBy.Text = DS5.Tables("Master").Rows(0)(0).ToString
            lblModifiedBy.Text = DS5.Tables("Master").Rows(0)(1).ToString
        Else
            lblAddedBy.Text = "N/A"
            lblModifiedBy.Text = "N/A"
        End If

    End Sub

    Sub GetTapeContentID(ByVal TapeNumber As String)
        Dim ObjSearch As New BusinessFacade.SearchEngine()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        lblMasterID.Text = ObjSearch.GetTapeContentID(TapeNumber, CokieBaseStationID, "Ent")
        If lblMasterID.Text = "-2" Then
            bttnEditMaster.Enabled = False
        Else
            bttnEditMaster.Enabled = True
        End If
    End Sub

    Private Sub GetIssuedEmployeeID()
        Dim ds As New DataSet
        Dim ObjUser As New BusinessFacade.SearchEngine()
        ObjUser.TapeNumber = Request.QueryString("TapeNumber")
        ds = ObjUser.GetEmployeeID_by_TapeNumber_SearchEngine_Ent()


        If ds.Tables.Count = 1 Then
            If ds.Tables(0).Rows(0)(0).ToString <> "0" Then
                bttnReminder1.Enabled = True
                Emp1.Text = ds.Tables(0).Rows(0)(0).ToString
            End If

        End If

        If ds.Tables.Count = 2 Then
            If ds.Tables(0).Rows(0)(0).ToString <> "0" Then
                bttnReminder1.Enabled = True
                Emp1.Text = ds.Tables(0).Rows(0)(0).ToString
            End If
            If ds.Tables(1).Rows(0)(0).ToString <> "0" Then
                bttnReminder2.Enabled = True
                Emp2.Text = ds.Tables(1).Rows(0)(0).ToString
            End If

        End If

    End Sub

    Private Sub GetIsDamage()
        ''****************************************''
        ''********* Get TapeLibraryID ************''
        ''****************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = Request.QueryString("TapeNumber")
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        Dim ObjUser As New BusinessFacade.SearchEngine()
        ObjUser.TapeLibraryID = LibraryID
        lblIsDamage.Text = ObjUser.GetLostDamage_by_TapeNumber_SearchEngine()

    End Sub

    Private Sub GetEmailContents(ByVal EmployeeID As Integer)

        ''****************************''
        '' ****** User Name **********''
        ''****************************''
        lbl_UserName.Text = Request.Cookies("userinfo")("username")
        Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
        If arr_UserID.Length > 1 Then
            lbl_UserName.Text = arr_UserID(1)
        End If
        ''****************************''

        Dim Dt As New DataTable
        Dim objSearch As New BusinessFacade.ReminderService()
        objSearch.EmployeeID = EmployeeID
        objSearch.DepartmentID = 0
        objSearch.Date = 0
        objSearch.isBlank = -1
        objSearch.TapeNumber = Request.QueryString("TapeNumber")
        Dt = objSearch.ReminderService_Getrecords_SearchEngine()

        ''***********************************************************''
        Dim ToEmail As String = objSearch.GetEmailAddress_ByEmployeeID(EmployeeID)
        Dim Arr As Array = Split(ToEmail, "@geo")

        Dim BCC As String
        'Dim strFrom As String = "<EMAIL>"

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        'strFrom = "<EMAIL>"
        Dim CC As String


        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            BCC = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            BCC = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            BCC = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            BCC = "<EMAIL>"
            'BCC = "<EMAIL>"
        End If


        CC = "<EMAIL>"
        'CC = "<EMAIL>"

        Dim subject As String = "Reminder: Tape OverDue"
        Dim Q As String
        Dim i As Integer
        Q = ""
        Dim TapeDetail As String = ""

        Dim J As String = "0"


        For i = 0 To Dt.Rows.Count - 1


            Q &= "Dear " & Dt.Rows(i).Item(1).ToString().ToUpper() & "," & " ^^The following tapes are outstanding against you. ^^"

            'Q &= "bbbccc S.No dddeee" & "~~" & "bbbccc Tape Type dddeee" & "~~" & "bbbccc Tape No. dddeee" & "~~" & "bbbccc Issued Date dddeee" & "~~" & "bbbccc Due Date dddeee" & "~~" & "bbbccc Remarks dddeee^^"

            Dim IssueDate As String
            IssueDate = CDate(Dt.Rows(i).Item(5).ToString()).ToString("dd-MMM-yyyy")
            Dim ReturnDate As String
            ReturnDate = CDate(Dt.Rows(i).Item(6).ToString()).ToString("dd-MMM-yyyy") 'Dt.Rows(i).Item(6).ToString()

            'Q &= "(" & (i + 1) & ")~~" & Dt.Rows(i).Item(8).ToString() & "~~" & Dt.Rows(i).Item(0).ToString() & "~~" & IssueDate & "~~" & ReturnDate & "~~Archival" + "^^"
            Dim Cnt As String = CStr(CInt(Dt.Rows(i)("TotalReminders").ToString) + 1)
            'Q &= "bbbccc" & Dt.Rows(i).Item(0).ToString() & "dddeee^!!!!!!!!!!Tape Type : " & Dt.Rows(i).Item(8).ToString() & "^!!!!!!!!!!Issue Date : " & IssueDate & "^!!!!!!!!!!Due Date:!!" & ReturnDate & "^!!!!!!!!!!Remarks :!! Archival" & "^!!!!!!!!!!Total Reminders : " & Cnt + "^^"
            Q &= "bbbccc" & Dt.Rows(i).Item(0).ToString() & "dddeee^!!!!!!!!!!Tape Type : " & Dt.Rows(i).Item(8).ToString() & "^!!!!!!!!!!Issue Date : " & IssueDate & "^!!!!!!!!!!Due Date:!!" & ReturnDate & "^!!!!!!!!!!Remarks :!! Archival" & "^!!!!!!!!!!Total Reminders : " & Cnt & "^!!!!!!!!!!Program / Slug : " & Dt.Rows(i).Item("ProgramSlug").ToString() & "^^"

            'TapeDetail &= (i + 1) & "," & Dt.Rows(i).Item(0).ToString() & "," & Dt.Rows(i).Item(8).ToString() & "," & IssueDate & "," & ReturnDate & ",Archival" & "," & Cnt & "$"
            TapeDetail &= (i + 1) & "-+" & Dt.Rows(i).Item(0).ToString() & "-+" & Dt.Rows(i).Item(8).ToString() & "-+" & IssueDate & "-+" & ReturnDate & "-+Archival" & "-+" & Cnt & "-+" & Dt.Rows(i).Item("ProgramSlug").ToString() & "$$"


            lblEmployeeName.Text = Dt.Rows(0).Item(1).ToString().ToUpper()

            ''************************************************************************************************************************************''
        Next

        Q &= "^Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us."
        Q &= "^^Regards,^^"
        'Q &= Request.Cookies("userinfo")("username").ToUpper()
        Q &= Request.Cookies("userinfo")("userfullname").ToUpper
        'Q &= "^Circulation Desk"
        'Q &= "^Ext:6132 (News Archive) , 6568 (Central Archive)"

        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            Q &= "^Lahore Archive"
            Q &= "^Ext: 334 - 374"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            Q &= "^Islamabad Archive"
            Q &= "^Ext: 218"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            Q &= "^Peshawar Archive"
            Q &= "^Ext: N/A"
        Else
            ''****** For Karachi *******''
            Q &= "^Circulation Desk"
            Q &= "^Ext:6132 (News Archive) , 6568 (Central Archive)"
        End If


        Dim objEmailSrvc As New BusinessFacade.ReminderService()
        Dim EmailId As Integer
        EmailId = objEmailSrvc.SaveEmailData(strFrom, ToEmail.ToString, CC, subject, Q, TapeDetail)

        Dim G As String
        G = ""
        If G = "" Or G = "&nbsp;" Then
            If Arr.Length = 2 Then
                G = ToEmail.ToString 'ToEmail.ToString '"<EMAIL>" 
            Else
                G = "" '"" '"<EMAIL>"
            End If
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            'script = script + "var mywindow = window.open('../ReminderService/Test.aspx?EmailID=" & EmailId & "&From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&EmployeeName=" + lblEmployeeName.Text + "&UserName=" + Request.Cookies("userinfo")("userfullname") + "&TapeDetail=" + TapeDetail + "', 'mywindow');"
            script = script + "var mywindow = window.open('../ReminderService/LocalReminders_Search.aspx?EmailID=" & EmailId & "&From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&EmployeeName=" + lblEmployeeName.Text + "&UserName=" + Request.Cookies("userinfo")("userfullname") + "&TapeDetail=" + TapeDetail + "', 'mywindow');"
            script = script + "}</script>"
            Page.RegisterClientScriptBlock("test", script)

        End If

        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

        'Dim Dt As New DataTable
        'Dim objSearch As New BusinessFacade.ReminderService()
        'objSearch.EmployeeID = EmployeeID
        'objSearch.DepartmentID = 0
        'objSearch.Date = 0
        'objSearch.isBlank = -1
        'objSearch.TapeNumber = Request.QueryString("TapeNumber")
        'Dt = objSearch.ReminderService_Getrecords_SearchEngine()

        ' ''***********************************************************''
        'Dim ToEmail As String = objSearch.GetEmailAddress_ByEmployeeID(EmployeeID)
        'Dim Arr As Array = Split(ToEmail, "@geo")

        'Dim BCC As String
        'Dim strFrom As String = "<EMAIL>"
        'Dim CC As String
        'BCC = "<EMAIL>"
        'CC = "<EMAIL>"
        'Dim subject As String = "Reminder: Tape OverDue"
        'Dim Q As String
        'Dim i As Integer
        'Q = ""

        ''Dt.Rows(0).Item(1).ToString()

        'Dim J As String = "0"

        'For i = 0 To Dt.Rows.Count - 1

        '    Q &= "Dear " & Dt.Rows(i).Item(1).ToString() & "," & " ^^This is to inform you that following Tape is Overdue against you. ^^"

        '    Dim IssueDate As String
        '    IssueDate = CDate(Dt.Rows(i).Item(5).ToString()).ToString("dd-MMM-yyyy")
        '    Dim ReturnDate As String
        '    ReturnDate = CDate(Dt.Rows(i).Item(6).ToString()).ToString("dd-MMM-yyyy") 'Dt.Rows(i).Item(6).ToString()

        '    Q &= "Tape Number: " & Dt.Rows(i).Item(0).ToString() & "(" & Dt.Rows(i).Item(3).ToString() & ")" & " , Issued On: " & IssueDate & " , Due Date: " & ReturnDate + "^^"


        'Next
        'Q &= "^Email Send by:" & Request.Cookies("userinfo")("username").ToUpper()
        'Q &= "^For Assistance contact Archive Department ^( Ext:6132 (News Archive) , 6568 (Central Archive)"
        'Q &= "^^Regards,"
        'Q &= "^^Archives Department"

        'Dim G As String
        'G = ""
        'If G = "" Or G = "&nbsp;" Then
        '    If Arr.Length = 2 Then
        '        G = ToEmail.ToString
        '    Else
        '        G = ""
        '    End If
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('../ReminderService/Test.aspx?From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&Body=" + Q + "', 'mywindow'); "
        '    script = script + "}</script>"
        '    Page.RegisterClientScriptBlock("test", script)

        'End If
    End Sub

    Protected Sub bttnReminder1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReminder1.Click
        GetEmailContents(Emp1.Text)
    End Sub

    Protected Sub bttnReminder2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReminder2.Click
        GetEmailContents(Emp2.Text)
    End Sub

    Private Sub GetUrduScript()

        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS5 As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetUrduScript_Entertainment"
        cmd.Connection = Con

        Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
        TapeNo.Value = Request.QueryString("TapeNumber")

        Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Program", Data.SqlDbType.Text)
        Prg.Value = Request.QueryString("ProgramChildName")

        Dim Epi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EpisodeNo", Data.SqlDbType.Int)
        Epi.Value = CInt(Request.QueryString("EpisodeNo"))

        Dim PNi As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@PartNo", Data.SqlDbType.Text)
        PNi.Value = CStr(Request.QueryString("PartNo"))


        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS5 = New DataSet
        da.SelectCommand = cmd
        da.Fill(DS5, "Master")
        If DS5.Tables("Master").Rows.Count > 0 Then
            lblUrduScript.Text = DS5.Tables("Master").Rows(0)(0).ToString
            txtUrduScript.Text = DS5.Tables("Master").Rows(0)(0)

        End If


    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        'Dim Message As New MailMessage() '= New MailMessage(_Sender, _RecipientAddress, _Subject, _MessageBody)
        'Dim fromAddress As New MailAddress("<EMAIL>")
        'Dim smtpClient As SmtpClient = New SmtpClient("**********", 25)

        'Message.From = fromAddress
        'Message.Subject = "ajsdasdjkahsd"
        'Message.To.Add("<EMAIL>")
        'Message.CC.Add("<EMAIL>")

        'Message.IsBodyHtml = True
        'Message.Body = "this is body"

        'smtpClient.Send(Message)

        'Dim MessageMail As New MailMessage
        'MessageMail.BodyFormat = MailFormat.Html
        'Dim strSubject As String
        'SmtpMail.SmtpServer = "mail.geo.tv"
        'MessageMail.From = "<EMAIL>"
        'MessageMail.To = "<EMAIL>"
        'MessageMail.CC = "<EMAIL>"
        'MessageMail.Subject = "The Logsheet of"
        ''MessageMail.Body = "Dear Sir <BR><BR> The Logsheet of " + Replace(strFromEmail, "@geo.tv", "") + " has been created on " + SchDate + " working on " + MachineName + " at " + SchTime + " <BR> Please Check And finalize."
        'MessageMail.Body = "asdagfjhasdfg"
        'SmtpMail.Send(MessageMail)
    End Sub

    Protected Sub bttnEditMaster_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEditMaster.Click
        Response.Redirect("../TapeContent/FrmArchiveEntry_Ent.aspx?TapeContentID=" + lblMasterID.Text)
    End Sub
End Class
