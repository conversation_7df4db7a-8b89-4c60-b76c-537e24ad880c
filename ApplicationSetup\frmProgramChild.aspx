<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmProgramChild.aspx.vb" Inherits="ApplicationSetup_frmProgramChild" title="Home > Program Child > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDataInput.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebDataInput" TagPrefix="igtxt" %>

<%@ Register Assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebSchedule" TagPrefix="igsch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
        <table width="100%">
        <tr>
            <td class="labelheading" style="width: 1026px; height: 21px; text-decoration: underline">
                &nbsp;<asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading" OnClick="LnkHomePage_Click">Home</asp:LinkButton>
                &gt;
                <asp:LinkButton ID="LinkButton1" runat="server" CssClass="labelheading" Width="112px">Program Child</asp:LinkButton>
                &gt; Add New</td>
        </tr>
        <tr>
            <td>
                <table>
                    <tr class="mytext">
                        <td style="height: 40px">
                            Program Child Name</td>
                        <td style="width: 209px; height: 40px">
                            <asp:TextBox ID="txt_ProgramChildName" runat="server" CssClass="mytext" Height="32px"
                                TextMode="MultiLine" Width="192px"></asp:TextBox></td>
                        <td style="width: 54px; height: 40px">
                            SubTitle</td>
                        <td style="height: 40px">
                            <asp:TextBox ID="txt_SubTitle" runat="server" CssClass="mytext" Height="32px" TextMode="MultiLine"
                                Width="140px"></asp:TextBox></td>
                        <td style="height: 40px">
                            Duration</td>
                        <td style="width: 126px; height: 40px">
                            <igtxt:WebMaskEdit ID="txt_Duration" runat="server" CssClass="mytext" HorizontalAlign="Left"
                                InputMask="##:##:##" Width="120px">
                            </igtxt:WebMaskEdit>
                            </td>
                        <td style="height: 40px">
                            </td>
                        <td style="height: 40px">
                            </td>
                    </tr>
                    <tr class="mytext">
                        <td style="height: 36px">
                            Program Master Name</td>
                        <td style="width: 209px; height: 36px">
                            <asp:DropDownList ID="ddl_ProgramMaster" runat="server" CssClass="mytext" Width="152px" Enabled="False">
                            </asp:DropDownList>
                            <asp:CheckBox ID="chkProgramMaster" runat="server" AutoPostBack="True" Checked="True"
                                Text="Ignore" /></td>
                        <td style="width: 54px; height: 36px;">
                            Alias</td>
                        <td style="height: 36px">
                            <asp:TextBox ID="txt_Alias" runat="server" CssClass="mytext" TextMode="MultiLine"
                                Width="140px"></asp:TextBox></td>
                        <td style="height: 36px">
                            Is Program</td>
                        <td style="width: 126px; height: 36px">
                            &nbsp;<asp:CheckBox ID="chkIsProgram" runat="server" Checked="True" /></td>
                        <td style="height: 36px">
                            </td>
                        <td style="height: 36px">
                            </td>
                    </tr>
                    <tr class="mytext">
                        <td style="height: 36px">
                            Genre</td>
                        <td style="width: 209px; height: 36px">
                            <asp:DropDownList ID="ddlGenre" runat="server" CssClass="mytext" Width="200px">
                            </asp:DropDownList></td>
                        <td style="width: 54px; height: 36px">
                            Genre Flavour</td>
                        <td style="height: 36px">
                            <asp:DropDownList ID="ddlGenreFlavor" runat="server" CssClass="mytext" Width="200px">
                            </asp:DropDownList></td>
                        <td style="height: 36px">
                        </td>
                        <td style="width: 126px; height: 36px">
                        </td>
                        <td style="height: 36px">
                        </td>
                        <td style="height: 36px">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="464px"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 1026px">
                &nbsp;<asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Text="Save" Width="64px" />&nbsp;<asp:Button
                    ID="BttnDelete" runat="server" CssClass="buttonA" Text="Delete" Width="64px" />
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Text="Clear" Width="64px" /></td>
        </tr>
            <tr>
                <td align="right" style="width: 100%" valign="top">
                    <asp:Label ID="lblTotalRecords" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
            </tr>
        <tr>
            <td style="width: 100%;" valign="top">
                <asp:GridView ID="dg_ProgramChild" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                    AutoGenerateSelectButton="True" CssClass="gridContent" PageSize="25" Width="100%">
                    <Columns>
                        <asp:BoundField DataField="ProgramChildID" Visible="False">
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="ProgramChildName" HeaderText="P.Child Name">
                            <HeaderStyle Width="150px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="ProgramMasterID" Visible="False">
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="ProgramMasterName" HeaderText="Prog.Master Name">
                            <HeaderStyle Width="175px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="Duration" HeaderText="Duration" />
                        <asp:BoundField DataField="SubTitle" HeaderText="Sub Title">
                            <HeaderStyle Width="120px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="Alias" HeaderText="Alias" />
                        <asp:BoundField DataField="Genre" HeaderText="Genre" />
                        <asp:BoundField DataField="GenreFalvour" HeaderText="Genre Falvour">
                            <HeaderStyle Width="150px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="IsProgram" HeaderText="IsProgram" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
            <tr>
                <td style="width: 100%" valign="top">
                    <asp:Label ID="lblAuditHistory" runat="server" CssClass="labelheading" Visible="False">Audit History - Program Child</asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100%" valign="top">
                    <asp:GridView ID="dgAuditHistory" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                        PageSize="25" Width="100%">
                        <Columns>
                            <asp:BoundField DataField="AddedBy" HeaderText="Added By" />
                            <asp:BoundField DataField="AddedDate" HeaderText="Added Date" />
                            <asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy" />
                            <asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate" />
                        </Columns>
                        <SelectedRowStyle BackColor="#FFE0C0" />
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                </td>
            </tr>
            <tr>
                <td style="width: 1026px; height: 21px">
                    <table class="mytext">
                        <tr>
                            <td class="labelheading" style="width: 364px; height: 26px">
                                Search Program Child</td>
                            <td style="width: 100px; height: 26px">
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 364px; height: 13px">
                                Program Child Name</td>
                            <td style="width: 100px; height: 13px">
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 364px; height: 22px">
                                <asp:TextBox ID="txt_SearchProgramChild" runat="server" CssClass="mytext" Width="344px" Height="40px" TextMode="MultiLine"></asp:TextBox></td>
                            <td style="width: 100px; height: 22px">
                                <asp:LinkButton ID="lnkSearch" runat="server" OnClick="lnkSearch_Click">Search</asp:LinkButton></td>
                        </tr>
                    </table>
                </td>
            </tr>
        <tr>
            <td style="width: 1026px; height: 21px">
                <asp:TextBox ID="txt_ProgramMasterID" runat="server" CssClass="mytext" Visible="False"
                    Width="48px"></asp:TextBox>
                <asp:TextBox ID="txt_ProgramChildID" runat="server" CssClass="mytext" Visible="False"
                    Width="48px"></asp:TextBox>
                            <asp:TextBox ID="txt_ProgramMasterName" runat="server" CssClass="mytext" TextMode="Password"
                                Width="140px" Visible="False"></asp:TextBox>
                <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
        </tr>
    </table>
<%--    <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Delete !"
        TargetControlID="BttnDelete">
    </cc1:ConfirmButtonExtender>--%>
</asp:Content>

