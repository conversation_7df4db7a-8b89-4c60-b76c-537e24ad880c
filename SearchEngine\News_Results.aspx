<%@ Page Language="VB" AutoEventWireup="false" CodeFile="News_Results.aspx.vb" Inherits="SearchEngine_News_Results" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
 <script src="searchhi.js" type="text/javascript" language="JavaScript"></script>
    <script type="text/javascript" language="javascript">
    <!--
    
    function loadTheDocument()
    {
        //document.searchhi.txtSearchText.value = searchhi_string; 
        if( location.hash.length > 1 ) location.hash = location.hash;    
        
     }
    function CallHighlighting()
    {
        bttnHighlight = document.getElementById("bttnHighlight")
        if (bttnHighlight.value=="Highlight")
        {
            localSearchHighlight('?h=' + document.getElementById('ENDATE').value ); 
            localSearchHighlight('?h=' + document.getElementById('RS1').value );   
            localSearchHighlight('?h=' + document.getElementById('RS2').value );   
            localSearchHighlight('?h=' + document.getElementById('RS3').value );   
            localSearchHighlight('?h=' + document.getElementById('RS4').value );   
            localSearchHighlight('?h=' + document.getElementById('RS5').value );   
            localSearchHighlight('?h=' + document.getElementById('Tape').value );
            localSearchHighlight('?h=' + document.getElementById('Rpt').value );   
            localSearchHighlight('?h=' + document.getElementById('T_type').value );   
            bttnHighlight.value="Normal";
        }
        else
        {
            bttnHighlight.value="Highlight";
            unhighlight(document.getElementsByTagName('body')[0]);
        }
    }
    
    //_______________________________________________\\
    
    function MouseEvents(objRef, evt) 
    { 
        var checkbox = objRef.getElementsByTagName("input")[0]; 
        if (evt.type == "mouseover") 
            { 
                objRef.style.backgroundColor = "#C5D5FC"; 
            } 
        else 
            { 
                objRef.style.backgroundColor = "white"; 
            } 
    } 
    
    //_______________________________________________\\
    
    // -->
  </script>
<head runat="server">
    <title>Home &gt; Search Engine &gt; News</title>
    <link href="main.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
    <!-- 
        SPAN.searchword { background-color:yellow; }
    // -->
    </style>
    
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <table width="100%">
            <tr>
                <td class="labelheading" style="width: 100%; text-decoration: underline">
                    Seach Engine &gt; News &gt; Results</td>
            </tr>
            <tr>
                <td style="width: 100%; height: 52px" class="mytext">
                    <table style="width: 100%">
                        <tr>
                            <td style="width: 50%; height: 21px">
                    <asp:Label ID="lbl_RecordCount" runat="server" Font-Bold="True" ForeColor="Red" Width="272px" CssClass="mytext" Font-Size="10pt"></asp:Label>
                                <asp:Label ID="lblTotalPages" runat="server" CssClass="mytext" Font-Bold="True" ForeColor="Red"
                                    Width="192px" Font-Size="10pt"></asp:Label></td>
                            <td align="right" style="width: 50%; height: 21px">
                                &nbsp;&nbsp;
                    <input id="bttnHighlight" onclick="CallHighlighting();" style="width: 80px; font-weight: bold;" type="button"
                        value="Highlight" />
                                &nbsp;<asp:Button ID="bttnExport" runat="server" Text="Export to Excel" Font-Bold="True" />
                                &nbsp;<asp:Button ID="btnSaveTape" runat="Server" Text="Add Tapes" Font-Bold="True" /> 
                                &nbsp;<asp:Button ID="btnViewTape" runat="Server" Text="View Tapes" Font-Bold="True" /> </td>
                        </tr>
                    </table>
                    </td>
            </tr>
            <tr>
                <td >
                    <asp:Label ID="lblErr" runat="Server" Font-Bold="True" ForeColor="Red"
                                    Width="100%" Font-Names="Arial" Font-Size="10pt"></asp:Label>
                </td>
            </tr>
            <tr>
                <td style="width: 100%; height: 32px; background-attachment: fixed;" align="center" valign="middle" class="bottomMain"><asp:ImageButton ID="bttn_Prev" runat="server" ImageUrl="~/Images/23a.gif" />
                    &nbsp; &nbsp; &nbsp;<asp:ImageButton ID="bttn_Next" runat="server" ImageUrl="~/Images/24a.gif" /></td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                    &nbsp;<asp:Label ID="lblPageNo2" runat="server" Font-Bold="True" ForeColor="Green" Width="200px" CssClass="lbl"></asp:Label>
                    &nbsp;&nbsp;
                </td>
            </tr>
            <tr>
                <td style="width: 100%">
                    <asp:GridView ID="dg_Search" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                        AutoGenerateSelectButton="True" PageSize="2000" Width="100%" CssClass="gridContent" AllowSorting="True">
                        <Columns>
                            <asp:TemplateField HeaderText="S.No">   
                                <ItemTemplate>
                                    <%#Container.DataItemIndex + 1 + lblPages.Text * lblPageIndex.Text%>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="EntryDate" HeaderText="EntryDate" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False" SortExpression="Entrydate" >
                                <HeaderStyle Width="90px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="ReportSlug" HeaderText="Report Slug" >
                                <HeaderStyle Width="450px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" >
                                <HeaderStyle Width="85px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" >
                                <HeaderStyle Width="90px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="StartTime" HeaderText="Start Time" SortExpression="StartTime" >
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle Width="80px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="EndTime" HeaderText="End Time" >
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle Width="80px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="isAvailable" HeaderText="Available" />
                            <asp:BoundField DataField="Reporter" HeaderText="Reporter" />
                            <asp:BoundField DataField="TapeSlugID" HeaderText="TapeSlugID" />
                            <asp:BoundField DataField="isDamage" HeaderText="isDamage" Visible="False" />
                            <asp:TemplateField HeaderText="Play">
                                <ItemTemplate>
                                    <asp:LinkButton ID="LinkButton1" runat="server" Enabled='<%# Eval("IsVideoExists") %>'
                                    CommandName="Play" CommandArgument='<%# Eval("TapeSlugID") %>' >Play</asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="BaseStation" HeaderText="Station" />
                            <asp:BoundField DataField="SpecialNote" HeaderText="SpecialNote" />
                            <asp:TemplateField HeaderText="Add to">
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckBox1" runat="server"  />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="BaseStationID"   />
                            <asp:BoundField DataField="Available" />
                                                   </Columns>
                        <HeaderStyle BackColor="LightGray" CssClass="gridheader" />
                        <AlternatingRowStyle BackColor="Lavender" />
                    </asp:GridView>
                </td>
            </tr>
            <tr>
                <td style="width: 100%; height: 29px" align="center" class="bottomMain">
                    &nbsp;<asp:ImageButton ID="ImageButton2" runat="server" ImageUrl="~/Images/23a.gif" />
                    &nbsp; &nbsp;
                    <asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/24a.gif" />
                </td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                    <table style="width: 100%">
                        <tr>
                            <td align="left" class="mytext" style="width: 50%">
                                Pages :-
                                <asp:DropDownList ID="ddlPageNo" runat="server" CssClass="lbl"
                        Width="72px" AutoPostBack="True">
                    </asp:DropDownList></td>
                            <td style="width: 50%" align="right">
                    <asp:Label ID="lbl_CurrentPage_No" runat="server" Font-Bold="True" ForeColor="Green" Width="200px" CssClass="lbl"></asp:Label></td>
                        </tr>
                    </table>
                    <asp:Label ID="lblTotal" runat="server" Visible="False"></asp:Label><asp:Button ID="Button2" runat="server" Text="<<" Font-Bold="True" Width="40px" Visible="False" /><asp:Button ID="Button1" runat="server" Text=">>" Font-Bold="True" Width="40px" Visible="False" /><asp:TextBox ID="ENDATE" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="RS1" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="RS2" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="RS3" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="RS4" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="RS5" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="Tape" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="Rpt" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="T_type" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                        ForeColor="#F2F5FE" Width="32px"></asp:TextBox><asp:Label ID="lblPages" runat="server" Visible="False"></asp:Label><asp:Label ID="lblPageIndex" runat="server" Text="0" Visible="False"></asp:Label></td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                <asp:GridView ID="GridView1" runat="server" Visible="False" AutoGenerateColumns="False" PageSize="200" Width="100%" CssClass="gridContent">
                    <Columns>
                        <asp:TemplateField HeaderText="S.No">
                            <ItemTemplate>
                                <%# Container.DataItemIndex + 1 %>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="EntryDate" HeaderText="Entry Date" >
                            <HeaderStyle Width="90px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="ReportSlug" HeaderText="Reporter Slug" >
                            <HeaderStyle Width="450px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" >
                            <HeaderStyle Width="85px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="TapeType" HeaderText="Tape Type" >
                            <HeaderStyle Width="90px" />
                        </asp:BoundField>                        
                        <asp:BoundField DataField="StartTime" HeaderText="Start Time" >
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle Width="80px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="EndTime" HeaderText="End Time" >
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle Width="80px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="Keyword" HeaderText="Keyword" />
                        <asp:BoundField DataField="Reporter" HeaderText="Reporter" />
                        <asp:BoundField DataField="Urdu_Script" HeaderText="Urdu Slug" />
                        <asp:BoundField DataField="EnglishScript" HeaderText="English Script" />
                        <asp:BoundField DataField="FootageTypes" HeaderText="Footage Types" />
                        <asp:BoundField DataField="isAvailable" HeaderText="Available" />
                    </Columns>
                    <HeaderStyle BackColor="LightGray" CssClass="gridheader" />
                </asp:GridView>
                    &nbsp;</td>
            </tr>
        </table>
    
    </div>
    </form>
</body>
</html>
