<%@ Page Language="VB" AutoEventWireup="false" CodeFile="EntertainmentTapeNumberDetails.aspx.vb" Inherits="SearchEngine_EntertainmentTapeNumberDetails" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <table style="font-weight: bold; background-color: lightblue;">
            <tr>
                <td style="width: 42%">
                    Tape Details</td>
                <td style="width: 100%">
                </td>
            </tr>
            <tr>
                <td style="width: 42%">
                </td>
                <td style="width: 100%">
                </td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Record ID:</td>
                <td style="width: 100%">
                    <asp:Label ID="lblRecordID" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Tape Number:</td>
                <td style="width: 100%">
                    <asp:Label ID="lblTapeNumber" runat="server" Font-Bold="True"></asp:Label></td>
              </tr>
            <tr>
                <td style="width: 42%; height: 21px;">
                    Keyword:
                </td>
                <td style="width: 100%; height: 21px">
                <asp:Label ID="lblKeyword" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Sub Closet :</td>
                <td style="width: 100%">
                    <asp:Label ID="lblSubCloset" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Is Available:
                </td>
                <td style="width: 100%">
                <asp:Label ID="lblisAvailable" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Entry Date:
                </td>
                <td style="width: 100%">
                <asp:Label ID="lblEntryDate" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Tape Type:
                </td>
                <td style="width: 100%">
                <asp:Label ID="lblTapeType" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Tape Status:
                </td>
                <td style="width: 100%">
                <asp:Label ID="lblTapeStatus" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%">
                    Strat Time :</td>
                <td style="width: 100%; height: 21px">
                    <asp:Label ID="lblStartTime" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%; height: 21px">
                    End Time :</td>
                <td style="width: 100%; height: 21px">
                    <asp:Label ID="lblEndTime" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
                <tr>
                <td style="width: 42%; height: 21px">
                    Program Title:
                </td>
                    <td style="width: 100%; height: 21px">
                <asp:Label ID="lblProgramChildName" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
                <tr>
                <td style="width: 42%; height: 21px">
                    Note Area :</td>
                    <td style="width: 100%; height: 21px">
                        <asp:Label ID="lblNoteArea" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
                <tr>
                <td style="width: 42%; height: 21px">
                    Abstract :
                </td>
                    <td style="width: 100%; height: 21px">
                <asp:Label ID="lblAbstract" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
                <tr>
                <td style="width: 42%; height: 21px">
                    Episode No:
                </td>
                    <td style="width: 100%; height: 21px">
                <asp:Label ID="lblEpisodeNo" runat="server" Font-Bold="True"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%; height: 21px">
                    Part No:
                </td>
                <td style="width: 100%; height: 21px">
                    <asp:Label ID="lblPartNo" runat="server" Font-Bold="True" Width="72px"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%; height: 21px">
                    Duration:</td>
                <td style="width: 100%; height: 21px">
                    <asp:Label ID="lblDuration" runat="server" Font-Bold="True" Width="72px"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 42%; height: 21px">
                </td>
                <td style="width: 100%; height: 21px">
                </td>
            </tr>
            <tr>
                <td colspan="2" style="height: 21px">
                 <asp:Button ID="bttnPrint" runat="server" OnClientClick="window.print()" CssClass="buttonA"
                            Font-Bold="True" Text="<< Print Page >>" Width="136px" />
                </td>
            </tr>
        </table>
    
    </div>
    </form>
</body>
</html>
