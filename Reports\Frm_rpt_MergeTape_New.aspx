<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_MergeTape_New.aspx.vb" Inherits="Frm_rpt_MergeTape_New" title="Archival Reports > How can I view Merge Tape Details ?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > How Can I view Tape Detail in which Tapes are Merged ?" Width="736px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE style="WIDTH: 608px"><TBODY><TR class="mytext"><TD style="WIDTH: 90px; HEIGHT: 1px" vAlign=middle></TD><TD style="WIDTH: 202px; HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 90px; HEIGHT: 22px" vAlign=middle>Content type</TD><TD style="WIDTH: 202px; HEIGHT: 22px" vAlign=middle>Entertainment Tape No&nbsp;&nbsp;&nbsp;<asp:CheckBox id="ChkTapeNoEnt" runat="server" Text="Ignore" __designer:wfdid="w7" Checked="True"></asp:CheckBox></TD><TD style="HEIGHT: 22px" vAlign=middle>News Tape No&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chkTapeNoNews" runat="server" Text="Ignore" __designer:wfdid="w8" Checked="True"></asp:CheckBox></TD><TD style="HEIGHT: 22px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" Height="8px" __designer:wfdid="w36"></asp:Label></TD></TR><TR class="mytext"><TD style="WIDTH: 90px; HEIGHT: 22px" vAlign=middle><asp:DropDownList id="ddlContentType" runat="server" CssClass="mytext" __designer:wfdid="w4"><asp:ListItem>Ent</asp:ListItem>
<asp:ListItem>News</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 202px; HEIGHT: 22px" vAlign=middle><asp:TextBox id="txtTapeNumber_Ent" runat="server" Width="160px" __designer:wfdid="w2"></asp:TextBox></TD><TD style="HEIGHT: 22px" vAlign=middle><asp:TextBox id="txtTapeNumber_News" runat="server" __designer:wfdid="w2"></asp:TextBox></TD><TD style="HEIGHT: 22px" vAlign=middle><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w37"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender2" runat="server" __designer:wfdid="w82" TargetControlID="ddlTapeNumber" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender><cc1:AutoCompleteExtender id="AutoCompleteExtender_TapeNo_2" runat="server" TargetControlID="txtTapeNumber_Ent" ServicePath="AutoComplete.asmx" ServiceMethod="GetTapes_Ent" MinimumPrefixLength="3" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1"></cc1:AutoCompleteExtender><cc1:AutoCompleteExtender id="AutoCompleteExtender1" runat="server" __designer:wfdid="w1" TargetControlID="txtTapeNumber_News" ServicePath="AutoComplete.asmx" ServiceMethod="GetTapes_News" MinimumPrefixLength="3" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1"></cc1:AutoCompleteExtender><BR />&nbsp;<asp:DropDownList id="ddlTapeNumber" runat="server" Width="175px" CssClass="mytext" Visible="False" __designer:wfdid="w77"></asp:DropDownList>&nbsp;<BR />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%; height: 29px;">
                                &nbsp;
                                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" Font-Bold="True" />
                                <asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" Visible="False" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form (Archival Reports > How Can I view Tape Detail in which Tapes are Merged ?) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form (Archival Reports > How Can I view Tape Detail in which Tapes are Merged ?) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

