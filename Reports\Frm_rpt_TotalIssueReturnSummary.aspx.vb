Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_TotalIssueReturnSummary
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkIgnoredate.Checked = False
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try

            Dim FromDate As String = ""
            Dim ToDate As String = ""

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")

            End If

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_logsheet.rpt&" + "@Date=" & strDate
            'Response.Redirect(qryString)


            'Dim jscript As String = "<script>"
            'jscript += "window.open('ReportViewer.aspx?ReportName=" + "rpt_logsheet.rpt&" + "@Date=" + strDate + "')"
            ''ToDo: Error processing original source shown below
            ''System.Char[]
            ''
            ''------------------------------^--- Illegal whitespace in constant
            ''
            ''ToDo: Error processing original source shown below
            ''System.Char[]
            ''
            ''-^--- Syntax error: ';' expected
            'jscript += "</script>" '
            ''ToDo: Error processing original source shown below
            ''
            ''
            ''-^--- Syntax error: ';' expected
            'Page.RegisterStartupScript("open", jscript)
            ''  Response.Write(jscript)

            '' bttnReport.Attributes.Add("OnClick", "window.open('ReportViewer.aspx?ReportName=rpt_logsheet.rpt&@Date=" & strDate & "');")


            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_logsheet.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_TotalIssueReturnSummary.aspx"
            ObjSave.ReportName = "Other Reports --> Q 6. How can I View Total Tapes Issued & Return Per Day?"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim FromDate As String = ""

        If chkIgnoredate.Checked = True Then
            FromDate = "-1"
        Else
            FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")

        End If

        'Dim BaseStationID As String
        'If Me.chkStation.Checked = True Then
        '    BaseStationID = "-1"
        'Else
        '    BaseStationID = ddlBaseStation.SelectedValue
        'End If

        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            ' script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_TotalTapesIssued.rpt&@Date=" + FromDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_TotalTapesIssued.rpt&@Date=" + FromDate + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            'script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_TotalTapesIssued.rpt&@Date=" + FromDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_TotalTapesIssued.rpt&@Date=" + FromDate + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_TotalIssueReturnSummary.aspx"
        ObjSave.ReportName = "Other Reports --> Q 6. How can I View Total Tapes Issued & Return Per Day?"
        ObjSave.SaveRecord()

        ''******************************************************''



    End Sub


End Class

