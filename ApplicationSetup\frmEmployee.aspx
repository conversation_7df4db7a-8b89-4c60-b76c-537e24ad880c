<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmEmployee.aspx.vb" Inherits="ApplicationSetup_frmEmployee" title="Home > Employee > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w5">Home</asp:LinkButton> &gt; Employee &gt; Add New</TD></TR><TR><TD style="HEIGHT: 133px"><TABLE style="WIDTH: 768px"><TBODY><TR class="mytext"><TD style="WIDTH: 820px; HEIGHT: 26px">Employee Category</TD><TD style="WIDTH: 168px; HEIGHT: 26px"><asp:DropDownList id="ddl_EmpCategory" runat="server" Width="160px" CssClass="mytext">
                            </asp:DropDownList></TD><TD style="WIDTH: 602px; HEIGHT: 26px">EmployeeName</TD><TD style="WIDTH: 172px; HEIGHT: 26px"><asp:TextBox id="txt_EmpName" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 241px; HEIGHT: 26px">Address</TD><TD style="WIDTH: 200px" rowSpan=2><asp:TextBox id="txt_Address" runat="server" Width="152px" CssClass="mytext" __designer:wfdid="w1" TextMode="MultiLine" Height="40px"></asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD></TR><TR class="mytext"><TD style="WIDTH: 820px; HEIGHT: 21px">Department Name</TD><TD style="WIDTH: 168px; HEIGHT: 21px"><asp:DropDownList id="ddl_Department" runat="server" Width="160px" CssClass="mytext">
                            </asp:DropDownList></TD><TD style="WIDTH: 602px; HEIGHT: 21px">Employee Geo Code</TD><TD style="WIDTH: 172px; HEIGHT: 21px"><asp:TextBox id="txt_EmpGeoCode" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 241px; HEIGHT: 21px"></TD></TR><TR class="mytext"><TD style="WIDTH: 820px">NICNumber</TD><TD style="WIDTH: 168px"><asp:TextBox id="txt_NIC" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 602px">Designation</TD><TD style="WIDTH: 172px"><asp:DropDownList id="ddl_Designation" runat="server" Width="160px" CssClass="mytext">
                        </asp:DropDownList></TD><TD style="WIDTH: 241px">Mobile</TD><TD style="WIDTH: 172px"><asp:TextBox id="txt_Mobile" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD></TR><TR class="mytext"><TD style="WIDTH: 820px">Phone</TD><TD style="WIDTH: 168px"><asp:TextBox id="txt_Phone" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 602px">Email</TD><TD style="WIDTH: 172px"><asp:TextBox id="txt_Email" runat="server" Width="152px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 241px">IsActive</TD><TD style="WIDTH: 172px"><asp:DropDownList id="ddl_IsActive" runat="server" Width="80px" CssClass="mytext"><asp:ListItem Value="1">Yes</asp:ListItem>
<asp:ListItem Value="0">No</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="336px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" Width="64px" CssClass="buttonA"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" Width="64px" CssClass="buttonA"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" Width="64px" CssClass="buttonA"></asp:Button> </TD></TR><TR><TD vAlign=top align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w4" Font-Bold="True"></asp:Label></TD></TR><TR><TD vAlign=top><asp:GridView id="dg_ContentType" runat="server" Width="100%" CssClass="gridContent" AutoGenerateColumns="False" AutoGenerateSelectButton="True" AllowPaging="True" PageSize="50"><Columns>
<asp:BoundField ApplyFormatInEditMode="True" DataField="EmployeeID" Visible="False">
<HeaderStyle Width="1px"></HeaderStyle>

<ItemStyle ForeColor="#F2F5FE"></ItemStyle>
</asp:BoundField>
<asp:BoundField ApplyFormatInEditMode="True" DataField="EmployeeCategoryID" Visible="False">
<HeaderStyle Width="1px"></HeaderStyle>

<ItemStyle ForeColor="#F2F5FE"></ItemStyle>
</asp:BoundField>
<asp:BoundField DataField="EmployeeGeoCode" HeaderText="Emp.Code"></asp:BoundField>
<asp:BoundField DataField="EmployeeName" HeaderText="Emp.Name">
<ItemStyle Width="210px"></ItemStyle>
</asp:BoundField>
<asp:BoundField DataField="DepartmentID" Visible="False">
<HeaderStyle Width="1px"></HeaderStyle>

<ItemStyle ForeColor="#F2F5FE"></ItemStyle>
</asp:BoundField>
<asp:BoundField DataField="NICNumber" HeaderText="NIC No."></asp:BoundField>
<asp:BoundField DataField="DesignationID" HeaderText="Desg." Visible="False"></asp:BoundField>
<asp:BoundField DataField="Address" HeaderText="Address"></asp:BoundField>
<asp:BoundField DataField="Mobile" HeaderText="Mobile"></asp:BoundField>
<asp:BoundField DataField="Phone" HeaderText="Phone"></asp:BoundField>
<asp:BoundField DataField="Email" HeaderText="Email"></asp:BoundField>
<asp:BoundField DataField="isActive" HeaderText="isActive"></asp:BoundField>
<asp:BoundField DataField="Designation" HeaderText="Designation"></asp:BoundField>
<asp:BoundField DataField="DepartmentName" HeaderText="Department"></asp:BoundField>
<asp:BoundField DataField="BaseStationName" HeaderText="Add by Station"></asp:BoundField>
<asp:BoundField DataField="AddedByUser" HeaderText="Add by User"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> <asp:TextBox id="txt_EmpID" runat="server" Width="48px" CssClass="mytext" Visible="False"></asp:TextBox><asp:TextBox id="txt_Designation" runat="server" Width="152px" CssClass="mytext" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w3" Visible="False"></asp:Label></TD></TR><TR><TD style="HEIGHT: 18px" class="labelheading" vAlign=top><asp:Label id="lblAuditHistory" runat="server" __designer:wfdid="w3" Visible="False">Audit History - Employee</asp:Label></TD></TR><TR><TD vAlign=top><asp:GridView id="dgAuditHistory" runat="server" Width="100%" CssClass="gridContent" __designer:wfdid="w1" AutoGenerateColumns="False" PageSize="25"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w14" ConfirmText="Do you want to Delete !" TargetControlID="BttnDelete"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
    <table class="mytext">
        <tr>
            <td class="labelheading" style="width: 140px; height: 26px">
                Search Employee</td>
            <td style="width: 100px; height: 26px">
            </td>
        </tr>
        <tr>
            <td style="width: 100px; height: 13px;">
                Employee Name</td>
            <td style="width: 100px; height: 13px;">
            </td>
        </tr>
        <tr>
            <td style="width: 100px">
                <asp:TextBox ID="txt_SearchEmployee" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
            <td style="width: 100px">
                <asp:LinkButton ID="lnkSearch" runat="server" OnClick="lnkSearch_Click">Search</asp:LinkButton></td>
        </tr>
    </table>
</asp:Content>

