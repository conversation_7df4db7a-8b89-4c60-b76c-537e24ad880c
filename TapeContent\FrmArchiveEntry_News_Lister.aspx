<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmArchiveEntry_News_Lister.aspx.vb" Inherits="TapeContent_FrmArchiveEntry_News_Lister" title="Home > Archival Entry for News" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebGrid" TagPrefix="igtbl" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table style="width: 100%">
        <tr>
            <td style="width: 942px; height: 23px; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Archive Entry for News</td>
        </tr>
        <tr>
            <td valign="top">
                <table style="width: 100%">
                    <tr class="mytext">
                        <td style="width: 147px">
                            Tape No&nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;&nbsp; 
                            <asp:CheckBox ID="Chk_TapeNumber" runat="server" Text="Ignore" /></td>
                        <td style="width: 187px">
                            Reporter &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                            &nbsp; 
                            <asp:CheckBox ID="Chk_Reporter" runat="server" Text="Ignore" /></td>
                        <td style="width: 189px">
                            Camera Man &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            <asp:CheckBox ID="Chk_CameraMan" runat="server" Text="Ignore" /></td>
                        <td style="width: 145px">
                            Location Code
                            <asp:CheckBox ID="Chk_Loc" runat="server" Text="Ignore" /></td>
                        <td>
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                            &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox ID="Chk_ReporterSlug" runat="server" Text="Ignore" Visible="False" />
                            &nbsp;
                        </td>
                        <td>
                        &nbsp;&nbsp; <asp:CheckBox ID="Chk_ProposedSlug" runat="server" Text="Ignore" Visible="False" /></td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 147px;" valign="top">
                            <asp:TextBox ID="txt_TapeNo" runat="server" CssClass="mytext" Width="128px"></asp:TextBox>
                            <asp:DropDownList ID="ddl_TapeNumber" runat="server" CssClass="mytext" Width="136px" Visible="False">
                            </asp:DropDownList></td>
                        <td style="width: 187px;" valign="top">
                            <asp:TextBox ID="txt_ReporterName" runat="server" CssClass="mytext" Width="168px"></asp:TextBox>
                            <asp:DropDownList ID="ddl_Reporter" runat="server" CssClass="mytext" Width="176px" Visible="False">
                            </asp:DropDownList></td>
                        <td style="width: 189px;" valign="top">
                            <asp:TextBox ID="txt_CameraMan" runat="server" CssClass="mytext" Width="168px"></asp:TextBox>
                            <asp:DropDownList ID="ddl_CameraMan" runat="server" CssClass="mytext" Width="176px" Visible="False">
                            </asp:DropDownList></td>
                        <td rowspan="1" style="width: 145px;" valign="top">
                            <asp:TextBox ID="txt_Loc" runat="server" CssClass="mytext" Width="120px"></asp:TextBox>
                            <asp:TextBox ID="txt_EnglishScript" runat="server" CssClass="mytext" Visible="False"
                                Width="16px"></asp:TextBox></td>
                        <td rowspan="1" style="width: 211px;" valign="top">
                            <asp:TextBox ID="txt_ReporterSlug" runat="server" CssClass="mytext" Height="32px"
                                TextMode="MultiLine" Width="176px" Visible="False"></asp:TextBox>&nbsp; &nbsp; &nbsp;&nbsp;
                            &nbsp;&nbsp;&nbsp;</td>
                        <td valign="top">
                            <asp:TextBox ID="txt_ProposedSlug" runat="server" CssClass="mytext" Width="152px" Visible="False"></asp:TextBox>&nbsp;
                            <asp:CheckBox ID="Chk_EnglishScript" runat="server" Text="Ignore" Visible="False" />
                            <asp:TextBox
                                ID="txt_UrduScript" runat="server" CssClass="mytext" Visible="False" Width="32px"></asp:TextBox>
                            <asp:CheckBox ID="Chk_UrduScript" runat="server" Text="Ignore" Visible="False" /></td>
                    </tr>
                </table>
                            <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_TapeNumber">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_Reporter">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_CameraMan">
                            </cc1:ListSearchExtender>
                               
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetTapeContentNews_Lister_TapeNo"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo">
                </cc1:AutoCompleteExtender>
                
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Reporter" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetReporter"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterName">
                </cc1:AutoCompleteExtender>
                
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_CameraMan" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetCameraMan"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_CameraMan">
                </cc1:AutoCompleteExtender>
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Delete !"
                    TargetControlID="bttnDelete">
                </cc1:ConfirmButtonExtender>
                <cc1:AutoCompleteExtender ID="AutoCompleteExtender_LocationCode" runat="server" CompletionInterval="1"
                    CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetLocationCodeNews" ServicePath="AutoComplete.asmx"
                    TargetControlID="txt_Loc">
                </cc1:AutoCompleteExtender>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Size="Small" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr class="mytext">
            <td class="bottomMain" style="width: 100%; height: 29px">
                &nbsp;<asp:Button ID="bttnSearch" runat="server" CssClass="buttonA" Text="Search"
                    Width="88px" />
                <asp:Button ID="bttnAdd" runat="server" CssClass="buttonA" Text="Add New" Width="80px" />&nbsp;
                <asp:Button ID="bttnEdit" runat="server" CssClass="buttonA" Text="Edit" Width="88px" />
                &nbsp;<asp:Button ID="bttnDelete" runat="server" CssClass="buttonA" Text="Delete"
                    Width="88px" />
                &nbsp;<asp:Button ID="bttnCancel" runat="server" CssClass="buttonA" Text="Cancel"
                    Width="88px" />&nbsp;
                </td>
        </tr>
        <tr>
            <td style="width: 100%" valign="top">
                &nbsp;<table style="width: 100%">
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 85%">
                <igtbl:UltraWebGrid ID="dg_search" runat="server" Width="100%">
                    <Bands>
                        <igtbl:UltraGridBand SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="Single">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentNewsID" HeaderText="TapeContentID"
                                    Hidden="True">
                                    <Header Caption="TapeContentID">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ContentTypeName" HeaderText="Content Type"
                                    Hidden="True">
                                    <Header Caption="Content Type">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="DepartmentName" HeaderText="Department Name">
                                    <Header Caption="Department Name">
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TelecastDate" HeaderText="Telecast Date" Hidden="True"
                                    Width="180px">
                                    <Header Caption="Telecast Date">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ClassificationCode" HeaderText="Classification Code">
                                    <Header Caption="Classification Code">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="CallNo" HeaderText="Call No">
                                    <Header Caption="Call No">
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LocationCode" HeaderText="Location Code">
                                    <Header Caption="Location Code">
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="IsActive" HeaderText="Is Active">
                                    <Header Caption="Is Active">
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="AddedBy" HeaderText="AddedBy">
                                    <Header Caption="AddedBy">
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ModifiedBy" HeaderText="ModifiedBy">
                                    <Header Caption="ModifiedBy">
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                        <igtbl:UltraGridBand SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="None" RowSelectors="No">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentID" HeaderText="TapeContentID"
                                    Hidden="True">
                                    <Header Caption="TapeContentID">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentDetail_News_ID" HeaderText="TapeContentDetail_News_ID"
                                    Hidden="True">
                                    <Header Caption="TapeContentDetail_News_ID">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeLibraryID" HeaderText="TapeLibraryID"
                                    Hidden="True">
                                    <Header Caption="TapeLibraryID">
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeType" HeaderText="TapeType">
                                    <Header Caption="TapeType">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="startTime_vc" HeaderText="Start Time">
                                    <Header Caption="Start Time">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="EndTime_vc" HeaderText="End Time">
                                    <Header Caption="End Time">
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="Duration" HeaderText="Duration">
                                    <Header Caption="Duration">
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ReporterSlug" HeaderText="Reporter Slug">
                                    <Header Caption="Reporter Slug">
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ProposedSlug" HeaderText="Proposed Slug">
                                    <Header Caption="Proposed Slug">
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="EnglishScript" HeaderText="English Script">
                                    <Header Caption="English Script">
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="UrduScript" HeaderText="Urdu Script">
                                    <Header Caption="Urdu Script">
                                        <RowLayoutColumnInfo OriginX="10" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="10" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                
                                 <igtbl:UltraGridColumn BaseColumnName="createdDate" HeaderText="Created Date">
                                    <Header Caption="Created Date">
                                        <RowLayoutColumnInfo OriginX="11" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="11" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                    </Bands>
                    <DisplayLayout AllowColSizingDefault="Free" AllowColumnMovingDefault="OnServer" AllowDeleteDefault="Yes"
                        AllowSortingDefault="OnClient" AllowUpdateDefault="Yes" AutoGenerateColumns="False"
                        BorderCollapseDefault="Separate" HeaderClickActionDefault="SortMulti" Name="dgxsearch"
                        RowHeightDefault="20px" SelectTypeRowDefault="Single" Version="4.00" ViewType="OutlookGroupBy">
                        <GroupByBox Hidden="True">
                            <Style BackColor="ActiveBorder" BorderColor="Window"></Style>
                        </GroupByBox>
                        <GroupByRowStyleDefault BackColor="Control" BorderColor="Window">
                        </GroupByRowStyleDefault>
                        <FooterStyleDefault BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </FooterStyleDefault>
                        <RowStyleDefault BackColor="Window" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="Window" ColorTop="Window" />
                            <Padding Left="3px" />
                        </RowStyleDefault>
                        <FilterOptionsDefault AllString="(All)" EmptyString="(Empty)" NonEmptyString="(NonEmpty)">
                            <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                Font-Size="11px" Width="200px">
                                <Padding Left="2px" />
                            </FilterDropDownStyle>
                            <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                            </FilterHighlightRowStyle>
                        </FilterOptionsDefault>
                        <HeaderStyleDefault BackColor="#5774C2" BorderStyle="Solid" HorizontalAlign="Center" ForeColor="White">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </HeaderStyleDefault>
                        <EditCellStyleDefault BorderStyle="None" BorderWidth="0px">
                        </EditCellStyleDefault>
                        <FrameStyle BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid"
                            BorderWidth="1px" Font-Names="Microsoft Sans Serif" Font-Size="8.25pt" Width="100%">
                        </FrameStyle>
                        <Pager AllowPaging="True" PageSize="20">
                            <Style BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </Pager>
                        <AddNewBox Hidden="False">
                            <Style BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </AddNewBox>
                        <SelectedRowStyleDefault BackColor="#F09D21" ForeColor="White">
                        </SelectedRowStyleDefault>
                    </DisplayLayout>
                </igtbl:UltraWebGrid></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="width: 942px">
                <asp:TextBox ID="txt_TapeContentID" runat="server" Visible="False" Width="72px"></asp:TextBox></td>
        </tr>
    </table>
                            <asp:DropDownList ID="ddl_SubCloset" runat="server" CssClass="mytext" Width="176px" Visible="False">
                            </asp:DropDownList>
                            <asp:CheckBox ID="Chk_SubCloset" runat="server" Text="Ignore" Visible="False" />
                            <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_SubCloset">
                            </cc1:ListSearchExtender>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

