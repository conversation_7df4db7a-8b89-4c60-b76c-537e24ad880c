<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmArchiveEntry_Ent.aspx.vb" Inherits="TapeContent_FrmArchiveEntry_Ent" Title="Home > Archival Entry for Entertainment > Add New" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDataInput.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebDataInput" TagPrefix="igtxt" %>



<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="height: 100%">
        <tr>
            <td style="height: 17px; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt;
                    <asp:LinkButton ID="LnkArchival_Ent" runat="server" CssClass="labelheading">Archive Entry for Entertainment  </asp:LinkButton>
                &gt; Add New</td>
        </tr>
        <tr>
            <td style="height: 22px;" align="center">
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="100%" Font-Names="Arial" Font-Size="11pt" Font-Underline="False"></asp:Label></td>
        </tr>
        <tr>
            <td valign="top">
                <cc1:TabContainer ID="TabContainer1" runat="server" ActiveTabIndex="0" Height="600px">
                    <cc1:TabPanel ID="TabPanel1" runat="server" HeaderText="TabPanel1">
                        <ContentTemplate>
                            <table>
                                <tr>
                                    <td colspan="5" style="height: 1px">&nbsp;</td>
                                    <td colspan="1" style="height: 1px"></td>
                                </tr>
                                <tr class="mytext">
                                    <td class="mytext" style="height: 10px">Channel</td>
                                    <td style="height: 10px">Department &nbsp; &nbsp; &nbsp;
                                            <asp:Image ID="Err_Department" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                    <td style="height: 10px">Sub Closet</td>
                                    <td style="height: 10px; width: 159px;">Tape No &nbsp; &nbsp; &nbsp;&nbsp;
                                            <asp:Image ID="Err_TapeNumber" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                    <td style="height: 10px; width: 121px;"></td>
                                    <td style="height: 10px"></td>
                                </tr>
                                <tr>
                                    <td style="width: 165px; height: 29px" valign="top">
                                        <asp:DropDownList ID="ddl_Channel" runat="server" CssClass="mytext" Width="144px">
                                        </asp:DropDownList></td>
                                    <td class="mytext" style="width: 165px; height: 29px" valign="top">
                                        <asp:TextBox ID="txt_Department" runat="server" CssClass="mytext" Width="136px"></asp:TextBox></td>
                                    <td style="width: 165px; height: 29px" valign="top">
                                        <asp:DropDownList ID="ddl_SubCloset" runat="server" CssClass="mytext" Width="144px">
                                        </asp:DropDownList></td>
                                    <td class="mytext" valign="top" style="width: 159px">
                                        <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext" Width="136px"></asp:TextBox>
                                        <asp:LinkButton ID="lnkBulkTapes" runat="server">Add Bulk Tapes</asp:LinkButton></td>
                                    <td style="width: 121px; height: 29px" valign="top">
                                        <asp:Button ID="bttnAddNEwTape" runat="server" CssClass="buttonA" Text="Add New Tape"
                                            Width="104px" />&nbsp;</td>
                                    <td style="width: 150px; height: 29px" valign="top">
                                        <asp:LinkButton ID="lnkCheckIsEnter" runat="server" Font-Size="Small" OnClick="lnkCheckIsEnter_Click">Tape Status</asp:LinkButton></td>
                                </tr>
                                <tr class="mytext">
                                    <td style="height: 18px"></td>
                                    <td style="height: 18px"></td>
                                    <td style="height: 18px"></td>
                                    <td style="width: 159px; height: 18px"></td>
                                    <td style="width: 121px; height: 18px"></td>
                                    <td style="height: 18px"></td>
                                </tr>
                                <tr class="mytext">
                                    <td style="height: 18px">Location Code</td>
                                    <td style="height: 18px">Classification Code</td>
                                    <td style="height: 18px">Special Note</td>
                                    <td style="height: 18px; width: 159px;">Recycle Turn</td>
                                    <td style="width: 121px; height: 18px">Copies</td>
                                    <td style="height: 18px"></td>
                                </tr>
                                <tr>
                                    <td style="width: 165px; height: 83px" valign="top">
                                        <asp:TextBox ID="txt_LocationCode" runat="server" CssClass="mytext" Height="56px"
                                            TextMode="MultiLine" Width="136px"></asp:TextBox><br />
                                    </td>
                                    <td style="width: 165px; height: 83px" valign="top">
                                        <asp:TextBox ID="txt_ClassificationCode" runat="server" CssClass="mytext" Height="56px"
                                            TextMode="MultiLine" Width="136px"></asp:TextBox></td>
                                    <td style="width: 165px; height: 83px" valign="top">
                                        <asp:TextBox ID="txt_CallNo" runat="server" CssClass="mytext" Height="56px" TextMode="MultiLine"
                                            Width="136px"></asp:TextBox></td>
                                    <td style="height: 83px; width: 159px;" valign="top">
                                        <asp:DropDownList ID="ddl_RecycleTurn" runat="server" CssClass="mytext" Width="144px">
                                        </asp:DropDownList><br />
                                        <br />
                                        &nbsp;</td>
                                    <td style="width: 121px; height: 83px" valign="top">
                                        <asp:DropDownList ID="ddlCopies" runat="server" CssClass="mytext" Width="32px">
                                            <asp:ListItem>1</asp:ListItem>
                                            <asp:ListItem>2</asp:ListItem>
                                        </asp:DropDownList></td>
                                    <td style="width: 150px; height: 83px" valign="top">&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="mytext" style="height: 6px" valign="top">Low Resolution File Name</td>
                                    <td class="mytext" style="height: 6px" valign="top">High Resolution File Name</td>
                                    <td class="mytext" style="height: 6px" valign="top">File Path</td>
                                    <td style="width: 159px; height: 6px" valign="top"></td>
                                    <td style="width: 121px; height: 6px" valign="top"></td>
                                    <td style="width: 150px; height: 6px" valign="top"></td>
                                </tr>
                                <tr>
                                    <td valign="top">
                                        <asp:TextBox ID="txtLowResFileName" runat="server" CssClass="mytext" Height="56px" TextMode="MultiLine"
                                            Width="136px"></asp:TextBox></td>
                                    <td valign="top">
                                        <asp:TextBox ID="txtHighResFileName" runat="server" CssClass="mytext" Height="56px" TextMode="MultiLine"
                                            Width="136px"></asp:TextBox></td>
                                    <td valign="top">
                                        <asp:TextBox ID="txtFiePath" runat="server" CssClass="mytext" Height="56px" TextMode="MultiLine"
                                            Width="136px"></asp:TextBox></td>
                                    <td style="width: 159px" valign="top">
                                        <asp:GridView ID="dgTapeNumber" runat="server" AutoGenerateColumns="False" Font-Names="Arial"
                                            Font-Size="X-Small" PageSize="200" Width="88px">
                                            <Columns>
                                                <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                                            </Columns>
                                            <HeaderStyle CssClass="gridheader" />
                                        </asp:GridView>
                                    </td>
                                    <td style="width: 121px" valign="top"></td>
                                    <td style="width: 150px" valign="top"></td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height: 28px" valign="top">
                                        <asp:Label ID="lblErr_MasterEntry" runat="server" ForeColor="Red"
                                            Width="376px" Font-Bold="True" Font-Size="Medium"></asp:Label></td>
                                    <td style="width: 121px; height: 28px" valign="top">
                                        <asp:DropDownList ID="ddl_TapeNo" runat="server" CssClass="mytext" Visible="False"
                                            Width="104px">
                                        </asp:DropDownList></td>
                                    <td style="width: 150px; height: 28px" valign="top"></td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height: 26px">
                                        <asp:TextBox ID="txt_dgKeyword_Index" runat="server" CssClass="mytext" Visible="False"
                                            Width="64px"></asp:TextBox>
                                        <asp:TextBox ID="txt_dgProgramInfo_Index" runat="server" CssClass="mytext" Visible="False"
                                            Width="64px"></asp:TextBox>
                                        <asp:TextBox ID="txt_TapeContentID" runat="server" CssClass="mytext" Visible="False"
                                            Width="96px"></asp:TextBox>
                                        <asp:TextBox ID="txt_TapeTypeID" runat="server" CssClass="mytext" Visible="False"
                                            Width="96px"></asp:TextBox>
                                        <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
                                        <asp:Label ID="lblQueryString" runat="server" Visible="False"></asp:Label></td>
                                    <td colspan="1" style="height: 26px; width: 121px;">
                                        <asp:DropDownList ID="ddl_Department" runat="server" CssClass="mytext" Visible="False"
                                            Width="104px">
                                        </asp:DropDownList></td>
                                    <td colspan="1" style="height: 26px"></td>
                                </tr>
                            </table>
                            <br />
                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Department" runat="server" CompletionInterval="1"
                                CompletionSetCount="12" MinimumPrefixLength="1" ServiceMethod="GetDepartment"
                                ServicePath="AutoComplete.asmx" TargetControlID="txt_Department" DelimiterCharacters="" Enabled="True">
                            </cc1:AutoCompleteExtender>
                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender1" runat="server" CompletionInterval="1"
                                CompletionSetCount="12" ServiceMethod="TapeContent_TapeNumer"
                                ServicePath="AutoComplete.asmx" TargetControlID="txt_TapeNumber" DelimiterCharacters="" Enabled="True">
                            </cc1:AutoCompleteExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_SubCloset" Enabled="True">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" Enabled="True" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_TapeNo">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_Channel" Enabled="True">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_Department" Enabled="True">
                            </cc1:ListSearchExtender>
                            &nbsp;
                                <br />
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />

                        </ContentTemplate>
                        <HeaderTemplate>
                            Master Level Entry
                        </HeaderTemplate>
                    </cc1:TabPanel>
                    <cc1:TabPanel ID="TabPanel2" runat="server" HeaderText="TabPanel2">
                        <HeaderTemplate>
                            Merge Tape Info
                        </HeaderTemplate>
                        <ContentTemplate>
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                <ContentTemplate>
                                    <table cellspacing="0" cellpadding="0" width="100%">
                                        <tbody>
                                            <tr class="mytext">
                                                <td style="height: 15px" valign="top">Merge Tape No.</td>
                                            </tr>
                                            <tr>
                                                <td class="mytext" valign="top">
                                                    <asp:TextBox ID="txt_MergeTapes" runat="server"></asp:TextBox>&nbsp;&nbsp;<asp:Button ID="bttnMerge" OnClick="bttnMerge_Click" runat="server" Text="Merge" CssClass="buttonA" Width="80px"></asp:Button><br />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="height: 11px" class="mytext" valign="top">
                                                    <asp:GridView ID="dg_Merge2" runat="server" Width="352px" AutoGenerateColumns="False">
                                                        <Columns>
                                                            <asp:BoundField DataField="MergeTapeNumber" HeaderText="Merge Tape Number"></asp:BoundField>
                                                            <asp:BoundField HtmlEncode="False" DataFormatString="{0:dd-MMM-yyyy}" DataField="MergeDate" HeaderText="Merge Date"></asp:BoundField>
                                                        </Columns>

                                                        <HeaderStyle CssClass="gridheader" Font-Underline="False"></HeaderStyle>
                                                    </asp:GridView>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="height: 11px" class="mytext" valign="top"></td>
                                            </tr>
                                            <tr>
                                                <td style="height: 26px" valign="middle" colspan="1">
                                                    <asp:GridView ID="dg_Merge" runat="server" Width="352px" AutoGenerateColumns="False">
                                                        <Columns>
                                                            <asp:BoundField DataField="MergeTapeNumberID">
                                                                <ItemStyle ForeColor="White"></ItemStyle>

                                                                <HeaderStyle Width="1px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="MergeTapeNumber" HeaderText="Merge Tape Number">
                                                                <HeaderStyle Width="300px"></HeaderStyle>
                                                            </asp:BoundField>
                                                        </Columns>

                                                        <HeaderStyle CssClass="gridheader" Font-Underline="False"></HeaderStyle>
                                                    </asp:GridView>
                                                    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_MergeTapes" runat="server" TargetControlID="txt_MergeTapes" ServicePath="AutoComplete.asmx" ServiceMethod="GetMergeTapeNumbers" CompletionSetCount="12" CompletionInterval="1" EnableCaching="true" MinimumPrefixLength="3">
                                                    </cc1:AutoCompleteExtender>
                                                    <cc1:ListSearchExtender ID="ListSearchExtender5" runat="server" TargetControlID="ddl_MergeTapeNo" PromptPosition="Bottom" PromptText="">
                                                    </cc1:ListSearchExtender>
                                                    <asp:DropDownList ID="ddl_MergeTapeNo" runat="server" CssClass="mytext" Width="168px" Visible="False">
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <asp:Label ID="lblErr_MergeTape" runat="server" ForeColor="Red" CssClass="mytext" Width="344px"></asp:Label>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </ContentTemplate>
                    </cc1:TabPanel>
                    <cc1:TabPanel ID="TabPanel3" runat="server" HeaderText="TabPanel3">
                        <HeaderTemplate>
                            Program Information
                        </HeaderTemplate>
                        <ContentTemplate>
                            <table style="width: 100%">
                                <tr>
                                    <td style="width: 1292px">
                                        <table width="100%">
                                            <tr>
                                                <td class="mytext" style="width: 100%;" valign="top">
                                                    <table style="width: 100%">
                                                        <tr>
                                                            <td style="width: 166px; height: 20px">Program Child</td>
                                                            <td style="width: 165px; height: 20px">Abstract</td>
                                                            <td style="width: 155px; height: 20px">Note</td>
                                                            <td style="width: 89px; height: 20px">From Episode</td>
                                                            <td style="width: 89px; height: 20px">To Episode</td>
                                                            <td style="width: 97px; height: 20px">Part No</td>
                                                            <td style="width: 112px; height: 20px">Prod. Status</td>
                                                            <td colspan="2" rowspan="3" valign="top">
                                                                <table style="width: 232px">
                                                                    <tr>
                                                                        <td style="width: 125px; height: 15px">KeyType</td>
                                                                        <td style="width: 125px; height: 15px">Keyword</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 150px">1.<asp:TextBox ID="KT1" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                        <td style="width: 150px">1.<asp:TextBox ID="KW1" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 44px; height: 13px"></td>
                                                                        <td style="width: 100px; height: 13px"></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 150px">2.<asp:TextBox ID="KT2" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                        <td style="width: 150px">2.<asp:TextBox ID="KW2" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 44px"></td>
                                                                        <td style="width: 100px"></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 150px">3.<asp:TextBox ID="KT3" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                        <td style="width: 150px">3.<asp:TextBox ID="KW3" runat="server" Width="128px" CssClass="myddl"></asp:TextBox></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="width: 44px"></td>
                                                                        <td style="width: 100px">
                                                                            <asp:LinkButton ID="lnkAddKW" runat="server" Width="104px">Add New Keyword</asp:LinkButton>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                &nbsp; &nbsp; &nbsp; &nbsp;<cc1:AutoCompleteExtender
                                                                    ID="AutoCompleteExtender_Ent_KW1" runat="server" CompletionInterval="1" CompletionSetCount="12" ServiceMethod="EntKeywords" ServicePath="AutoComplete.asmx"
                                                                    TargetControlID="KW1" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                                <cc1:AutoCompleteExtender ID="AutoCompleteExtender3" runat="server" CompletionInterval="1"
                                                                    CompletionSetCount="12" ServiceMethod="EntKeywords"
                                                                    ServicePath="AutoComplete.asmx" TargetControlID="KW2" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                                <cc1:AutoCompleteExtender ID="AutoCompleteExtender4" runat="server" CompletionInterval="1"
                                                                    CompletionSetCount="12" ServiceMethod="EntKeywords"
                                                                    ServicePath="AutoComplete.asmx" TargetControlID="KW3" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                                <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KeyType_1" runat="server" CompletionInterval="1"
                                                                    CompletionSetCount="12" ServiceMethod="GetKeyTypes"
                                                                    ServicePath="AutoComplete.asmx" TargetControlID="KT1" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                                <cc1:AutoCompleteExtender ID="AutoCompleteExtender5" runat="server" CompletionInterval="1"
                                                                    CompletionSetCount="12" ServiceMethod="GetKeyTypes"
                                                                    ServicePath="AutoComplete.asmx" TargetControlID="KT2" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                                <cc1:AutoCompleteExtender ID="AutoCompleteExtender6" runat="server" CompletionInterval="1"
                                                                    CompletionSetCount="12" ServiceMethod="GetKeyTypes"
                                                                    ServicePath="AutoComplete.asmx" TargetControlID="KT3" DelimiterCharacters="" Enabled="True">
                                                                </cc1:AutoCompleteExtender>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td valign="top" style="width: 166px">
                                                                <asp:TextBox ID="txt_ProgramChild" runat="server" CssClass="mytext" Width="104px" TextMode="MultiLine"></asp:TextBox>&nbsp;&nbsp;
                                                                    &nbsp;
                                                            </td>
                                                            <td valign="top" style="width: 165px">
                                                                <asp:TextBox ID="txt_Abstract" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                                    Width="112px"></asp:TextBox>
                                                                &nbsp;
                                                            </td>
                                                            <td valign="top" style="width: 155px">
                                                                <asp:TextBox ID="txt_Note" runat="server" CssClass="mytext" Width="104px" TextMode="MultiLine"></asp:TextBox>&nbsp;&nbsp;&nbsp;
                                                            </td>
                                                            <td valign="top" style="width: 89px">
                                                                <asp:TextBox ID="txt_EpisodesNo" runat="server" CssClass="mytext" MaxLength="5" Width="40px"></asp:TextBox></td>
                                                            <td style="width: 89px" valign="top">
                                                                <asp:TextBox ID="txtToEpisode" runat="server" CssClass="mytext" MaxLength="5" Width="40px"></asp:TextBox></td>
                                                            <td valign="top" style="width: 97px">
                                                                <asp:TextBox ID="txt_PartNo" runat="server" CssClass="mytext" Width="50px"></asp:TextBox></td>
                                                            <td style="width: 112px; height: 36px" valign="top">
                                                                <asp:DropDownList ID="ddl_ProductionStatus" runat="server" CssClass="mytext" Width="90px">
                                                                </asp:DropDownList></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="7" style="height: 36px" valign="top">
                                                                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                                                                    <ContentTemplate>
                                                                        <table style="width: 774px" cellspacing="0" cellpadding="0" border="0">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td style="width: 122px; height: 19px"></td>
                                                                                    <td style="width: 122px; height: 19px"></td>
                                                                                    <td style="width: 79px; height: 19px"></td>
                                                                                    <td style="width: 122px; height: 19px"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td style="width: 122px; height: 19px">Start Time&nbsp; &nbsp;
                                                                                        <asp:Image ID="Err_StratTime" runat="server" Visible="False" ImageUrl="~/Images/error.gif"></asp:Image>
                                                                                    </td>
                                                                                    <td style="width: 122px; height: 19px">End Time &nbsp;
                                                                                        <asp:Image ID="Err_EndTime" runat="server" Visible="False" ImageUrl="~/Images/error.gif"></asp:Image>
                                                                                    </td>
                                                                                    <td style="width: 79px; height: 19px">Entry Date</td>
                                                                                    <td style="width: 122px; height: 19px">
                                                                                        <asp:Label ID="Label3" runat="server" Text="Urdu Script"></asp:Label></td>
                                                                                    <td style="width: 79px; height: 19px">On Air Date</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td style="width: 122px; height: 56px" valign="top">
                                                                                        <igtxt:WebMaskEdit ID="txt_starttime1" runat="server" CssClass="mytext" Width="85px" HorizontalAlign="Left" InputMask="##:##:##:##"></igtxt:WebMaskEdit>
                                                                                    </td>
                                                                                    <td style="width: 122px; height: 56px" valign="top">
                                                                                        <igtxt:WebMaskEdit ID="txt_Endtime1" runat="server" CssClass="mytext" Width="86px" HorizontalAlign="Left" InputMask="##:##:##:##"></igtxt:WebMaskEdit>
                                                                                    </td>
                                                                                    <td style="width: 79px; height: 56px" valign="top">
                                                                                        <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" Width="88px"></asp:TextBox></td>
                                                                                    <td style="width: 340px; height: 56px">
                                                                                        <asp:TextBox ID="txt_UrduScript" runat="server" CssClass="mytext" Font-Size="Medium" Width="330px" Height="50px" TextMode="MultiLine"></asp:TextBox>
                                                                                    </td>
                                                                                    <td style="width: 79px; height: 56px" valign="top">
                                                                                        <asp:TextBox ID="txtonairdate" runat="server" CssClass="mytext" Width="88px"></asp:TextBox></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td>
                                                                                        <asp:TextBox ID="END1" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="END2" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="END3" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="END4" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>
                                                                                        <%-- <igtxt:WebMaskEdit ID="txt_starttime1" runat="server" CssClass="mytext" HorizontalAlign="Left"
                                InputMask="##:##:##:##" Width="115px" >
                            </igtxt:WebMaskEdit>--%></td>
                                                                                    <td>
                                                                                        <asp:TextBox ID="ST1" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="ST2" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="ST3" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>&nbsp;
                                                                                        <asp:TextBox ID="ST4" runat="server" CssClass="myddl" Width="16px" Visible="false">00</asp:TextBox>
                                                                                        <%--<igtxt:WebMaskEdit ID="txt_Endtime1" runat="server" CssClass="mytext" HorizontalAlign="Left"
                                InputMask="##:##:##:##" Width="114px">
                            </igtxt:WebMaskEdit>--%></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td style="width: 122px; height: 29px"></td>
                                                                                    <td style="width: 122px; height: 29px">
                                                                                        <asp:TextBox ID="txt_StartTime" runat="server" Width="8px" Visible="False"></asp:TextBox>
                                                                                        <asp:TextBox ID="txt_EndTime" runat="server" Width="8px" Visible="False"></asp:TextBox>
                                                                                        <asp:TextBox ID="txt_TimeDuration" runat="server" Width="16px" Visible="False"></asp:TextBox>
                                                                                        <asp:Label ID="Label2" runat="server" Text="Duration" Visible="False"></asp:Label></td>
                                                                                    <td style="width: 79px; height: 29px">
                                                                                        <asp:TextBox ID="DUR_1" runat="server" CssClass="myddl" Width="16px" Visible="False" Enabled="False"></asp:TextBox>
                                                                                        <asp:TextBox ID="DUR_2" runat="server" CssClass="myddl" Width="16px" Visible="False" Enabled="False"></asp:TextBox>
                                                                                        <asp:TextBox ID="DUR_3" runat="server" CssClass="myddl" Width="16px" Visible="False" Enabled="False"></asp:TextBox>
                                                                                        <asp:TextBox ID="DUR_4" runat="server" CssClass="myddl" Width="16px" Visible="False" Enabled="False"></asp:TextBox>
                                                                                    </td>
                                                                                    <td style="width: 122px; height: 29px">
                                                                                        <asp:Button ID="Button2" runat="server" Text="Calculate Duration" CssClass="buttonA" Font-Size="X-Small" Width="312px" Visible="False"></asp:Button>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender1" runat="server" TargetControlID="END1" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender2" runat="server" TargetControlID="END2" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender3" runat="server" TargetControlID="END3" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender4" runat="server" TargetControlID="END4" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender5" runat="server" TargetControlID="ST1" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender6" runat="server" TargetControlID="ST2" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender7" runat="server" TargetControlID="ST3" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:MaskedEditExtender ID="MaskedEditExtender8" runat="server" TargetControlID="ST4" MaskType="Number" Mask="99"></cc1:MaskedEditExtender>
                                                                        <cc1:CalendarExtender ID="CalendarExtender2" runat="server" CssClass="MyCalendar" TargetControlID="txtEntryDate" Enabled="True" Format="dd-MMM-yyyy"></cc1:CalendarExtender>
                                                                        <cc1:CalendarExtender ID="CalendarExtender3" runat="server" CssClass="MyCalendar" TargetControlID="txtonairdate" Enabled="True" Format="dd-MMM-yyyy"></cc1:CalendarExtender>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                                                    </ContentTemplate>
                                                                </asp:UpdatePanel>
                                                                <asp:TextBox ID="txt_title" runat="server" CssClass="mytext" Visible="False"></asp:TextBox>
                                                                <asp:DropDownList ID="ddl_ProgramChild" runat="server" Visible="False" Width="120px">
                                                                </asp:DropDownList>
                                                                <asp:TextBox ID="txtBttnProgram" runat="server" Visible="False" Width="40px"></asp:TextBox></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="bottomMain" colspan="1" style="height: 29px; width: 100%;" valign="middle">&nbsp;<asp:Button ID="bttnSaveProg_2" runat="server" CssClass="buttonA" Text="Save Program"
                                                    Width="104px" />
                                                    <asp:Button ID="bttnSaveKeyword" runat="server" CssClass="buttonA" Text="Save Keyword"
                                                        Width="104px" />
                                                    <asp:Button ID="bttnClearProgram" runat="server" CssClass="buttonA" Text="Clear"
                                                        Width="48px" />
                                                    <asp:Button ID="bttnSaveProgramInfo" runat="server" CssClass="buttonA"
                                                        Text="Save Program Info." Visible="False" Width="152px" /></td>
                                            </tr>
                                        </table>
                                        <asp:Label ID="lblErr_ProgramInfo" runat="server" CssClass="mytext" ForeColor="Red"
                                            Width="520px"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td style="width: 1292px">
                                        <asp:Panel ID="TitlePanel" runat="server" BackColor="LightSteelBlue" BorderColor="#E0E0E0"
                                            Width="100%">
                                            &nbsp;
                                                <asp:Image ID="Image1" runat="server" />
                                            <asp:Label ID="Label1" runat="server" CssClass="heading1" Font-Bold="True" Font-Names="Arial"
                                                Font-Size="Medium" Text="Program" Width="832px"></asp:Label>
                                        </asp:Panel>
                                        <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                                            <table style="width: 100%; height: 100%">
                                                <tr>
                                                    <td colspan="2" style="height: 161px">
                                                        <asp:Panel ID="pnlScroll" runat="server" Width="100%" Height="140px" ScrollBars="Both">
                                                            <asp:GridView ID="dg_TestProgram" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                                OnSelectedIndexChanged="dg_TestProgram_SelectedIndexChanged" PageSize="15" Width="100%">
                                                                <Columns>
                                                                    <asp:BoundField DataField="Program" HeaderText="Program">
                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="PartNo" HeaderText="Part No.">
                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="ProductionStatus">
                                                                        <ItemStyle ForeColor="White" Width="0px" />
                                                                        <HeaderStyle Width="0px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="Note" HeaderText="Note" />
                                                                    <asp:BoundField DataField="EpisodeNo" HeaderText="Episode No.">
                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="Abstract" HeaderText="Abstract">
                                                                        <ItemStyle Width="125px" />
                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="StartTime" HeaderText="Start Time" />
                                                                    <asp:BoundField DataField="EndTime" HeaderText="End Time" />
                                                                    <asp:BoundField DataField="Duration" HeaderText="Duration" />
                                                                    <asp:BoundField DataField="TapeContentDetailID">
                                                                        <ItemStyle ForeColor="White" />
                                                                        <HeaderStyle Width="1px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="ProgramChildID">
                                                                        <ItemStyle ForeColor="White" Width="0px" />
                                                                        <HeaderStyle Width="0px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="EntryDate" DataFormatString="{0:dd-MMM-yyyy}" HeaderText="Entry Date"
                                                                        HtmlEncode="False" />
                                                                    <asp:TemplateField ShowHeader="False">
                                                                        <ItemTemplate>
                                                                            <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Delete"
                                                                                OnClientClick="return confirm('Are you Sure you want to delete this Program?')"
                                                                                Text="Delete"></asp:LinkButton>
                                                                        </ItemTemplate>
                                                                    </asp:TemplateField>
                                                                    <asp:BoundField DataField="UrduScript" HeaderText="Urdu Script">
                                                                        <ItemStyle Width="250px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="onAirdate" HeaderText="On Air Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False"></asp:BoundField>
                                                                </Columns>
                                                                <HeaderStyle CssClass="gridheader" Font-Underline="False" />
                                                                <AlternatingRowStyle CssClass="AlternateRows" />
                                                            </asp:GridView>
                                                        </asp:Panel>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2" style="height: 21px">Keywords</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <asp:Panel ID="Panel2" runat="server" Width="100%" Height="140px" ScrollBars="Both">
                                                            <asp:GridView ID="dg_keyword_2" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                                OnSelectedIndexChanged="dg_keyword_2_SelectedIndexChanged" Width="744px">
                                                                <Columns>
                                                                    <asp:BoundField DataField="ProgramChildID">
                                                                        <ItemStyle ForeColor="White" Width="0px" />
                                                                        <HeaderStyle Width="0px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="ProgramChildName" HeaderText="Program Child">
                                                                        <HeaderStyle Width="250px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="EntertainmentKeywordID">
                                                                        <ItemStyle ForeColor="White" Width="0px" />
                                                                        <HeaderStyle Width="0px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="EntertainmentKeyword" HeaderText="Keyword">
                                                                        <HeaderStyle Width="250px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="ProgramVsKeywordID">
                                                                        <ItemStyle ForeColor="White" Width="0px" />
                                                                        <HeaderStyle Width="0px" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="KeyTypeID">
                                                                        <ItemStyle ForeColor="White" />
                                                                    </asp:BoundField>
                                                                    <asp:BoundField DataField="KeyType" HeaderText="KeyType" />
                                                                    <asp:BoundField DataField="EpisodeNo" HeaderText="Episode No" />
                                                                    <asp:BoundField DataField="PartNo" HeaderText="Part No" />
                                                                    <asp:TemplateField ShowHeader="False">
                                                                        <ItemTemplate>
                                                                            <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Delete"
                                                                                OnClientClick="return confirm('Are you Sure you want to delete this Keyword?')"
                                                                                Text="Delete"></asp:LinkButton>
                                                                        </ItemTemplate>
                                                                    </asp:TemplateField>
                                                                </Columns>
                                                                <HeaderStyle CssClass="gridheader" Font-Underline="False" />
                                                                <AlternatingRowStyle CssClass="AlternateRows" />
                                                            </asp:GridView>
                                                        </asp:Panel>
                                                    </td>
                                                </tr>
                                            </table>
                                        </asp:Panel>
                                    </td>
                                </tr>
                                <tr>
                                    <td valign="top" style="width: 1292px">
                                        <cc1:ListSearchExtender ID="ListSearchExtender6" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="ddl_ProgramChild" Enabled="True">
                                        </cc1:ListSearchExtender>
                                        <cc1:FilteredTextBoxExtender ID="FilteredTextBoxExtender2" runat="server" Enabled="True"
                                            FilterType="Numbers" InvalidChars="1234567890" TargetControlID="txt_EpisodesNo">
                                        </cc1:FilteredTextBoxExtender>
                                        <cc1:ListSearchExtender ID="ListSearchExtender7" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="ddl_ProductionStatus" Enabled="True">
                                        </cc1:ListSearchExtender>
                                        <cc1:ListSearchExtender ID="ListSearchExtender8" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="lstKeyword" Enabled="True">
                                        </cc1:ListSearchExtender>
                                        <cc1:CollapsiblePanelExtender
                                            ID="CollapsiblePanelExtender1"
                                            runat="server"
                                            CollapseControlID="TitlePanel"
                                            Collapsed="True"
                                            CollapsedImage="~/Images/Collapse.gif"
                                            CollapsedText="-- Show Program --"
                                            ExpandControlID="TitlePanel"
                                            ExpandedImage="~/Images/expand.gif"
                                            ExpandedText="-- Hide Program --"
                                            ImageControlID="Image1"
                                            SuppressPostBack="True"
                                            TextLabelID="Label1"
                                            TargetControlID="ContentPanel" Enabled="True">
                                        </cc1:CollapsiblePanelExtender>
                                        <cc1:ListSearchExtender ID="ListSearchExtender9" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="lstKeyType" Enabled="True">
                                        </cc1:ListSearchExtender>
                                        &nbsp;&nbsp;
                                                                    <asp:ListBox ID="lstKeyType" runat="server" CssClass="mytext" Height="120px" SelectionMode="Multiple"
                                                                        Width="88px" Visible="False"></asp:ListBox><asp:ListBox ID="lstKeyword" runat="server" CssClass="mytext" Height="120px" SelectionMode="Multiple"
                                                                            Width="176px" Visible="False"></asp:ListBox><asp:GridView ID="dg_programInfo" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                                                PageSize="15" Width="1008px">
                                                                                <Columns>
                                                                                    <asp:BoundField DataField="Program" HeaderText="Prog.Name">
                                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                                    </asp:BoundField>
                                                                                    <asp:BoundField DataField="PartNo" HeaderText="Part No.">
                                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                                    </asp:BoundField>
                                                                                    <asp:BoundField DataField="ProductionStatus" HeaderText="Prod.Status" />
                                                                                    <asp:BoundField DataField="Note" HeaderText="Note" />
                                                                                    <asp:BoundField DataField="EpisodeNo" HeaderText="Episode No.">
                                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                                    </asp:BoundField>
                                                                                    <asp:BoundField DataField="Abstract" HeaderText="Abstract">
                                                                                        <ItemStyle Width="125px" />
                                                                                        <HeaderStyle HorizontalAlign="Center" />
                                                                                    </asp:BoundField>
                                                                                    <asp:BoundField DataField="StartTime" HeaderText="Start Time" />
                                                                                    <asp:BoundField DataField="EndTime" HeaderText="End Time" />
                                                                                    <asp:BoundField DataField="Duration" HeaderText="Duration" />
                                                                                    <asp:BoundField DataField="TapeContentDetailID">
                                                                                        <ItemStyle ForeColor="White" />
                                                                                        <HeaderStyle Width="1px" />
                                                                                    </asp:BoundField>
                                                                                </Columns>
                                                                                <HeaderStyle CssClass="gridheader" Font-Italic="False" Font-Underline="False" />
                                                                                <AlternatingRowStyle CssClass="AlternateRows" />
                                                                            </asp:GridView>
                                        <br />
                                        <cc1:AutoCompleteExtender ID="AutoCompleteExtender2" runat="server" CompletionInterval="1"
                                            CompletionSetCount="12" ServiceMethod="TapeContent_ProgramChild"
                                            ServicePath="AutoComplete.asmx" TargetControlID="txt_ProgramChild" DelimiterCharacters="" Enabled="True">
                                        </cc1:AutoCompleteExtender>
                                        <asp:DropDownList ID="ddl_Keyword" runat="server" CssClass="mytext" Visible="False"
                                            Width="144px">
                                        </asp:DropDownList></td>
                                </tr>
                            </table>
                        </ContentTemplate>
                    </cc1:TabPanel>
                </cc1:TabContainer></td>
        </tr>
        <tr>
            <td class="bottomMain" style="height: 29px">&nbsp;&nbsp;
                    <asp:Button CssClass="buttonA" ID="bttnFinalSave" runat="server" Text="Save Record" Width="96px" Font-Bold="True" Visible="False" />&nbsp;
                    <asp:Button CssClass="buttonA" ID="bttnSave" runat="server" Text="Save Record" Width="96px" Visible="False" />
                <asp:Button ID="bttnCancel" runat="server" Text="Cancel" CssClass="buttonA" Width="72px" Visible="False" />
                <asp:Button CssClass="buttonA" ID="Button1" runat="server" Text="<< Save >>" Width="96px" Font-Bold="True" /></td>
        </tr>
        <tr>
            <td style="height: 16px">&nbsp;
            </td>
        </tr>
    </table>
    <asp:GridView ID="dg_KeyWord" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
        Visible="False" Width="544px">
        <Columns>
            <asp:BoundField DataField="KeyType" HeaderText="Key Type" />
            <asp:BoundField DataField="KeyWord" HeaderText="Key Word" />
            <asp:BoundField DataField="EntertainmentKeywordID">
                <ItemStyle ForeColor="White" />
                <HeaderStyle Width="1px" />
            </asp:BoundField>
        </Columns>
        <HeaderStyle CssClass="gridheader" Font-Underline="False" />
        <AlternatingRowStyle CssClass="AlternateRows" />
    </asp:GridView>
    <asp:UpdatePanel ID="UpdatePanel2" runat="server" Visible="False">
        <ContentTemplate>
            <table>
                <tbody>
                    <tr class="mytext">
                        <td>Key Type</td>
                        <td>Key Word</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td style="width: 160px; height: 24px" valign="top">
                            <asp:DropDownList ID="ddl_KeywordType" runat="server" Width="152px" CssClass="mytext">
                            </asp:DropDownList></td>
                        <td style="height: 24px" valign="top">
                            <asp:TextBox ID="txt_KeyWord" runat="server" CssClass="mytext"></asp:TextBox>&nbsp; </td>
                        <td style="width: 100px; height: 24px" valign="top">
                            <asp:Button ID="bttnSave_Keyword" runat="server" Text="Save KeyWord" Width="96px" CssClass="buttonA"></asp:Button></td>
                    </tr>
                </tbody>
            </table>
            <table>
                <tbody>
                    <tr>
                        <td style="width: 565px">&nbsp;&nbsp; </td>
                    </tr>
                </tbody>
            </table>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>

