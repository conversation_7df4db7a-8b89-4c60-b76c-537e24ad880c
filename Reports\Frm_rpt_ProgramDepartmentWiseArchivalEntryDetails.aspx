<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ProgramDepartmentWiseArchivalEntryDetails.aspx.vb" Inherits="Frm_rpt_ProgramDepartmentWiseArchivalEntryDetails" title="Archival Reports > How Can I View Department and Program Wise Archival Details?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Department , Program Wise Archival Tape Details" Width="752px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <table cellspacing="3">
                        <tr class="mytext">
                            <td style="height: 13px">
                                &nbsp;</td>
                            <td style="width: 199px; height: 13px">
                            </td>
                            <td style="height: 13px; width: 199px;">
                            </td>
                            <td style="height: 13px">
                            </td>
                            <td style="height: 13px">
                            </td>
                            <td style="height: 13px">
                            </td>
                            <td style="height: 13px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 20px">
                                Department Name</td>
                            <td style="height: 20px">
                                Program Name &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;
                                <asp:CheckBox ID="chkProgram" runat="server" Text="Ignore" Checked="True" /></td>
                            <td style="height: 20px" >
                                From Date &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                <asp:CheckBox
                                    ID="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Names="Verdana" Font-Size="X-Small" Font-Bold="False" /></td>
                            <td style="height: 20px" >
                                To Date</td>
                            <td style="height: 20px">
                                Station</td>
                            <td style="height: 20px" >
                                <asp:Label ID="Label2" runat="server" Text="Export to" Width="48px"></asp:Label></td>
                            <td style="width: 500px; height: 20px">
                                &nbsp;</td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top" >
                                <asp:DropDownList CssClass="mytext" ID="ddlDepartment" runat="server" DataSourceID="dsDepartment"
                                    DataTextField="DepartmentName" DataValueField="DepartmentID" Width="200px" Font-Names="Verdana" Font-Size="X-Small">
                                </asp:DropDownList><asp:SqlDataSource ID="dsDepartment" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>"
                                    SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;">
                                </asp:SqlDataSource>
                            </td>
                            <td style="height: 25px" valign="top">
                                <asp:TextBox ID="txtProgram" runat="server" CssClass="mytext" Font-Names="Verdana"
                                    Font-Size="X-Small" Width="175px"></asp:TextBox></td>
                            <td style="height: 25px;" valign="top" >
                                <asp:TextBox CssClass="mytext" ID="txtFromdate" runat="server" Font-Names="Verdana" Font-Size="X-Small" Width="175px"></asp:TextBox>&nbsp;
                            </td>
                            <td style="height: 25px" valign="top" >
                                <asp:TextBox CssClass="mytext" ID="txtToDate" runat="server" Font-Names="Verdana" Font-Size="X-Small"
                                    Width="168px"></asp:TextBox>&nbsp;
                            </td>
                            <td valign="top">
                                <asp:DropDownList ID="ddlBaseStation" runat="server" CssClass="mytext" Width="88px">
                                </asp:DropDownList></td>
                            <td valign="top">
                                <asp:DropDownList ID="ddlPDF" runat="server" CssClass="mytext">
                                    <asp:ListItem>PDF</asp:ListItem>
                                    <asp:ListItem>EXCEL</asp:ListItem>
                                </asp:DropDownList></td>
                            <td style="height: 25px">
                            </td>
                        </tr>
                    </table>
                                <cc1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtFromdate" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                                </cc1:CalendarExtender>
                                <cc1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtToDate" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                                </cc1:CalendarExtender>
                                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender6" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtProgram">
                </cc1:AutoCompleteExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddlDepartment">
                    </cc1:ListSearchExtender>
                </asp:Panel>
                <table style="width: 100%">
                    <tr>
                        <td class="bottomMain" style="width: 100%; height: 21px">
                            &nbsp;<asp:Button CssClass="buttonA" ID="bttnReport" runat="server" Text="View Report" Width="88px" Font-Names="Verdana" Font-Size="X-Small" /></td>
                    </tr>
                </table>
                <br />
                <br />
                &nbsp;&nbsp;
                                       
            </td>
        </tr>
    </table>
    <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
    </cc1:ToolkitScriptManager>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (Archival Reports --> Department , Program Wise Tape Details) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (Archival Reports --> Department , Program Wise Tape Details) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

