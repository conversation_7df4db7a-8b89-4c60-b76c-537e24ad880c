Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource
Imports System.IO

Partial Class ReminderService_frmOSRReminders
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()
    Dim Email As New clsEmail
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)
                End If

                lblErr.Text = String.Empty
                Chk_Employee.Checked = True
                chkDepartment.Checked = True
                Chk_Date.Checked = True
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnSendMail_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSendMail.Click

        Dim EmpCount As Integer = CheckEmployeeCount()
        Dim Counter As Integer = 0

        If EmpCount <> 0 Then
            lblErr.Text = "You Can not send Email to More than One Emplyee at a Time !!"
        ElseIf txt_EmailAddress.Text = "" Then
            lblErr.Text = "Please enter Email Address !!"
        Else

            ''**************************************************''
            ''************* Count Check Boxes ******************''
            ''**************************************************''
            Dim T As Integer
            Dim CheckCount As Integer = 0
            For T = 0 To dg_search.Rows.Count - 1
                Dim myCheckbox As CheckBox = CType(dg_search.Rows(T).Cells(6).Controls(1), CheckBox)
                If myCheckbox.Checked = True Then
                    CheckCount = CheckCount + 1
                End If
            Next

            ''*************************************************''
            If CheckCount = 0 Then
                lblErr.Text = "Plz Select Employee First !!"
            Else
                Dim UserID As Integer
                Dim objUserID As New BusinessFacade.Employee()
                objUserID.SM_LoginID = lbl_UserName.Text
                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                Dim BCC As String

                'Dim strFrom As String = "<EMAIL>"
                Dim strFrom As String
                If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                    ''****** For Lahore *******''
                    strFrom = "<EMAIL>"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                    ''****** For Islamabad *****''
                    strFrom = "<EMAIL>"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                    ''****** For Peshawar *****''
                    strFrom = "<EMAIL>"
                Else
                    ''****** For Karachi *******''
                    strFrom = "<EMAIL>"
                End If


                Dim CC As String

                If txt_BCC.Text = "" Then

                    If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                        ''****** For Lahore *******''
                        BCC = "<EMAIL>"
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                        ''****** For Islamabad *****''
                        BCC = "<EMAIL>"
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                        ''****** For Peshawar *****''
                        BCC = "<EMAIL>"
                    Else
                        ''****** For Karachi *******''
                        BCC = "<EMAIL>"
                        'BCC = "<EMAIL>"
                    End If

                Else
                    If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                        ''****** For Lahore *******''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                        ''****** For Islamabad *****''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                        ''****** For Peshawar *****''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    Else
                        ''****** For Karachi *******''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    End If
                End If

                If txt_CC.Text = "" Then
                    CC = "<EMAIL>"
                    'CC = "<EMAIL>"
                Else
                    CC = "<EMAIL>," & txt_CC.Text
                End If

                Dim ConcernArchive, extension As String

                If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                    ''****** For Lahore *******''
                    ConcernArchive = "Lahore Archive"
                    extension = "Ext: 334 - 374"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                    ''****** For Islamabad *****''
                    ConcernArchive = "Islamabad Archive"
                    extension = "Ext: 218"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                    ''****** For Peshawar *****''
                    ConcernArchive = "Peshawar Archive"
                    extension = "Ext: N/A"
                Else
                    ''****** For Karachi *******''
                    ConcernArchive = "Circulation Desk"
                    extension = "Ext:6132 (News Archive) , 6568 (Central Archive)"
                End If


                For i As Integer = 0 To dg_search.Rows.Count - 1

                    Dim myCheckbox As CheckBox = CType(dg_search.Rows(i).Cells(6).Controls(1), CheckBox)
                    If myCheckbox.Checked = True Then

                        Dim dt As New Hashtable
                        Dim EmployeeID, DepartmentID, FromDate, ToDate, EmployeeName As String
                        EmployeeID = dg_search.Rows(i).Cells(1).Text
                        EmployeeName = dg_search.Rows(i).Cells(2).Text
                        DepartmentID = dg_search.Rows(i).Cells(7).Text

                        FromDate = IIf(txtFromDate.Text = "", -1, txtFromDate.Text)
                        ToDate = IIf(txtToDate.Text = "", -1, txtToDate.Text)


                        dt.Add("@EmployeeID", EmployeeID)
                        dt.Add("@DepartmentID", DepartmentID)
                        dt.Add("@FromDate", FromDate)
                        dt.Add("@ToDate", ToDate)
                        LoadPDFReport("OSR_Reminders", dt)

                        UploadFile(EmployeeID, EmployeeName, txt_EmailAddress.Text, UserID, strFrom, BCC, CC, ConcernArchive, extension)

                    End If
                Next
                txt_EmailAddress.Text = ""
                For U As Integer = 0 To dg_search.Rows.Count - 1
                    Dim myCheckbox As CheckBox = CType(dg_search.Rows(U).Cells(6).Controls(1), CheckBox)
                    If myCheckbox.Checked = True Then
                        myCheckbox.Checked = False
                    End If
                Next


            End If
        End If

    End Sub

    Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        '--- Exporting Crystal Report to PDF process...


        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports/")

        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/Reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("sa", "Newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".pdf" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        crReportDocument.Export()
        crReportDocument.Close()



    End Sub

    Sub UploadFile(ByVal EmployeeID As Integer, ByVal EmployeeName As String, ByVal EmailAddress As String, ByVal UserID As String, ByVal strFrom As String, ByVal BCC As String, ByVal CC As String, ByVal ConcernArchive As String, ByVal extension As String)
        Dim fs As FileStream = Nothing

        Dim strFile As String = Server.MapPath("~/Reports/") & "OSR_Reminders" & ".pdf"

        'filepath = strFile
        ' Me.FileUpload1.SaveAs(strFile)

        fs = New FileStream(strFile, FileMode.Open, FileAccess.Read)
        Dim blob As Byte() = New Byte(fs.Length - 1) {}
        fs.Read(blob, 0, blob.Length)

        Dim FileContent As Byte() = blob
        fs.Close()


        ''***********************************************************''
        ''***********************************************************''
        ''***********************************************************''

        Dim ObjOSR As New BusinessFacade.ReminderService()
        Dim ID As String = ObjOSR.Insert_OSRReminders(EmployeeID, EmployeeName, EmailAddress, UserID, strFrom, BCC, CC, ConcernArchive, extension, FileContent)


        ''***********************************************************''

        'System.IO.File.Delete(strFile)

        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('LocalOSRReminders.aspx?OSRReminderID=" & ID + "', 'mywindow');"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)


        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onload=function OpenReport() {"
        'script = script + "var mywindow = window.open('frmSendOSRReminders.aspx?OSRReminderID=" & ID + "', 'mywindow');"
        'script = script + "}</script>"
        'Page.RegisterClientScriptBlock("test", script)



    End Sub

    Sub BindGrid()
        lblErr.Text = String.Empty

        Try

            Dim EmployeeID, DepartmentID As Integer
            Dim FromDates, ToDates As String

            Dim objSearch As New BusinessFacade.ReminderService()

            If Chk_Employee.Checked = True Then
                EmployeeID = 0
            Else
                ''***************************************''
                ''********** Get EmployeeID *************''
                ''***************************************''
                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                objEmployeeID.EmployeeName = txtEmployee.Text
                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

            End If

            If chkDepartment.Checked = True Then
                DepartmentID = 0
            Else
                ''***************************************''
                ''********** Get DepartmentID ***********''
                ''***************************************''

                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_DepartmentName.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

            End If

            If Chk_Date.Checked = True Then
                FromDates = "0"
                ToDates = "0"
            Else
                FromDates = txtFromDate.Text
                ToDates = txtToDate.Text
            End If


            '*********************** Store Procedure **********************'

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "GetOSRBulkReminderData"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim EmployeeIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
            EmployeeIDs.Value = EmployeeID
            Dim DepartmentIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@DepartmentID", Data.SqlDbType.Int)
            DepartmentIDs.Value = DepartmentID
            Dim FromDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@FromDate", Data.SqlDbType.Text)
            FromDate.Value = FromDates
            Dim ToDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
            ToDate.Value = ToDates
            Dim da As New System.Data.SqlClient.SqlDataAdapter
            Dim DS As New DataSet
            da.SelectCommand = cmd
            da.Fill(DS)
            dg_search.DataSource = DS.Tables(0).DefaultView
            dg_search.Columns(1).Visible = True
            dg_search.Columns(7).Visible = True
            dg_search.DataBind()
            dg_search.Columns(1).Visible = False
            dg_search.Columns(7).Visible = False

        Catch ex As Exception
            Me.lblErr.Text = "Error: " & ex.Message
            Me.lblErr.Visible = True
        End Try
    End Sub

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch.Click
        BindGrid()
        txt_EmailAddress.Text = ""
    End Sub

    Protected Sub dg_search_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_search.PageIndexChanging
        dg_search.PageIndex = e.NewPageIndex()
        BindGrid()
    End Sub

    Protected Sub dg_search_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_search.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            e.Row.Attributes.Add("onmouseover", "MouseEvents(this, event)")
            e.Row.Attributes.Add("onmouseout", "MouseEvents(this, event)")
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click

        txt_EmailAddress.Text = ""


        Dim EmpCount As Integer = CheckEmployeeCount()
        lblErr.Text = String.Empty

        If EmpCount <> 0 Then
            lblErr.Text = "You Can not send Email to More than One Emplyee at a Time !!"
        Else
            Dim I As Integer
            For I = 0 To dg_search.Rows.Count - 1

                Dim myCheckbox As CheckBox = CType(dg_search.Rows(I).Cells(6).Controls(1), CheckBox)
                If myCheckbox.Checked = True Then
                    Dim ObjReminder As New BusinessFacade.ReminderService()
                    ObjReminder.EmployeeName = dg_search.Rows(I).Cells(2).Text
                    Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                    If EmailAddress <> "N/A" Then
                        Dim Arr As Array = Split(EmailAddress, "@")
                        If Arr.Length = 2 Then
                            txt_EmailAddress.Text = EmailAddress
                            txt_EmailAddress.BackColor = Drawing.Color.PaleGreen
                        Else
                            lblErr.Text = "Invalid Email Address, Please Correct Email Address in Employee Form!"
                            txt_EmailAddress.BackColor = Drawing.Color.Pink
                        End If
                    Else
                        lblErr.Text = "Email Address not Exists"
                        txt_EmailAddress.BackColor = Drawing.Color.Pink
                    End If
                    Exit Sub
                End If
            Next
        End If
    End Sub

    Private Function CheckEmployeeCount()
        ''*********************************************************''
        ''*********** Chec for Multiple Employee ******************''
        ''*********************************************************''
        Dim FirstEmployee As String = ""
        Dim EmployeeCount As Integer = 0
        Dim FirstCheck As Integer = 0
        Dim J As Integer

        Dim i As Integer
        For i = 0 To dg_search.Rows.Count - 1
            Dim myCheckbox As CheckBox = CType(dg_search.Rows(i).Cells(6).Controls(1), CheckBox)
            If myCheckbox.Checked = True Then

                FirstCheck = FirstCheck + 1

                If FirstCheck = 1 Then
                    FirstEmployee = dg_search.Rows(i).Cells(2).Text
                    J = i
                End If

                If i <> J Then
                    If dg_search.Rows(i).Cells(2).Text <> FirstEmployee Then
                        EmployeeCount += 1
                    End If
                End If
            End If
        Next
        Return EmployeeCount
    End Function

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_search.DataSource = Nothing
        dg_search.DataBind()
        ClearControls()
    End Sub

    Private Sub ClearControls()
        txtEmployee.Text = String.Empty
        txtFromDate.Text = String.Empty
        txtToDate.Text = String.Empty
        Chk_Employee.Checked = True
        Chk_Date.Checked = True
        txt_CC.Text = String.Empty
        txt_BCC.Text = String.Empty
        txt_EmailAddress.Text = String.Empty
        lblErr.Text = String.Empty
        txtToDate.Text = String.Empty
        txt_EmailAddress.BackColor = Drawing.Color.White
    End Sub

End Class
