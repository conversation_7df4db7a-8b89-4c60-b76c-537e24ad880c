Public Class Viewer
    Inherits System.Web.UI.Page
    Protected WithEvents crv As CrystalDecisions.Web.CrystalReportViewer

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub

    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Put user code to initialize the page here
        'If Not Page.IsPostBack Then
        '    Dim result As TargetedFPC
        '    result = CType(Context.Handler, TargetedFPC)
        '    Dim content As String = "Name: " & result.MasterID & "<br>" & "Phone: " & result.Sheet1
        '    lbl.text = content
        'End If

        Dim reportName As String = Request("ReportName")

        Dim qryParam As Integer = Request.QueryString.Count
        Dim keys() As String = Request.QueryString.AllKeys
        'Dim paramValues As New ArrayList()
        Dim paramValues As New Hashtable()
        Dim i As Integer = 0
        For i = 1 To keys.Length - 1
            paramValues.Add(keys(i), Request.QueryString.Get(keys(i)))
        Next
        showReport(reportName, paramValues)
        If Not Page.IsPostBack Then
        End If
    End Sub

    Private Sub showReport(ByVal reportName As String, ByVal paramValues As Hashtable)
        ''Dim masterID As String = Request("MasterID")
        ''Dim sheet1 As String = Request("Sheet1")
        ''Dim sheet2 As String = Request("Sheet2")

        ''Dim rpt As New rpt_ReqWIP()
        Dim rpt As New CrystalDecisions.CrystalReports.Engine.ReportDocument()
        rpt.Load(Server.MapPath(reportName))

        Dim logInfo As New CrystalDecisions.Shared.TableLogOnInfo()
        Dim connInfo As New CrystalDecisions.Shared.ConnectionInfo()

        logInfo = rpt.Database.Tables(0).LogOnInfo
        connInfo = rpt.Database.Tables(0).LogOnInfo.ConnectionInfo

        '        connInfo.ServerName = Connection.server
        If Session("RptDataBase") = "" Then
            connInfo.DatabaseName = New Connection().DataBase
        Else
            connInfo.DatabaseName = Session("RptDataBase").ToString()
        End If
        Session("RptDataBase") = ""

        If Session("RptServer") = "" Then
            connInfo.ServerName = Connection.server
        Else
            connInfo.ServerName = Session("RptServer").ToString()
        End If
        Session("RptServer") = ""

        connInfo.UserID = New Connection().DBUserName
        connInfo.Password = Connection.Password

        logInfo.ConnectionInfo = connInfo
        rpt.Database.Tables(0).ApplyLogOnInfo(logInfo)

        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator
        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField()
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue()
            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)
            'Response.Write(CType(enumerator.Current, String))
            'Response.Write(" " & paramValues.Item(CType(enumerator.Current, String)))
            rpt.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While


        crv.ReportSource = rpt

    End Sub

#Region "Parameter Code --- Do Not Delete"
    '''Dim masterID As String = Request("MasterID")
    '''Dim sheet1 As String = Request("Sheet1")
    '''Dim sheet2 As String = Request("Sheet2")

    '''Dim rpt As New rpt_ReqWIP()
    ''''Dim rpt As CrystalDecisions.CrystalReports.Engine.ReportDocument
    ''''rpt.Load("rpt_ReqWIP.rpt")

    '''Dim logInfo As New CrystalDecisions.Shared.TableLogOnInfo()
    '''Dim connInfo As New CrystalDecisions.Shared.ConnectionInfo()

    '''    logInfo = rpt.Database.Tables(0).LogOnInfo
    '''    connInfo = rpt.Database.Tables(0).LogOnInfo.ConnectionInfo

    '''    connInfo.ServerName = Connection.server
    '''    connInfo.DatabaseName = Connection.DataBase
    '''    connInfo.UserID = Connection.DBUserName
    '''    connInfo.Password = Connection.Password

    '''    logInfo.ConnectionInfo = connInfo
    '''    rpt.Database.Tables(0).ApplyLogOnInfo(logInfo)

    '''Dim pMasterID As New CrystalDecisions.Shared.ParameterField()
    '''Dim vMasterID As New CrystalDecisions.Shared.ParameterDiscreteValue()
    '''    pMasterID.ParameterFieldName = "@Master_ID"
    '''    vMasterID.Value = masterID '5
    '''    pMasterID.CurrentValues.Add(vMasterID)
    '''    rpt.DataDefinition.ParameterFields(0).ApplyCurrentValues(pMasterID.CurrentValues)

    '''Dim pSheet1 As New CrystalDecisions.Shared.ParameterField()
    '''Dim vSheet1 As New CrystalDecisions.Shared.ParameterDiscreteValue()
    '''    pSheet1.ParameterFieldName = "@Sheet1"
    '''    vSheet1.Value = sheet1 '5
    '''    pSheet1.CurrentValues.Add(vSheet1)
    '''    rpt.DataDefinition.ParameterFields(1).ApplyCurrentValues(pSheet1.CurrentValues)

    '''Dim pSheet2 As New CrystalDecisions.Shared.ParameterField()
    '''Dim vSheet2 As New CrystalDecisions.Shared.ParameterDiscreteValue()
    '''    pSheet2.ParameterFieldName = "@Master_ID"
    '''    vSheet2.Value = sheet2 '5
    '''    pSheet2.CurrentValues.Add(vSheet2)
    '''    rpt.DataDefinition.ParameterFields(2).ApplyCurrentValues(pSheet2.CurrentValues)

    '''    crv.ReportSource = rpt

#End Region

End Class
