Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports System.Web.Mail

Public Class clsEmail

    Public Sub sentEmail(ByVal strTo As String, ByVal strFrom As String, ByVal strSubject As String, ByVal strBody As String)
        'Dim SMTPServer As String = ''"10.1.10.60"
        Dim SMTPServer As String = "mail.geo.tv"
        Dim FromEmail As String = strFrom
        Dim mailNew As New MailMessage

        mailNew.To = strTo
        'mailNew.Cc = "<EMAIL>"
        'mailNew.Bcc = ""

        mailNew.From = FromEmail
        mailNew.Subject = strSubject
        mailNew.BodyFormat = System.Web.Mail.MailFormat.Html
        SmtpMail.SmtpServer = SMTPServer

        ''Dim client As WebClient = New WebClient()
        ''Dim ftext As String = ""
        ''Dim strm As Stream = client.OpenRead("http://finosys/Sales_Portal/DealModule/FrmDesignEmailForDealUnlock.aspx?ID=" + id + "&RecId=" + recid)
        ''' Dim strm As Stream = client.OpenRead("http://software-050/Sales_Portal/DealModule/FrmDesignEmailForDealUnlock.aspx?ID=" + id + "&RecId=" + recid)
        ''Dim sr As StreamReader = New StreamReader(strm)
        ''Dim str2 As String = sr.ReadToEnd()
        ''strm.Close()
        'Dim msgRoshni As String = " <p><font face='Verdana' size='2' color='#000080'>This is a system generated "
        'msgRoshni += " email message for technical information Contact Muhammad Saood Ext: 6237 , 6355 Mob : 0345-2721001</font></p> "

        mailNew.Body = strBody
        SmtpMail.Send(mailNew)

    End Sub

    Public Sub sentEmail_Reminder(ByVal strTo As String, ByVal strFrom As String, ByVal strCC As String, ByVal strBCC As String, ByVal strSubject As String, ByVal strBody As String)
        'Dim SMTPServer As String = "10.1.10.60"
        Dim SMTPServer As String = "mail.geo.tv"
        Dim FromEmail As String = strFrom
        Dim mailNew As New MailMessage

        mailNew.To = strTo
        mailNew.Cc = strCC
        mailNew.Bcc = strBCC

        mailNew.From = FromEmail
        mailNew.Subject = strSubject
        mailNew.BodyFormat = System.Web.Mail.MailFormat.Html
        SmtpMail.SmtpServer = SMTPServer

        ''Dim client As WebClient = New WebClient()
        ''Dim ftext As String = ""
        ''Dim strm As Stream = client.OpenRead("http://finosys/Sales_Portal/DealModule/FrmDesignEmailForDealUnlock.aspx?ID=" + id + "&RecId=" + recid)
        ''' Dim strm As Stream = client.OpenRead("http://software-050/Sales_Portal/DealModule/FrmDesignEmailForDealUnlock.aspx?ID=" + id + "&RecId=" + recid)
        ''Dim sr As StreamReader = New StreamReader(strm)
        ''Dim str2 As String = sr.ReadToEnd()
        ''strm.Close()
        'Dim msgRoshni As String = " <p><font face='Verdana' size='2' color='#000080'>This is a system generated "
        'msgRoshni += " email message for technical information Contact Muhammad Saood Ext: 6237 , 6355 Mob : 0345-2721001</font></p> "

        mailNew.Body = strBody
        SmtpMail.Send(mailNew)

    End Sub

End Class
