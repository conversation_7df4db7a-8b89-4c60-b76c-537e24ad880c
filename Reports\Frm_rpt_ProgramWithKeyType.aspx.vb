Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ProgramWithKeyType
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkKeyType.Checked = True
                ChkProgram.Checked = True
                LbKeyType.Enabled = False
                ddlPrograms.Enabled = False

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        'Dim qryString As String

        'Dim ContentTypeID As Integer
        'Dim ProgramID As Integer
        'Dim KeyTypes As String = ""

        'ContentTypeID = ddlContentType.SelectedValue
        'If ChkProgram.Checked = True Then
        '    ProgramID = -1
        'Else
        '    ProgramID = ddlPrograms.SelectedValue
        'End If
        'If chkKeyType.Checked = True Then
        '    KeyTypes = "All"
        'Else
        '    For a As Integer = 0 To LbKeyType.Items.Count - 1
        '        If LbKeyType.Items(a).Selected = True Then
        '            KeyTypes = KeyTypes + LbKeyType.Items(a).Text + ";"
        '        End If

        '    Next

        'End If
        'qryString = "crvViewer.aspx?reportname=" + "rpt_test.rpt&" + "@Program= " & ProgramID & "&@ContentTypeID = " & ContentTypeID & "&@KeyType='" & KeyTypes & "'"
        'reportviewerframe.Attributes.Add("src", qryString)
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ProgramWithKeytypeReport_New.rpt"

            Dim Program As String
            Dim ContentTypeID As String
            Dim KeyType As String

            If ddlContentType.SelectedValue = 23 Then
                If ChkProgram.Checked = True Then
                    Program = "-1"
                Else
                    Program = ddlPrograms.SelectedValue
                End If
            Else
                Program = "-1"
            End If

            ContentTypeID = ddlContentType.SelectedValue

            For Each item As ListItem In LbKeyType.Items
                If item.Selected = True Then
                    strKeyWords += item.Text.ToString + ";"
                End If
            Next
            If strKeyWords = "" Or chkKeyType.Checked = True Then
                strKeyWords = "All"
            End If
            KeyType = strKeyWords
         
            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ProgramWithKeytypeReport_New.rpt&" + "@Program=" & Program & "&@ContentTypeID=" & ContentTypeID & "&@KeyType=" & KeyType

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ProgramWithKeytypeReport_New.rpt&@Program=" + Program + "&@ContentTypeID=" + ContentTypeID + "&@KeyType=" + KeyType + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)


            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_ProgramWithKeyType.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try


        'Try
        '    Dim paramValues As New Hashtable
        '    Dim strRptPath As String
        '    Dim strKeyWords As String = ""
        '    Dim rpt As String
        '    rpt = "rpt_ProgramWithKeytypeReport_New.rpt"

        '    If ddlContentType.SelectedValue = 23 Then
        '        If ChkProgram.Checked = True Then
        '            paramValues.Add("@Program", -1)
        '        Else
        '            paramValues.Add("@Program", ddlPrograms.SelectedValue)
        '        End If
        '    Else
        '        paramValues.Add("@Program", -1)
        '    End If

        '    paramValues.Add("@ContentTypeID", ddlContentType.SelectedValue)

        '    For Each item As ListItem In LbKeyType.Items
        '        If item.Selected = True Then
        '            strKeyWords += item.Text.ToString + ";"
        '        End If
        '    Next
        '    If strKeyWords = "" Or chkKeyType.Checked = True Then
        '        strKeyWords = "All"
        '    End If
        '    paramValues.Add("@KeyType", strKeyWords)
        '    LoadPDFReport("rpt_ProgramWithKeytypeReport_New.rpt", paramValues)

        'Catch ex As Exception
        '    Throw
        'End Try

    End Sub

   
    Protected Sub ddlContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlContentType.SelectedIndexChanged
        If ddlContentType.SelectedValue = 29 Then
            ddlPrograms.Enabled = False
        Else
            ddlPrograms.Enabled = True

        End If
    End Sub

    'Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)
    '    '--- Exporting Crystal Report to PDF process...
    '    Dim valpWhere As New ParameterDiscreteValue
    '    Dim valpFilter As New ParameterDiscreteValue

    '    Dim ExportPath As String = Server.MapPath("~/reports")
    '    'Server.MapPath("~\reports")

    '    'Dim crReportDocument As New ReportDocument()
    '    Dim RptName As String = rpt

    '    Rpt = Server.MapPath("~/reports") & "\" & rpt
    '    crReportDocument.Load(rpt)
    '    'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
    '    ApplyConnectionInfo(crReportDocument)


    '    Dim keyCollection As ICollection = paramValues.Keys()
    '    Dim enumerator As IEnumerator = keyCollection.GetEnumerator

    '    While enumerator.MoveNext
    '        Dim param As New CrystalDecisions.Shared.ParameterField
    '        Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
    '        Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

    '        param.ParameterFieldName = CType(enumerator.Current, String)
    '        paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
    '        param.CurrentValues.Add(paramValue)

    '        crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
    '    End While

    '    'crv.ReportSource = crReportDocument


    '    Dim expOptions As ExportOptions
    '    Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

    '    'destinationOption.DiskFileName = ExportPath & "\RptInventoryList.pdf"
    '    destinationOption.DiskFileName = ExportPath & "\" & Replace(RptName, ".rpt", ".pdf")
    '    expOptions = crReportDocument.ExportOptions
    '    expOptions.DestinationOptions = destinationOption

    '    expOptions.ExportDestinationType = ExportDestinationType.DiskFile

    '    expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
    '    crReportDocument.Export()
    '    crReportDocument.Close()

    '    'Dim scriptString As String = "<script type='text/javascript'>" & _
    '    '    "window.onload = function(){" & _
    '    '    "var url = '" + "../reports/RptInventoryList.pdf" + "';" & _
    '    '    "var winPop = window.open(url,'winPop');" & _
    '    '    "}" & _
    '    '    "</script>"

    '    Dim scriptString As String = "<script type='text/javascript'>" & _
    '       "window.onload = function(){" & _
    '       "var url = '" + "../reports/" + Replace(RptName, ".rpt", ".pdf") + "';" & _
    '       "var winPop = window.open(url,'winPop');" & _
    '       "}" & _
    '       "</script>"
    '    'Register this script to avoid font size increase automatically when viewing the report
    '    ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)

    '    'This was causing to enlarge the font size automatically
    '    'Response.Write(scriptString)   

    'End Sub

    'Public Function convertAndOpeninPDF(ByVal Rptname As String, ByVal paramValues As Hashtable) As String
    '    Dim pathandname As String
    '    Dim ReportName As String
    '    Dim DiskOpts As CrystalDecisions.Shared.DiskFileDestinationOptions = New CrystalDecisions.Shared.DiskFileDestinationOptions

    '    ReportName = Replace(Rptname, ".rpt", ".pdf")
    '    'ReportName = Rptname
    '    pathandname = Server.MapPath(Rptname)

    '    'Try
    '    Dim RptDocument As New CrystalDecisions.CrystalReports.Engine.ReportDocument

    '    RptDocument.Load(pathandname)
    '    ApplyConnectionInfo(RptDocument)

    '    'Dim paramValues As New Hashtable
    '    'paramValues.Add("@Datefrom", "05/04/2008")
    '    'paramValues.Add("@dateto", "05/04/2008")

    '    Dim keyCollection As ICollection = paramValues.Keys()
    '    Dim enumerator As IEnumerator = keyCollection.GetEnumerator
    '    While enumerator.MoveNext
    '        Dim param As New CrystalDecisions.Shared.ParameterField
    '        Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
    '        param.ParameterFieldName = CType(enumerator.Current, String)
    '        paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
    '        param.CurrentValues.Add(paramValue)
    '        RptDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
    '    End While

    '    Dim exportpath As String = Server.MapPath(ReportName)

    '    Dim expOptions As ExportOptions
    '    Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions
    '    destinationOption.DiskFileName = exportpath
    '    expOptions = RptDocument.ExportOptions
    '    expOptions.DestinationOptions = destinationOption
    '    expOptions.ExportDestinationType = ExportDestinationType.DiskFile
    '    expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
    '    RptDocument.Export()
    '    RptDocument.Close()

    '    Return ReportName

    '    'Catch ex As Exception
    '    '    Return Nothing
    '    'End Try
    'End Function
    'Private Sub ApplyConnectionInfo(ByRef pRptDocument As ReportDocument)

    '    Dim logOnInfo As New CrystalDecisions.Shared.TableLogOnInfo
    '    logOnInfo = pRptDocument.Database.Tables(0).LogOnInfo

    '    Dim strConnection As String = "Data Source=SOFT-SERVER\SOFT;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********"
    '    Dim connection As New SqlClient.SqlConnection(strConnection)

    '    Dim connectionInfo As New CrystalDecisions.Shared.ConnectionInfo
    '    connectionInfo = pRptDocument.Database.Tables.Item(0).LogOnInfo.ConnectionInfo
    '    ' Set the Connection parameters.

    '    'connectionInfo.DatabaseName = con.DatabaseName
    '    'connectionInfo.ServerName = con.ServerName
    '    connectionInfo.Password = "**********"
    '    connectionInfo.UserID = "sa"
    '    logOnInfo.ConnectionInfo = connectionInfo
    '    pRptDocument.Database.Tables(0).ApplyLogOnInfo(logOnInfo)

    '    Dim sec As Section
    '    Dim rptObj As ReportObject
    '    Dim subRpt As CrystalDecisions.CrystalReports.Engine.SubreportObject

    '    For Each sec In pRptDocument.ReportDefinition.Sections
    '        For Each rptObj In sec.ReportObjects
    '            If rptObj.Kind = CrystalDecisions.[Shared].ReportObjectKind.SubreportObject Then
    '                subRpt = CType(rptObj, CrystalDecisions.CrystalReports.Engine.SubreportObject)
    '                subRpt.OpenSubreport(subRpt.SubreportName).Database.Tables(0).ApplyLogOnInfo(logOnInfo)
    '            End If
    '        Next
    '    Next
    'End Sub


    Protected Sub ChkProgram_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If ChkProgram.Checked = True Then
            ddlPrograms.Enabled = False
        Else
            ddlPrograms.Enabled = True
        End If
    End Sub

    Protected Sub chkKeyType_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkKeyType.Checked = True Then
            LbKeyType.Enabled = False
        Else
            LbKeyType.Enabled = True
        End If
    End Sub
End Class
