
Partial Class ApplicationSetup_frmSubContentType
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_ContentType.DataSource = New BusinessFacade.ContentType().GetRecords()
        ddl_ContentType.DataTextField = "ContentTypeName"
        ddl_ContentType.DataValueField = "ContentTypeID"
        ddl_ContentType.DataBind()
        ddl_ContentType.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_SubContentTypeID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_SubContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_SubContentTypeName.Text = "" Then
            lblErr.Text = "Please insert SubContent Type!!"
        ElseIf ddl_ContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Content Type!!"
        Else
            Dim objSubContentType As New BusinessFacade.SubContentType()
            objSubContentType.SubContentTypeName = txt_SubContentTypeName.Text
            objSubContentType.ContentTypeID = ddl_ContentType.SelectedValue
            objSubContentType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim objSubContentType As New BusinessFacade.SubContentType()
        objSubContentType.SubContentTypeID = txt_SubContentTypeID.Text
        objSubContentType.SubContentTypeName = txt_SubContentTypeName.Text
        objSubContentType.ContentTypeID = ddl_ContentType.SelectedValue
        objSubContentType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_SubContentType.DataSource() = New BusinessFacade.SubContentType().GetRecords()
        dg_SubContentType.DataBind()
        'dg_SubContentType.Columns(0).Visible = False
        'dg_SubContentType.Columns(2).Visible = False
    End Sub

    Protected Sub dg_SubContentType_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_SubContentType.RowCreated
        e.Row.Cells(1).Visible = False
        e.Row.Cells(3).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_SubContentType.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_SubContentType.SelectedIndex.ToString
        txt_SubContentTypeID.Text = Convert.ToInt32(dg_SubContentType.Rows(I).Cells(1).Text)
        txt_SubContentTypeName.Text = dg_SubContentType.Rows(I).Cells(2).Text
        ddl_ContentType.SelectedValue = Convert.ToInt32(dg_SubContentType.Rows(I).Cells(3).Text)
        dg_SubContentType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_SubContentTypeID.Text = "" Then
                lblErr.Text = "Please Select ContentType First!!"
            Else
                Dim objSubContentType As New BusinessFacade.SubContentType()
                objSubContentType.SubContentTypeID = txt_SubContentTypeID.Text
                objSubContentType.DeleteRecord(objSubContentType.SubContentTypeID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_SubContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This ContentType is Already in Used !"
            clrscr()
            dg_SubContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_SubContentTypeName.Text = String.Empty
        txt_SubContentTypeID.Text = String.Empty
        ddl_ContentType.SelectedIndex = 0
    End Sub

    'Protected Sub dg_SubContentType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_SubContentType.PageIndexChanging
    '    dg_SubContentType.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_SubContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
