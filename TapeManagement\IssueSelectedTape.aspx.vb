Imports System.Data

Partial Class SearchEngine_IssueSelectedTape
    Inherits System.Web.UI.Page
    Dim strCommand As String
    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

    Dim dtEntIssue As DataTable
    Dim dtEntIssueNew As DataTable
    Dim dtNewsIssue As DataTable
    Dim dtNewsIssueNew As DataTable

    Dim dtReturn As DataTable
    Dim dtReturnNew As DataTable

    Dim cls As New BusinessFacade.SearchEngine
    Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                Chk_Program.Checked = True
                Chk_TapeNo.Checked = True
                txtEntryDate_Issue.Text = Date.Now().ToString("dd-MMM-yyyy")

                FillGrids()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub FillGrids()
        Dim cls As New BusinessFacade.SearchEngine

        dg_TapeIssuance_SlugWise.DataSource = cls.GetSETapeCollection(lbl_UserName.Text, "News")
        dg_TapeIssuance_SlugWise.Columns(3).Visible = True
        dg_TapeIssuance_SlugWise.DataBind()
        dg_TapeIssuance_SlugWise.Columns(3).Visible = False

        dg_TapeIssue.DataSource = cls.GetSETapeCollection(lbl_UserName.Text, "Ent")
        dg_TapeIssue.Columns(3).Visible = True
        dg_TapeIssue.DataBind()
        dg_TapeIssue.Columns(3).Visible = False


        If Not dg_TapeIssuance_SlugWise.Rows.Count > 0 Then
            lblNewsTapes.Visible = False
        End If

        If Not dg_TapeIssue.Rows.Count > 0 Then
            lblEntTapes.Visible = False
        End If


        Dim dr As GridViewRow

        For Each dr In dg_TapeIssuance_SlugWise.Rows
            Dim MyCheckBox As CheckBox = CType(dr.Cells(1).Controls(1), CheckBox)
            If dr.Cells(6).Text = "Not-Available" Then
                MyCheckBox.Enabled = False
            End If
        Next

        For Each dr In dg_TapeIssue.Rows
            Dim MyCheckBox As CheckBox = CType(dr.Cells(1).Controls(1), CheckBox)
            If dr.Cells(6).Text = "Not-Available" Then
                MyCheckBox.Enabled = False
            End If
        Next


    End Sub

    Protected Sub bttnIssue_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnIssue.Click

      
        bttnIssue.Enabled = False

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_IssueToEmp.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        '********************* End ********************' 

        If EmployeeID = 0 Then
            Err_Employee_Issue.Visible = True
        Else
            Err_Employee_Issue.Visible = False
        End If

        If EmployeeID <> "0" And txtEntryDate_Issue.Text <> "" Then

            ''**********************************************''
            ''************ Get Department ID ***************''
            ''**********************************************''

            Dim DepartmentID As Integer
            Dim objDepartmentID As New BusinessFacade.TapeIssuance()
            objDepartmentID.EmployeeID = EmployeeID
            DepartmentID = objDepartmentID.GetDepartmentID_by_EmployeeID(objDepartmentID.EmployeeID)

            '********************* End ********************' 

            Dim EmployeeName As String = "0"

            Dim P As Integer
            Dim Count As Integer
            Dim ct As Integer = 0

            For P = 0 To dg_TapeIssue.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(P).Cells(1).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then

                    ObjTapeNumber.TapeNumber = dg_TapeIssue.Rows(P).Cells(2).Text
                    EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber(dg_TapeIssue.Rows(P).Cells(2).Text)

                    If (EmployeeName <> "0" And Not EmployeeName Is Nothing) Then
                        dg_TapeIssue.Rows(P).BackColor = Drawing.Color.DarkKhaki
                        MyCheckBox.Enabled = False
                        MyCheckBox.Checked = False
                        ct = ct + 1
                    Else
                        Count = Count + 1
                    End If

                End If
            Next

            Dim q As Integer
            Dim Cnt As Integer
            For q = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(q).Cells(1).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then

                    ObjTapeNumber.TapeNumber = dg_TapeIssuance_SlugWise.Rows(q).Cells(2).Text
                    EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber(dg_TapeIssuance_SlugWise.Rows(q).Cells(2).Text)

                    If (EmployeeName <> "0" And Not EmployeeName Is Nothing) Then
                        dg_TapeIssuance_SlugWise.Rows(q).BackColor = Drawing.Color.DarkKhaki
                        MyCheckBox.Enabled = False
                        MyCheckBox.Checked = False
                        ct = ct + 1
                    Else
                        Cnt = Cnt + 1
                    End If


                End If
            Next


            If ct > 0 Then
                lblErr_Issue.Text = "Few record(s) are already issued, which for reference highlighted in DarkKhaki color."
                lblErr_Issue.Visible = True
                Exit Sub
            End If
            

            Dim IsTapeChecked As Integer = 0

            If Count <> 0 Then

                Dim T As Integer
                For T = 0 To dg_TapeIssue.Rows.Count - 1
                    Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(T).Cells(1).Controls(1), CheckBox)
                    If MyCheckBox.Checked = True Then
 

                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = CInt(dg_TapeIssue.Rows(T).Cells(3).Text)
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then

                            imgWait.Visible = True

                            ''*******************************************************'
                            ''***************** Save Tape Issuance ******************'
                            ''*******************************************************'
                            Dim LibID As Integer = dg_TapeIssue.Rows(T).Cells(3).Text
                            Dim ProgID As Integer = 0

                            strCommand = "TapeIssuance_OneByOne_SaveRecord_ArchivalEntry_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_Issue.Text & "'," & LibID & "," & ProgID
                            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                            If Con.State = Data.ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd1By1_Save.Connection = Con
                            cmd1By1_Save.executenonquery()
                            Con.Close()
                            lblErr_Issue.Text = "Tapes have been Issued!!"
                            IsTapeChecked = 1

                            cls.UpdateSETapeCollection(dg_TapeIssue.Rows(T).Cells(2).Text)

                            ''*********************** End **************************'

                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
                        End If
                        ''************ End *************''
                        ''******************************''

                    End If
                Next

                Try
                    If IsTapeChecked = 1 Then
                        Dim ObjReminder As New BusinessFacade.ReminderService()
                        ObjReminder.EmployeeName = txt_IssueToEmp.Text
                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                        If EmailAddress <> "N/A" Then
                            Dim Arr As Array = Split(EmailAddress, "@")
                            If Arr.Length = 2 Then
                                sendissuancemail(EmailAddress)
                            Else
                                lblErr_Issue.Text += "But Email Not send due to Invalid Email Address!"
                            End If
                        Else
                            lblErr_Issue.Text += "But Email Not send b/c Email Address not Exists!"

                        End If
                    End If
                Catch ex As Exception

                End Try

                'ViewState("dtEntIssueNew") = Nothing
                'dtEntIssueNew = Nothing

                'dg_TapeIssue.DataSource = Nothing
                'dg_TapeIssue.DataBind()

                lblEntTapes.Visible = False

            End If


            If Cnt <> 0 Then
                Dim f As Integer
                For f = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
                    Dim MyChkBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(f).Cells(1).Controls(1), CheckBox)
                    If MyChkBox.Checked = True Then

                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dg_TapeIssuance_SlugWise.Rows(f).Cells(3).Text
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then

                            imgWait.Visible = True

                            Dim ProgID As Integer = 0
                            Dim LibID As Integer = dg_TapeIssuance_SlugWise.Rows(f).Cells(3).Text

                            strCommand = "TapeIssuance_OneByOne_SaveRecord_ArchivalEntry_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_Issue.Text & "'," & LibID & "," & ProgID
                            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                            If Con.State = Data.ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd1By1_Save.Connection = Con
                            cmd1By1_Save.executenonquery()
                            Con.Close()
                            lblErr_Issue.Text = "Tapes have been Issued!!"
                            IsTapeChecked = 1

                            cls.UpdateSETapeCollection(dg_TapeIssuance_SlugWise.Rows(f).Cells(2).Text)

                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
                        End If
                        ''************ End *************''
                        ''******************************''



                    End If
                Next

                Try
                    If IsTapeChecked = 1 Then

                        Dim ObjReminder As New BusinessFacade.ReminderService()
                        ObjReminder.EmployeeName = txt_IssueToEmp.Text
                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                        If EmailAddress <> "N/A" Then
                            Dim Arr As Array = Split(EmailAddress, "@")
                            If Arr.Length = 2 Then
                                sendissuancemail_Slug(EmailAddress)
                            Else
                                lblErr_Issue.Text += "But Email Not send due to Invalid Email Address!"
                            End If
                        Else
                            lblErr_Issue.Text += "But Email Not send b/c Email Address not Exists!"

                        End If

                    End If
                Catch ex As Exception

                End Try


                'ViewState("dtNewsIssueNew") = Nothing
                lblNewsTapes.Visible = False

                Clrscr()

                'dg_TapeIssuance_SlugWise.DataSource = Nothing
                'dg_TapeIssuance_SlugWise.DataBind()

            End If
        ElseIf txtEntryDate_Issue.Text = "" Then
                    lblErr_Issue.Text = "Please Select Date !!"
        Else
                    lblErr_Issue.Text = "Please Select Employee !!"
        End If

        ''********* Clear Controls ***********''

        FillGrids()

        bttnIssue.Enabled = True
        imgWait.Visible = False

    End Sub

    Private Sub Clrscr()
        Dim N As Integer
        For N = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(N).Cells(0).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next
        txt_TapeNo.Text = String.Empty

    End Sub

    Public Sub sendissuancemail(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>IssuanceDate  </b> at <b>IssuanceTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issued by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_IssueToEmp.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(P).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = txt_IssueToEmp.Text

                If txt_IssueToEmp.Text.Contains("P-") Then
                    empname = txt_IssueToEmp.Text
                End If


                '' get Email Address


                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = txt_IssueToEmp.Text
                EmailAddress = ObjReminder.GetEmailAddress()



                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empissue", empname)
                headermessage = headermessage.Replace("IssuanceDate", txtEntryDate_Issue.Text)
                headermessage = headermessage.Replace("IssuanceTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Issuedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_TapeIssue.Rows(P).Cells(2).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_Issue.Text)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_TapeIssue.Rows(P).Cells(5).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Issuedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)
                iterativemessage = iterativemessage.Replace("IssuetoEmp", txt_IssueToEmp.Text)
                'IssuetoEmp
                fullheader = fullheader + iterativemessage

                Sno = Sno + 1



            End If
        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage


        'If EmailAddress = "N/A" Then
        '    lblErr_Issue.Text = "Tape Issuance Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If


        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")

    End Sub

    Public Sub sendissuancemail_Slug(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>IssuanceDate  </b> at <b>IssuanceTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issued by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_IssueToEmp.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(P).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = txt_IssueToEmp.Text

                If txt_IssueToEmp.Text.Contains("P-") Then
                    empname = txt_IssueToEmp.Text
                End If


                '' get Email Address


                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = txt_IssueToEmp.Text
                EmailAddress = ObjReminder.GetEmailAddress()



                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empissue", empname)
                headermessage = headermessage.Replace("IssuanceDate", txtEntryDate_Issue.Text)
                headermessage = headermessage.Replace("IssuanceTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Issuedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_TapeIssuance_SlugWise.Rows(P).Cells(2).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_Issue.Text)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_TapeIssuance_SlugWise.Rows(P).Cells(5).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Issuedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)
                iterativemessage = iterativemessage.Replace("IssuetoEmp", txt_IssueToEmp.Text)
                'IssuetoEmp
                fullheader = fullheader + iterativemessage

                Sno = Sno + 1



            End If
        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage


        'If EmailAddress = "N/A" Then
        '    lblErr_Issue.Text = "Tape Issuance Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")

    End Sub

    Protected Sub bttnClearSel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClearSel.Click
        Dim L As Integer
        For L = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(L).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

        Dim M As Integer
        For M = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(M).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

        lblErr_Issue.Text = String.Empty
        txt_IssueToEmp.Text = String.Empty
        txtEntryDate_Issue.Text = Date.Now().ToString("dd-MMM-yyyy")

        'dg_TapeIssuance_SlugWise.DataSource = Nothing
        'dg_TapeIssuance_SlugWise.DataBind()

        'dg_TapeIssue.DataSource = Nothing
        'dg_TapeIssue.DataBind()

        'ViewState("dtEntIssueNew") = Nothing

        'ViewState("dtNewsIssueNew") = Nothing

    End Sub

    Protected Sub btnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnDelete.Click

        Dim f As Integer
        For f = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            Dim MyChkBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(f).Cells(1).Controls(1), CheckBox)
            If MyChkBox.Checked = True Then
                cls.DeleteSETapeCollection(dg_TapeIssuance_SlugWise.Rows(f).Cells(2).Text)
            Else
                'lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
            End If
            ''************ End *************''
          
        Next

        Dim T As Integer
        For T = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(T).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                cls.DeleteSETapeCollection(dg_TapeIssue.Rows(T).Cells(2).Text)
            Else
                'lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
            End If
            ''************ End *************''
           
        Next

        lblErr_Issue.Text = "operation perform successufully."
        lblErr_Issue.Visible = True

        FillGrids()

    End Sub


End Class
