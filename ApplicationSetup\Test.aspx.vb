Imports System.Data.SqlClient
Imports System.Data
Partial Class ApplicationSetup_Test
    Inherits System.Web.UI.Page
    Dim DS As New System.Data.DataSet
    Dim DS1 As New System.Data.DataSet
    Dim connStr As String = "server=ccs-server\roshni;database=CFPCM;uid=sa;pwd=**********"
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        CreateTable()
    End Sub
    Private Sub CreateTable()
        Dim cmd_channel As New System.Data.SqlClient.SqlCommand
        cmd_channel.CommandText = "Proc_ProgramRating_Channels"
        cmd_channel.Connection = Con
        cmd_channel.CommandType = CommandType.StoredProcedure
        Dim da_Channel As New System.Data.SqlClient.SqlDataAdapter
        da_Channel.SelectCommand = cmd_channel
        da_Channel.Fill(DS1, "Channel")

        Dim K As Integer
        Dim str As String
        str = "<table id=""Table5"" style=""FONT-SIZE: 12px; Font:Arial;Z-INDEX: 102; LEFT: 70px; POSITION: absolute; TOP: 30px;"" border=1 cellspacing=0 cellpadding=0 bgcolor=""White"">"
        str += "<tr>"
        str += "<td colspan=""13"">CFPCM Channel wise Data Status</td>"
        str += "</tr>"
        str += "<tr  bgcolor=""Yellow"">"
        str += "<td>-</td>"
        str += "<td width=""30px"" align=""Center"">Jan</td>"
        str += "<td width=""30px"" align=""Center"">Feb</td>"
        str += "<td width=""30px"" align=""Center"">Mar</td>"
        str += "<td width=""30px"" align=""Center"">Apr</td>"
        str += "<td width=""30px"" align=""Center"">May</td>"
        str += "<td width=""30px"" align=""Center"">Jun</td>"
        str += "<td width=""30px"" align=""Center"">Jul</td>"
        str += "<td width=""30px"" align=""Center"">Aug</td>"
        str += "<td width=""30px"" align=""Center"">Sep</td>"
        str += "<td width=""30px"" align=""Center"">Oct</td>"
        str += "<td width=""30px"" align=""Center"">Nov</td>"
        str += "<td width=""30px"" align=""Center"">Dec</td>"
        str += "</tr>"

        For K = 0 To DS1.Tables("Channel").Rows.Count - 1
            Dim cmd_grid As New System.Data.SqlClient.SqlCommand
            cmd_grid.CommandText = "Proc_ProgramRating_Asmat"
            cmd_grid.Connection = Con
            cmd_grid.CommandType = CommandType.StoredProcedure
            cmd_grid.Parameters.AddWithValue("@Channel", DS1.Tables("Channel").Rows(K).Item(0))
            Dim da_Grid As New System.Data.SqlClient.SqlDataAdapter
            da_Grid.SelectCommand = cmd_grid
            da_Grid.Fill(DS)

            Dim Channel As String = DS1.Tables("Channel").Rows(K).Item(0)

            str += "<tr  bgcolor=""Yellow"">"
            str += "<td width=""70px"">"
            str += Channel
            str += "</td>"
            ''*****************************''
            Dim bg_Jan As String = ""
            Dim bgJan As String = bg_Jan
            Dim J As Integer

            Dim MonthCnt As Integer
            For MonthCnt = 0 To 11
                For J = 0 To DS.Tables(0).Rows.Count - 1
                    If DS.Tables(0).Rows(J).Item(0) = DS.Tables(2).Rows(MonthCnt).Item(1).ToString And DS.Tables(0).Rows(J).Item(1) = DS.Tables(1).Rows(K).Item(0) Then
                        bg_Jan = DS.Tables(0).Rows(J).Item(2)
                        str += "<td " + "bgcolor =" + bg_Jan + "></td>"
                    End If
                Next

            Next
            str += "</tr>"
        Next
        str += "</table>"
        str += "<table style=""FONT-SIZE: 12px; Font:Arial;Z-INDEX: 102; LEFT: 70px; POSITION: absolute; TOP: 160px;"" border=1 cellspacing=0 cellpadding=0 bgcolor=""White"">"
        str += "<tr><td> </td></tr><tr></tr>"
        str += "<tr>"
        str += "<td width=""30px"" bgcolor=""Green""></td><td>Complete</td><td></td><td width=""30px"" bgcolor=""Red""></td><td>Incomplete</td><td></td><td width=""30px"" bgcolor=""Blue""></td><td>Not Exists</td>"
        str += "</tr>"
        str += "</table>"

        Response.Write(str)

    End Sub
End Class
