<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmExcelUpload.aspx.vb" Inherits="FrmExcelUpload" title=":: Upload Data ::" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
   <table style="width: 100%">
        <tr>
            <td style="width: 100%; height: 28px;">
                <asp:LinkButton ID="LinkButton1" runat="server" CssClass="labelheading" Font-Underline="True">Home</asp:LinkButton>
                <asp:Label ID="Label5" runat="server" CssClass="labelheading" Font-Bold="True" Font-Size="Medium"
                    Text=">> Upload Data" Font-Underline="True"></asp:Label></td>
        </tr>
        <tr>
            <td align="center" style="width: 100%" valign="middle">
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
        </tr>
       <tr>
           <td style="width: 100%; height: 33px; background-color: #cccc99">
               <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Names="Script MT Bold"
                   Font-Size="X-Large" ForeColor="#404000" Text=": : Generate New Tape Number : :"></asp:Label></td>
       </tr>
        <tr>
            <td style="width: 100%">
                <table class="mytext">
                    <tr>
                        <td style="width: 100px">
                            Tape Type</td>
                        <td style="width: 100px">
                            Tape Number</td>
                        <td style="width: 100px">
                            Station</td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddl_TapeType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext"></asp:TextBox></td>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddlStation" runat="server" CssClass="myddl" Width="107px">
                            </asp:DropDownList></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
       <tr>
           <td class="bottomMain" style="width: 100%; height: 28px">
               &nbsp;<asp:Button ID="bttnTapeNumber" runat="server" Text="Generate Tape Number" Width="179px" CssClass="buttonA" Font-Bold="True" />
               <asp:Button
                                ID="bttnClear_TapeNumber" runat="server" Text="Clear" Width="120px" CssClass="buttonA" Font-Bold="True" /></td>
       </tr>
       <tr>
           <td style="width: 100%">
           </td>
       </tr>
       <tr>
           <td style="width: 100%; height: 33px; background-color: #cccc99">
               <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Script MT Bold"
                   Font-Size="X-Large" ForeColor="#404000" Text=": : Excel Upload Area For News : :"></asp:Label></td>
       </tr>
        <tr>
            <td style="width: 100%">
                <table style="width: 807px" class="mytext">
                    <tr>
                        <td colspan="1" style="height: 26px">
                            File Path</td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 26px">
                            <asp:FileUpload ID="FileUpload" runat="server" Width="627px" /></td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 21px">
                <asp:Label ID="lblFilePath" runat="server" Visible="False"></asp:Label>
                <asp:Label ID="lblFileName" runat="server" Visible="False"></asp:Label></td>
                    </tr>
                </table>
                </td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 29px;">
                &nbsp;
                            <asp:Button ID="bttnLoadExcel" runat="server" Text="Load Excel" CssClass="buttonA" Font-Bold="True" Width="126px" />&nbsp;<asp:Button
                                ID="bttnSave" runat="server" Text="Save" Width="90px" CssClass="buttonA" Font-Bold="True" />&nbsp;
                <asp:Button
                                ID="bttnClear_ExcelArea" runat="server" Text="Clear" Width="97px" CssClass="buttonA" Font-Bold="True" /></td>
        </tr>
        <tr>
            <td style="width: 100px; height: 21px;">
                &nbsp;
            </td>
        </tr>
        <tr>
            <td valign="top">
                <asp:GridView ID="dgExcel" runat="server" AutoGenerateColumns="False" Width="100%" CssClass="gridContent">
                    <Columns>
                        <asp:BoundField DataField="S No" HeaderText="S No" />
                        <asp:BoundField DataField="Entry Date" HeaderText="Entry Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False"  />
                        <asp:BoundField DataField="Reporter Slug" HeaderText="Reporter Slug" />
                        <asp:BoundField DataField="Tape Number" HeaderText="Tape Number" />
                        <asp:BoundField DataField="Tape Type" HeaderText="Tape Type" />
                        <asp:BoundField DataField="Start Time" HeaderText="Start Time" />
                        <asp:BoundField DataField="End Time" HeaderText="End Time" />
                        <asp:BoundField DataField="Keyword" HeaderText="Keyword" />
                        <asp:BoundField DataField="Reporter" HeaderText="Reporter" />
                    </Columns>
                    <AlternatingRowStyle CssClass="gridAlternate" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
       <tr>
           <td style="width: 100%; height: 33px; background-color: #cccc99">
               <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Names="Script MT Bold"
                   Font-Size="X-Large" ForeColor="#404000" Text=" :  : Sample Excel Format for Upload Data :  :"></asp:Label></td>
       </tr>
       <tr>
           <td style="width: 100%">
           </td>
       </tr>
        <tr>
            <td style="width: 100%; height: 18px;">
             <a class="Link" href=Sample.xls>Download Excel Sheet to Place Your Spots</a>
             
                &nbsp;<asp:LinkButton Visible="false" ID="lnkSample" runat="server" Font-Size="11pt">Download Sample Format</asp:LinkButton></td>
                  <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
        </tr>
    </table>
   
     
</asp:Content>

