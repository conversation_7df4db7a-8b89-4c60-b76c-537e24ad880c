<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmEntertainmentKeyword.aspx.vb" Inherits="ApplicationSetup_frmEntertainmentKeyword" title="Home > Entertainment Keyword > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table width="100%">
        <tr>
            <td class="labelheading" style="height: 21px; text-decoration: underline">
                &nbsp;<asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading" OnClick="LnkHomePage_Click">Home</asp:LinkButton>
                &gt; Entertainment Keyword &gt; Add New</td>
        </tr>
        <tr>
            <td style="height: 91px" valign="top">
                <table style="width: 920px">
                    <tr class="mytext">
                        <td style="height: 36px" valign="top">
                            Entertainment Keyword</td>
                        <td style="width: 213px; height: 36px" valign="top">
                            <asp:TextBox ID="txt_EntertainmentKeyword" runat="server" CssClass="mytext" TextMode="MultiLine"
                                Width="184px" Height="56px"></asp:TextBox></td>
                        <td style="height: 36px" valign="top">
                            Sub Content Type</td>
                        <td style="height: 36px" valign="top">
                            <asp:DropDownList ID="ddl_SubContentType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></td>
                        <td style="height: 36px" valign="top">
                            Key type</td>
                        <td style="height: 36px" valign="top">
                            <asp:ListBox ID="lstKeyType" runat="server" CssClass="mytext" Height="64px" SelectionMode="Multiple"
                                Width="168px"></asp:ListBox></td>
                        <td style="height: 36px" valign="top">
                            <asp:TextBox ID="txt_TKeytype" runat="server" CssClass="mytext" TextMode="MultiLine"
                                Visible="False" Width="40px"></asp:TextBox></td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="504px"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain" style="height: 26px">
                &nbsp;<asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Text="Save" Width="64px" />
                <asp:Button ID="BttnDelete" runat="server" CssClass="buttonA" Text="Delete" Width="64px" />
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Text="Clear" Width="64px" /></td>
        </tr>
        <tr>
            <td align="right" valign="top">
                <asp:Label ID="lblTotalRecords" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr>
            <td valign="top">
                <asp:GridView ID="dg_EntKeyword" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                    AutoGenerateSelectButton="True" CssClass="gridContent" OnPageIndexChanging="dg_EntKeyword_PageIndexChanging"
                    PageSize="25" Width="800px">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="EntertainmentKeywordID" Visible="False">
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="SubContentTypeID" Visible="False">
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="SubContentTypeName" HeaderText="Sub-Content Type" />
                        <asp:BoundField DataField="EntertainmentKeyword" HeaderText="Ent.Keyword">
                            <HeaderStyle Width="250px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="TKeytype" HeaderText="Key Type">
                            <ItemStyle Width="350px" />
                        </asp:BoundField>
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Delete !"
                    TargetControlID="BttnDelete">
                </cc1:ConfirmButtonExtender>
            </td>
        </tr>
        <tr>
            <td valign="top">
                <asp:Label ID="lblAuditHistory" runat="server" CssClass="labelheading" Visible="False">Audit History - Entertainment Keywords</asp:Label></td>
        </tr>
        <tr>
            <td valign="top">
                <asp:GridView ID="dgAuditHistory" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                    PageSize="25" Width="100%">
                    <Columns>
                        <asp:BoundField DataField="AddedBy" HeaderText="Added By" />
                        <asp:BoundField DataField="AddedDate" HeaderText="Added Date" />
                        <asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy" />
                        <asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
        <tr>
            <td style="height: 21px">
                <table class="mytext" style="width: 576px">
                    <tr>
                        <td class="labelheading" style="width: 200px; height: 26px">
                            Search Keyword</td>
                        <td style="width: 100px; height: 26px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 200px; height: 13px">
                            Entertainment Keyword</td>
                        <td style="width: 100px; height: 13px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 200px">
                            <asp:TextBox ID="txt_SearchKW" runat="server" CssClass="mytext" Width="360px" TextMode="MultiLine"></asp:TextBox></td>
                        <td style="width: 100px">
                            <asp:LinkButton ID="lnkSearch" runat="server" OnClick="lnkSearch_Click">Search</asp:LinkButton></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 21px">
                <asp:TextBox ID="txt_EntKeywordID" runat="server" CssClass="mytext" Visible="False"
                    Width="48px"></asp:TextBox>
                <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
        </tr>
    </table>
</asp:Content>

