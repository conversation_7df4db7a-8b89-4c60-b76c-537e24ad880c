Imports System.Data
Imports System.Data.SqlClient

Partial Class BulkTapeManagement_OSRNewTapeNumber
    Inherits System.Web.UI.Page
    Dim dt_TapeNumber As New DataTable
    Dim Diff As Integer
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Private Con As New Data.SqlClient.SqlConnection(strConnection)
    Dim datatable As DataTable

    Protected Sub GridView1_SelectedIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSelectEventArgs)
        txt_SRDID.Text = dg.Rows(e.NewSelectedIndex).Cells(1).Text
        txt_TapeTypeID.Text = dg.Rows(e.NewSelectedIndex).Cells(8).Text
        txt_Count.Text = dg.Rows(e.NewSelectedIndex).Cells(5).Text
        Label1.Text = dg.Rows(e.NewSelectedIndex).Cells(2).Text

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        bttnSave.Attributes.Add("onClick", "return mainValues();")

        Try
            If Not Page.IsPostBack = True Then
                'BindGrid()
                Dim ObjTemp As New BusinessFacade.NewTapeNumber
                ObjTemp.Delete_Temp()
                FillGridMain()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindGrid()

        datatable = New DataTable
        Dim objNewTape As New BusinessFacade.NewTapeNumber()
        objNewTape.TapeTypeID = txt_TapeTypeID.Text
        datatable = objNewTape.NewTapeNumber_GetRecord()
        ViewState("DataTable") = datatable


    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_SRDID.Text = "" Then
            lblErr.Text = "Please Select Record!!"
        End If

    End Sub

    Private Sub BindGrid_TapeNo()
        dg_tapeNumber.DataSource = dt_TapeNumber
        dg_tapeNumber.DataBind()
    End Sub

    Protected Sub bttnOK_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnOK.Click

        datatable = ViewState("DataTable")

        lblErr.Text = String.Empty

        Dim col1 As DataColumn = New DataColumn("StockRegisterDetailID")
        col1.DataType = System.Type.GetType("System.Int32")
        dt_TapeNumber.Columns.Add("StockRegisterDetailID")


        Dim col2 As DataColumn = New DataColumn("TapeTypeID")
        col2.DataType = System.Type.GetType("System.Int32")
        dt_TapeNumber.Columns.Add("TapeTypeID")

        Dim col3 As DataColumn = New DataColumn("TapeNumber")
        col3.DataType = System.Type.GetType("System.Int32")
        dt_TapeNumber.Columns.Add("TapeNumber")

        Dim col4 As DataColumn = New DataColumn("TapeType")
        col4.DataType = System.Type.GetType("System.String")
        dt_TapeNumber.Columns.Add("TapeType")

        Dim col5 As DataColumn = New DataColumn("StationID")
        col5.DataType = System.Type.GetType("System.Int32")
        dt_TapeNumber.Columns.Add("StationID")

        Dim col6 As DataColumn = New DataColumn("StationName")
        col6.DataType = System.Type.GetType("System.String")
        dt_TapeNumber.Columns.Add("StationName")

        If txt_TapeNumber.Text = "" Then
            Dim myString As String = txt_FromTape_Head.Text
            Dim Prefix As String = myString.Substring(0, 3)
            Dim q As Integer = myString.Length - 3
            Dim W As Integer = myString.Length - 4

            Dim TapeNumber1 As String = myString.Substring(3, q)
            Dim TapeNumber2 As String = txt_ToDate_Head.Text

            Dim FirstCharAssciCode As String = Asc((myString.Substring(0, 1)))



            If ViewState("Table_TapeNumber") Is Nothing Then
            Else
                dt_TapeNumber = ViewState("Table_TapeNumber")
            End If

            If txt_TapeTypeID.Text = "" Then
                lblErr.Text = "Please Select TapeType First!!"
            Else
                If txt_TapeNumber.Text = "" Then

                    If txt_FromTape_Head.Text <> "" And txt_ToDate_Head.Text <> "" Then
                        If (FirstCharAssciCode > 64 And FirstCharAssciCode < 91) Or (FirstCharAssciCode > 96 And FirstCharAssciCode < 123) Then

                            Dim A As String
                            A = TapeNumber1
                            Dim B As String
                            B = TapeNumber2


                            Dim Arr As Array = Split(A, "-")

                            Dim ArrB As Array = Split(B, "-")

                            If Arr.Length <> 2 Then

                                ''*****************************************''
                                ''********* For Khi1000 to 1005 **********''
                                ''*****************************************''

                                Dim A1 As String
                                Dim B1 As String

                                A1 = Prefix & A
                                B1 = Prefix & B

                                If A < B Then

                                    Dim Count As Integer
                                    Count = (B - A) + 1

                                    Dim I As Integer
                                    For I = 0 To datatable.Rows.Count - 1

                                        If A <> B + 1 Then
                                            Dim K As Integer
                                            For K = 0 To datatable.Rows.Count - 1

                                                Dim RowCount As Integer
                                                RowCount = datatable.Rows(K).Item(8).ToString()

                                                txt_SRDID.Text = datatable.Rows(K).Item(0).ToString()
                                                txt_TapeTypeID.Text = datatable.Rows(K).Item(4).ToString()
                                                txt_Count.Text = datatable.Rows(K).Item(4).ToString()
                                                Label1.Text = datatable.Rows(K).Item(5).ToString()

                                                Dim Remaining As Integer

                                                Dim C As Integer
                                                For C = 0 To datatable.Rows(K).Item(8).ToString() - 1
                                                    If A <> B + 1 Then
                                                        A1 = Prefix & A
                                                        Dim row As DataRow
                                                        row = dt_TapeNumber.NewRow
                                                        row.Item("StockRegisterDetailID") = txt_SRDID.Text
                                                        row.Item("TapeTypeID") = txt_TapeTypeID.Text
                                                        row.Item("TapeNumber") = A1
                                                        row.Item("TapeType") = Label1.Text
                                                        row.Item("StationID") = ddlStation.SelectedValue
                                                        row.Item("StationName") = ddlStation.SelectedItem
                                                        dt_TapeNumber.Rows.Add(row)
                                                        A = A + 1
                                                    End If

                                                Next
                                                Remaining = Count - RowCount
                                                Count = Remaining
                                                RowCount = Remaining

                                            Next
                                        End If
                                    Next
                                End If
                            Else

                                ''*****************************************''
                                ''********* For Khi-1000 to 1005 **********''
                                ''*****************************************''

                                Dim A1 As String
                                If Arr(1) < B Then

                                    Dim I As Integer
                                    For I = 0 To datatable.Rows.Count - 1

                                        If Arr(1) <> B + 1 Then
                                            Dim K As Integer
                                            For K = 0 To datatable.Rows.Count - 1

                                                Dim RowCount As Integer
                                                RowCount = datatable.Rows(K).Item(8).ToString()

                                                txt_SRDID.Text = datatable.Rows(K).Item(0).ToString()
                                                txt_TapeTypeID.Text = datatable.Rows(K).Item(4).ToString()
                                                txt_Count.Text = datatable.Rows(K).Item(4).ToString()
                                                Label1.Text = datatable.Rows(K).Item(5).ToString()

                                                Dim C As Integer
                                                For C = 0 To datatable.Rows(K).Item(8).ToString() - 1
                                                    If Arr(1) <> B + 1 Then
                                                        A1 = Prefix & "-" & Arr(1)
                                                        Dim row As DataRow
                                                        row = dt_TapeNumber.NewRow
                                                        row.Item("StockRegisterDetailID") = txt_SRDID.Text
                                                        row.Item("TapeTypeID") = txt_TapeTypeID.Text
                                                        row.Item("TapeNumber") = A1
                                                        row.Item("TapeType") = Label1.Text
                                                        row.Item("StationID") = ddlStation.SelectedValue
                                                        row.Item("StationName") = ddlStation.SelectedItem
                                                        dt_TapeNumber.Rows.Add(row)
                                                        Arr(1) = Arr(1) + 1
                                                    End If

                                                Next

                                            Next
                                        End If
                                    Next
                                End If

                                ''****************************************************************************************--


                            End If
                        Else

                            Dim Arr001 As Array = Split(txt_FromTape_Head.Text, "-")

                            Dim Arr002 As Array = Split(txt_ToDate_Head.Text, "-")

                            If Arr001.Length <> 2 And Arr002.Length <> 2 Then

                                Dim A As Integer
                                A = txt_FromTape_Head.Text
                                Dim B As Integer
                                B = txt_ToDate_Head.Text

                                If A < B Then


                                    ''*************************************''
                                    ''********* For 1000 to 1005 **********''
                                    ''*************************************''

                                    Dim I As Integer
                                    For I = 0 To datatable.Rows.Count - 1
                                        If A <> B + 1 Then
                                            Dim K As Integer
                                            For K = 0 To datatable.Rows.Count - 1

                                                txt_SRDID.Text = datatable.Rows(K).Item(0).ToString()
                                                txt_TapeTypeID.Text = datatable.Rows(K).Item(4).ToString()
                                                txt_Count.Text = datatable.Rows(K).Item(4).ToString()
                                                Label1.Text = datatable.Rows(K).Item(5).ToString()

                                                Dim C As Integer
                                                For C = 0 To datatable.Rows(K).Item(8).ToString() - 1
                                                    If A <> B + 1 Then
                                                        Dim row As DataRow
                                                        row = dt_TapeNumber.NewRow
                                                        row.Item("StockRegisterDetailID") = txt_SRDID.Text
                                                        row.Item("TapeTypeID") = txt_TapeTypeID.Text
                                                        row.Item("TapeNumber") = A
                                                        row.Item("TapeType") = Label1.Text
                                                        row.Item("StationID") = ddlStation.SelectedValue
                                                        row.Item("StationName") = ddlStation.SelectedItem
                                                        dt_TapeNumber.Rows.Add(row)
                                                        A = A + 1
                                                    End If

                                                Next
                                            Next
                                        End If
                                    Next


                                    txt_FromTape_Head.Text = String.Empty
                                    txt_ToDate_Head.Text = String.Empty
                                Else
                                    lblErr.Text = "TapeNo's are not valid!! Please Check It!!"
                                End If

                            Else

                                ''*****************************************''
                                ''********* For 1000-01 to 1005 **********''
                                ''*****************************************''


                                If Arr001.Length = 2 And Arr002.Length = 2 Then
                                    If Arr001(0) < Arr002(0) Then

                                        Dim I As Integer
                                        For I = 0 To datatable.Rows.Count - 1
                                            Dim A1 As String
                                            If Arr001(0) <> Arr002(0) + 1 Then
                                                Dim K As Integer
                                                For K = 0 To datatable.Rows.Count - 1

                                                    Dim RowCount As Integer
                                                    RowCount = datatable.Rows(K).Item(8).ToString()

                                                    txt_SRDID.Text = datatable.Rows(K).Item(0).ToString()
                                                    txt_TapeTypeID.Text = datatable.Rows(K).Item(4).ToString()
                                                    txt_Count.Text = datatable.Rows(K).Item(4).ToString()
                                                    Label1.Text = datatable.Rows(K).Item(5).ToString()

                                                    Dim C As Integer
                                                    For C = 0 To datatable.Rows(K).Item(8).ToString() - 1
                                                        If Arr001(0) <> Arr002(0) + 1 Then
                                                            A1 = Arr001(0) & "-" & Arr001(1).ToString
                                                            Dim row As DataRow
                                                            row = dt_TapeNumber.NewRow
                                                            row.Item("StockRegisterDetailID") = txt_SRDID.Text
                                                            row.Item("TapeTypeID") = txt_TapeTypeID.Text
                                                            row.Item("TapeNumber") = A1
                                                            row.Item("TapeType") = Label1.Text
                                                            row.Item("StationID") = ddlStation.SelectedValue
                                                            row.Item("StationName") = ddlStation.SelectedItem
                                                            dt_TapeNumber.Rows.Add(row)
                                                            Arr001(0) = Arr001(0) + 1
                                                        End If

                                                    Next

                                                Next
                                            End If
                                        Next

                                    End If
                                End If

                            End If

                        End If
                    End If

                End If
            End If

            ViewState("Table_TapeNumber") = dt_TapeNumber
            BindGrid_TapeNo()
        Else

            If txt_TapeTypeID.Text = "" Then
                lblErr.Text = "Please Select TapeType First!!"
            Else
                ''******************************************************''
                ''**************** Single Tape Number ******************''
                ''******************************************************''

                If ViewState("Table_TapeNumber") Is Nothing Then
                Else
                    dt_TapeNumber = ViewState("Table_TapeNumber")
                End If

                txt_SRDID.Text = datatable.Rows(0).Item(0).ToString()
                txt_TapeTypeID.Text = datatable.Rows(0).Item(4).ToString()
                txt_Count.Text = datatable.Rows(0).Item(4).ToString()
                Label1.Text = datatable.Rows(0).Item(5).ToString()

                Dim row As DataRow
                row = dt_TapeNumber.NewRow
                row.Item("StockRegisterDetailID") = txt_SRDID.Text
                row.Item("TapeTypeID") = txt_TapeTypeID.Text
                row.Item("TapeNumber") = txt_TapeNumber.Text
                row.Item("TapeType") = Label1.Text
                row.Item("StationID") = ddlStation.SelectedValue
                row.Item("StationName") = ddlStation.SelectedItem
                dt_TapeNumber.Rows.Add(row)


                txt_TapeNumber.Text = String.Empty

                ViewState("Table_TapeNumber") = dt_TapeNumber
                BindGrid_TapeNo()
            End If
        End If
    End Sub

    Protected Sub bttnTableSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnTableSave.Click

        Dim IsEnter As String
        Dim Count As Integer = 0

        ''********************************************************************''
        ''*************** Check Tape Already Issued or Not *******************''
        ''********************************************************************''

        Dim Y As Integer
        For Y = 0 To dg_tapeNumber.Rows.Count - 1
            Dim ObjCheck As New BusinessFacade.NewTapeNumber()
            ObjCheck.TapeNumber = dg_tapeNumber.Rows(Y).Cells(2).Text
            'ObjCheck.StationID = dg_tapeNumber.Rows(Y).Cells(4).Text
            IsEnter = ObjCheck.CheckTapeAlreadyEnter_OSR(ObjCheck.TapeNumber)
            If IsEnter <> "0" Then
                Count = Count + 1
            End If
        Next

        ''********************************************************************''
        ''****************** Check Duplication in Grid ***********************''
        ''********************************************************************''

        Dim z As Integer
        Dim CntDuplicate As Integer = 0
        For z = 0 To dg_tapeNumber.Rows.Count - 1
            Dim I As Integer
            For I = 0 To dg_tapeNumber.Rows.Count - 1
                If I <> z Then
                    'If (dg_tapeNumber.Rows(z).Cells(2).Text = dg_tapeNumber.Rows(I).Cells(2).Text) And (dg_tapeNumber.Rows(z).Cells(4).Text = dg_tapeNumber.Rows(I).Cells(4).Text) Then
                    If (dg_tapeNumber.Rows(z).Cells(2).Text = dg_tapeNumber.Rows(I).Cells(2).Text) Then
                        CntDuplicate = CntDuplicate + 1
                    End If
                End If
            Next
        Next

        ''********************************************************************''
        ''****************** Save Tapes in Tape Library **********************''
        ''********************************************************************''

        If Count = 0 And CntDuplicate = 0 Then
            Dim Cnt As Integer
            For Cnt = 0 To dg_tapeNumber.Rows.Count - 1
                Dim objSave As New BusinessFacade.NewTapeNumber()
                objSave.TapeTypeID = dg_tapeNumber.Rows(Cnt).Cells(1).Text
                objSave.TapeNumber = dg_tapeNumber.Rows(Cnt).Cells(2).Text
                objSave.stockRegisterDetaiID = dg_tapeNumber.Rows(Cnt).Cells(0).Text
                objSave.StationID = dg_tapeNumber.Rows(Cnt).Cells(4).Text
                objSave.NewTapeNumber_SaveRecord_OSR()
                lblErr.Text = "Tapes has been Saved Successfully !"
            Next

            Dim ObjTemp As New BusinessFacade.NewTapeNumber
            ObjTemp.Delete_Temp()
        ElseIf CntDuplicate <> 0 Then
            lblErr.Text = "- - Attention: You can not Issue Particular Tape to Same Station  --"
        ElseIf Count <> 0 Then
            lblErr.Text = "- - Attention: Tape Already Entered --"
            dt_TapeNumber.Clear()
            ViewState("Table_TapeNumber") = Nothing
            BindGrid_TapeNo()
        End If

    End Sub

    Protected Sub dg_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg.PageIndexChanging
        dg.PageIndex = e.NewPageIndex()
        BindGrid()
    End Sub

    Protected Sub dg_SelectedIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSelectEventArgs) Handles dg.SelectedIndexChanging
        txt_SRDID.Text = dg.Rows(e.NewSelectedIndex).Cells(1).Text
        txt_TapeTypeID.Text = dg.Rows(e.NewSelectedIndex).Cells(8).Text
        txt_Count.Text = dg.Rows(e.NewSelectedIndex).Cells(5).Text
        Label1.Text = dg.Rows(e.NewSelectedIndex).Cells(2).Text
    End Sub

    Protected Sub dgMain_SelectedIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSelectEventArgs)
        txt_TapeTypeID.Text = dgMain.Rows(e.NewSelectedIndex).Cells(1).Text
        BindGrid()
    End Sub

    Private Sub FillGridMain()

        dgMain.DataSource = New BusinessFacade.TapeIssuance().GetOSRTapes()
        dgMain.DataBind()
        dgMain.Columns(0).Visible = False

    End Sub


    Protected Sub dg_tapeNumber_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs)
        Dim RowId As Integer
        RowId = e.RowIndex
        dt_TapeNumber = ViewState("Table_TapeNumber")
        dt_TapeNumber.Rows(RowId).Delete()
        dt_TapeNumber.AcceptChanges()
        ViewState("Table_TapeNumber") = dt_TapeNumber
        dg_tapeNumber.DataSource = dt_TapeNumber
        dg_tapeNumber.DataBind()
    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim myscript As String = ""
        Dim URL As String = ""

        URL = "FrmArchiveEntry_Ent.aspx"
        myscript = "<script language='javascript' type='text/javascript'>" & _
                    "window.onload= function closewin() " & _
                    "{" & _
                    "window.opener.location.href='" & URL & "'" & _
                    "; self.close(); }" & _
                    "</script>"

        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", myscript)
        Exit Sub
    End Sub
End Class
