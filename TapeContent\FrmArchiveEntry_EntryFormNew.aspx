<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmArchiveEntry_EntryFormNew.aspx.vb" Inherits="TapeContent_FrmArchiveEntry_EntryFormNew" title="Home > MSR Entry Form > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDataInput.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebDataInput" TagPrefix="igtxt" %>
   

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<%--<script language=javascript runat=server>

function pageLoad(sender,e)
{
restoreCurrentTabIndex();
}
</script>--%>

    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
<table width=100%>
            <tr>
                <td style="height: 17px; text-decoration: underline; width: 1163px;" class="labelheading">
                    <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                    &gt;
                    <asp:LinkButton ID="LnkMSR_EntryForm" runat="server" CssClass="labelheading">MSR Entry Form</asp:LinkButton>
                    &gt; Add New</td>
            </tr>
    <tr>
        <td align="center" style="width: 1163px; height: 17px">
            &nbsp;<asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="100%" Font-Overline="False" Font-Italic="False" Font-Size="Small" Font-Underline="False"></asp:Label></td>
    </tr>
    <tr>
        <td style="height: 29px; width: 1163px;" align="center" bgcolor="#ff6633">
            </td>
    </tr>
            <tr>
                <td rowspan="" style="width: 1163px; height: 64px;">
                    <table>
                        <tr>
                            <td style="width: 100px">
                                            Channel</td>
                            <td style="width: 100px">
                                Program Name</td>
                            <td style="width: 100px">
                                On Air Date</td>
                            <td style="width: 100px">
                                Date</td>
                        </tr>
                        <tr>
                            <td style="width: 100px">
                                <asp:DropDownList ID="ddl_Channel" runat="server" CssClass="mytext" Width="160px">
                                </asp:DropDownList></td>
                            <td style="width: 100px">
                                <asp:DropDownList ID="ddl_ProgramMaster" runat="server" CssClass="mytext" Width="160px">
                                </asp:DropDownList></td>
                            <td style="width: 100px">
                                <asp:TextBox ID="txtOnAirDate" runat="server" CssClass="mytext" TabIndex="1"
                                    Width="120px"></asp:TextBox></td>
                            <td style="width: 100px">
                                <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" TabIndex="1" Width="120px"></asp:TextBox></td>
                        </tr>
                    </table>
                </td>
            </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
            <table>
                <tr>
                    <td style="width: 100px">
                        Guests:</td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 25px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px; height: 22px">
                        <asp:DropDownList ID="ddl_Guest1" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px; height: 22px">
                    </td>
                    <td style="width: 25px; height: 22px">
                        <asp:DropDownList ID="ddl_Guest2" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px; height: 22px">
                    </td>
                    <td style="width: 100px; height: 22px">
                        <asp:DropDownList ID="ddl_Guest3" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px; height: 22px">
                    </td>
                    <td style="width: 100px; height: 22px">
                        <asp:DropDownList ID="ddl_Guest4" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                </tr>
                <tr>
                    <td style="width: 100px">
                        Hosts</td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 25px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Host1" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 25px">
                        <asp:DropDownList ID="ddl_Host2" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Host3" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Host4" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                </tr>
                <tr>
                    <td style="width: 100px">
                        Topics</td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 25px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Topic1" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 25px">
                        <asp:DropDownList ID="ddl_Topic2" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Topic3" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                    <td style="width: 20px">
                    </td>
                    <td style="width: 100px">
                        <asp:DropDownList ID="ddl_Topic4" runat="server" CssClass="mytext" Width="160px">
                        </asp:DropDownList></td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
            <table>
                <tr>
                    <td style="width: 177px">
                        Urdu Script</td>
                </tr>
                <tr>
                    <td style="width: 177px">
                        <asp:TextBox ID="txt_UrduScript" runat="server" CssClass="mytext" Font-Bold="False"
                            Font-Size="Medium" Height="56px" TabIndex="3" TextMode="MultiLine" Width="429px">N/A</asp:TextBox></td>
                </tr>
            </table>
            <asp:LinkButton ID="LnkShowKeyBoard" runat="server" TabIndex="3">Show Keyboard</asp:LinkButton></td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 72px;">
            <table>
                <tr>
                    <td style="width: 100px; height: 21px;">
                        Keywords</td>
                    <td style="width: 100px; height: 21px;">
                    </td>
                    <td style="width: 100px; height: 21px;">
                    </td>
                    <td style="width: 100px; height: 21px;">
                    </td>
                    <td style="width: 100px; height: 21px;">
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px; height: 29px;">
                        <asp:TextBox ID="KW1" runat="server" BackColor="Window" CssClass="myddl" TabIndex="14"
                            Width="180px" Height="18px"></asp:TextBox></td>
                    <td style="width: 100px; height: 29px;">
                        <asp:TextBox ID="KW2" runat="server" BackColor="Window" CssClass="myddl" TabIndex="16"
                            Width="180px" Height="19px"></asp:TextBox></td>
                    <td style="width: 100px; height: 29px;">
                        <asp:TextBox ID="KW3" runat="server" BackColor="Window" CssClass="myddl" TabIndex="18"
                            Width="180px" Height="19px"></asp:TextBox></td>
                    <td style="width: 100px; height: 29px;">
                        <asp:TextBox ID="KW4" runat="server" BackColor="Window" CssClass="myddl" TabIndex="20"
                            Width="180px" Height="19px"></asp:TextBox></td>
                    <td style="width: 100px; height: 29px;">
                        <asp:TextBox ID="KW5" runat="server" BackColor="Window" CssClass="myddl" TabIndex="22"
                            Width="180px" Height="19px"></asp:TextBox></td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 72px">
            <asp:GridView ID="dg_ProgramInfo" runat="server" AutoGenerateColumns="False"
                                AutoGenerateSelectButton="True" CssClass="gridContent" Height="126px" PageSize="15"
                                Width="845px">
                                <Columns>
                                    <asp:BoundField DataField="SOTID" HeaderText="SOTID" Visible="False" />
                                    <asp:BoundField DataField="SNo" HeaderText="SNo">
                                        <ControlStyle Width="0px" />
                                        <FooterStyle Width="0px" />
                                        <HeaderStyle Width="0px" />
                                        <ItemStyle ForeColor="White" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SotText" HeaderText="SotText" />
                                    <asp:BoundField DataField="StartTime" HeaderText="Start Time">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="EndTime" HeaderText="End Time">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="Keyword1ID" HeaderText="Keyword1ID" Visible="False" />
                                    <asp:BoundField DataField="SubContentTypeKW1" HeaderText="SubContentTypeKW1" Visible="False" />
                                    <asp:BoundField DataField="Keyword1" HeaderText="Keyword1">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="Keyword2ID" HeaderText="Keyword2ID" Visible="False" />
                                    <asp:BoundField DataField="Keyword2" HeaderText="Keyword2">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SubContentTypeKW2" HeaderText="SubContentTypeKW2" Visible="False" />
                                    <asp:BoundField DataField="Keyword3ID" HeaderText="Keyword3ID" Visible="False" />
                                    <asp:BoundField DataField="Keyword3" HeaderText="Keyword3">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SubContentTypeKW3" HeaderText="SubContentTypeKW3" Visible="False" />
                                    <asp:BoundField DataField="Keyword4ID" HeaderText="Keyword4ID" Visible="False" />
                                    <asp:BoundField DataField="Keyword4" HeaderText="Keyword4">
                                        <HeaderStyle Width="700px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SubContentTypeKW4" HeaderText="SubContentTypeKW4" Visible="False" />
                                    <asp:BoundField DataField="Keyword5ID" HeaderText="Keyword5ID" Visible="False" />
                                    <asp:BoundField DataField="Keyword5" HeaderText="Keyword5" />
                                    <asp:BoundField DataField="SubContentTypeKW5" HeaderText="SubContentTypeKW5" Visible="False" />
                                    <asp:BoundField DataField="TranscriberName" HeaderText="TranscriberName" Visible="False" />
                                    <asp:BoundField DataField="ClipPath" HeaderText="ClipPath">
                                        <HeaderStyle Width="600px" />
                                    </asp:BoundField>
                                    <asp:CommandField ShowDeleteButton="True" />
                                </Columns>
                                <HeaderStyle CssClass="gridheader" />
                                <AlternatingRowStyle CssClass="AlternateRows" />
                            </asp:GridView>
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 18px">
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 18px">
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 18px">
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
            &nbsp; &nbsp; &nbsp;&nbsp;
                    <asp:Button CssClass="buttonA" ID="bttnSaveTracDetail" runat="server" Text="Save Transaction Detail" Width="201px" Font-Bold="True" />&nbsp;
                    &nbsp; &nbsp;&nbsp;
            <asp:Button CssClass="buttonA" ID="bttnCancel" runat="server" Text="Cancel" Width="72px" Font-Bold="True" />
            &nbsp; &nbsp; &nbsp;<asp:Button CssClass="buttonA" ID="bttnSaveSotDetail" runat="server" Text="Save Sot Detail" Width="140px" Font-Bold="True" />&nbsp;&nbsp; &nbsp;
            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp; &nbsp; &nbsp;
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
        </td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px">
            <asp:Image ID="ImgKeyBoard" runat="server" ImageUrl="~/Images/UrduKeyboard.gif" Visible="False" /></td>
    </tr>
    <tr>
        <td rowspan="1" style="width: 1163px; height: 232px;">
            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW1" runat="server" CompletionInterval="1"
                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW1">
            </cc1:AutoCompleteExtender>
            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW2" runat="server" CompletionInterval="1"
                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW2">
            </cc1:AutoCompleteExtender>
            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW3" runat="server" CompletionInterval="1"
                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW3">
            </cc1:AutoCompleteExtender>
            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW4" runat="server" CompletionInterval="1"
                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW4">
            </cc1:AutoCompleteExtender>
            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW5" runat="server" CompletionInterval="1"
                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW5">
            </cc1:AutoCompleteExtender>
            <cc1:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="MyCalendar"
                Enabled="True" Format="dd-MMM-yyyy" TargetControlID="txtEntryDate">
            </cc1:CalendarExtender>
            <cc1:CalendarExtender ID="CalendarExtender_OnAirDate" runat="server" Enabled="True"
                Format="dd-MMM-yyyy" TargetControlID="txtOnAirDate">
            </cc1:CalendarExtender>
            <cc1:CalendarExtender ID="CalendarExtender_EntryDate" runat="server" Enabled="True"
                Format="dd-MMM-yyyy" TargetControlID="txtEntryDate">
            </cc1:CalendarExtender>
            &nbsp; &nbsp; &nbsp;
            &nbsp; &nbsp;
            &nbsp; &nbsp;&nbsp;
        </td>
    </tr>
            <tr>
                <td style="height: 16px; width: 1163px;">
                    &nbsp; &nbsp;
                                            </td>
            </tr>
    <tr>
        <td style="width: 1163px; height: 16px">
            <asp:ListBox ID="lstKeyword" runat="server" CssClass="mytext" Height="48px" SelectionMode="Multiple"
                Visible="False" Width="150px"></asp:ListBox></td>
    </tr>
        </table>
</asp:Content>

