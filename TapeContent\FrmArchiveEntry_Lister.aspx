<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmArchiveEntry_Lister.aspx.vb" Inherits="TapeContent_FrmArchiveEntry_Lister" title="Home > Archival Entry for Entertainment" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebGrid" TagPrefix="igtbl" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table style="width: 100%">
        <tr>
            <td style="width: 100%; text-decoration: underline; height: 21px;" class="labelheading">
                <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Archive Entry For Entertainment</td>
        </tr>
        <tr class="mytext">
            <td>
                <table>
                    <tr>
                        <td style="width: 160px">
                            TapeNumber&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox ID="Chk_TapeNumber"
                                runat="server" Text="Ignore" /></td>
                        <td style="width: 225px">
                            Program &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox ID="Chk_Program" runat="server" Text="Ignore" /></td>
                        <td style="width: 160px">
                            Location Code &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 
                            <asp:CheckBox ID="Chk_Loc" runat="server" Text="Ignore" /></td>
                        <td style="width: 160px">
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; 
                            <asp:CheckBox ID="Chk_Abstract" runat="server" Text="Ignore" Visible="False" /></td>
                        <td style="width: 160px">
                            &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                            <asp:CheckBox ID="Chk_Note" runat="server" Text="Ignore" Visible="False" /></td>
                        <td style="width: 200px">
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px; height: 42px;" valign="top">
                            <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext" Width="160px"></asp:TextBox></td>
                        <td style="width: 144px; height: 42px;" valign="top">
                            <asp:TextBox ID="txt_ProgramChild" runat="server" Width="225px"></asp:TextBox></td>
                        <td style="width: 100px; height: 42px" valign="top">
                            <asp:TextBox ID="txt_Loc" runat="server" CssClass="mytext" Width="160px"></asp:TextBox></td>
                        <td style="width: 100px; height: 42px;" valign="top">
                            <asp:TextBox ID="txt_Abstract" runat="server" CssClass="mytext" Width="160px" Visible="False"></asp:TextBox></td>
                        <td style="width: 100px; height: 42px;" valign="top">
                            <asp:TextBox ID="txt_Note" runat="server" CssClass="mytext" Width="160px" Visible="False"></asp:TextBox></td>
                        <td style="width: 100px; height: 42px;" valign="top">
                            </td>
                        <td style="width: 100px; height: 42px;">
                            <asp:CheckBox ID="Chk_Title" runat="server" Text="Ignore" Visible="False" />
                            <asp:TextBox ID="txt_Title" runat="server" CssClass="mytext" Width="72px" Visible="False"></asp:TextBox></td>
                    </tr>
                </table>
                            <cc1:AutoCompleteExtender 
                            ID="AutoCompleteExtender1" 
                            runat="server" 
                            CompletionInterval="1"
                            CompletionSetCount="12" 
                            EnableCaching="true" 
                            MinimumPrefixLength="3" 
                            ServiceMethod="Program"
                            ServicePath="AutoComplete.asmx" 
                            TargetControlID="txt_ProgramChild">
                            </cc1:AutoCompleteExtender>                           
                            <cc1:AutoCompleteExtender 
                            ID="AutoCompleteExtender2" 
                            runat="server" 
                            CompletionInterval="1"
                            CompletionSetCount="12" 
                            EnableCaching="true" 
                            MinimumPrefixLength="3" 
                            ServiceMethod="TapeContentEnt_TapeNumber_GetRecords_2"
                            ServicePath="AutoComplete.asmx" 
                            TargetControlID="txt_TapeNumber">
                            </cc1:AutoCompleteExtender>                           
                <cc1:AutoCompleteExtender ID="AutoCompleteExtender_LocationCode" runat="server" CompletionInterval="1"
                    CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetLocationCodeEnt"
                    ServicePath="AutoComplete.asmx" TargetControlID="txt_Loc">
                </cc1:AutoCompleteExtender>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Size="Small" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr class="mytext">
            <td class="bottomMain" style="width: 100%; height: 29px">
                &nbsp;
                <asp:Button ID="bttnGetRecord" runat="server" CssClass="buttonA" Text="Search" Width="80px" />&nbsp;
                <asp:Button ID="bttnAdd" runat="server" CssClass="buttonA" Text="Add New" Width="80px" />&nbsp;
                <asp:Button
                    ID="bttnEdit" runat="server" CssClass="buttonA" Text="Edit" Width="88px" />
                <asp:Button ID="bttnDelete" runat="server" CssClass="buttonA" Text="Delete" Width="88px" />&nbsp;
                <asp:Button ID="bttnCancel" runat="server" CssClass="buttonA" Text="Cancel" Width="88px" />&nbsp;
                </td>
        </tr>
        <tr>
            <td style="width: 100%" valign="top">
                <table width="100%">
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 85%">
                <igtbl:UltraWebGrid ID="UltraWebGrid1" runat="server" Width="100%">
                    <Bands>
                        <igtbl:UltraGridBand SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="Single">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentID" HeaderText="TapeContentID"
                                    Hidden="True">
                                    <Header Caption="TapeContentID">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ContentTypeName" HeaderText="ContentType Name"
                                    Hidden="True">
                                    <Header Caption="ContentType Name">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="DepartmentName" HeaderText="Department Name">
                                    <Header Caption="Department Name">
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TelecastDate" Format="dd-MMM-yyyy" HeaderText="Telecast Date"
                                    Hidden="True" Width="180px">
                                    <Header Caption="Telecast Date">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ClassificationCode" HeaderText="Classification Code">
                                    <Header Caption="Classification Code">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="CallNo" HeaderText="Call No">
                                    <Header Caption="Call No">
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LocationCode" HeaderText="Location Code">
                                    <Header Caption="Location Code">
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="IsActive" HeaderText="IsActive">
                                    <Header Caption="IsActive">
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="AddedBy" HeaderText="AddedBy">
                                    <Header Caption="AddedBy">
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ModifiedBy" HeaderText="ModifiedBy">
                                    <Header Caption="ModifiedBy">
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                        <igtbl:UltraGridBand SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="None" RowSelectors="No">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentID" HeaderText="TapeContentID"
                                    Hidden="True">
                                    <Header Caption="TapeContentID">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeContentDetailID" HeaderText="TapeContentDetailID"
                                    Hidden="True">
                                    <Header Caption="TapeContentDetailID">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeType" HeaderText="Tape Type">
                                    <Header Caption="Tape Type">
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeNumber" HeaderText="Tape Number">
                                    <Header Caption="Tape Number">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="ProgramChildName" HeaderText="Prog.Child Name">
                                    <Header Caption="Prog.Child Name">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="NoteArea" HeaderText="Note Area">
                                    <Header Caption="Note Area">
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="Abstract" HeaderText="Abstract">
                                    <Header Caption="Abstract">
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="startTime_vc" HeaderText="Start Time">
                                    <Header Caption="Start Time">
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="7" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="EndTime_vc" HeaderText="End Time">
                                    <Header Caption="End Time">
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="8" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="Duration" HeaderText="Duration">
                                    <Header Caption="Duration">
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="9" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                    </Bands>
                    <DisplayLayout AllowColSizingDefault="Free" AllowColumnMovingDefault="OnServer" AllowDeleteDefault="Yes"
                        AllowSortingDefault="OnClient" AllowUpdateDefault="Yes" AutoGenerateColumns="False"
                        BorderCollapseDefault="Separate" HeaderClickActionDefault="SortMulti" Name="UltraWebGrid1"
                        RowHeightDefault="20px" SelectTypeRowDefault="Single" Version="4.00" ViewType="OutlookGroupBy">
                        <GroupByBox Hidden="True">
                            <Style BackColor="ActiveBorder" BorderColor="Window"></Style>
                        </GroupByBox>
                        <GroupByRowStyleDefault BackColor="Control" BorderColor="Window">
                        </GroupByRowStyleDefault>
                        <FooterStyleDefault BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </FooterStyleDefault>
                        <RowStyleDefault BackColor="Window" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="Window" ColorTop="Window" />
                            <Padding Left="3px" />
                        </RowStyleDefault>
                        <FilterOptionsDefault AllString="(All)" EmptyString="(Empty)" NonEmptyString="(NonEmpty)">
                            <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                Font-Size="11px" Width="200px">
                                <Padding Left="2px" />
                            </FilterDropDownStyle>
                            <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                            </FilterHighlightRowStyle>
                        </FilterOptionsDefault>
                        <HeaderStyleDefault BackColor="#5774C2" BorderStyle="Solid" HorizontalAlign="Center" ForeColor="White">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </HeaderStyleDefault>
                        <EditCellStyleDefault BorderStyle="None" BorderWidth="0px">
                        </EditCellStyleDefault>
                        <FrameStyle BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid"
                            BorderWidth="1px" Font-Names="Microsoft Sans Serif" Font-Size="8.25pt" Width="100%">
                        </FrameStyle>
                        <Pager AllowPaging="True" PageSize="20">
                            <Style BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </Pager>
                        <AddNewBox Hidden="False">
                            <Style BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </AddNewBox>
                        <SelectedRowStyleDefault BackColor="#F09D21" ForeColor="White">
                        </SelectedRowStyleDefault>
                    </DisplayLayout>
                </igtbl:UltraWebGrid></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td valign="top">
                <asp:TextBox ID="txt_TapeContentID" runat="server" Visible="False" Width="72px"></asp:TextBox>
                &nbsp; &nbsp; &nbsp;
                            <asp:DropDownList ID="ddl_ProdStatus" runat="server" CssClass="mytext" Width="152px" Visible="False">
                            </asp:DropDownList>
                            <asp:CheckBox ID="Chk_ProdStatus"
                                runat="server" Text="Ignore" Visible="False" />
                <asp:DropDownList ID="ddl_SubCloset" runat="server" CssClass="mytext" Width="152px" Visible="False">
                            </asp:DropDownList>
                            <asp:CheckBox ID="Chk_SubCloset" runat="server" Text="Ignore" Visible="False" />
                            <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_ProdStatus">
                            </cc1:ListSearchExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_SubCloset">
                            </cc1:ListSearchExtender>
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Delete !"
                    TargetControlID="bttnDelete">
                </cc1:ConfirmButtonExtender>
                            <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                                PromptText="" TargetControlID="ddl_ProgramChild">
                            </cc1:ListSearchExtender>
                <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
                            <asp:DropDownList ID="ddl_TapeNumber" runat="server" CssClass="mytext" Width="168px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="ddl_ProgramChild" runat="server" CssClass="mytext" Width="152px" Visible="False">
                            </asp:DropDownList></td>
        </tr>
    </table>
 
</asp:Content>

