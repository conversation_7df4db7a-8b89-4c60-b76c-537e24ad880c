Imports System.Data
Imports System.Web
Partial Class SearchEngine_SearchEnt
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim dt_Keyword As New DataTable


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                txtUrduScript.Attributes.Add("onkeypress", "search()")
                txtUrduScript.Attributes.Add("Dir", "Rtl")


                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")


                    ''***********************''

                    Dim I As Integer
                    Dim J As Integer
                    For I = 1 To 31
                        J = I - 1
                        ddlDay.Items.Insert(J, I)
                    Next

                    ''************************''

                    Dim K As Integer = 0
                    Dim L As Integer = 0
                    For K = 1995 To 2008
                        ddlYear.Items.Insert(L, K)
                        L = L + 1
                    Next


                    ''***********************''

                    Dim I1 As Integer
                    Dim J1 As Integer
                    For I1 = 1 To 31
                        J1 = I1 - 1
                        ddlToDay.Items.Insert(J1, I1)
                    Next

                    ''************************''

                    Dim K1 As Integer = 0
                    Dim L1 As Integer = 0
                    For K1 = 1995 To 2008
                        ddlToYear.Items.Insert(L1, K1)
                        L1 = L1 + 1
                    Next

                    ''***********************''
                    Dim A As Integer = 0
                    Dim B As Integer = 0
                    For A = 1 To 12
                        B = A - 1
                        ddlMonth.Items.Insert(B, A)
                    Next

                    ''***********************''
                    Dim A1 As Integer = 0
                    Dim B1 As Integer = 0
                    For A1 = 1 To 12
                        B1 = A1 - 1
                        ddlToMonth.Items.Insert(B1, A1)
                    Next

                End If

                If Not Request.QueryString("TapeNumber") Is Nothing Then
                    txt_TapeNo_1.Text = Request.QueryString("TapeNumber")
                End If

                If Not Request.QueryString("Tapetype") Is Nothing Then
                    txtTapeType1.Text = Request.QueryString("Tapetype")
                End If

                If Not Request.QueryString("Program") Is Nothing Then
                    txt_Program_1.Text = Request.QueryString("Program")
                End If

                If Not Request.QueryString("Abstract") Is Nothing Then
                    txt_Abstract1.Text = Request.QueryString("Abstract")
                End If

                If Not Request.QueryString("NoteArea") Is Nothing Then
                    txt_NoreArea_1.Text = Request.QueryString("NoteArea")
                End If

                If Not Request.QueryString("Keytype") Is Nothing Then
                    txt_KeyType_1.Text = Request.QueryString("Keytype")
                End If

                If Not Request.QueryString("Keyword") Is Nothing Then
                    txt_keyword_1.Text = Request.QueryString("Keyword")
                End If


                ddlBaseStation.SelectedValue = CInt(Request.Cookies("userinfo")("BaseStationID"))

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Function IsUrduSearchOnly() As Integer
        If (txt_TapeNo_1.Text = "") And (txtTapeType1.Text = "") And (txt_Program_1.Text = "") And (AllAbstract.Text = "") And (AllNoteArea.Text = "") And (txt_KeyType_1.Text = "") And (txt_keyword_1.Text = "") And (txt_keyword_2.Text = "") And (txtEpisodeNo.Text = "") And (txtEntryDate.Text = "") And (txtToDate.Text = "") Then
            Return 1
        Else
            Return 0
        End If
    End Function

    Function IsAnySearch() As Integer

        If (txt_TapeNo_1.Text = "") And (txtTapeType1.Text = "") And (txt_Program_1.Text = "") _
        And (AllAbstract.Text = "") And (AllNoteArea.Text = "") And (txt_KeyType_1.Text = "") _
        And (txt_keyword_1.Text = "") And (txt_keyword_2.Text = "") And (txtEpisodeNo.Text = "") _
        And (txtEntryDate.Text = "") And (txtToDate.Text = "") And (txtUrduScript.Text = "") Then

            Return 0

        Else

            Return 1

        End If
    End Function

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        lblErr.Text = ""
        Dim AnySearch As Integer = IsAnySearch()
        If AnySearch = 0 Then
            lblErr.Text = "Attention: No Searching criteria are enterted.."
            Exit Sub
        End If

        Dim UrduSearchOnly As Integer = IsUrduSearchOnly()


        If UrduSearchOnly = 1 Then
            GetSearch_ByUrduScript()
        Else
            FillControls()
            Try
                ''******************************************************************'
                ''*************************** Tape Number **************************'
                ''******************************************************************'

                Dim IgnoreTape As String
                If txt_TapeNo_1.Text = "" And txt_TapeNo_2.Text = "" And txt_TapeNo_3.Text = "" And txt_TapeNo_4.Text = "" And txt_TapeNo_5.Text = "" Then
                    IgnoreTape = "Yes"
                Else
                    IgnoreTape = "No"
                End If

                Dim arr1 As Array = Split(txt_TapeNo_1.Text, "#")
                If arr1.Length = 2 Then
                    txt_TapeNo_1.Text = arr1(1)
                End If
                Dim Tape1 As String
                If Trim(txt_TapeNo_1.Text) = "" Then
                    Tape1 = ""
                Else
                    Tape1 = txt_TapeNo_1.Text
                    Tape1 = Trim(Tape1)
                End If


                Dim arr2 As Array = Split(txt_TapeNo_2.Text, "#")
                If arr2.Length = 2 Then
                    txt_TapeNo_2.Text = arr2(1)
                End If
                Dim Tape2 As String
                If Trim(txt_TapeNo_2.Text) = "" Then
                    Tape2 = ""
                Else
                    Tape2 = txt_TapeNo_2.Text
                    Tape2 = Trim(Tape2)
                End If


                Dim arr3 As Array = Split(txt_TapeNo_3.Text, "#")
                If arr3.Length = 2 Then
                    txt_TapeNo_3.Text = arr3(1)
                End If
                Dim Tape3 As String
                If Trim(txt_TapeNo_3.Text) = "" Then
                    Tape3 = ""
                Else
                    Tape3 = txt_TapeNo_3.Text
                    Tape3 = Trim(Tape3)
                End If

                Dim arr4 As Array = Split(txt_TapeNo_4.Text, "#")
                If arr4.Length = 2 Then
                    txt_TapeNo_4.Text = arr4(1)
                End If
                Dim Tape4 As String
                If Trim(txt_TapeNo_4.Text) = "" Then
                    Tape4 = ""
                Else
                    Tape4 = txt_TapeNo_4.Text
                    Tape4 = Trim(Tape4)
                End If

                Dim arr5 As Array = Split(txt_TapeNo_5.Text, "#")
                If arr5.Length = 2 Then
                    txt_TapeNo_5.Text = arr5(1)
                End If
                Dim Tape5 As String
                If Trim(txt_TapeNo_5.Text) = "" Then
                    Tape5 = ""
                Else
                    Tape5 = txt_TapeNo_5.Text
                    Tape5 = Trim(Tape5)
                End If

                Dim Operator_Tape1_2 As String = Tape_option_2.SelectedItem.Text
                Dim Operator_Tape2_3 As String = Tape_option_2.SelectedItem.Text
                Dim Operator_Tape3_4 As String = Tape_option_3.SelectedItem.Text
                Dim Operator_Tape4_5 As String = Tape_option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Program *****************************''
                ''******************************************************************'

                Dim IngnoreProgram As String
                If txt_Program_1.Text = "" And txt_Program_2.Text = "" And txt_Program_3.Text = "" And txt_Program_4.Text = "" And txt_Program_5.Text = "" Then
                    IngnoreProgram = "Yes"
                Else
                    IngnoreProgram = "No"
                End If
                
                Dim Program1 As String
                If Trim(txt_Program_1.Text) = "" Then
                    Program1 = ""
                Else
                    Program1 = txt_Program_1.Text
                    Program1 = Trim(Program1)
                End If

                Dim Program2 As String
                If Trim(txt_Program_2.Text) = "" Then
                    Program2 = ""
                Else
                    Program2 = txt_Program_2.Text
                    Program2 = Trim(Program2)
                End If

                Dim Program3 As String
                If Trim(txt_Program_3.Text) = "" Then
                    Program3 = ""
                Else
                    Program3 = txt_Program_3.Text
                    Program3 = Trim(Program3)
                End If

                Dim Program4 As String
                If Trim(txt_Program_4.Text) = "" Then
                    Program4 = ""
                Else
                    Program4 = txt_Program_4.Text
                    Program4 = Trim(Program4)
                End If

                Dim Program5 As String
                If Trim(txt_Program_5.Text) = "" Then
                    Program5 = ""
                Else
                    Program5 = txt_Program_5.Text
                    Program5 = Trim(Program5)
                End If

                Dim Operator_Program1_2 As String = Program_Option_1.SelectedItem.Text
                Dim Operator_Program2_3 As String = Program_Option_2.SelectedItem.Text
                Dim Operator_Program3_4 As String = Program_Option_3.SelectedItem.Text
                Dim Operator_Program4_5 As String = Program_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Note Area ***************************''
                ''******************************************************************'

                Dim IngnoreNoteArea As String
                If txt_NoreArea_1.Text = "" And txt_NoreArea_2.Text = "" And txt_NoreArea_3.Text = "" And txt_NoreArea_4.Text = "" And txt_NoreArea_5.Text = "" Then
                    IngnoreNoteArea = "Yes"
                Else
                    IngnoreNoteArea = "No"
                End If

                Dim NoteArea1 As String
                If Trim(txt_NoreArea_1.Text) = "" Then
                    NoteArea1 = ""
                Else
                    NoteArea1 = txt_NoreArea_1.Text
                    NoteArea1 = Trim(NoteArea1)
                    NoteArea1 = NoteArea(NoteArea1)
                    NoteArea1 = FormatText(NoteArea1)
                End If

                Dim NoteArea2 As String
                If Trim(txt_NoreArea_2.Text) = "" Then
                    NoteArea2 = ""
                Else
                    NoteArea2 = txt_NoreArea_2.Text
                    NoteArea2 = Trim(NoteArea2)
                    NoteArea2 = NoteArea(NoteArea2)
                    NoteArea2 = FormatText(NoteArea2)
                End If

                Dim NoteArea3 As String
                If Trim(txt_NoreArea_3.Text) = "" Then
                    NoteArea3 = ""
                Else
                    NoteArea3 = txt_NoreArea_3.Text
                    NoteArea3 = Trim(NoteArea3)
                    NoteArea3 = NoteArea(NoteArea3)
                    NoteArea3 = FormatText(NoteArea3)
                End If

                Dim NoteArea4 As String
                If Trim(txt_NoreArea_4.Text) = "" Then
                    NoteArea4 = ""
                Else
                    NoteArea4 = txt_NoreArea_4.Text
                    NoteArea4 = Trim(NoteArea4)
                    NoteArea4 = NoteArea(NoteArea4)
                    NoteArea4 = FormatText(NoteArea4)
                End If

                Dim NoteArea5 As String
                If Trim(txt_NoreArea_5.Text) = "" Then
                    NoteArea5 = ""
                Else
                    NoteArea5 = txt_NoreArea_5.Text
                    NoteArea5 = Trim(NoteArea5)
                    NoteArea5 = NoteArea(NoteArea5)
                    NoteArea5 = FormatText(NoteArea5)
                End If

                Dim Operator_NoteArea1_2 As String = Note_Option_1.SelectedItem.Text
                Dim Operator_NoteArea2_3 As String = Note_Option_2.SelectedItem.Text
                Dim Operator_NoteArea3_4 As String = Note_Option_3.SelectedItem.Text
                Dim Operator_NoteArea4_5 As String = Note_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Key Word  ***************************''
                ''******************************************************************'

                Dim IngnoreKeyWords As String
                If txt_keyword_1.Text = "" And txt_keyword_2.Text = "" And txt_keyword_3.Text = "" And txt_keyword_4.Text = "" And txt_keyword_5.Text = "" Then
                    IngnoreKeyWords = "Yes"
                Else
                    IngnoreKeyWords = "No"
                End If

                Dim KeyWord1 As String
                If Trim(txt_keyword_1.Text) = "" Then
                    KeyWord1 = ""
                Else
                    KeyWord1 = txt_keyword_1.Text
                    KeyWord1 = Trim(KeyWord1)
                    
                End If

                Dim KeyWord2 As String
                If Trim(txt_keyword_2.Text) = "" Then
                    KeyWord2 = ""
                Else
                    KeyWord2 = txt_keyword_2.Text
                    KeyWord2 = Trim(KeyWord2)
                    KeyWord2 = KW(KeyWord2)
                    KeyWord2 = FormatText(KeyWord2)
                End If

                Dim KeyWord3 As String
                If Trim(txt_keyword_3.Text) = "" Then
                    KeyWord3 = ""
                Else
                    KeyWord3 = txt_keyword_3.Text
                    KeyWord3 = Trim(KeyWord3)
                    KeyWord3 = KW(KeyWord3)
                    KeyWord3 = FormatText(KeyWord3)
                End If

                Dim KeyWord4 As String
                If Trim(txt_keyword_4.Text) = "" Then
                    KeyWord4 = ""
                Else
                    KeyWord4 = txt_keyword_4.Text
                    KeyWord4 = Trim(KeyWord4)
                    KeyWord4 = KW(KeyWord4)
                    KeyWord4 = FormatText(KeyWord4)
                End If

                Dim KeyWord5 As String
                If Trim(txt_keyword_5.Text) = "" Then
                    KeyWord5 = ""
                Else
                    KeyWord5 = txt_keyword_5.Text
                    KeyWord5 = Trim(KeyWord5)
                    KeyWord5 = KW(KeyWord5)
                    KeyWord5 = FormatText(KeyWord5)
                End If

                Dim Operator_KeyWord1_2 As String = KW_Option_1.SelectedItem.Text
                Dim Operator_KeyWord2_3 As String = KW_Option_2.SelectedItem.Text
                Dim Operator_KeyWord3_4 As String = KW_Option_3.SelectedItem.Text
                Dim Operator_KeyWord4_5 As String = KW_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Key Types  **************************''
                ''******************************************************************'

                Dim IngnoreKeyTypes As String
                If txt_KeyType_1.Text = "" And txt_KeyType_2.Text = "" And txt_KeyType_3.Text = "" And txt_KeyType_4.Text = "" And txt_KeyType_5.Text = "" Then
                    IngnoreKeyTypes = "Yes"
                Else
                    IngnoreKeyTypes = "No"
                End If
               
                Dim KeyType1 As String
                If Trim(txt_KeyType_1.Text) = "" Then
                    KeyType1 = ""

                Else
                    KeyType1 = txt_KeyType_1.Text
                    KeyType1 = Trim(KeyType1)
                End If

                Dim KeyType2 As String
                If Trim(txt_KeyType_2.Text) = "" Then
                    KeyType2 = ""
                Else
                    KeyType2 = txt_KeyType_2.Text
                    KeyType2 = Trim(KeyType2)
                    KeyType2 = FormatText(KeyType2)
                End If

                Dim KeyType3 As String
                If Trim(txt_KeyType_3.Text) = "" Then
                    KeyType3 = ""
                Else
                    KeyType3 = txt_KeyType_3.Text
                    KeyType3 = Trim(KeyType3)
                    KeyType3 = FormatText(KeyType3)
                End If

                Dim KeyType4 As String
                If Trim(txt_KeyType_4.Text) = "" Then
                    KeyType4 = ""
                Else
                    KeyType4 = txt_KeyType_4.Text
                    KeyType4 = Trim(KeyType4)
                    KeyType4 = FormatText(KeyType4)
                End If

                Dim KeyType5 As String
                If Trim(txt_KeyType_5.Text) = "" Then
                    KeyType5 = ""
                Else
                    KeyType5 = txt_KeyType_5.Text
                    KeyType5 = Trim(KeyType5)
                    KeyType5 = FormatText(KeyType5)
                End If

                Dim Operator_KeyType1_2 As String = KT_Option_1.SelectedItem.Text
                Dim Operator_KeyType2_3 As String = KT_Option_2.SelectedItem.Text
                Dim Operator_KeyType3_4 As String = KT_Option_3.SelectedItem.Text
                Dim Operator_KeyType4_5 As String = KT_Option_4.SelectedItem.Text

                ''******************************************************************'
                ''*************************** Episode No **************************''
                ''******************************************************************'
                Dim Episode1 As String = ""
                Dim Episode2 As String = ""
                Dim Episode3 As String = ""
                Dim Episode4 As String = ""
                Dim Episode5 As String = ""

                Dim IgnoreEpisode As String

                If txtEpisodeNo.Text = "" Then
                    IgnoreEpisode = "Yes"
                    Episode1 = ""
                    Episode2 = ""
                    Episode3 = ""
                    Episode4 = ""
                    Episode5 = ""
                Else
                    IgnoreEpisode = "No"
                    Dim Ep As String = txtEpisodeNo.Text

                    Dim stringItems() As String = Ep.Split("~")
                    Dim k As Integer
                    k = stringItems.Length

                    If txtEpisodeNo.Text = "" Then
                        Episode1 = ""
                        Episode2 = ""
                        Episode3 = ""
                        Episode4 = ""
                        Episode5 = ""
                    End If

                    If k <> 0 Then
                        Episode1 = stringItems(0).ToString
                    End If


                    If k = 1 Then
                        Episode2 = ""
                        Episode3 = ""
                        Episode4 = ""
                        Episode5 = ""
                    End If

                    If k > 1 And k < 3 Then
                        Episode2 = stringItems(1).ToString
                        Episode3 = ""
                        Episode4 = ""
                        Episode5 = ""

                    End If
                    If k > 2 And k < 4 Then
                        Episode2 = stringItems(1).ToString
                        Episode3 = stringItems(2).ToString
                        Episode4 = ""
                        Episode5 = ""
                    End If

                    If k > 3 And k < 5 Then
                        Episode2 = stringItems(1).ToString
                        Episode3 = stringItems(2).ToString
                        Episode4 = stringItems(3).ToString
                        Episode5 = ""
                    End If

                    If k > 4 And k < 6 Then
                        Episode2 = stringItems(1).ToString
                        Episode3 = stringItems(2).ToString
                        Episode4 = stringItems(3).ToString
                        Episode5 = stringItems(4).ToString
                    End If

                End If


                ''******************************************************************'
                ''*************************** To Date *****************************''
                ''******************************************************************'
                Dim IgnoreDate As String
                If txtEntryDate.Text = "" Then
                    IgnoreDate = "Yes"
                Else
                    IgnoreDate = "No"
                End If

                Dim EntryDate As String
                EntryDate = txtEntryDate.Text

                Dim ToDate As String
                If txtToDate.Text = "" And txtEntryDate.Text <> "" Then
                    ToDate = txtEntryDate.Text
                Else
                    ToDate = txtToDate.Text
                End If


                ''*****************************************************************'
                ''*************************** Abstract  **************************''
                ''*****************************************************************'

                Dim IgnoreAbstract As String
                If txt_Abstract1.Text = "" And txt_Abstract2.Text = "" And txt_Abstract3.Text = "" And txt_Abstract4.Text = "" And txt_Abstract5.Text = "" Then
                    IgnoreAbstract = "Yes"
                Else
                    IgnoreAbstract = "No"
                End If

                Dim Abstract1 As String
                If Trim(txt_Abstract1.Text) = "" Then
                    Abstract1 = ""
                Else
                    Abstract1 = txt_Abstract1.Text
                    Abstract1 = Trim(Abstract1)
                    Abstract1 = FormatText(Abstract1)
                End If

                Dim Abstract2 As String
                If Trim(txt_Abstract2.Text) = "" Then
                    Abstract2 = ""
                Else
                    Abstract2 = txt_Abstract2.Text
                    Abstract2 = Trim(Abstract2)
                    Abstract2 = FormatText(Abstract2)
                End If

                Dim Abstract3 As String
                If Trim(txt_Abstract3.Text) = "" Then
                    Abstract3 = ""
                Else
                    Abstract3 = txt_Abstract3.Text
                    Abstract3 = Trim(Abstract3)
                    Abstract3 = FormatText(Abstract3)
                End If

                Dim Abstract4 As String
                If Trim(txt_Abstract4.Text) = "" Then
                    Abstract4 = ""
                Else
                    Abstract4 = txt_Abstract4.Text
                    Abstract4 = Trim(Abstract4)
                    Abstract4 = FormatText(Abstract4)
                End If

                Dim Abstract5 As String
                If Trim(txt_Abstract5.Text) = "" Then
                    Abstract5 = ""
                Else
                    Abstract5 = txt_Abstract5.Text
                    Abstract5 = Trim(Abstract5)
                    Abstract5 = FormatText(Abstract5)
                End If

                Dim Operator_Abstract1_2 As String = Abstract_Option_1.SelectedItem.Text
                Dim Operator_Abstract2_3 As String = Abstract_Option_2.SelectedItem.Text
                Dim Operator_Abstract3_4 As String = Abstract_Option_3.SelectedItem.Text
                Dim Operator_Abstract4_5 As String = Abstract_Option_4.SelectedItem.Text


                ''*****************************************************************'
                ''*************************** Tape Type  *************************''
                ''*****************************************************************'

                Dim IgnoreTapeType As String
                If txtTapeType1.Text = "" Then
                    IgnoreTapeType = "Yes"
                Else
                    IgnoreTapeType = "No"
                End If

                Dim TapeType1 As String
                If Trim(txtTapeType1.Text) = "" Then
                    TapeType1 = ""
                Else
                    TapeType1 = txtTapeType1.Text
                    TapeType1 = Trim(TapeType1)
                End If
                ''*************************************************************''
                Dim Qry As String
                Qry = "@ContentType=23&@IngnoreTape=" + IgnoreTape + "" & _
                                    "&@Tape1=" + Tape1 & _
                                    "&@Tape2=" + Tape2 & _
                                    "&@Tape3=" + Tape3 & _
                                    "&@Tape4=" + Tape4 & _
                                    "&@Tape5=" + Tape5 & _
                                    "&@Operator_Tape1_2=" + Operator_Tape1_2 & _
                                    "&@Operator_Tape2_3=" + Operator_Tape2_3 & _
                                    "&@Operator_Tape3_4=" + Operator_Tape3_4 & _
                                    "&@Operator_Tape4_5=" + Operator_Tape4_5 & _
                                    "&@IngnoreKeyWords=" + IngnoreKeyWords & _
                                    "&@IngnoreProposedSlug=" + IngnoreProgram & _
                                    "&@ProposedSlug1=" + Program1 & _
                                    "&@ProposedSlug2=" + Program2 & _
                                    "&@ProposedSlug3=" + Program3 & _
                                    "&@ProposedSlug4=" + Program4 & _
                                    "&@ProposedSlug5=" + Program5 & _
                                    "&@Operator_ProposedSlug1_2=" + Operator_Program1_2 & _
                                    "&@Operator_ProposedSlug2_3=" + Operator_Program2_3 & _
                                    "&@Operator_ProposedSlug3_4=" + Operator_Program3_4 & _
                                    "&@Operator_ProposedSlug4_5=" + Operator_Program4_5 & _
                                    "&@IngnoreReporterSlug=" + IngnoreNoteArea & _
                                    "&@RptSlug1=" + NoteArea1 & _
                                    "&@RptSlug2=" + NoteArea2 & _
                                    "&@RptSlug3=" + NoteArea3 & _
                                    "&@RptSlug4=" + NoteArea4 & _
                                    "&@RptSlug5=" + NoteArea5 & _
                                    "&@Operator_RptSlug1_2=" + Operator_NoteArea1_2 & _
                                    "&@Operator_RptSlug2_3=" + Operator_NoteArea2_3 & _
                                    "&@Operator_RptSlug3_4=" + Operator_NoteArea3_4 & _
                                    "&@Operator_RptSlug4_5=" + Operator_NoteArea4_5 & _
                                    "&@KeyWord1=" + HttpUtility.UrlEncode( KeyWord1) & _
                                    "&@KeyWord2=" + KeyWord2 & _
                                    "&@KeyWord3=" + KeyWord3 & _
                                    "&@KeyWord4=" + KeyWord4 & _
                                    "&@KeyWord5=" + KeyWord5 & _
                                    "&@Operator_KeyWord1_2=" + Operator_KeyWord1_2 & _
                                    "&@Operator_KeyWord2_3=" + Operator_KeyWord2_3 & _
                                    "&@Operator_KeyWord3_4=" + Operator_KeyWord3_4 & _
                                    "&@Operator_KeyWord4_5=" + Operator_KeyWord4_5 & _
                                    "&@IngnoreKeyTypes=" + IngnoreKeyTypes & _
                                    "&@KeyType1=" + KeyType1 & _
                                    "&@KeyType2=" + KeyType2 & _
                                    "&@KeyType3=" + KeyType3 & _
                                    "&@KeyType4=" + KeyType4 & _
                                    "&@KeyType5=" + KeyType5 & _
                                    "&@Operator_KeyType1_2=" + Operator_KeyType1_2 & _
                                    "&@Operator_KeyType2_3=" + Operator_KeyType2_3 & _
                                    "&@Operator_KeyType3_4=" + Operator_KeyType3_4 & _
                                    "&@Operator_KeyType4_5=" + Operator_KeyType4_5 & _
                                    "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue & _
                                    "&@IgnoreDate=" + IgnoreDate & _
                                    "&@EntryDate=" + EntryDate & _
                                    "&@IgnoreEpisode=" + IgnoreEpisode & _
                                    "&@Episode1=" + Episode1 & _
                                    "&@Episode2=" + Episode2 & _
                                    "&@Episode3=" + Episode3 & _
                                    "&@Episode4=" + Episode4 & _
                                    "&@Episode5=" + Episode5 & _
                                    "&@IgnoreAbstract=" + IgnoreAbstract & _
                                    "&@Abstract1=" + Abstract1 & _
                                    "&@Abstract2=" + Abstract2 & _
                                    "&@Abstract3=" + Abstract3 & _
                                    "&@Abstract4=" + Abstract4 & _
                                    "&@Abstract5=" + Abstract5 & _
                                    "&@Operator_Abstract1_2=" + Operator_Abstract1_2 & _
                                    "&@Operator_Abstract2_3=" + Operator_Abstract2_3 & _
                                    "&@Operator_Abstract3_4=" + Operator_Abstract3_4 & _
                                    "&@Operator_Abstract4_5=" + Operator_Abstract4_5 & _
                                    "&@IgnoreTapeType=" + IgnoreTapeType & _
                                    "&@TapeType1=" + TapeType1 & _
                                    "&@ToDate=" + ToDate & _
                                    "&@BaseStationID=" + ddlBaseStation.SelectedValue

                ''*************************************************************''


                Dim Arr As Array = Split(txtUrduScript.Text, "+")
                If Arr.Length = 5 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(3) + "%&@UrduScript5=%" + Arr(4) + "%"
                ElseIf Arr.Length = 4 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(3) + "%&@UrduScript5=%" + Arr(3) + "%"
                ElseIf Arr.Length = 3 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(2) + "%&@UrduScript4=%" + Arr(2) + "%&@UrduScript5=%" + Arr(2) + "%"
                ElseIf Arr.Length = 2 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(1) + "%&@UrduScript3=%" + Arr(1) + "%&@UrduScript4=%" + Arr(1) + "%&@UrduScript5=%" + Arr(1) + "%"
                ElseIf Arr.Length = 1 Then
                    Qry += "&@UrduScript1=%" + Arr(0) + "%&@UrduScript2=%" + Arr(0) + "%&@UrduScript3=%" + Arr(0) + "%&@UrduScript4=%" + Arr(0) + "%&@UrduScript5=%" + Arr(0) + "%"
                End If

                Qry += "&@IsUrduSearchOnly=0"

                Response.Write("<script type='text/javascript'>detailedresults=window.open('Entertainment_Results.aspx?" & Qry & "');</script>")

                MostSearchElements()

                SetLableSize()

                Try
                    Me.Session.Add("rowindex", -1)
                Catch ex As Exception
                End Try

            Catch ex As Exception
                Throw
            End Try
        End If

    End Sub

    Private Sub SetLableSize()
        Label7.Font.Size = 8
        Label8.Font.Size = 8
        Label9.Font.Size = 8
        Label10.Font.Size = 8
        Label11.Font.Size = 8
        Label12.Font.Size = 8
        Label13.Font.Size = 8
        Label14.Font.Size = 8
        Label15.Font.Size = 8
        Label16.Font.Size = 8
        Label17.Font.Size = 8
        Label18.Font.Size = 8
        txt_Program_1.Font.Size = 8
    End Sub

    Private Function KW(ByVal KW1 As String) As String
        Dim arr As Array = Split(KW1, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = KW1
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            KW1 = a

        Else
            Dim b As String
            b = KW1
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            KW1 = b

        End If
        Return KW1
    End Function

    Private Function Program(ByVal prg As String) As String
        Dim arr As Array = Split(prg, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = prg
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            prg = a

        Else
            Dim b As String
            b = prg
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            prg = b

        End If
        Return prg
    End Function

    Private Function NoteArea(ByVal NA As String) As String
        Dim arr As Array = Split(NA, " (")
        If arr.Length <> 1 Then
            Dim a As String
            a = NA
            a = a.Replace("( ", "(")
            a = a.Replace("(", """(")
            a = a.Replace(" )", ")")
            a = a.Replace(")", ")""")
            NA = a

        Else
            Dim b As String
            b = NA
            b = b.Replace("(", " ""(")
            b = b.Replace(")", ")""")
            NA = b

        End If
        Return NA
    End Function

    Private Function TrimTxt(ByVal txt As String)
        Dim i As Integer
        Dim q As String
        For i = 0 To 5
            q = txt
            Dim lastchar As Char
            lastchar = q.Substring(q.Length - 1)
            If lastchar = " " Then
                txt = Left(q, q.Length - 1)
            End If
        Next
        q = txt
        Return q
    End Function

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click

        ''*****************************''
        ''********* Tape Number *******''
        ''*****************************''

        txt_TapeNo_1.Text = String.Empty
        txt_TapeNo_2.Text = String.Empty
        txt_TapeNo_3.Text = String.Empty
        txt_TapeNo_4.Text = String.Empty
        txt_TapeNo_5.Text = String.Empty

        ''*****************************''
        ''************ Program ********''
        ''*****************************''

        txt_Program_1.Text = String.Empty
        txt_Program_2.Text = String.Empty
        txt_Program_3.Text = String.Empty
        txt_Program_4.Text = String.Empty
        txt_Program_5.Text = String.Empty

        ''*****************************''
        ''********** Note Area ********''
        ''*****************************''

        txt_NoreArea_1.Text = String.Empty
        txt_NoreArea_2.Text = String.Empty
        txt_NoreArea_3.Text = String.Empty
        txt_NoreArea_4.Text = String.Empty
        txt_NoreArea_5.Text = String.Empty

        ''*****************************''
        ''********** Key Word *********''
        ''*****************************''

        txt_keyword_1.Text = String.Empty
        txt_keyword_2.Text = String.Empty
        txt_keyword_3.Text = String.Empty
        txt_keyword_4.Text = String.Empty
        txt_keyword_5.Text = String.Empty

        ''*****************************''
        ''********** Key Types ********''
        ''*****************************''

        txt_KeyType_1.Text = String.Empty
        txt_KeyType_2.Text = String.Empty
        txt_KeyType_3.Text = String.Empty
        txt_KeyType_4.Text = String.Empty
        txt_KeyType_5.Text = String.Empty

        ''*****************************''
        ''********** Entry Date *******''
        ''*****************************''

        txtEntryDate.Text = String.Empty

        ''*****************************''
        ''********** Episode **********''
        ''*****************************''

        txtEpisodeNo.Text = String.Empty

        ''*****************************''
        ''****** Results Per Page *****''
        ''*****************************''

        ddl_ResultPerPage.SelectedIndex = 0
        chkEpisode.Checked = True
        chkDate.Checked = True

        ''***************''
        ''****** All ****''
        ''***************''

        AllAbstract.Text = String.Empty
        AllNoteArea.Text = String.Empty
        txtTapeType1.Text = String.Empty

        txtUrduScript.Text = String.Empty

    End Sub

    Private Sub FillControls()

        ''**************************''
        ''******** Abstract ********''
        ''**************************''

        Dim Description As String = AllAbstract.Text

        txt_Abstract1.Text = ""
        txt_Abstract2.Text = ""
        txt_Abstract3.Text = ""
        txt_Abstract4.Text = ""
        txt_Abstract5.Text = ""


        Dim stringItems() As String = Description.Split("~")
        Dim myArrayList As New ArrayList
        Dim k As Integer
        k = stringItems.Length

        If AllAbstract.Text = "" Then
            txt_Abstract1.Text = ""
            txt_Abstract2.Text = ""
            txt_Abstract3.Text = ""
            txt_Abstract4.Text = ""
            txt_Abstract5.Text = ""
        End If

        If k <> 0 Then
            txt_Abstract1.Text = stringItems(0).ToString
        End If


        If k = 1 Then
            txt_Abstract2.Text = ""
            txt_Abstract3.Text = ""
            txt_Abstract4.Text = ""
            txt_Abstract5.Text = ""
        End If

        If k > 1 And k < 3 Then
            txt_Abstract2.Text = stringItems(1).ToString
            txt_Abstract3.Text = ""
            txt_Abstract4.Text = ""
            txt_Abstract5.Text = ""

        End If
        If k > 2 And k < 4 Then
            txt_Abstract2.Text = stringItems(1).ToString
            txt_Abstract3.Text = stringItems(2).ToString
            txt_Abstract4.Text = ""
            txt_Abstract5.Text = ""
        End If

        If k > 3 And k < 5 Then
            txt_Abstract2.Text = stringItems(1).ToString
            txt_Abstract3.Text = stringItems(2).ToString
            txt_Abstract4.Text = stringItems(3).ToString
            txt_Abstract5.Text = ""
        End If

        If k > 4 And k < 6 Then
            txt_Abstract2.Text = stringItems(1).ToString
            txt_Abstract3.Text = stringItems(2).ToString
            txt_Abstract4.Text = stringItems(3).ToString
            txt_Abstract5.Text = stringItems(4).ToString
        End If

        ''***************************''
        ''******** Note Area ********''
        ''***************************''

        Dim NoteArea As String = AllNoteArea.Text

        txt_NoreArea_1.Text = ""
        txt_NoreArea_2.Text = ""
        txt_NoreArea_3.Text = ""
        txt_NoreArea_4.Text = ""
        txt_NoreArea_5.Text = ""

        Dim NoteArea_stringItems() As String = NoteArea.Split("~")
        Dim NoteArea_myArrayList As New ArrayList
        Dim k1 As Integer
        k1 = NoteArea_stringItems.Length

        If AllNoteArea.Text = "" Then
            txt_NoreArea_1.Text = ""
            txt_NoreArea_2.Text = ""
            txt_NoreArea_3.Text = ""
            txt_NoreArea_4.Text = ""
            txt_NoreArea_5.Text = ""
        End If

        If k1 <> 0 Then
            txt_NoreArea_1.Text = NoteArea_stringItems(0).ToString
        End If


        If k1 = 1 Then
            txt_NoreArea_2.Text = ""
            txt_NoreArea_3.Text = ""
            txt_NoreArea_4.Text = ""
            txt_NoreArea_5.Text = ""
        End If

        If k1 > 1 And k1 < 3 Then
            txt_NoreArea_2.Text = NoteArea_stringItems(1).ToString
            txt_NoreArea_3.Text = ""
            txt_NoreArea_4.Text = ""
            txt_NoreArea_5.Text = ""

        End If
        If k1 > 2 And k1 < 4 Then
            txt_NoreArea_2.Text = NoteArea_stringItems(1).ToString
            txt_NoreArea_3.Text = NoteArea_stringItems(2).ToString
            txt_NoreArea_4.Text = ""
            txt_NoreArea_5.Text = ""
        End If

        If k1 > 3 And k1 < 5 Then
            txt_NoreArea_2.Text = NoteArea_stringItems(1).ToString
            txt_NoreArea_3.Text = NoteArea_stringItems(2).ToString
            txt_NoreArea_4.Text = NoteArea_stringItems(3).ToString
            txt_NoreArea_5.Text = ""
        End If

        If k1 > 4 And k1 < 6 Then
            txt_NoreArea_2.Text = NoteArea_stringItems(1).ToString
            txt_NoreArea_3.Text = NoteArea_stringItems(2).ToString
            txt_NoreArea_4.Text = NoteArea_stringItems(3).ToString
            txt_NoreArea_5.Text = NoteArea_stringItems(4).ToString
        End If

       
    End Sub

    Private Function FormatText(ByVal Text As String) As String
        Try
            Dim A As String = Text
            A = A.Replace("_", " ")
            A = A.Replace("!", " ")
            A = A.Replace("@", " ")
            A = A.Replace("""", " ")
            A = A.Replace("#", " ")
            A = A.Replace("$", " ")
            A = A.Replace("%", " ")
            A = A.Replace("^", " ")
            A = A.Replace("&", " ")
            A = A.Replace("(", " ")
            A = A.Replace(")", " ")
            A = A.Replace("+", " ")
            A = A.Replace("|", " ")
            A = A.Replace("~", " ")
            A = A.Replace("`", " ")
            A = A.Replace("-", " ")
            A = A.Replace("=", " ")
            A = A.Replace(",", " ")
            A = A.Replace(".", " ")
            A = A.Replace("/", " ")
            A = A.Replace("?", " ")
            A = A.Replace(">", " ")
            A = A.Replace("<", " ")
            A = A.Replace(";", " ")
            A = A.Replace(":", " ")
            A = A.Replace("'", " ")
            A = A.Replace("[", " ")
            A = A.Replace("]", " ")
            A = A.Replace("{", " ")
            A = A.Replace("}", " ")
            A = A.Replace("    ", " ")
            A = A.Replace("   ", " ")
            A = A.Replace("  ", " ")
            A = A.Replace("  ", " ")
            Text = A
            Return Text
        Catch ex As Exception
            Throw
        End Try


    End Function

    Private Sub MostSearchElements()
        Try
            Dim ObjSave As New BusinessFacade.Reports()

            ''********** Tape Number **********''
            If txt_TapeNo_1.Text <> "" Then
                ObjSave.SearchType = "TapeNumber"
                ObjSave.SearchElement = txt_TapeNo_1.Text
                ObjSave.SearchTextBox = "txt_TapeNo_1.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''*********** Tape Type ***********''
            If txtTapeType1.Text <> "" Then
                ObjSave.SearchType = "Tapetype"
                ObjSave.SearchElement = txtTapeType1.Text
                ObjSave.SearchTextBox = "txtTapeType1.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ Program ************''
            If txt_Program_1.Text <> "" Then
                ObjSave.SearchType = "Program"
                ObjSave.SearchElement = txt_Program_1.Text
                ObjSave.SearchTextBox = "txt_Program_1.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If


            ''************ Abstract ***********''
            If AllAbstract.Text <> "" Then
                ObjSave.SearchType = "Abstract"
                ObjSave.SearchElement = AllAbstract.Text
                ObjSave.SearchTextBox = "AllAbstract.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ Note Area ***********''
            If AllNoteArea.Text <> "" Then
                ObjSave.SearchType = "NoteArea"
                ObjSave.SearchElement = AllNoteArea.Text
                ObjSave.SearchTextBox = "AllNoteArea.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ Key Type ***********''
            If txt_KeyType_1.Text <> "" Then
                ObjSave.SearchType = "Keytype"
                ObjSave.SearchElement = txt_KeyType_1.Text
                ObjSave.SearchTextBox = "txt_KeyType_1.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ Key Word ***********''
            If txt_keyword_1.Text <> "" Then
                ObjSave.SearchType = "Keyword"
                ObjSave.SearchElement = txt_keyword_1.Text
                ObjSave.SearchTextBox = "txt_keyword_1.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

            ''************ Keyword_2 ***********''
            If txt_keyword_2.Text <> "" Then
                ObjSave.SearchType = "Keyword"
                ObjSave.SearchElement = txt_keyword_2.Text
                ObjSave.SearchTextBox = "txt_keyword_2.Text"
                ObjSave.ContentType = "Ent"
                ObjSave.Insert_MostSearchedElements()
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetSearch_ByUrduScript()

        Dim Arr As Array = Split(txtUrduScript.Text, "+")

        If Arr.Length < 6 Then
            Dim Qry As String = ""
            If Arr.Length = 5 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(3) + "&@UrduScript5=" + Arr(4) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 4 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(3) + "&@UrduScript5=" + Arr(3) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 3 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(2) + "&@UrduScript4=" + Arr(2) + "&@UrduScript5=" + Arr(2) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 2 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(1) + "&@UrduScript3=" + Arr(1) + "&@UrduScript4=" + Arr(1) + "&@UrduScript5=" + Arr(1) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            ElseIf Arr.Length = 1 Then
                Qry = "@UrduScript1=" + Arr(0) + "&@UrduScript2=" + Arr(0) + "&@UrduScript3=" + Arr(0) + "&@UrduScript4=" + Arr(0) + "&@UrduScript5=" + Arr(0) + "&@No_of_Record_Per_Page=" + ddl_ResultPerPage.SelectedValue
            End If

            Qry += "&@IsUrduSearchOnly=1"
            Qry += "&@BaseStationID=" + ddlBaseStation.SelectedValue.ToString()


            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('Entertainment_Results.aspx?" + Qry + "', 'mywindow','width=1000, height=760, left=50, top=30, menubar=yes, status=yes, location=yes, toolbar=yes, scrollbars=yes, resizable=yes'); "
            script = script + "}</script>"
            Page.RegisterClientScriptBlock("test", script)

        End If

    End Sub



    Protected Sub btnView_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnView.Click
        Response.Write("<script type='text/javascript'>detailedresults=window.open('frmTapeAddforIssue.aspx','_blank','toolbar=yes, scrollbars=yes, resizable=yes, top=100, left=100, width=400, height=400');</script>")
    End Sub
End Class
