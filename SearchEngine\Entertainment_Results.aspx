<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Entertainment_Results.aspx.vb" 
Inherits="SearchEngine_Entertainment_Results" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
    

 <script src="searchhi.js" type="text/javascript" language="JavaScript"></script>
    <script type="text/javascript" language="javascript">
    <!--
    
    function loadTheDocument()
    {
        //document.searchhi.txtSearchText.value = searchhi_string; 
        if( location.hash.length > 1 ) location.hash = location.hash;    
        
     }
    function CallHighlighting()
    {
        bttnHighlight = document.getElementById("bttnHighlight")
        if (bttnHighlight.value=="Highlight")
        {
            localSearchHighlight('?h=' + document.getElementById('Tape').value ); 
            localSearchHighlight('?h=' + document.getElementById('ENDATE').value );
            localSearchHighlight('?h=' + document.getElementById('PRG').value );
            localSearchHighlight('?h=' + document.getElementById('EP1').value );
            localSearchHighlight('?h=' + document.getElementById('EP2').value );
            localSearchHighlight('?h=' + document.getElementById('EP3').value );
            localSearchHighlight('?h=' + document.getElementById('EP4').value );
            localSearchHighlight('?h=' + document.getElementById('EP5').value );   
            localSearchHighlight('?h=' + document.getElementById('T_type').value );   
            bttnHighlight.value="Normal";
        }
        else
        {
            bttnHighlight.value="Highlight";
            unhighlight(document.getElementsByTagName('body')[0]);
        }
    }
    
    //_______________________________________________\\
    
    function MouseEvents(objRef, evt) 
    { 
        var checkbox = objRef.getElementsByTagName("input")[0]; 
        if (evt.type == "mouseover") 
            { 
                objRef.style.backgroundColor = "#C5D5FC"; 
            } 
        else 
            { 
                objRef.style.backgroundColor = "white"; 
            } 
    } 
    
    //_______________________________________________\\
    // -->
  </script>
  

    
<head runat="server">
    <title>Home &gt; Search Engine &gt; Entertainment</title>
    <link href="main.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
    <!-- 
        SPAN.searchword { background-color:yellow; }
    // -->
    </style>
        </head>
<body>
    <form id="form1" runat="server">
    <div>
    
    </div>
        <table style="width: 100%">
            <tr>
                <td align="left" style="width: 100%; height: 20px; text-decoration: underline;" class="labelheading">
                    Seach Engine &gt; Entertainment &gt; Results</td>
            </tr>
            <tr>
                <td align="right" style="width: 100%;">
                    <table style="width: 100%">
                        <tr>
                            <td align="left" style="width: 50%">
                <asp:Label ID="lbl_RecordCount" runat="server" Font-Bold="True" ForeColor="Red" Width="262px" Font-Names="Arial" Font-Size="10pt"></asp:Label>
                                <asp:Label ID="lblTotalPages" runat="server" Font-Bold="True" ForeColor="Red"
                                    Width="262px" Font-Names="Arial" Font-Size="10pt"></asp:Label></td>
                            <td style="width: 50%">
                                &nbsp;<input style="width:80px; font-weight: bold;" type="button" value="Highlight"   id="bttnHighlight"  onclick="CallHighlighting();" />&nbsp;
                                <asp:Button ID="ConvertExcel" runat="server" Text="Export to Excel" Width="120px" Font-Bold="True" />
                                <asp:Button ID="BttnPrint" runat="server" Text="Print" Visible="False" />
                                <asp:Button ID="btnSaveTape" runat="Server" Text="Add Tapes" Font-Bold="True" />
                                &nbsp;<asp:Button ID="btnViewTape" runat="Server" Text="View Tapes" Font-Bold="True" /> 
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td align="center" style="width: 100%;" class="bottomMain" valign="middle">
                    &nbsp; &nbsp;&nbsp;
                    <asp:ImageButton ID="bttn_Prev" runat="server" ImageUrl="~/Images/23a.gif" />
                    &nbsp; &nbsp;&nbsp;&nbsp;
                    <asp:ImageButton ID="bttn_Next" runat="server" ImageUrl="~/Images/24a.gif" /></td>
            </tr>
             <tr>
                <td >
                    <asp:Label ID="lblErr" runat="Server" Font-Bold="True" ForeColor="Red"
                                    Width="100%" Font-Names="Arial" Font-Size="10pt" ></asp:Label>
                </td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 19px">
                    <asp:Label ID="lblPageNo2" runat="server" Font-Bold="True" ForeColor="Green" Width="208px" CssClass="lbl"></asp:Label></td>
            </tr>
            <tr>
                <td style="width: 100%;" valign="top" align="left">
                    <asp:GridView ID="dg_Search" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                         AutoGenerateSelectButton="True" PageSize="2000" Width="100%" BackColor="Transparent" CssClass="gridContent" AllowSorting="True">
                        <Columns>
                            <asp:TemplateField HeaderText="S.No">   
                                <ItemTemplate>
                                    <%#Container.DataItemIndex + 1 + lblPages.Text * lblPageIndex.Text%>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="EntryDate" HeaderText="EntryDate" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False" SortExpression="EntryDate" ApplyFormatInEditMode="True" >
                                <HeaderStyle Width="125px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:BoundField DataField="ProgramChildName" HeaderText="Program Name" />
                            <asp:BoundField DataField="EpisodeNo" HeaderText="Episode No" />
                            <asp:BoundField DataField="IsAvailable" HeaderText="Available" />
                            <asp:BoundField DataField="RecordID" HeaderText="RecordID" Visible="False" />
                            <asp:BoundField DataField="Duration" HeaderText="Duration" />
                            <asp:BoundField DataField="TapeLibraryID" HeaderText="TapeLibraryID" Visible="False" />
                            <asp:BoundField DataField="PartNo" HeaderText="Part No" />
                            <asp:BoundField DataField="isDamage" HeaderText="isDamage" Visible="False" />
                            <asp:BoundField DataField="BaseStation" HeaderText="Station" />
                            <asp:BoundField DataField="SpecialNote" HeaderText="SpecialNote" />
                                 <asp:BoundField DataField="OnAirDate" HeaderText="OnAirDate" DataFormatString="{0:dd-MMM-yyyy}" SortExpression="OnAirDate"  HtmlEncode="False" ApplyFormatInEditMode="True" >
                                <HeaderStyle Width="125px" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="Add to">
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckBox1" runat="server"  />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="BaseStationID"   />
                            <asp:BoundField DataField="Available" />
                        </Columns>
                        <HeaderStyle BackColor="Gainsboro" CssClass="gridheader" />
                        <AlternatingRowStyle BackColor="Lavender" />
                    </asp:GridView>
                    &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 100%;" align="center" valign="middle" class="bottomMain">
                    &nbsp;<asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/23a.gif" />
                    &nbsp;&nbsp; &nbsp;
                    <asp:ImageButton ID="ImageButton2" runat="server" ImageUrl="~/Images/24a.gif" />
                    </td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                    <table style="width: 100%">
                        <tr>
                            <td align="left" class="mytext" style="width: 50%">
                                Pages :-
                                <asp:DropDownList ID="ddlPageNo" runat="server" AutoPostBack="True" CssClass="lbl"
                                    Width="72px">
                                </asp:DropDownList></td>
                            <td align="right" style="width: 50%">
                    <asp:Label ID="lbl_CurrentPage_No" runat="server" Font-Bold="True" ForeColor="Green" Width="208px" CssClass="lbl"></asp:Label></td>
                        </tr>
                    </table>
                    <asp:Label ID="lblTotal" runat="server" Visible="False"></asp:Label>
                    <asp:Button ID="bttnPrevious" runat="server" Font-Bold="True" Text=" << " Visible="False" /><asp:Button ID="bttnNext" runat="server" Font-Bold="True" Text=" >> " Visible="False" /><asp:TextBox ID="Tape" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="ENDATE" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="PRG" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP1" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP2" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP3" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP4" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP5" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="T_type" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                        ForeColor="#F2F5FE" Width="24px"></asp:TextBox><asp:Label ID="lblPages" runat="server" Visible="False"></asp:Label><asp:Label ID="lblPageIndex" runat="server" Visible="False">0</asp:Label></td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                    
                    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" PageSize="1" Width="100%" BackColor="Transparent" CssClass="gridContent">
                        <Columns>
                            <asp:TemplateField HeaderText="S.No">
                                <ItemTemplate>
                                    <%# Container.DataItemIndex + 1 %>
                                </ItemTemplate>
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="EntryDate" HeaderText="Entry Date" >
                                <HeaderStyle Width="75px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="OnAirDate" HeaderText="OnAirDate" />
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:BoundField DataField="ProgramChildName" HeaderText="Program Name" />
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />                            
                            <asp:BoundField DataField="EpisodeNo" HeaderText="Episode No" />
                            <asp:BoundField DataField="PartNo" HeaderText="Part No" />
                            <asp:BoundField DataField="Urdu_script" HeaderText="Urdu_script" />
                            <asp:BoundField DataField="NoteArea" HeaderText="Note Area" />
                            <asp:BoundField DataField="Abstract" HeaderText="Abstract" />
                            <asp:BoundField DataField="KeyType" HeaderText="KeyType" />
                            <asp:BoundField DataField="KeyWord" HeaderText="KeyWord" />
                            <asp:BoundField DataField="StartTime" HeaderText="StartTime" />
                            <asp:BoundField DataField="EndTime" HeaderText="EndTime" />
                            <asp:BoundField DataField="Duration" HeaderText="Duration" />                            
                            <asp:BoundField DataField="TapeStatus" HeaderText="Tape Status" />
                            <asp:BoundField DataField="IsAvailable" HeaderText="Available" />
                             
                        </Columns>
                        <HeaderStyle BackColor="Gainsboro" CssClass="gridheader" />
                    </asp:GridView>
                 </td>
            </tr>
        </table>
        <asp:GridView ID="gvTest" runat="server" CellPadding="4" EnableModelValidation="True" ForeColor="#333333" GridLines="None">
            <AlternatingRowStyle BackColor="White" ForeColor="#284775" />
            <EditRowStyle BackColor="#999999" />
            <FooterStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" />
            <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
            <RowStyle BackColor="#F7F6F3" ForeColor="#333333" />
            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
        </asp:GridView>
    </form>
</body>
</html>
