Imports System.Data
Partial Class SearchEngine_NewsTapeDetail
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim DS1 As DataSet
    Dim DS2 As DataSet
    Dim DS3 As DataSet

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try
            txtUrduScript.Attributes.Add("onkeypress", "search()")
            txtUrduScript.Attributes.Add("Dir", "Rtl")

            Dim TapeNumber As String
            TapeNumber = Request.QueryString("TapeNumber")

            Dim Program As Integer
            Program = Request.QueryString("ReportSlug")

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "GetNewsSearchEngine_Keywords"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo.Value = TapeNumber

            Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReportSlug", Data.SqlDbType.Int)
            Prg.Value = Program

            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS, "Table0")

            If DS.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS.Tables("Table0").Rows.Count - 1 Then
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + ",</b><br>"
                    Else
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(0).ToString + "</b><br>"
                    End If
                Next
            End If

            ''********************************************''
            ''*************** Footages *******************''
            ''********************************************''

            Dim cmd1 As New System.Data.SqlClient.SqlCommand
            cmd1.CommandType = Data.CommandType.StoredProcedure
            cmd1.CommandText = "GetNewsSearchEngine_Footages"
            cmd1.Connection = Con
            cmd1.CommandTimeout = 0

            Dim TapeNo1 As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo1.Value = TapeNumber

            Dim Prg1 As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@ReportSlug", Data.SqlDbType.Int)
            Prg1.Value = Program

            Dim da1 As New System.Data.SqlClient.SqlDataAdapter
            DS1 = New DataSet

            da1.SelectCommand = cmd1
            da1.Fill(DS1, "Table0")

            If DS1.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS1.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS1.Tables("Table0").Rows.Count - 1 Then
                        lblFootages.Text = lblFootages.Text + "<b>" + J + " : " + DS1.Tables("Table0").Rows(I)(0).ToString + ",</b><br>"
                    Else
                        lblFootages.Text = lblFootages.Text + "<b>" + J + " : " + DS1.Tables("Table0").Rows(I)(0).ToString + "</b><br>"
                    End If
                Next
            End If

            ''********************************************''
            ''*************** Eng Script *******************''
            ''********************************************''

            Dim cmd2 As New System.Data.SqlClient.SqlCommand
            cmd2.CommandType = Data.CommandType.StoredProcedure
            cmd2.CommandText = "GetEnglishScript"
            cmd2.Connection = Con
            cmd2.CommandTimeout = 0

            Dim TapeNo2 As Data.SqlClient.SqlParameter = cmd2.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo2.Value = TapeNumber

            Dim Prg2 As Data.SqlClient.SqlParameter = cmd2.Parameters.Add("@ReportSlug", Data.SqlDbType.Int)
            Prg2.Value = Program


            Dim da2 As New System.Data.SqlClient.SqlDataAdapter
            DS2 = New DataSet

            da2.SelectCommand = cmd2
            da2.Fill(DS2, "Table0")
            'lblEngScript.Text = DS.Tables(0).Rows(0)(0)
            If DS2.Tables("Table0").Rows.Count > 0 Then
                lblEngScript.Text = DS2.Tables("Table0").Rows(0)(0)
            End If

            ''********************************************''
            ''************* Availability *****************''
            ''********************************************''

            Dim cmd3 As New System.Data.SqlClient.SqlCommand
            cmd3.CommandType = Data.CommandType.StoredProcedure
            cmd3.CommandText = "GetEmployeeName_by_TapeNumber_SearchEngine"
            cmd3.Connection = Con
            cmd3.CommandTimeout = 0

            Dim TapeNo3 As Data.SqlClient.SqlParameter = cmd3.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo3.Value = TapeNumber

            Dim da3 As New System.Data.SqlClient.SqlDataAdapter
            DS3 = New DataSet

            da3.SelectCommand = cmd3
            da3.Fill(DS3, "Table0")

            lblAvailability.Text = DS3.Tables("Table0").Rows(0)(0).ToString

            ''*****************************************''
            ''*********** Urdu Script *****************''
            ''*****************************************''

            GetUrduScript()

            ''*****************************************''

            GetIssuedEmployeeID()

            GetIsDamage()

            GetVideoDetails()

            GetAddedBy()

            GetTapeContentID(TapeNumber)

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Sub GetAddedBy()
        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS5 As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetNews_AddedBy"
        cmd.Connection = Con

        Dim TapeSlugID As Integer
        TapeSlugID = Request.QueryString("ReportSlug")

        Dim Slug As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeSlugID", Data.SqlDbType.Int)
        Slug.Value = TapeSlugID

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS5 = New DataSet
        da.SelectCommand = cmd
        da.Fill(DS5, "Master")
        If DS5.Tables("Master").Rows.Count > 0 Then
            lblAddedBy.Text = DS5.Tables("Master").Rows(0)(0).ToString
            lblModifiedBy.Text = DS5.Tables("Master").Rows(0)(1).ToString
        Else
            lblAddedBy.Text = "N/A"
            lblModifiedBy.Text = "N/A"
        End If

    End Sub

    Private Sub GetVideoDetails()
        'Dim dt As DataTable
        'Dim ObjVideo As New BusinessFacade.SearchEngine()
        'ObjVideo.TapeSlugID = Request.QueryString("ReportSlug")
        'dt = ObjVideo.GetVideoDetails()

        'If dt.Rows.Count > 0 Then
        '    bttnPlayVideo.Visible = True
        '    'bttnPlayVideo.Visible = False
        'End If
    End Sub

    Private Sub GetIsDamage()
        ''****************************************''
        ''********* Get TapeLibraryID ************''
        ''****************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = Request.QueryString("TapeNumber")
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        Dim ObjUser As New BusinessFacade.SearchEngine()
        ObjUser.TapeLibraryID = LibraryID
        lblIsDamage.Text = ObjUser.GetLostDamage_by_TapeNumber_SearchEngine()

    End Sub

    Private Sub GetIssuedEmployeeID()
        Dim ObjUser As New BusinessFacade.SearchEngine()
        ObjUser.TapeNumber = Request.QueryString("TapeNumber")
        Dim EmployeeID As Integer = ObjUser.GetEmployeeID_by_TapeNumber_SearchEngine()

        If EmployeeID = 0 Then
            bttnReminder.Enabled = False
        Else
            lblEmployeeID.Text = EmployeeID
            bttnReminder.Enabled = True
        End If
    End Sub

    Private Sub GetUrduScript()

        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS5 As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetUrduScript"
        cmd.Connection = Con
        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SlugID", Data.SqlDbType.Int)
        Dim SlugID As Integer
        SlugID = Request.QueryString("ReportSlug")
        p1.Value = SlugID
        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS5 = New DataSet
        da.SelectCommand = cmd
        da.Fill(DS5, "Master")
        lblUrduScript.Text = DS5.Tables("Master").Rows(0)(0)
        txtUrduScript.Text = DS5.Tables("Master").Rows(0)(0)

    End Sub

    Protected Sub bttnEditSlug_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEditSlug.Click
        Response.Redirect("../ApplicationSetup/frmSlugEdit.aspx?TapeSlugID=" + Request.QueryString("ReportSlug"))
    End Sub

    Protected Sub bttnReminder_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReminder.Click
        ''****************************''
        '' ****** User Name **********''
        ''****************************''
        lbl_UserName.Text = Request.Cookies("userinfo")("username")
        Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
        If arr_UserID.Length > 1 Then
            lbl_UserName.Text = arr_UserID(1)
        End If
        ''****************************''

        Dim Dt As New DataTable
        Dim objSearch As New BusinessFacade.ReminderService()
        objSearch.EmployeeID = lblEmployeeID.Text
        objSearch.DepartmentID = 0
        objSearch.Date = 0
        objSearch.isBlank = -1
        objSearch.TapeNumber = Request.QueryString("TapeNumber")
        Dt = objSearch.ReminderService_Getrecords_SearchEngine()

        ''***********************************************************''
        Dim ToEmail As String = objSearch.GetEmailAddress_ByEmployeeID(lblEmployeeID.Text)
        Dim Arr As Array = Split(ToEmail, "@geo")

        Dim BCC As String
        'Dim strFrom As String = "<EMAIL>"

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim CC As String
        'BCC = "<EMAIL>"

        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            BCC = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            BCC = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            BCC = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            BCC = "<EMAIL>"
            'BCC = "<EMAIL>"
        End If

        CC = "<EMAIL>"
        'BCC = "<EMAIL>"
        'CC = "<EMAIL>"

        Dim subject As String = "Reminder: Tape OverDue"
        Dim Q As String
        Dim i As Integer
        Q = ""
        Dim TapeDetail As String = ""

        'Dt.Rows(0).Item(1).ToString()

        Dim J As String = "0"

        For i = 0 To Dt.Rows.Count - 1

            Q &= "Dear " & Dt.Rows(i).Item(1).ToString().ToUpper() & "," & " ^^The following tapes are outstanding against you. ^^"

            'Q &= "bbbccc S.No dddeee" & "~~" & "bbbccc Tape Type dddeee" & "~~" & "bbbccc Tape No. dddeee" & "~~" & "bbbccc Issued Date dddeee" & "~~" & "bbbccc Due Date dddeee" & "~~" & "bbbccc Remarks dddeee" & "~~" & "bbbccc Total Reminders dddeee^^"

            Dim IssueDate As String
            IssueDate = CDate(Dt.Rows(i).Item(5).ToString()).ToString("dd-MMM-yyyy")
            Dim ReturnDate As String
            ReturnDate = CDate(Dt.Rows(i).Item(6).ToString()).ToString("dd-MMM-yyyy") 'Dt.Rows(i).Item(6).ToString()


            Dim Cnt As String = CStr(CInt(Dt.Rows(i)("TotalReminders").ToString) + 1)
            'Q &= "(" & (i + 1) & ")~~" & Dt.Rows(i).Item(8).ToString() & "~~" & Dt.Rows(i).Item(0).ToString() & "~~" & IssueDate & "~~" & ReturnDate & "~~Archival~~" & Cnt + "^^"
            Q &= "bbbccc" & Dt.Rows(i).Item(0).ToString() & "dddeee^!!!!!!!!!!Tape Type : " & Dt.Rows(i).Item(8).ToString() & "^!!!!!!!!!!Issue Date : " & IssueDate & "^!!!!!!!!!!Due Date:!!" & ReturnDate & "^!!!!!!!!!!Remarks :!! Archival" & "^!!!!!!!!!!Total Reminders : " & Cnt & "^!!!!!!!!!!Program / Slug : " & Dt.Rows(i).Item("ProgramSlug").ToString() & "^^"


            'TapeDetail &= (i + 1) & "," & Dt.Rows(i).Item(0).ToString() & "," & Dt.Rows(i).Item(8).ToString() & "," & IssueDate & "," & ReturnDate & ",Archival," & Cnt & "$"
            TapeDetail &= (i + 1) & "-+" & Dt.Rows(i).Item(0).ToString() & "-+" & Dt.Rows(i).Item(8).ToString() & "-+" & IssueDate & "-+" & ReturnDate & "-+Archival" & "-+" & Cnt & "-+" & Dt.Rows(i).Item("ProgramSlug").ToString() & "$$"


            lblEmployeeName.Text = Dt.Rows(0).Item(1).ToString().ToUpper()

        Next
        Q &= "^Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us."
        Q &= "^^Regards,^^"
        'Q &= Request.Cookies("userinfo")("username").ToUpper()
        Q &= Request.Cookies("userinfo")("userfullname").ToUpper
        'Q &= "^Circulation Desk"
        'Q &= "^Ext:6132 (News Archive) , 6568 (Central Archive)"


        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            Q &= "^Lahore Archive"
            Q &= "^Ext: 334 - 374"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            Q &= "^Islamabad Archive"
            Q &= "^Ext: 218"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            Q &= "^Peshawar Archive"
            Q &= "^Ext: N/A"
        Else
            ''****** For Karachi *******''
            Q &= "^Circulation Desk"
            Q &= "^Ext:6132 (News Archive) , 6568 (Central Archive)"
        End If

        Dim objEmailSrvc As New BusinessFacade.ReminderService()
        Dim EmailId As Integer
        EmailId = objEmailSrvc.SaveEmailData(strFrom, ToEmail.ToString, CC, subject, Q, TapeDetail)

        Dim G As String
        G = ""
        If G = "" Or G = "&nbsp;" Then
            G = "" '"<EMAIL>" 'txt_EmailAddress.Text
            If Arr.Length = 2 Then
                G = ToEmail.ToString 'ToEmail.ToString '"<EMAIL>"
            Else
                G = "" '"" '"<EMAIL>"
            End If

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            'script = script + "var mywindow = window.open('../ReminderService/Test.aspx?EmailID=" & EmailId & "&From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&EmployeeName=" + lblEmployeeName.Text + "&UserName=" + Request.Cookies("userinfo")("userfullname") + "&TapeDetail=" + TapeDetail + "', 'mywindow');"
            script = script + "var mywindow = window.open('../ReminderService/LocalReminders_Search.aspx?EmailID=" & EmailId & "&From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&EmployeeName=" + lblEmployeeName.Text + "&UserName=" + Request.Cookies("userinfo")("userfullname") + "&TapeDetail=" + TapeDetail + "', 'mywindow');"
            script = script + "}</script>"
            Page.RegisterClientScriptBlock("test", script)

        End If


        'Dim Dt As New DataTable
        'Dim objSearch As New BusinessFacade.ReminderService()
        'objSearch.EmployeeID = lblEmployeeID.Text
        'objSearch.DepartmentID = 0
        'objSearch.Date = 0
        'objSearch.isBlank = -1
        'objSearch.TapeNumber = Request.QueryString("TapeNumber")
        'Dt = objSearch.ReminderService_Getrecords_SearchEngine()

        ' ''***********************************************************''
        'Dim ToEmail As String = objSearch.GetEmailAddress_ByEmployeeID(lblEmployeeID.Text)
        'Dim Arr As Array = Split(ToEmail, "@geo")

        'Dim BCC As String
        'Dim strFrom As String = "<EMAIL>"
        'Dim CC As String
        'BCC = "<EMAIL>"
        'CC = "<EMAIL>"
        'Dim subject As String = "Reminder: Tape OverDue"
        'Dim Q As String
        'Dim i As Integer
        'Q = ""

        ''Dt.Rows(0).Item(1).ToString()

        'Dim J As String = "0"

        'For i = 0 To Dt.Rows.Count - 1

        '    Q &= "Dear " & Dt.Rows(i).Item(1).ToString() & " ( " & Dt.Rows(i).Item(3).ToString() & " ) " & "," & " ^^This is to inform you that following Tape is Overdue against you. ^^"

        '    Dim IssueDate As String
        '    IssueDate = CDate(Dt.Rows(i).Item(5).ToString()).ToString("dd-MMM-yyyy")
        '    Dim ReturnDate As String
        '    ReturnDate = CDate(Dt.Rows(i).Item(6).ToString()).ToString("dd-MMM-yyyy") 'Dt.Rows(i).Item(6).ToString()

        '    Q &= "Tape Number: " & Dt.Rows(i).Item(0).ToString() & " , Issued On: " & IssueDate & " , Due Date: " & ReturnDate + "^^"


        'Next
        'Q &= "^Email Send by:" & Request.Cookies("userinfo")("username").ToUpper()
        'Q &= "^For Assistance contact Archive Department ^( Ext:6132 (News Archive) , 6568 (Central Archive)"
        'Q &= "^^Regards,"
        'Q &= "^^Archives Department"

        'Dim G As String
        'G = ""
        'If G = "" Or G = "&nbsp;" Then
        '    G = "" '"<EMAIL>" 'txt_EmailAddress.Text
        '    If Arr.Length = 2 Then
        '        G = ToEmail.ToString
        '    Else
        '        G = ""
        '    End If

        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('../ReminderService/Test.aspx?From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&Body=" + Q + "', 'mywindow'); "
        '    script = script + "}</script>"
        '    Page.RegisterClientScriptBlock("test", script)



        'End If

    End Sub

    
    Protected Sub lnkPlayVideo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkPlayVideo.Click
        ''Dim Filepath As String = "\\misops1\c$\Websites\DAMS\Clips\1.jpg"
        ''Dim Filepath As String = Server.MapPath("Clips").ToString + "\1.jpg"

        ''Dim script As String
        ''script = "<script language='javascript' type='text/javascript'>"
        ''script = script + "window.onload=function OpenReport() {"
        ' ''script = script + "var mywindow = window.open('UploadFiles/SalesPortal.bmp', 'mywindow'); "
        ''script = script + "var mywindow = window.open('" & Filepath & "', 'mywindow'); "
        ''script = script + "}</script>"

        ''Page.RegisterClientScriptBlock("test", script)

        'Dim FileName As String = "4 Inch Bona Video.wmv"

        'Dim strDiv As String = String.Empty

        'strDiv += "<table width='100%'>"
        ''strDiv += "<PARAM NAME=""URL"" VALUE=""file://misops1\c$\Websites\DAMS\Clips\4 Inch Bona Video.wmv"">"

        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://misops1\c$\Websites\DAMS\Clips\" + FileName + """>"

        'divNews.InnerHtml = strDiv

        ''-----------------------------------------------------------------------------------------------''
        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onload=function OpenReport() {"
        'script = script + "var mywindow = window.open('PlayVideo.aspx', 'mywindow'); "
        'script = script + "}</script>"
        'Page.RegisterClientScriptBlock("test", script)
        ''------------------------------------------------------------------------------------------------''
        Response.Write("<script type='text/javascript'>detailedresults=window.open('PlayVideo.aspx');</script>")

    End Sub

    Protected Sub bttnPlayVideo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnPlayVideo.Click
        Dim dt As DataTable
        Dim ObjVideo As New BusinessFacade.SearchEngine()
        ObjVideo.TapeSlugID = Request.QueryString("ReportSlug")
        dt = ObjVideo.GetVideoDetails()

        If dt.Rows.Count > 0 Then
            Response.Write("<script type='text/javascript'>detailedresults=window.open('PlayVideo.aspx?FilePath=" + dt.Rows(0).Item(2).ToString + "');</script>")
        End If


    End Sub

    Sub GetTapeContentID(ByVal TapeNumber As String)
        Dim ObjSearch As New BusinessFacade.SearchEngine()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        lblMasterID.Text = ObjSearch.GetTapeContentID(TapeNumber, CokieBaseStationID, "News")
        If lblMasterID.Text = "-2" Then
            bttnEditMaster.Enabled = False
        Else
            bttnEditMaster.Enabled = True
        End If
    End Sub


    Protected Sub bttnEditMaster_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEditMaster.Click
        Response.Redirect("../TapeContent/FrmArchiveEntry_News.aspx?TapeContentID=" + lblMasterID.Text)
    End Sub
End Class
