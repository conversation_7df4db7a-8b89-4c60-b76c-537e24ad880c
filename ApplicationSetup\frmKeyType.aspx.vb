
Partial Class ApplicationSetup_frmKeyType
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_ContentType.DataSource = New BusinessFacade.ContentType().GetRecords()
        ddl_ContentType.DataTextField = "ContentTypeName"
        ddl_ContentType.DataValueField = "ContentTypeID"
        ddl_ContentType.DataBind()
        ddl_ContentType.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_KeyTypeID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_KeyType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_KeyType.Text = "" Then
            lblErr.Text = "Please Insert Keyword Type!!"
        ElseIf ddl_ContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Content Type!!"
        Else
            Dim objKeyType As New BusinessFacade.KeyType()
            objKeyType.KeyType = txt_KeyType.Text
            objKeyType.ContentTypeID = ddl_ContentType.SelectedValue
            objKeyType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim objKeyType As New BusinessFacade.KeyType()
        objKeyType.KeyTypeID = txt_KeyTypeID.Text
        objKeyType.KeyType = txt_KeyType.Text
        objKeyType.ContentTypeID = ddl_ContentType.SelectedValue
        objKeyType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.KeyType().GetRecords()
        dg_KeyType.DataSource() = dt
        dg_KeyType.Columns(0).Visible = True
        dg_KeyType.Columns(2).Visible = True
        dg_KeyType.DataBind()
        dg_KeyType.Columns(0).Visible = False
        dg_KeyType.Columns(2).Visible = False
        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_KeyTypeID.Text = "" Then
                lblErr.Text = "Please Select KeyType!!"
            Else
                Dim objKeyType As New BusinessFacade.KeyType()
                objKeyType.KeyTypeID = txt_KeyTypeID.Text
                objKeyType.DeleteRecord(objKeyType.KeyTypeID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_KeyType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This KeyType is Already in Used !"
            clrscr()
            dg_KeyType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_KeyType.Text = String.Empty
        txt_KeyTypeID.Text = String.Empty
        ddl_ContentType.SelectedIndex = 0
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_KeyType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub dg_KeyType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs)
        dg_KeyType.PageIndex = e.NewPageIndex()
        FillGrid()
    End Sub

    Protected Sub dg_KeyType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        lblErr.Text = String.Empty
        I = dg_KeyType.SelectedIndex.ToString
        txt_KeyTypeID.Text = Convert.ToInt32(dg_KeyType.Rows(I).Cells(1).Text)
        txt_KeyType.Text = dg_KeyType.Rows(I).Cells(2).Text
        ddl_ContentType.SelectedValue = Convert.ToInt32(dg_KeyType.Rows(I).Cells(3).Text)
        dg_KeyType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub
End Class
