<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmSubCloset.aspx.vb" Inherits="ApplicationSetup_frmSubCloset" title="Home > Sub Closet > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w15">Home</asp:LinkButton>&nbsp;&gt; Sub Closet &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 151px; HEIGHT: 24px">Closet Master</TD><TD style="WIDTH: 161px; HEIGHT: 24px">Tape Type</TD><TD style="WIDTH: 143px; HEIGHT: 24px">Sub Closet Name</TD><TD style="WIDTH: 114px; HEIGHT: 24px">Capacity</TD><TD style="WIDTH: 157px; HEIGHT: 24px"></TD></TR><TR class="mytext"><TD style="WIDTH: 151px; HEIGHT: 22px"><asp:DropDownList id="ddl_ClosetMaster" runat="server" CssClass="mytext" Width="144px"></asp:DropDownList></TD><TD style="WIDTH: 161px; HEIGHT: 22px"><asp:DropDownList id="ddl_TapeType" runat="server" CssClass="mytext" Width="144px"></asp:DropDownList></TD><TD style="WIDTH: 143px; HEIGHT: 22px"><asp:TextBox id="txt_SubClosetName" runat="server" CssClass="mytext" __designer:wfdid="w4"></asp:TextBox></TD><TD style="WIDTH: 114px; HEIGHT: 22px"><asp:TextBox id="txt_Capacity" runat="server" CssClass="mytext" __designer:wfdid="w5"></asp:TextBox></TD><TD style="WIDTH: 157px; HEIGHT: 22px"></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Width="496px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_SubCloset" runat="server" CssClass="gridContent" Width="712px" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="SubClostID" HeaderText="SubClostID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="ClosetMasterID" HeaderText="ClosetMasterID" />
                        <asp:BoundField DataField="ClosetMasterName" HeaderText="Closet Master Name" />
                        <asp:BoundField DataField="SubClosetName" HeaderText="Sub Closet Name" />
                        <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" />
                        <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                        <asp:BoundField DataField="Capacity" HeaderText="Capacity" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD style="HEIGHT: 22px"><asp:TextBox id="txt_SubClosetID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w6" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

