Imports System
Imports System.Data

Partial Class Login
    Inherits System.Web.UI.Page
    'Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")
    'Private strConnection As String = "server=MISOPS1\DAMS;database=DAMS_NewDB;uid=sa;password=**********"

    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.txtLoginID.Focus()
        If Not Page.IsPostBack = True Then
        End If
    End Sub

    Protected Sub btnLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnLogin.Click

        'Cache.Remove("RootDT")
        'Cache.Remove("ChildDT")
        'Cache.Remove("SubChildDT")

        Dim objManager As JangSalesPortal.SecurityLayer.SecurityManager = JangSalesPortal.SecurityLayer.SecurityManager.CreateInstance
        Dim ticket As JangSalesPortal.SecurityLayer.AuthenticationTicket
        ticket = objManager.Authenticate(txtLoginID.Text, txtpassword.Text)


        If Not ticket Is Nothing Then
            Dim query2 As String = "select * from dbo.vu_checkuserforapp where LoginID='" + Me.txtLoginID.Text + "'  And Application_ID=25"
            Dim sqlCom2 As New System.Data.SqlClient.SqlCommand(query2, objConnection)
            objConnection.Open()
            Dim dr2 As System.Data.SqlClient.SqlDataReader = sqlCom2.ExecuteReader()
            If dr2.Read() Then

                ''*************************************************************''
                ''*********************** Fill Department Cokie****************''
                ''*************************************************************''

                Dim ObjEmployee As New BusinessFacade.Employee
                ObjEmployee.SM_LoginID = txtLoginID.Text.ToString
                Dim BaseStationID As String
                BaseStationID = ObjEmployee.GetBaseStationID()
                Response.Cookies("userinfo")("BaseStationID") = BaseStationID

                ''****************************** END **************************''
                ''*************************************************************''

                Session("ApplicationTicket") = ticket
                Response.Cookies("userinfo")("username") = txtLoginID.Text.ToString
                Response.Cookies("userinfo")("userfullname") = dr2("Name")
                Response.Cookies("userinfo").Expires = DateTime.Now.AddDays(1)

                Dim objUser As New BusinessFacade.User
                Dim IsExists As String = objUser.CheckAuthetication(dr2("Id"))
                If IsExists = "no" Then
                    Response.Redirect("~/Home/Home.aspx")
                Else
                    Response.Redirect("~/Home/HomeBlank.aspx")
                End If
            Else
                lblErr.Text = "You are not a Valid User for Digital Archive Management System!"
                Me.txtpassword.Focus()
            End If

        Else
            lblErr.Text = "Please Try Again !!"
            Me.txtpassword.Focus()
        End If

    End Sub

    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click
        txtLoginID.Text = String.Empty
        txtpassword.Text = String.Empty
        lblErr.Text = String.Empty
    End Sub

    Function GetUserName() As String
        If TypeOf My.User.CurrentPrincipal Is Security.Principal.WindowsPrincipal Then
            Dim parts() As String = Split(My.User.Name, "\")
            Dim username As String = parts(1)
            If parts(1).ToUpper <> "ADMINISTRATOR" Then
                txtLoginID.Text = parts(1)
            End If
            Return username
        Else
            Return My.User.Name
        End If
    End Function

End Class

'Partial Class Login
'    Inherits System.Web.UI.Page

'    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

'    'Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
'    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

'    Function GetUserName() As String
'        If TypeOf My.User.CurrentPrincipal Is Security.Principal.WindowsPrincipal Then
'            Dim parts() As String = Split(My.User.Name, "\")
'            Dim username As String = parts(1)
'            If parts(1).ToUpper <> "ADMINISTRATOR" Then
'                txtLoginID.Text = parts(1)
'            End If
'            Return username
'        Else
'            Return My.User.Name
'        End If
'    End Function

'    Protected Sub btnLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnLogin.Click

'        'Cache.Remove("RootDT")
'        'Cache.Remove("ChildDT")
'        'Cache.Remove("SubChildDT")

'        Dim objManager As JangSalesPortal.SecurityLayer.SecurityManager = JangSalesPortal.SecurityLayer.SecurityManager.CreateInstance
'        Dim ticket As JangSalesPortal.SecurityLayer.AuthenticationTicket
'        ticket = objManager.Authenticate(txtLoginID.Text, txtpassword.Text)

'        If Not ticket Is Nothing Then
'            Dim query2 As String = "select * from dbo.vu_checkuserforapp where LoginID='" + Me.txtLoginID.Text + "'  And Application_ID=25"
'            Dim sqlCom2 As New System.Data.SqlClient.SqlCommand(query2, objConnection)
'            objConnection.Open()
'            Dim dr2 As System.Data.SqlClient.SqlDataReader = sqlCom2.ExecuteReader()
'            If dr2.Read() Then

'                ''*************************************************************''
'                ''*********************** Fill Department Cokie****************''
'                ''*************************************************************''

'                Dim ObjEmployee As New BusinessFacade.Employee
'                ObjEmployee.SM_LoginID = txtLoginID.Text.ToString
'                Dim BaseStationID As String
'                BaseStationID = ObjEmployee.GetBaseStationID()
'                Response.Cookies("userinfo")("BaseStationID") = BaseStationID

'                ''****************************** END **************************''
'                ''*************************************************************''

'                Session("ApplicationTicket") = ticket
'                Response.Cookies("userinfo")("username") = txtLoginID.Text.ToString
'                Response.Cookies("userinfo")("userfullname") = dr2("Name")
'                Response.Cookies("userinfo").Expires = DateTime.Now.AddDays(1)

'                Dim objUser As New BusinessFacade.User
'                Dim IsExists As String = objUser.CheckAuthetication(dr2("Id"))
'                If IsExists = "no" Then
'                    Response.Redirect("~/Home/Home.aspx")
'                Else
'                    Response.Redirect("~/Home/HomeBlank.aspx")
'                End If
'            Else
'                lblErr.Text = "You are not a Valid User for Digital Archive Management System!"
'                Me.txtpassword.Focus()
'            End If
'        Else
'            lblErr.Text = "Please Try Again !!"
'            Me.txtpassword.Focus()
'        End If

'    End Sub

'    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click
'        txtLoginID.Text = String.Empty
'        txtpassword.Text = String.Empty
'        lblErr.Text = String.Empty
'    End Sub

'    'Private strConnection As String = "server=MISOPS1\DAMS;database=DAMS_NewDB;uid=sa;password=**********"
'    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
'        Me.txtLoginID.Focus()
'        If Not Page.IsPostBack = True Then
'        End If
'    End Sub
'End Class