<%@ Page Language="VB" AutoEventWireup="false" CodeFile="AddNewsKeyword.aspx.vb" Inherits="TapeContent_AddNewsKeyword" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<script language="javascript" type="text/javascript" >
 function CloseNewKeyword()
    {
         window.opener.location.href="FrmArchiveEntry_News.aspx";
         self.close();
    }
</script>
<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:ScriptManager ID="ScriptManager1" runat="server">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <table width="100%">
                    <tr>
                        <td class="labelheading" style="width: 709px; height: 21px; text-decoration: underline" bgcolor="#000000">
                            <asp:Label ID="Label4" runat="server" Font-Bold="True" Font-Names="Arial Rounded MT Bold"
                                Font-Size="Large" Font-Strikeout="False" ForeColor="Yellow" Text="News Keyword > Add New"></asp:Label></td>
                    </tr>
                    <tr>
                        <td style="width: 709px; height: 67px" bgcolor="#add8e6">
                            <table>
                                <tr class="mytext">
                                    <td style="height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="News Keyword" Width="104px"></asp:Label></td>
                                    <td style="width: 138px; height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:TextBox ID="txt_NewsKeyword" runat="server" CssClass="mytext"></asp:TextBox></td>
                                    <td style="width: 89px; height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="Subcontent Type" Width="120px"></asp:Label></td>
                                    <td style="height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:DropDownList ID="ddl_SubContentType" runat="server" CssClass="mytext" Width="152px">
                                        </asp:DropDownList></td>
                                    <td style="width: 49px; height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Size="Medium" Font-Strikeout="False"
                                            Text="Key type" Width="72px"></asp:Label>&nbsp;
                                    </td>
                                    <td style="width: 49px; height: 17px" valign="top" bgcolor="#add8e6">
                                        <asp:ListBox ID="lstNews" runat="server" CssClass="mytext" Height="40px" Width="120px">
                                        </asp:ListBox></td>
                                    <td style="height: 17px" bgcolor="#add8e6">
                                        <asp:TextBox ID="txt_TKeytype" runat="server" CssClass="mytext" Visible="False" Width="24px"></asp:TextBox></td>
                                </tr>
                            </table>
                            &nbsp;<asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="472px"></asp:Label></td>
                    </tr>
                    <tr>
                        <td class="bottomMain" style="width: 709px" bgcolor="#add8e6">
                            &nbsp;<asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Text="Save" Width="64px" />&nbsp;<asp:Button
                                ID="bttnClear" runat="server" CssClass="buttonA" Text="Clear" Width="64px" />&nbsp;<asp:Button
                                    ID="bttnClose" runat="server" CssClass="buttonA" Text="Close" Width="64px" OnClientClick="javascript:CloseNewKeyword();" /></td>
                    </tr>
                    <tr>
                        <td style="width: 709px; height: 130px;">
                            <asp:GridView ID="dg_NewsKeyword" runat="server" AutoGenerateColumns="False" CssClass="gridContent" PageSize="25" Width="100%" BackColor="#FFE0C0" Font-Size="Small">
                                <Columns>
                                    <asp:BoundField ApplyFormatInEditMode="True" DataField="NewsKeywordID" Visible="False">
                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                        <HeaderStyle Width="0px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="NewsKeyword" HeaderText="News Keyword">
                                        <ItemStyle Width="150px" />
                                        <HeaderStyle Width="120px" />
                                    </asp:BoundField>
                                    <asp:BoundField ApplyFormatInEditMode="True" DataField="SubContentTypeID" Visible="False">
                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                        <HeaderStyle Width="0px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="SubContentTypeName" HeaderText="Sub-ContentType">
                                        <HeaderStyle Width="150px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="TKeytype" HeaderText="Key Type">
                                        <ItemStyle Width="250px" />
                                    </asp:BoundField>
                                </Columns>
                                <SelectedRowStyle BackColor="#FFE0C0" />
                                <HeaderStyle CssClass="gridheader" BackColor="#FFFF80" />
                            </asp:GridView>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 709px; height: 22px">
                            <asp:TextBox ID="txt_NewsKeywordID" runat="server" CssClass="mytext" Visible="False"
                                Width="48px"></asp:TextBox></td>
                    </tr>
                </table>
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp; &nbsp;
            </ContentTemplate>
        </asp:UpdatePanel>
    
    </div>
    </form>
</body>
</html>
