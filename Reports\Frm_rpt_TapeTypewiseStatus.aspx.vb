Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_TapeTypewiseStatus
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                'chkIgnoredate.Checked = True
                txtFromdate.Text = Date.Now.ToString("dd-MMM-yyyy")
                txt_ToDate.Text = Date.Now.ToString("dd-MMM-yyyy")
                chkIgnore.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()
        ddlTapeType.DataSource = New BusinessFacade.TapeType().GetRecords()
        ddlTapeType.DataTextField = "TapeType"
        ddlTapeType.DataValueField = "TapeTypeID"
        ddlTapeType.DataBind()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim FromDate As String = ""
        Dim ToDate As String = ""
        Dim TapeType As String = ""
        Dim IsBlank As String = ""

        If chkIgnore.Checked = True Then
            TapeType = "-1"
        Else
            TapeType = ddlTapeType.SelectedValue
        End If

        IsBlank = ddlisBlank.SelectedValue
        'If chkIgnoredate.Checked = True Then
        '    FromDate = "-1"
        '    ToDate = "-1"
        'Else
        '    FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
        '    ToDate = Convert.ToDateTime(txt_ToDate.Text).ToString("dd-MMM-yyyy")
        'End If

        FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
        ToDate = Convert.ToDateTime(txt_ToDate.Text).ToString("dd-MMM-yyyy")

        Dim BaseStationID As String
        Dim BaseStation As String
        If Me.chkStation.Checked = True Then
            BaseStationID = "-1"
            BaseStation = "All"
        Else
            BaseStationID = ddlBaseStation.SelectedValue
            BaseStation = ddlBaseStation.SelectedItem.Text
        End If

        Dim Chart As String
        Chart = Me.ddlChart.SelectedValue

        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_TapeTypewiseStatus.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@TapeTypeID=" + TapeType + "&@IsBlank=" + IsBlank + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "&@Graph=" + Chart + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_TapeTypewiseStatus.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@TapeTypeID=" + TapeType + "&@IsBlank=" + IsBlank + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "&@Graph=" + Chart + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"
            Page.RegisterClientScriptBlock("test", script)
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_TapeTypewiseStatus.aspx"
        ObjSave.ReportName = "Other Reports > How can I view TapeType wise Tapes Status ?"
        ObjSave.SaveRecord()

        ''******************************************************''
    End Sub
End Class

