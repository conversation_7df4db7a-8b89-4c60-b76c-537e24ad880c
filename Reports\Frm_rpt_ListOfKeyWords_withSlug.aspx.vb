Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ListOfKeyWords
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                Chk_EntKW.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

        ddlBaseStation_News.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation_News.DataTextField = "BaseStationName"
        ddlBaseStation_News.DataValueField = "BaseStationID"
        ddlBaseStation_News.DataBind()

    End Sub

    Protected Sub bttnEntReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEntReport.Click

        Dim Slug1 As String = ""
        If chk_EntKW.Checked = True Then
            Slug1 = ""
        Else
            Slug1 = txtEntKeywords.Text
        End If

        Dim BaseStationID As String
        If Me.chkStation.Checked = True Then
            BaseStationID = "-1"
        Else
            BaseStationID = ddlBaseStation.SelectedValue
        End If


        ''**********************************************************''
        ''*************** For Getting Program Child ID *************''
        ''**********************************************************''
        Dim ProgramChildID As Integer
        If Trim(txtProgramName.Text) = "" Then
            ProgramChildID = "-1"
        Else

            Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
            ObjProgramChildID.ProgramChildName = txtProgramName.Text
            ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

        End If

        Dim FromDate, ToDate As String
        If chkIgnoredate.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = txtFromdate.Text
            ToDate = txtToDate.Text
        End If

        ''**********************************************************''

        Dim qryString As String
        qryString = "viewer.aspx?reportname=" & "rpt_ListofKeywordWiseReport_Ent.rpt&@KeyWord1=" + Slug1 + "&@BaseStationID=" + BaseStationID + "&@ProgramChildID=" + CStr(ProgramChildID) + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate
        test.Attributes.Add("src", qryString)

       
    End Sub

    Protected Sub bttnNewsReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnNewsReport.Click
        Dim NewsKW As String = ""
        If chk_NewKW.Checked = True Then
            NewsKW = ""
        Else
            NewsKW = txtNewKeywords.Text
        End If

        Dim BaseStationID As String
        If Me.chkStation.Checked = True Then
            BaseStationID = "-1"
        Else
            BaseStationID = ddlBaseStation.SelectedValue
        End If

        ''''''''''''''''''''''''''''''''''''

        Dim Description As String = txtReportSlug.Text

        Dim Slug1 As String = ""
        Dim Slug2 As String = ""
        Dim Slug3 As String = ""
        Dim Slug4 As String = ""
        Dim Slug5 As String = ""

        ''*****************************''

        Dim stringItems() As String = Description.Split("+")
        Dim myArrayList As New ArrayList
        Dim k As Integer
        k = stringItems.Length

        If txtReportSlug.Text = "" Then
            Slug1 = ""
            Slug1 = ""
            Slug2 = ""
            Slug3 = ""
            Slug4 = ""
            Slug5 = ""
        End If

        If k <> 0 Then
            Slug1 = stringItems(0).ToString
        End If


        If k = 1 Then
            Slug2 = stringItems(0).ToString
            Slug3 = stringItems(0).ToString
            Slug4 = stringItems(0).ToString
            Slug5 = stringItems(0).ToString
        End If

        If k > 1 And k < 3 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(1).ToString
            Slug4 = stringItems(1).ToString
            Slug5 = stringItems(1).ToString

        End If
        If k > 2 And k < 4 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(2).ToString
            Slug5 = stringItems(2).ToString
        End If

        If k > 3 And k < 5 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(3).ToString
            Slug5 = stringItems(3).ToString
        End If

        If k > 4 And k < 6 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(3).ToString
            Slug5 = stringItems(4).ToString
        End If


        Dim FromDate, ToDate As String
        If chkIgnoredate_News.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = txtFromdate_News.Text
            ToDate = txtToDate_News.Text
        End If


        Dim FootageTypeID As String = "-1"
        If Trim(txt_NewsKeyTypes_1.Text) = "" Then
            FootageTypeID = "-1"
        Else

            ''''''''''' Get FootageTypeID '''''''''''''''''
            Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
            objFootageTypeID.FootageType = txt_NewsKeyTypes_1.Text
            FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

        End If

        Dim qryString As String
        'qryString = "viewer.aspx?reportname=" & "rpt_ListofKeywordWiseReport_New_withUrduSlug.rpt&@KeyWord1=" + NewsKW + "&@BaseStationID=" + BaseStationID + "&@Slug1=" + Slug1 + "&@Slug2=" + Slug2 + "&@Slug3=" + Slug3 + "&@Slug4=" + Slug4 + "&@Slug5=" + Slug5 + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate
        qryString = "viewer.aspx?reportname=" & "rpt_ListofKeywordWiseReport_New_withUrduSlug.rpt&@KeyWord1=" + NewsKW + "&@BaseStationID=" + BaseStationID + "&@Slug1=" + Slug1 + "&@Slug2=" + Slug2 + "&@Slug3=" + Slug3 + "&@Slug4=" + Slug4 + "&@Slug5=" + Slug5 + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@FootageTypeID=" + FootageTypeID
        test.Attributes.Add("src", qryString)
    
    End Sub

   
End Class
