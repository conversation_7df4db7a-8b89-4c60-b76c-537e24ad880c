<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmTapeType.aspx.vb" Inherits="ApplicationSetup_frmTapeType" title="Home > Tape Type > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebSchedule" TagPrefix="igsch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w18" CssClass="labelheading">Home</asp:LinkButton> &gt; Tape Type &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD>Tape Type</TD><TD style="WIDTH: 140px"><asp:TextBox id="txt_TapeType" runat="server" CssClass="mytext" Width="120px"></asp:TextBox></TD><TD>Description</TD><TD><asp:TextBox id="txt_Description" runat="server" CssClass="mytext" Width="152px" Height="40px" TextMode="MultiLine"></asp:TextBox></TD></TR><TR class="mytext"><TD>EntryDate</TD><TD style="WIDTH: 140px"><asp:TextBox id="WDC_EntryDate" runat="server" CssClass="mytext" Width="120px"></asp:TextBox></TD><TD>UOM</TD><TD><asp:TextBox id="txt_UOM" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px">Opening Balance</TD><TD style="WIDTH: 140px; HEIGHT: 22px"><asp:TextBox id="txt_OpeningBal" runat="server" CssClass="mytext" Width="120px"></asp:TextBox></TD><TD style="HEIGHT: 22px">Current Balance</TD><TD style="HEIGHT: 22px"><asp:TextBox id="txt_CurrentBal" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD></TR><TR class="mytext"><TD>ReOrder Level</TD><TD style="WIDTH: 140px"><asp:TextBox id="txt_ReOrderLevel" runat="server" CssClass="mytext" Width="120px"></asp:TextBox></TD><TD>Cost</TD><TD><asp:TextBox id="txt_Cost" runat="server" CssClass="mytext"></asp:TextBox></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px">Is Active</TD><TD style="WIDTH: 140px; HEIGHT: 22px"><asp:DropDownList id="ddl_IsActive" runat="server" CssClass="mytext" Width="72px">
                                <asp:ListItem Value="1">True</asp:ListItem>
                                <asp:ListItem Value="0">False</asp:ListItem>
                            </asp:DropDownList></TD><TD style="HEIGHT: 22px"></TD><TD style="HEIGHT: 22px"></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="496px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_TapeType" runat="server" CssClass="gridContent" Width="100%" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25" AllowPaging="True"><Columns>
<asp:BoundField DataField="TapeTypeID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="TapeType" HeaderText="Tape Type" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="Description" HeaderText="Desc."></asp:BoundField>
<asp:BoundField DataField="EntryDate" HeaderText="Entry Date"></asp:BoundField>
<asp:BoundField DataField="UOM" HeaderText="UOM"></asp:BoundField>
<asp:BoundField DataField="OpeningBal" HeaderText="Opening Bal."></asp:BoundField>
<asp:BoundField DataField="CurrentBal" HeaderText="Current Bal."></asp:BoundField>
<asp:BoundField DataField="ReOrderLevel" HeaderText="ReOrder Level"></asp:BoundField>
<asp:BoundField DataField="Cost" HeaderText="Cost"></asp:BoundField>
<asp:BoundField DataField="IsActive" HeaderText="Is Active"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD style="HEIGHT: 22px"><asp:TextBox id="txt_TapeTypeID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox>&nbsp; <cc1:CalendarExtender id="CalendarExtender1" runat="server" TargetControlID="WDC_EntryDate">
                </cc1:CalendarExtender> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w9" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

