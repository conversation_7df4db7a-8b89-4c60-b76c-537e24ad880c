Imports System.Data
Imports Infragistics.Excel

Partial Class ExcelUpload_FrmExcelDownload_Ent
    Inherits System.Web.UI.Page

    Protected Sub bttnDownload_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDownload.Click
        ' UltraWebGridExcelExporter1.Export(UltraWebGrid1)
    End Sub

    Private Sub ExportGridToExcel_Complete(ByVal Ds As DataSet)

        Dim Excel As Object = CreateObject("Excel.Application")

        If Excel Is Nothing Then
            MsgBox("It appears that Excel is not installed on this machine. This operation requires MS Excel to be installed on this machine.", MsgBoxStyle.Critical)
            Return
        End If

        'Make Excel visible
        Excel.Visible = True

        Dim dt As New DataTable
        dt = Ds.Tables(0)
        Dim dt2 As New DataTable
        dt2 = Ds.Tables(1)
        Dim dt3 As New DataTable
        dt3 = Ds.Tables(2)

        'Initialize Excel Sheet
        With Excel
            .SheetsInNewWorkbook = 3
            .Workbooks.Add()

            Dim ColumnNo As Integer = 1
            Dim RowNo As Integer = 2

            .Worksheets(2).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt2.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt2.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt2.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt2.Rows(J)(L).ToString()
                Next
            Next

            .Worksheets(3).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt3.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt3.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt2.Rows.Count - 1
                For L As Integer = 0 To dt3.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt3.Rows(J)(L).ToString()
                Next
            Next

            .Worksheets(1).Select()

            ''***************************************************************''
            '' Get Excel Columns Name
            ColumnNo = 1
            For K As Integer = 0 To dt.Columns.Count - 1
                .Cells(1, ColumnNo).Value = dt.Columns(K).ToString()
                ColumnNo += 1
            Next
            ''***************************************************************''

            '' Get Excel Rows Data
            RowNo = 2
            For J As Integer = 0 To dt.Rows.Count - 1
                For L As Integer = 0 To dt.Columns.Count - 1
                    .Cells(J + 2, L + 1).Value = dt.Rows(J)(L).ToString()
                Next
            Next

        End With

        'Excel.Quit()
        System.Runtime.InteropServices.Marshal.ReleaseComObject(Excel)
        Excel = Nothing
        'MsgBox("Export to Excel Complete", MsgBoxStyle.Information)
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim ds_Excel As New DataSet
        Dim dtNK_Excel, dtRP_Excel As New DataTable
        ds_Excel = New BusinessFacade.ExcelUpload().SetupList_ExeclUpload_Ent()

        UltraWebGrid1.DataSource = ds_Excel.Tables(0)
        UltraWebGrid1.DataBind()

        UltraWebGrid2.DataSource = ds_Excel.Tables(1)
        UltraWebGrid2.DataBind()

        UltraWebGrid3.DataSource = ds_Excel.Tables(2)
        UltraWebGrid3.DataBind()

        UltraWebGrid4.DataSource = ds_Excel.Tables(3)
        UltraWebGrid4.DataBind()

        Dim workbook As New Workbook()

        Dim sheet1 As Worksheet = workbook.Worksheets.Add("Sheet1")
        Me.UltraWebGridExcelExporter1.Export(Me.UltraWebGrid1, sheet1)

        Dim sheet2 As Worksheet = workbook.Worksheets.Add("Sheet2")
        Me.UltraWebGridExcelExporter1.Export(Me.UltraWebGrid2, sheet2)

        Dim sheet3 As Worksheet = workbook.Worksheets.Add("Sheet3")
        Me.UltraWebGridExcelExporter1.Export(Me.UltraWebGrid3, sheet3)

        Dim sheet4 As Worksheet = workbook.Worksheets.Add("Sheet4")
        Me.UltraWebGridExcelExporter1.Export(Me.UltraWebGrid4, sheet4)

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Button1_Click(sender, e)
    End Sub

End Class