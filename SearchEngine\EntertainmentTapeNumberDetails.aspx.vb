Imports System.Data

Partial Class SearchEngine_EntertainmentTapeNumberDetails
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Page.IsPostBack = False Then

            Dim TapeNumber As String
            TapeNumber = Request.QueryString("TapeNumber")

            Dim Program As String
            Program = Request.QueryString("ProgramChildName")

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "GetEntSearchEngine_TapeDetails"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim TapeNo As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
            TapeNo.Value = TapeNumber

            Dim Prg As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Program", Data.SqlDbType.Text)
            Prg.Value = Program

            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS, "Table0")

            If DS.Tables("Table0").Rows.Count > 0 Then
                Dim I As Integer
                For I = 0 To DS.Tables("Table0").Rows.Count - 1
                    Dim J As String = I + 1
                    If I <> DS.Tables("Table0").Rows.Count - 1 Then
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(6).ToString + ",</b><br>"
                    Else
                        lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)(6).ToString + "</b><br>"
                    End If
                Next


                lblRecordID.Text = DS.Tables("Table0").Rows(0)("RecordID").ToString
                lblTapeNumber.Text = DS.Tables("Table0").Rows(0)("TapeNumber").ToString
                ' lblKeyword.text = DS.Tables("Table0").Rows(0)(6).ToString
                lblSubCloset.Text = DS.Tables("Table0").Rows(0)("SubCloset").ToString
                lblisAvailable.Text = DS.Tables("Table0").Rows(0)("isAvailable").ToString
                lblEntryDate.Text = DS.Tables("Table0").Rows(0)("EntryDate").ToString
                lblTapeType.Text = DS.Tables("Table0").Rows(0)("TapeType").ToString
                lblTapeStatus.Text = DS.Tables("Table0").Rows(0)("TapeStatus").ToString
                lblStartTime.Text = DS.Tables("Table0").Rows(0)("StartTime").ToString
                lblEndTime.Text = DS.Tables("Table0").Rows(0)("EndTime").ToString
                lblProgramChildName.Text = DS.Tables("Table0").Rows(0)("ProgramChildName").ToString
                lblNoteArea.Text = DS.Tables("Table0").Rows(0)("NoteArea").ToString
                lblAbstract.Text = DS.Tables("Table0").Rows(0)("Abstract").ToString
                lblEpisodeNo.Text = DS.Tables("Table0").Rows(0)("EpisodeNo").ToString
                lblPartNo.Text = DS.Tables("Table0").Rows(0)("PartNo").ToString
                lblDuration.Text = DS.Tables("Table0").Rows(0)("Duration").ToString
            End If
        End If



    End Sub

   
 
End Class
