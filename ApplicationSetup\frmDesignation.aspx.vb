
Partial Class frmDesignation
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.Designation().IsExists_Designation(txt_DesignationName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : Designation already Exists !"
        Else
            If txt_DesignationID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        FillGrid()
        dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim ObjDesg As New BusinessFacade.Designation()
        ObjDesg.Designation = txt_DesignationName.Text
        ObjDesg.UserID = UserID
        ObjDesg.SaveRecord()
        lblErr.Text = "Record has been Saved!!"
        ClearAuditHistory()

    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim objDesg As New BusinessFacade.Designation()
        objDesg.DesignationID = txt_DesignationID.Text
        objDesg.Designation = txt_DesignationName.Text
        objDesg.UserID = UserID
        objDesg.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
        ClearAuditHistory()
    End Sub

    Private Sub FillGrid()
        dg_Designation.DataSource() = New BusinessFacade.Designation().GetRecords()
        dg_Designation.DataBind()
    End Sub

    Protected Sub dg_ContentType_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_Designation.RowCreated
        e.Row.Cells(1).Visible = False
        e.Row.Cells(3).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Designation.SelectedIndexChanged
        I = dg_Designation.SelectedIndex.ToString
        txt_DesignationID.Text = Convert.ToInt32(dg_Designation.Rows(I).Cells(1).Text)
        txt_DesignationName.Text = dg_Designation.Rows(I).Cells(2).Text
        dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Wheat
        lblErr.Text = String.Empty

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.Designation()
        ObjAudit.DesignationID = txt_DesignationID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_Designation()
        dgAuditHistory.DataBind()


    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_DesignationID.Text = "" Then
                lblErr.Text = "Please Select Record First!!"
            Else
                Dim objdesg As New BusinessFacade.Designation()
                objdesg.DesignationID = txt_DesignationID.Text
                objdesg.DeleteRecord(objdesg.DesignationID)
                FillGrid()
                dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                Clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Designation is Already in Used !"
            Clrscr()
            dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try       
    End Sub

    Private Sub Clrscr()
        txt_DesignationName.Text = String.Empty
        txt_DesignationID.Text = String.Empty
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_Designation.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
        ClearAuditHistory()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
