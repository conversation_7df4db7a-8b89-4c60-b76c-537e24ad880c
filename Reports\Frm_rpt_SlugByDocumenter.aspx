<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="Frm_rpt_SlugByDocumenter.aspx.vb" Inherits="Frm_rpt_SlugByDocumenter"
    Title="Home > Other Reports > Q 1. How Can I View Slug report by Documenter Name?" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table width="100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports >  How Can I View Slugs Contain Specific Word?"
                        Width="704px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="WIDTH: 164px; HEIGHT: 13px">&nbsp; </TD><TD style="WIDTH: 192px; HEIGHT: 13px"></TD><TD style="WIDTH: 191px; HEIGHT: 13px"></TD><TD style="WIDTH: 110px; HEIGHT: 13px"></TD><TD style="WIDTH: 162px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 21px">Documenter Name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="ChkEmployee" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" Checked="True" AutoPostBack="True" __designer:wfdid="w3"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 192px; FONT-FAMILY: Arial">Tape Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="ChkTapeNumber" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" Checked="True" AutoPostBack="True" __designer:wfdid="w2"></asp:CheckBox></TD><TD style="WIDTH: 191px">1stKeyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chk_1stKW" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" AutoPostBack="True" __designer:wfdid="w5"></asp:CheckBox></TD><TD style="WIDTH: 180px">2ndKeyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="Chk_2ndKW" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" AutoPostBack="True" __designer:wfdid="w8"></asp:CheckBox></TD><TD style="WIDTH: 162px">Footage Type&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkFootageType" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" AutoPostBack="True" __designer:wfdid="w14"></asp:CheckBox></TD><TD style="WIDTH: 107px">Station <asp:CheckBox id="chkStation" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" AutoPostBack="True" __designer:wfdid="w13"></asp:CheckBox></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 21px"><asp:TextBox id="txtEmployee" runat="server" Width="178px" CssClass="mytext" __designer:wfdid="w4"></asp:TextBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 192px; FONT-FAMILY: Arial"><asp:TextBox id="txtTapeNumber" runat="server" Width="178px" CssClass="mytext" __designer:wfdid="w1"></asp:TextBox></TD><TD style="WIDTH: 191px"><asp:TextBox id="txt1stKeywords" runat="server" __designer:dtid="1970324836974615" Width="174px" CssClass="mytext" __designer:wfdid="w6"></asp:TextBox></TD><TD style="WIDTH: 110px"><asp:TextBox id="txt2ndKeywords" runat="server" __designer:dtid="1970324836974615" Width="174px" CssClass="mytext" __designer:wfdid="w11"></asp:TextBox></TD><TD style="WIDTH: 162px"><asp:TextBox id="txtFootageType" runat="server" __designer:dtid="1970324836974615" Width="174px" CssClass="mytext" __designer:wfdid="w15"></asp:TextBox></TD><TD style="WIDTH: 107px"><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w7">
                                </asp:DropDownList></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 23px">&nbsp;</TD><TD style="FONT-SIZE: 8pt; WIDTH: 192px; FONT-FAMILY: Arial; HEIGHT: 23px"></TD><TD style="WIDTH: 191px; HEIGHT: 23px"></TD><TD style="WIDTH: 110px; HEIGHT: 23px"></TD><TD style="WIDTH: 162px; HEIGHT: 23px"></TD><TD style="WIDTH: 107px; HEIGHT: 23px"></TD><TD style="WIDTH: 107px; HEIGHT: 23px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 21px">Reporter&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkReporter" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" Checked="True" AutoPostBack="True" __designer:wfdid="w35"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 192px; FONT-FAMILY: Arial">Reporter Slug &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkSlug" runat="server" Text="Ignore" Checked="True" AutoPostBack="True" __designer:wfdid="w36"></asp:CheckBox></TD><TD style="WIDTH: 191px">&nbsp;From Date &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore" Checked="True" __designer:wfdid="w487"></asp:CheckBox></TD><TD style="WIDTH: 110px">To Date</TD><TD style="WIDTH: 162px"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w46"></asp:Label></TD><TD style="WIDTH: 107px"></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 164px" vAlign=top><asp:TextBox id="txt_Reporter" tabIndex=28 runat="server" __designer:dtid="1970324836974899" Width="168px" CssClass="mytext" __designer:wfdid="w219"></asp:TextBox></TD><TD style="WIDTH: 192px" vAlign=top><asp:TextBox id="txtReporterSlug" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w41" TextMode="MultiLine"></asp:TextBox></TD><TD style="WIDTH: 191px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="104px" CssClass="mytext" __designer:wfdid="w43"></asp:TextBox></TD><TD style="WIDTH: 110px" vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="96px" CssClass="mytext" __designer:wfdid="w44"></asp:TextBox></TD><TD style="WIDTH: 162px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w47"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 107px" vAlign=top><asp:CheckBox id="ChkUrduSlug" runat="server" Text="Ignore" Width="1px" Checked="True" AutoPostBack="True" __designer:wfdid="w37" Visible="False"></asp:CheckBox>&nbsp; <asp:TextBox id="txtUrduSlug" runat="server" Width="45px" CssClass="mytext" __designer:wfdid="w42" TextMode="MultiLine" Visible="False"></asp:TextBox></TD><TD style="WIDTH: 107px" vAlign=top></TD></TR><TR class="mytext"><TD vAlign=top colSpan=6><cc1:CalendarExtender id="CalendarExtender1" runat="server" __designer:dtid="281474976710670" TargetControlID="txtFromdate" CssClass="MyCalendar" __designer:wfdid="w49" Format="dd-MMM-yyyy"></cc1:CalendarExtender><cc1:CalendarExtender id="CalendarExtender2" runat="server" __designer:dtid="281474976710671" TargetControlID="txtToDate" CssClass="MyCalendar" __designer:wfdid="w50" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_NewsKeywords_1" runat="server" __designer:dtid="3377699720527977" TargetControlID="txt1stKeywords" __designer:wfdid="w7" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_2ndKeywords" runat="server" __designer:dtid="3377699720527977" TargetControlID="txt2ndKeywords" __designer:wfdid="w12" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_FootageTypes" runat="server" __designer:dtid="3096224743817357" TargetControlID="txtFootageType" __designer:wfdid="w16" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx" MinimumPrefixLength="1"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender1" runat="server" __designer:dtid="1970324836975034" TargetControlID="txt_Reporter" __designer:wfdid="w220" CompletionInterval="1" CompletionSetCount="12" ServiceMethod="Reporter_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx" Enabled="True" DelimiterCharacters=""></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" TargetControlID="txtEmployee" __designer:wfdid="w283" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetEmployee" ServicePath="AutoComplete.asmx" MinimumPrefixLength="3"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_2" runat="server" TargetControlID="txtTapeNumber" __designer:wfdid="w284" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetTapeContentNews_Lister_TapeNo" ServicePath="AutoComplete.asmx" MinimumPrefixLength="1"></cc1:AutoCompleteExtender></TD><TD vAlign=top colSpan=1></TD></TR></TBODY></TABLE>
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain">
                                &nbsp;
                                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Text="View Report" Width="88px" />
                            </td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;&nbsp;
            </td>
        </tr>
    </table>
    &nbsp;<cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server"
        CollapseControlID="TitlePanel" Collapsed="false" CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Form (Other Reports > How Can I View Slugs Contain Specific Word?) --"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" ExpandedText="-- Hide Form (Other Reports > How Can I View Slugs Contain Specific Word?) --"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>
