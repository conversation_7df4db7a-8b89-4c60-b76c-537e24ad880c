Imports System.Data
Imports System.Data.SqlClient
Partial Class LogSheet_LogSheet_Search
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    'Dim ConString As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                Chk_Ignore.Checked = True
                ChkTapeKhi.Checked = True
                ChkTapeDbx.Checked = True
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch.Click
        lblErr.Text = String.Empty
        BindGrid()


    End Sub

    Private Sub BindGrid()

        ''''''''''''''''''''''''''''''''''''
        Dim Description As String = txt_Description.Text

        Dim Slug1 As String = ""
        Dim Slug2 As String = ""
        Dim Slug3 As String = ""
        Dim Slug4 As String = ""
        Dim Slug5 As String = ""



        Dim stringItems() As String = Description.Split("~")
        Dim myArrayList As New ArrayList
        Dim k As Integer
        k = stringItems.Length

        If txt_Description.Text = "" Then
            Slug1 = ""
            Slug1 = ""
            Slug2 = ""
            Slug3 = ""
            Slug4 = ""
            Slug5 = ""
        End If

        If k <> 0 Then
            Slug1 = stringItems(0).ToString
        End If


        If k = 1 Then
            Slug2 = stringItems(0).ToString
            Slug3 = stringItems(0).ToString
            Slug4 = stringItems(0).ToString
            Slug5 = stringItems(0).ToString
        End If

        If k > 1 And k < 3 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(1).ToString
            Slug4 = stringItems(1).ToString
            Slug5 = stringItems(1).ToString

        End If
        If k > 2 And k < 4 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(2).ToString
            Slug5 = stringItems(2).ToString
        End If

        If k > 3 And k < 5 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(3).ToString
            Slug5 = stringItems(3).ToString
        End If

        If k > 4 And k < 6 Then
            Slug2 = stringItems(1).ToString
            Slug3 = stringItems(2).ToString
            Slug4 = stringItems(3).ToString
            Slug5 = stringItems(4).ToString
        End If

        ''''''''''''''''''''''''''''''''''''

        Dim Qry As String
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetData_TapeLog_Lister_Master"
        cmd.Connection = Con

        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@FromDate", Data.SqlDbType.Text)
        If Chk_Ignore.Checked = True Then
            p1.Value = "-1"
        Else
            p1.Value = txt_FromDate.Text
        End If

        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
        If Chk_Ignore.Checked = True Then
            p2.Value = "-1"
        Else
            p2.Value = txt_ToDate.Text
        End If

        Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
        If ChkTapeKhi.Checked = True Then
            p3.Value = "-1"
        Else
            p3.Value = Txt_TapeKhi.Text
        End If

        Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
        If ChkTapeDbx.Checked = True Then
            p4.Value = "-1"
        Else
            p4.Value = txt_TapeDbx.Text
        End If

        Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug1", Data.SqlDbType.Text)
        p5.Value = Slug1

        Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug2", Data.SqlDbType.Text)
        p6.Value = Slug2

        Dim p7 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug3", Data.SqlDbType.Text)
        p5.Value = Slug3

        Dim p8 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug4", Data.SqlDbType.Text)
        p5.Value = Slug4

        Dim p9 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug5", Data.SqlDbType.Text)
        p5.Value = Slug5

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS = New DataSet



        Try
            da.SelectCommand = cmd
            da.Fill(DS, "Master")
            Qry = da.SelectCommand.CommandText
            da.SelectCommand.CommandText = "GetData_TapeLog_Lister_Detail"
            da.SelectCommand.Parameters.Clear()

            Dim R1 As Data.SqlClient.SqlParameter = da.SelectCommand.Parameters.Add("@FromDate", SqlDbType.Text)
            If Chk_Ignore.Checked = True Then
                R1.Value = "-1"
            Else
                R1.Value = txt_FromDate.Text
            End If

            Dim R2 As Data.SqlClient.SqlParameter = da.SelectCommand.Parameters.Add("@ToDate", SqlDbType.Text)
            If Chk_Ignore.Checked = True Then
                R2.Value = "-1"
            Else
                R2.Value = txt_ToDate.Text
            End If

            Dim R3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
            If ChkTapeKhi.Checked = True Then
                R3.Value = "-1"
            Else
                R3.Value = Txt_TapeKhi.Text
            End If

            Dim R4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
            If ChkTapeDbx.Checked = True Then
                R4.Value = "-1"
            Else
                R4.Value = txt_TapeDbx.Text
            End If

            Dim R5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug1", Data.SqlDbType.Text)
            R5.Value = Slug1

            Dim R6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug2", Data.SqlDbType.Text)
            R6.Value = Slug2

            Dim R7 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug3", Data.SqlDbType.Text)
            R7.Value = Slug3

            Dim R8 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug4", Data.SqlDbType.Text)
            R8.Value = Slug4

            Dim R9 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Slug5", Data.SqlDbType.Text)
            R9.Value = Slug5


            da.Fill(DS, "Child")
            DS.Relations.Add(DS.Tables("Master").Columns("LogID"), DS.Tables("Child").Columns("LogMasterId"))
            UltraWebGrid1.DataSource = DS.Tables("Master").DefaultView
            UltraWebGrid1.DataBind()
            'GridView1.DataSource = DS.Tables("Master").DefaultView
            'GridView1.DataBind()

            If DS.Tables(0).Rows.Count > 0 Then
                UltraWebGrid1.DataSource = DS.Tables(0).DefaultView
                UltraWebGrid1.DataBind()
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnAdd_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAdd.Click
        Response.Redirect("..\LogSheet\LogSheet_Save.aspx")
    End Sub

    Protected Sub bttnEdit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEdit.Click
        If txt_MasterID.Text <> "" Then
            Response.Redirect("LogSheet_Save.aspx?ID=" & txt_MasterID.Text)
        End If
    End Sub

    Protected Sub UltraWebGrid1_SelectedRowsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedRowsEventArgs) Handles UltraWebGrid1.SelectedRowsChange
        'txt_MasterID.Text = e.SelectedRows(0).Cells(0).Value

        txt_MasterID.Text = UltraWebGrid1.DisplayLayout.SelectedRows.Item(0).Cells(0).ToString 'e.SelectedRows.Item(e.SelectedRows.IndexOf(grd_Re).Cells(1).Value.ToString
        If txt_MasterID.Text <> "" Then
            Response.Redirect("LogSheet_Save.aspx?ID=" & txt_MasterID.Text)
        End If
    End Sub

    Protected Sub bttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDelete.Click
        If txt_MasterID.Text <> "" Then
            Dim ObjUser As New BusinessFacade.LostSheet()
            ObjUser.LogMasterID = txt_MasterID.Text
            ObjUser.DeleteRecord()
            lblErr.Text = "Record has been Deleted!!"
            BindGrid()
        End If
    End Sub

    Protected Sub Chk_Ignore_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Chk_Ignore.CheckedChanged
        If Chk_Ignore.Checked = True Then
            txt_FromDate.Enabled = False
            txt_ToDate.Enabled = False
        End If

        If Chk_Ignore.Checked = False Then
            txt_FromDate.Enabled = True
            txt_ToDate.Enabled = True
        End If
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

   
End Class
