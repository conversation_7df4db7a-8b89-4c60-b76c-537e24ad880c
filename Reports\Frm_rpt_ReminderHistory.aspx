<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ReminderHistory.aspx.vb" Inherits="Frm_rpt_ReminderHistory" title="Reminder Service > Q 1. How Can I View Employee and Department wise Reminder History of Tapes?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > Q 4. How Can I View Employee & Department Wise Tapes Issue And Return?" Width="880px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 7px" vAlign=middle>&nbsp;</TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 7px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 7px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 7px" vAlign=middle></TD></TR><TR class="mytext"><TD style="HEIGHT: 23px" vAlign=middle>Department Name</TD><TD style="HEIGHT: 23px" vAlign=middle>Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" Checked="True" __designer:wfdid="w24" AutoPostBack="True" OnCheckedChanged="chkEmployee_CheckedChanged"></asp:CheckBox></TD><TD style="HEIGHT: 23px" vAlign=middle>From Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" __designer:wfdid="w25"></asp:CheckBox></TD><TD style="HEIGHT: 23px" vAlign=middle>To Date</TD><TD style="HEIGHT: 23px" vAlign=middle>Station <asp:CheckBox id="chkStation" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" __designer:wfdid="w24" AutoPostBack="True"></asp:CheckBox></TD><TD style="WIDTH: 100px; HEIGHT: 23px" vAlign=middle>Report Type</TD><TD style="WIDTH: 100px; HEIGHT: 23px" vAlign=middle>Reminders Counter</TD><TD style="WIDTH: 100px; HEIGHT: 23px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w58"></asp:Label></TD></TR><TR class="mytext"><TD vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="200px" CssClass="mytext" Font-Size="X-Small" Font-Names="Verdana" __designer:wfdid="w26" AutoPostBack="True" DataSourceID="dsDepartment" DataTextField="DepartmentName" DataValueField="DepartmentID">
                                </asp:DropDownList><asp:SqlDataSource id="dsDepartment" runat="server" __designer:wfdid="w27" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;">
                                </asp:SqlDataSource> </TD><TD vAlign=top><asp:TextBox id="TxtEmployee" runat="server" __designer:dtid="3377699720527948" Width="192px" CssClass="mytext" __designer:wfdid="w5" EnableViewState="False"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w30"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w31"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w25">
                                </asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlReportType" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w1"><asp:ListItem>Detail</asp:ListItem>
<asp:ListItem>Summary</asp:ListItem>
</asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlReminderCounter" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w1"><asp:ListItem Value="-1">- - Ignore - -</asp:ListItem>
<asp:ListItem>1</asp:ListItem>
<asp:ListItem>2</asp:ListItem>
<asp:ListItem Value="3">3 and More Than 3</asp:ListItem>
</asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w59"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w32" TargetControlID="ddlDepartment" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender><cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w34" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w35" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" __designer:wfdid="w6" TargetControlID="TxtEmployee" CompletionSetCount="12" EnableCaching="true" CompletionInterval="1" MinimumPrefixLength="3" ServiceMethod="GetEmployee" ServicePath="AutoComplete.asmx">
</cc1:AutoCompleteExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100px">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    &nbsp;</asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="--Show Form (Reminder Service > Employee and Department wise Reminder History of Tapes)--"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="--Hide Form (Reminder Service > Employee and Department wise Reminder History of Tapes)--"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

