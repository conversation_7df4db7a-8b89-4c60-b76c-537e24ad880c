Imports System
Imports System.Data
Imports System.Data.SqlClient

Partial Class ApplicationSetup_FrmEmployeeMapping
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")
            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)
        End If


        If Not Page.IsPostBack = True Then
            bttnMappEmployee.Enabled = False
            lblErr.Text = "You are not Authorize for Employee Mapping !"


            ''***************************''
            Dim objUsers As New BusinessFacade.Employee
            Dim IsValid As Integer = objUsers.GetMappingUsers("Employee", lbl_UserName.Text.ToLower)
            If IsValid = 1 Then
                bttnMappEmployee.Enabled = True
                lblErr.Text = ""
            Else
                bttnMappEmployee.Enabled = False
                lblErr.Text = "You are not Authorize for Employee Mapping !"
            End If

            ''***************************''

            'If (lbl_UserName.Text.ToLower = "turab.ali") Or (lbl_UserName.Text.ToLower = "sobia.aziz") Then
            '    bttnMappEmployee.Enabled = True
            '    lblErr.Text = ""
            'End If

        End If
        
    End Sub

    Protected Sub bttnMappEmployee_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappEmployee.Click
        Try
            '**************************************************'
            '**************** Get Old EmployeeID **************'
            '**************************************************'

            Dim OldEmployeeID As Integer
            Dim objOldEmployeeID As New BusinessFacade.TapeIssuance()
            objOldEmployeeID.EmployeeName = txtOldEmployee.Text
            OldEmployeeID = objOldEmployeeID.GetEmployeeID_byEmployeeName(objOldEmployeeID.EmployeeName)

            '**************************************************'
            '**************** Get New EmployeeID **************'
            '**************************************************'

            Dim NewEmployeeID As Integer
            Dim objNewEmployeeID As New BusinessFacade.TapeIssuance()
            objNewEmployeeID.EmployeeName = txtNewEmployee.Text
            NewEmployeeID = objNewEmployeeID.GetEmployeeID_byEmployeeName(objNewEmployeeID.EmployeeName)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If OldEmployeeID <> "0" And NewEmployeeID <> "0" Then

                ''****************************************************''
                ''*********** Insert in Employee Mapping *************''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString") '"server=MISOPS1\DAMS;database=Dams_NewDB;uid=sa;pwd=**********"
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "Proc_EmployeeMapping " & OldEmployeeID & "," & NewEmployeeID & "," & UserID
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr.Text = "Employee has been Mapped Successfully."

                'bttnMappEmployee.Enabled = False
                If Not Page.IsPostBack Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmEmployeeMapping.aspx" + ";"""
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                End If
                txtNewEmployee.Text = String.Empty
                txtOldEmployee.Text = String.Empty
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmEmployeeMapping.aspx" + ";"""
        'script = script + "}</script>"
        'Page.RegisterClientScriptBlock("test", script)
        txtNewEmployee.Text = String.Empty
        txtOldEmployee.Text = String.Empty
        lblErr.Text = String.Empty

    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onbeforeunload = function() {"
        script = script + "return ""Closing the page now may result in data loss."";"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)
    End Sub
End Class
