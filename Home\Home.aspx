<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Home.aspx.vb" Inherits="Home_Home" title="Archive Management System > Home Page" %>

<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="DCWC" %>

<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="DCWC" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table width="100%">
 <tr>
            <td id="td_Ticker" runat="server" width="100%" height="16" align="center" valign="middle"
                colspan="2" class="FooterLink">
            </td>
        </tr>
        <tr>
            <td style="width: 428px; height: 21px">
                <asp:Image ID="Image2" runat="server" ImageUrl="~/Images/Archive.jpg" Height="485px" /></td>
            <td style="width: 100%;" valign="top">
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <td align="right" class="hometext" style="color: #330099; height: 24px">
                        </td>
                        <td align="right" class="hometext" style="color: #330099; height: 24px">
                            &nbsp; |
                            <asp:LinkButton ID="lnkEntSearch" runat="server" Font-Size="Small" Font-Underline="False"
                                ForeColor="Navy">Entertainment Search</asp:LinkButton>
                            |
                            <asp:LinkButton ID="lnkNewsSearch" runat="server" Font-Size="Small" Font-Underline="False"
                                ForeColor="Navy">News Search</asp:LinkButton>
                            |
                            <asp:LinkButton ID="lnkPassword" runat="server" Font-Size="Small" Font-Underline="False"
                                ForeColor="Navy" Visible="False">Change Password</asp:LinkButton>
                            |</td>
                    </tr>
                    <tr>
                        <td class="hometext">
                        </td>
                        <td class="hometext">
                Digital Archive
                Management System is designed to provide central repository to record and retrieve
                data related to all types of contents/ programs and news reports with smart search
                options, so producers, archive department and any other stake holder can also easily
                search their required content / videos.</td>
                    </tr>
                    <tr>
                        <td style="height: 1px">
                        </td>
                    <td style="height:1px"></td>
                    </tr>
                    <tr>
                        <td align="center" style="font-weight: bold; color: #333399; height: 1px; background-color: #cedafb">
                        </td>
                        <td align="center" style="font-weight: bold; height: 1px; background-color: #cedafb; color: #333399;">
                            - - Spot Light Summary - -</td>
                    </tr>
                    <tr>
                        <td align="left" style="height: 221px">
                        </td>
                        <td align="left" style="height: 221px">
                            <table style="width: 100%">
                                <tr>
                                    <td style="width: 50%" valign="top">
                                        <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False"
                                            Font-Size="X-Small" Width="100%" BackColor="#CEDAFB">
                                        <Columns>
                                            <asp:TemplateField HeaderText="- - Most Viewed Reports - -">
                                                <EditItemTemplate>
                                                    <asp:LinkButton ID="LinkButton2" runat="server">LinkButton</asp:LinkButton>
                                                </EditItemTemplate>
                                                <ItemTemplate>
                                                    <asp:LinkButton ID="LinkButton1" PostBackUrl='<% # Eval("Redirect") %>' Text='<% # Eval("ReportName") %>' runat="server" CssClass="mytext" Font-Underline="True"></asp:LinkButton>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:BoundField DataField="Redirect" HeaderText="Redirect" Visible="False" />
                                        </Columns>
                                            <HeaderStyle BackColor="#CEDAFB" CssClass="mytext" />
                                    </asp:GridView>
                                    </td>
                                    <td style="width: 50%" valign="top"><asp:GridView ID="GridView2" runat="server" AutoGenerateColumns="False"
                                            Font-Size="X-Small" Width="100%" BackColor="#CEDAFB">
                                        <Columns>
                                            <asp:TemplateField HeaderText="- - Most Searched Elements - -">
                                                <EditItemTemplate>
                                                    <asp:LinkButton ID="LinkButton4" runat="server">LinkButton</asp:LinkButton>
                                                </EditItemTemplate>
                                                <ItemTemplate>
                                                    <asp:LinkButton ID="LinkButton3" PostBackUrl='<% # Eval("Redirect") %>' Text='<% # Eval("SearchElement") %>' runat="server" CssClass="mytext"></asp:LinkButton>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:BoundField DataField="SearchTextBox" HeaderText="SearchTextBox" Visible="False" />
                                            <asp:BoundField DataField="Redirect" HeaderText="Redirect" Visible="False" />
                                            <asp:BoundField DataField="ContentType" HeaderText="Content Type">
                                                <ItemStyle CssClass="mytext" />
                                            </asp:BoundField>
                                        </Columns>
                                        <HeaderStyle BackColor="#CEDAFB" CssClass="mytext" />
                                    </asp:GridView>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 50%; height: 61px;" align="center">
                                        <DCWC:Chart ID="Chart1" runat="server" BackColor="LightBlue" BackGradientEndColor="PaleTurquoise"
                                            BackGradientType="TopBottom" BorderLineColor="Gray" DataSourceID="SqlDataSource1"
                                            Height="225px" Palette="Dundas">
                                            <Legends>
                                                <DCWC:Legend BackColor="Transparent" BorderColor="Transparent" Docking="Bottom" Name="Default">
                                                </DCWC:Legend>
                                            </Legends>
                                            <Titles>
                                                <DCWC:Title Name="Title1">
                                                </DCWC:Title>
                                            </Titles>
                                            <Series>
                                                <DCWC:Series BackGradientEndColor="White" BackGradientType="VerticalCenter" BorderColor="64, 64, 64"
                                                    Label="#VAL{D}" Name="Grand Issue Return Details" ShadowOffset="2" ValueMembersY="TapeAmount"
                                                    ValueMemberX="TapeStatus" XValueType="Single" YValueType="Int">
                                                </DCWC:Series>
                                            </Series>
                                            <ChartAreas>
                                                <DCWC:ChartArea BackColor="Transparent" BorderColor="64, 64, 64" Name="Default">
                                                    <AxisY LineColor="64, 64, 64">
                                                        <MajorGrid Enabled="False" LineColor="Gray" LineStyle="Dash" />
                                                    </AxisY>
                                                    <AxisX LineColor="64, 64, 64" TitleAlignment="Near">
                                                        <MajorGrid Enabled="False" LineColor="Gray" LineStyle="Dash" />
                                                        <MajorTickMark Style="none" />
                                                    </AxisX>
                                                    <AxisX2 LineColor="64, 64, 64">
                                                    </AxisX2>
                                                    <AxisY2 LineColor="64, 64, 64">
                                                    </AxisY2>
                                                </DCWC:ChartArea>
                                            </ChartAreas>
                                            <BorderSkin FrameBackColor="PowderBlue" FrameBackGradientEndColor="Olive" PageColor="AliceBlue"
                                                SkinStyle="FrameThin6" />
                                        </DCWC:Chart>
                                        <asp:SqlDataSource ID="SqlDataSource1" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString5 %>"
                                            SelectCommand="GetSpotLight_GrandIssueReturn_BaseStation" SelectCommandType="StoredProcedure">
                                            <SelectParameters>
                                                <asp:Parameter Direction="ReturnValue" Name="RETURN_VALUE" Type="Int32" />
                                                <asp:ControlParameter ControlID="lblBaseStation" Name="BaseStationID" PropertyName="Text"
                                                    Type="Int32" />
                                            </SelectParameters>
                                        </asp:SqlDataSource>
                                    </td>
                                    <td style="width: 50%; height: 61px;" align="center" valign="top">
                                        <DCWC:Chart ID="Chart2" runat="server" BackColor="LightBlue" BackGradientEndColor="PaleTurquoise"
                                            BackGradientType="TopBottom" BorderLineColor="Gray" DataSourceID="SqlDataSource2"
                                            Height="225px" Palette="DundasDark">
                                            <Legends>
                                                <DCWC:Legend BackColor="Transparent" BorderColor="Transparent" Docking="Bottom" Name="Default">
                                                </DCWC:Legend>
                                            </Legends>
                                            <Titles>
                                                <DCWC:Title Name="Title1">
                                                </DCWC:Title>
                                            </Titles>
                                            <Series>
                                                <DCWC:Series BackGradientEndColor="White" BackGradientType="VerticalCenter" BorderColor="64, 64, 64"
                                                    Label="#VAL{D}" Name="Yesterday Issue Return Details" ShadowOffset="2" ValueMembersY="TapeAmount"
                                                    ValueMemberX="TapeStatus" XValueType="Single" YValueType="Int">
                                                </DCWC:Series>
                                            </Series>
                                            <ChartAreas>
                                                <DCWC:ChartArea BackColor="Transparent" BorderColor="64, 64, 64" Name="Default">
                                                    <AxisY LineColor="64, 64, 64">
                                                        <MajorGrid Enabled="False" LineColor="Gray" LineStyle="Dash" />
                                                    </AxisY>
                                                    <AxisX LineColor="64, 64, 64" TitleAlignment="Near">
                                                        <MajorGrid Enabled="False" LineColor="Gray" LineStyle="Dash" />
                                                        <MajorTickMark Style="none" />
                                                    </AxisX>
                                                    <AxisX2 LineColor="64, 64, 64">
                                                    </AxisX2>
                                                    <AxisY2 LineColor="64, 64, 64">
                                                    </AxisY2>
                                                </DCWC:ChartArea>
                                            </ChartAreas>
                                            <BorderSkin FrameBackColor="PowderBlue" FrameBackGradientEndColor="Olive" PageColor="AliceBlue"
                                                SkinStyle="FrameThin6" />
                                        </DCWC:Chart>
                                        <asp:SqlDataSource ID="SqlDataSource2" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString5 %>"
                                            SelectCommand="GetSloptLights_YesterDayIssueReturn_BaseStation" SelectCommandType="StoredProcedure">
                                            <SelectParameters>
                                                <asp:Parameter Direction="ReturnValue" Name="RETURN_VALUE" Type="Int32" />
                                                <asp:ControlParameter ControlID="lblBaseStation" Name="BaseStationID" PropertyName="Text"
                                                    Type="Int32" />
                                            </SelectParameters>
                                        </asp:SqlDataSource>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="width: 428px; height: 21px;">
                </td>
            <td style="width: 100px; height: 21px;" valign="top">
                &nbsp;<asp:Label ID="lblBaseStation" runat="server" Visible="False"></asp:Label></td>
        </tr>
        <tr>
            <td style="width: 428px">
            </td>
            <td style="width: 100px">
            </td>
        </tr>
    </table>
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:Panel ID="Panel1" runat="server" CssClass="modalPopup" Style="display: none"
        Width="233px">
        <table style="width: 16px">
            <tr>
                <td class="labelheading" colspan="2" style="height: 20px; text-decoration: underline">
                    Change Password</td>
            </tr>
            <tr>
                <td style="width: 106px; height: 9px">
                </td>
                <td style="width: 41px; height: 9px">
                </td>
            </tr>
            <tr>
                <td class="mytext" style="width: 106px; height: 22px">
                    Old Password</td>
                <td style="width: 41px; height: 22px">
                    <asp:TextBox ID="txtOldPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            </tr>
            <tr>
                <td class="mytext" style="width: 106px; height: 21px">
                    New Password</td>
                <td style="width: 41px; height: 21px">
                    <asp:TextBox ID="txtNewPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            </tr>
            <tr>
                <td class="mytext" style="width: 106px; height: 21px">
                    Confirm Password</td>
                <td style="width: 41px; height: 21px">
                    <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            </tr>
            <tr>
                <td style="width: 106px; height: 21px">
                </td>
                <td style="width: 41px; height: 21px">
                </td>
            </tr>
            <tr>
                <td colspan="2" style="height: 29px">
                    &nbsp;<asp:Button ID="bttnSave" runat="server" Font-Bold="True" Text="Save" Width="56px" />
                    <asp:Button ID="CancelButton" runat="server" Font-Bold="True" Text="Cancel" />
                    <asp:Button ID="OkButton" runat="server" Text="OK" Visible="False" /></td>
            </tr>
            <tr>
                <td colspan="2" style="height: 21px">
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="232px"></asp:Label></td>
            </tr>
        </table>
        <div align="center">
            &nbsp; &nbsp;</div>
    </asp:Panel>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
    <cc1:ModalPopupExtender ID="ModalPopupExtender1" runat="server"
            TargetControlID="lnkPassword"
            PopupControlID="Panel1"
            BackgroundCssClass="modalBackground"
            DropShadow="true"               
            CancelControlID="CancelButton" >
    </cc1:ModalPopupExtender>
</asp:Content>

