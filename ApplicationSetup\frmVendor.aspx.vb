
Partial Class ApplicationSetup_frmVendor
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_City.DataSource = New BusinessFacade.City().GetRecords()
        ddl_City.DataTextField = "CityName"
        ddl_City.DataValueField = "CityID"
        ddl_City.DataBind()
        ddl_City.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_VendorID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_VendorName.Text = "" Then
            lblErr.Text = "Please Insert Vendor Name!!"
        ElseIf ddl_City.SelectedIndex = "0" Then
            lblErr.Text = "Please Select City!!"
        Else
            Dim ObjVendor As New BusinessFacade.Vendor()
            ObjVendor.VendorName = txt_VendorName.Text
            ObjVendor.VendorAddress = txt_VendorAddress.Text
            ObjVendor.CityID = ddl_City.SelectedValue
            ObjVendor.Mobile = txt_Mobile.Text
            ObjVendor.Phone = txt_Phone.Text
            ObjVendor.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjVendor As New BusinessFacade.Vendor()
        ObjVendor.VendorID = txt_VendorID.Text
        ObjVendor.VendorName = txt_VendorName.Text
        ObjVendor.VendorAddress = txt_VendorAddress.Text
        ObjVendor.CityID = ddl_City.SelectedValue
        ObjVendor.Mobile = txt_Mobile.Text
        ObjVendor.Phone = txt_Phone.Text
        ObjVendor.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_Vendor.DataSource() = New BusinessFacade.Vendor().GetRecords()
        dg_Vendor.Columns(0).Visible = True
        dg_Vendor.Columns(3).Visible = True
        dg_Vendor.DataBind()
        dg_Vendor.Columns(0).Visible = False
        dg_Vendor.Columns(3).Visible = False
    End Sub

    'Protected Sub dg_Vendor_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_Vendor.RowCreated
    '    e.Row.Cells(1).Visible = False
    '    e.Row.Cells(4).Visible = False
    'End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Vendor.SelectedIndexChanged
        I = dg_Vendor.SelectedIndex.ToString
        txt_VendorID.Text = Convert.ToInt32(dg_Vendor.Rows(I).Cells(1).Text)
        txt_VendorName.Text = dg_Vendor.Rows(I).Cells(2).Text
        txt_VendorAddress.Text = dg_Vendor.Rows(I).Cells(3).Text
        ddl_City.SelectedValue = dg_Vendor.Rows(I).Cells(4).Text
        txt_Phone.Text = dg_Vendor.Rows(I).Cells(6).Text
        txt_Mobile.Text = dg_Vendor.Rows(I).Cells(7).Text
        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        lblErr.Text = String.Empty
        Activate()
        'If MsgBox("Do you want to Delete!!", MsgBoxStyle.YesNo, "ALERT") = MsgBoxResult.Yes Then
        '    If txt_VendorID.Text = "" Then
        '        lblErr.Text = "Please Select Record"
        '    Else
        '        Dim ObjVendor As New BusinessFacade.Vendor()
        '        ObjVendor.VendorID = txt_VendorID.Text
        '        ObjVendor.DeleteRecord(ObjVendor.VendorID)
        '        FillGrid()
        '        clrscr()
        '        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        '        lblErr.Text = "Record has been Deleted !!"
        '    End If

        'Else
        '    dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        '    clrscr()
        '    lblErr.Text = String.Empty
        'End If
        
    End Sub

    Private Sub clrscr()
        txt_VendorName.Text = String.Empty
        txt_VendorID.Text = String.Empty
        txt_VendorAddress.Text = String.Empty
        ddl_City.SelectedIndex = "0"
        txt_Mobile.Text = String.Empty
        txt_Phone.Text = String.Empty
    End Sub

    Protected Sub dg_Vendor_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_Vendor.PageIndexChanging
        dg_Vendor.PageIndex = e.NewPageIndex()
        FillGrid()
        clrscr()
        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Sub Activate()
        Me.btnYes.Visible = True
        Me.btnNo.Visible = True
        Me.lblMsg.Visible = True
    End Sub

    Sub DeActivate()
        Me.btnYes.Visible = False
        Me.btnNo.Visible = False
        Me.lblMsg.Visible = False
    End Sub

    Protected Sub btnYes_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnYes.Click
        DeActivate()
        If txt_VendorID.Text = "" Then
            lblErr.Text = "Please Select Record"
        Else
            Dim ObjVendor As New BusinessFacade.Vendor()
            ObjVendor.VendorID = txt_VendorID.Text
            ObjVendor.DeleteRecord(ObjVendor.VendorID)
            FillGrid()
            clrscr()
            dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            lblErr.Text = "Record has been Deleted !!"
        End If
    End Sub

    Protected Sub btnNo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnNo.Click
        DeActivate()
        dg_Vendor.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        lblMsg.Visible = False
    End Sub

End Class
