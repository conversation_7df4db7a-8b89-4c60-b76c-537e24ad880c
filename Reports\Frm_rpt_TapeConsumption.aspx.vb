Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_TapeConsumption
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkEmployee.Checked = True
                chkProgram.Checked = True
                TxtEmployee.Enabled = False
                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim Obj As New BusinessFacade.Reports()
        ddlMonth.DataSource = Obj.GetConsumptionMonths()
        ddlMonth.DataTextField = "MonthName"
        ddlMonth.DataValueField = "MonthID"
        ddlMonth.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            lblErr.Text = String.Empty

            If ddlMonth.SelectedValue = "-1" Then
                lblErr.Text = "Please select Month!"
                Exit Sub
            End If

            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String

            Dim Dept As String
            Dim Emp As String

            Dept = ddlDepartment.SelectedValue.ToString()

            If chkEmployee.Checked = True Then
                Emp = "-1"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As String
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    Emp = EmployeeID.ToString
                Else
                    Emp = "-1"
                End If

            End If

            ''**********************************************************''
            ''********************Get Program ChildID ******************''
            ''**********************************************************''
            Dim ProgramChildID As String
            If chkProgram.Checked = True Then
                ProgramChildID = "-1"
            Else
                Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
                ObjProgramChildID.ProgramChildName = txtProgram.Text
                ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)
            End If


            Dim Month, Year As String
            Dim Arr As Array = Split(ddlMonth.SelectedItem.Text, " - ")
            Month = ddlMonth.SelectedValue
            Year = Arr(1).ToString()


            Dim dt As New Hashtable
            dt.Add("@Month", Month)
            dt.Add("@Year", Year)
            dt.Add("@EmployeeID", Emp)
            dt.Add("@DepartmentID", Dept)
            dt.Add("@ProgramID", ProgramChildID)

            If ddlPDF.SelectedValue = "PDF" Then
                LoadPDFReport("Rpt_MonthlyTapeConsumtion", dt)
            Else
                LoadExcelReport("Rpt_MonthlyTapeConsumtion", dt)
            End If

            'If ddlPDF.SelectedValue = "PDF" Then
            '    Dim script As String
            '    script = "<script language='javascript' type='text/javascript'>"
            '    script = script + "window.onload=function OpenReport() {"
            '    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=Rpt_MonthlyTapeConsumtion.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@ProgramID=" + ProgramChildID + "&@Month=" + Month + "&@Year=" + Year + "', 'mywindow'); "
            '    script = script + "}</script>"

            '    Page.RegisterClientScriptBlock("test", script)
            'Else
            '    Dim script As String
            '    script = "<script language='javascript' type='text/javascript'>"
            '    script = script + "window.onload=function OpenReport() {"
            '    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=Rpt_MonthlyTapeConsumtion.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@ProgramID=" + ProgramChildID + "&@Month=" + Month + "&@Year=" + Year + "', 'mywindow'); "
            '    script = script + "}</script>"

            '    Page.RegisterClientScriptBlock("test", script)

            'End If

        Catch ex As Exception
            Throw
        End Try

    End Sub


    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub


    Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        '--- Exporting Crystal Report to PDF process...


        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports/")

        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/Reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("sa", "Newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".pdf" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        crReportDocument.Export()
        crReportDocument.Close()

        Dim scriptString As String = "<script type='text/javascript'>" & _
           "window.onload = function(){" & _
           "var url = '" + "" + RptName + ".pdf" + "';" & _
           "var winPop = window.open(url,'winPop');" & _
           "}" & _
           "</script>"
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)
    End Sub

    Sub LoadExcelReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        '--- Exporting Crystal Report to PDF process...


        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports/")

        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/Reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("sa", "Newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".xls" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.Excel
        crReportDocument.Export()
        crReportDocument.Close()

        Dim scriptString As String = "<script type='text/javascript'>" & _
           "window.onload = function(){" & _
           "var url = '" + "" + RptName + ".xls" + "';" & _
           "var winPop = window.open(url,'winPop');" & _
           "}" & _
           "</script>"
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)
    End Sub
End Class
