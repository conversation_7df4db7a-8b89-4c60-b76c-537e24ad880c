
Partial Class TapeContent_AddNewsKeyword
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_SubContentType.DataSource = New BusinessFacade.SubContentType().GetRecords()
        ddl_SubContentType.DataTextField = "SubContentTypeName"
        ddl_SubContentType.DataValueField = "SubContentTypeID"
        ddl_SubContentType.DataBind()
        ddl_SubContentType.Items.Insert(0, "--Select--")

        ''''''''''''''''''''''''''''''''''''''''''''
        lstNews.DataSource = New BusinessFacade.KeyType().KeyType_News_GetRecords()
        lstNews.DataTextField = "KeyType"
        lstNews.DataValueField = "KeyTypeID"
        lstNews.DataBind()
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        lblErr.Text = String.Empty

        ''******************************************''
        ''************** Get Keyword  **************''
        ''******************************************''

        Dim KeywordID As Integer
        Dim objKeywordID As New BusinessFacade.NewsKeyword()
        objKeywordID.NewsKeyword = txt_NewsKeyword.Text
        KeywordID = objKeywordID.IsExists_NewsKeyword(objKeywordID.NewsKeyword)

        If KeywordID <> "0" Then
            lblErr.Text = "This Keyword already Exists !!"
        Else
            SaveRecord()
        End If

    End Sub

    Private Sub SaveRecord()

        If txt_NewsKeyword.Text = "" Then
            lblErr.Text = "Please Insert Entertainment Keyword!!"
        ElseIf ddl_SubContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select SubContent Type!!"
        Else
            Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
            objNewsKeyword.NewsKeyword = txt_NewsKeyword.Text
            objNewsKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objNewsKeyword.TKeytype = lstNews.SelectedItem.ToString
            objNewsKeyword.SaveRecord()

            FillGrid()
            lblErr.Text = "Record has been Saved !!"
        End If
    End Sub

    Private Sub FillGrid()

        Dim dt As Data.DataTable
        Dim ObjGrid As New BusinessFacade.NewsKeyword()
        ObjGrid.NewsKeyword = txt_NewsKeyword.Text
        dt = ObjGrid.ShowNewsKeyword(ObjGrid.NewsKeyword)
        dg_NewsKeyword.DataSource() = dt
        dg_NewsKeyword.Columns(0).Visible = True
        dg_NewsKeyword.Columns(2).Visible = True
        dg_NewsKeyword.DataBind()
        dg_NewsKeyword.Columns(0).Visible = False
        dg_NewsKeyword.Columns(2).Visible = False

    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        lblErr.Text = String.Empty
    End Sub
End Class
