Imports System.Data

Partial Class LogSheet_LogSheet_Save
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim dt_Save As New DataTable
    Dim dt_Update As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                '    lbl_UserName.Text = Master.FooterText
                '    Dim Arr_UserID As Array = Split(lbl_UserName.Text, ",")
                '    lbl_UserName.Text = Arr_UserID(1)
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)
                End If

                BindCombo()
                If Not Request.QueryString.Get("ID") = "" Then
                    txtId.Text = Request.QueryString.Get("ID").ToString
                    Me.fillEditControl(txtId.Text)
                    Me.fillEditGrid(txtId.Text)
                    lnk_MoreRecords.Visible = True
                Else
                    dt_Save.Clear()
                    '    InitailizeGrid()
                End If

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub fillEditControl(ByVal Masterid As String)
        Dim qry As String = "select * from Dealing.TapeLogMaster where LogId = " & txtId.Text
        Dim da As New System.Data.SqlClient.SqlDataAdapter(qry, Con)
        Dim ds As New DataSet
        da.Fill(ds)
        If ds.Tables(0).Rows.Count > 0 Then
            txt_origin_dept.Text = CStr(ds.Tables(0).Rows(0)("LogOriginDept"))
            Me.ddl_OriginDept.SelectedIndex = Me.ddl_OriginDept.Items.IndexOf(Me.ddl_OriginDept.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("LogOriginDept"))))

            txt_Origin_Person.Text = CStr(ds.Tables(0).Rows(0)("LogOrginContactPerson"))
            Me.ddl_OriginPerson.SelectedIndex = Me.ddl_OriginPerson.Items.IndexOf(Me.ddl_OriginPerson.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("LogOrginContactPerson"))))

            txt_dest_Dept.Text = CStr(ds.Tables(0).Rows(0)("LogDestinationDept"))
            Me.ddl_DestDept.SelectedIndex = Me.ddl_DestDept.Items.IndexOf(Me.ddl_DestDept.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("LogDestinationDept"))))

            txt_Dest_Person.Text = CStr(ds.Tables(0).Rows(0)("LogDestContactPerson"))
            Me.ddl_DestPerson.SelectedIndex = Me.ddl_DestPerson.Items.IndexOf(Me.ddl_DestPerson.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("LogDestContactPerson"))))

            txt_Date1.Text = ds.Tables(0).Rows(0)("LogDate").ToString
            'Me.cldDate.SelectedDate = Convert.ToDateTime(gen.CheckDBNull(ds.Tables(0).Rows(0)("Date")))
            'txt_Date1.Text = txt_date.Text
            txt_date.Text = txt_Date1.Text
        End If
    End Sub

    Public Sub fillEditGrid(ByVal Masterid As String)

        Dim cmd_2 As New System.Data.SqlClient.SqlCommand
        cmd_2.Connection = Con
        cmd_2.CommandType = Data.CommandType.StoredProcedure
        cmd_2.CommandText = "Proc_GetLogSheetData"

        Dim T1 As Data.SqlClient.SqlParameter = cmd_2.Parameters.Add("@Masterid", Data.SqlDbType.Int)
        T1.Value = txtId.Text

        Dim da_2 As New System.Data.SqlClient.SqlDataAdapter
        Dim ds_2 As New DataSet
        da_2.SelectCommand = cmd_2
        da_2.Fill(ds_2)

        Me.DgItem.DataSource = ds_2
        Me.DgItem.DataBind()

    End Sub

    Private Sub BindCombo()


        ddl_DestDept.DataTextField = "DepartmentName"
        ddl_DestDept.DataValueField = "DepartmentID"
        ddl_DestDept.DataSource = New BusinessFacade.LostSheet().LostSheet_DestDept()
        ddl_DestDept.DataBind()
        ddl_DestDept.Items.Insert(0, "--Select--")
        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

        ddl_OriginDept.DataTextField = "DepartmentName"
        ddl_OriginDept.DataValueField = "DepartmentID"
        ddl_OriginDept.DataSource = New BusinessFacade.LostSheet().LostSheet_OriginDept()
        ddl_OriginDept.DataBind()
        ddl_OriginDept.Items.Insert(0, "--Select--")
        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

        ddl_DestPerson.DataTextField = "EmployeeName"
        ddl_DestPerson.DataValueField = "EmployeeID"
        ddl_DestPerson.DataSource = New BusinessFacade.LostSheet().LostSheet_DestEmployee()
        ddl_DestPerson.DataBind()
        ddl_DestPerson.Items.Insert(0, "--Select--")
        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

        ddl_OriginPerson.DataTextField = "EmployeeName"
        ddl_OriginPerson.DataValueField = "EmployeeID"
        ddl_OriginPerson.DataSource = New BusinessFacade.LostSheet().LostSheet_OriginEmployee()
        ddl_OriginPerson.DataBind()
        ddl_OriginPerson.Items.Insert(0, "--Select--")
        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

    End Sub

    Private Sub InitailizeGrid()

        Dim LogMasterId As DataColumn = New DataColumn("LogMasterId")
        LogMasterId.DataType = System.Type.GetType("System.Int32")
        dt_Save.Columns.Add(LogMasterId)

        Dim TapeKhi As DataColumn = New DataColumn("TapeKhi")
        TapeKhi.DataType = System.Type.GetType("System.String")
        dt_Save.Columns.Add(TapeKhi)

        Dim TapeDbx As DataColumn = New DataColumn("TapeDbx")
        TapeDbx.DataType = System.Type.GetType("System.String")
        dt_Save.Columns.Add(TapeDbx)

        Dim DBXBranded As DataColumn = New DataColumn("DBXBranded")
        DBXBranded.DataType = System.Type.GetType("System.String")
        dt_Save.Columns.Add(DBXBranded)

        Dim Description As DataColumn = New DataColumn("Description")
        Description.DataType = System.Type.GetType("System.String")
        dt_Save.Columns.Add(Description)

        Dim Duration As DataColumn = New DataColumn("Duration")
        Duration.DataType = System.Type.GetType("System.String")
        dt_Save.Columns.Add(Duration)

        Dim LogDetailId As DataColumn = New DataColumn("LogDetailId")
        LogDetailId.DataType = System.Type.GetType("System.Int32")
        dt_Save.Columns.Add(LogDetailId)

        Dim h As Integer
        '   For h = 0 To 24
        For h = 0 To txt_Rows.Text - 1

            Dim Row(h) As DataRow
            Row(h) = dt_Save.NewRow()
            Row(h).Item("LogDetailId") = System.DBNull.Value
            Row(h).Item("LogMasterId") = System.DBNull.Value
            Row(h).Item("TapeKhi") = System.DBNull.Value
            Row(h).Item("TapeDbx") = System.DBNull.Value
            Row(h).Item("DBXBranded") = System.DBNull.Value
            Row(h).Item("Description") = System.DBNull.Value
            Row(h).Item("Duration") = System.DBNull.Value
            dt_Save.Rows.Add(Row(h))
        Next

        DgItem.DataSource = dt_Save
        DgItem.DataBind()

        DgItem.Columns(5).Visible = False
        DgItem.Columns(6).Visible = False

       

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''

        If txtId.Text = "" Then
            If txt_date.Text = "" Then
                lblErr.Text = "Please Select Date!!"
            ElseIf ddl_OriginDept.SelectedIndex = 0 Then
                lblErr.Text = "Please Select Origin Department!!"
            ElseIf ddl_DestDept.SelectedIndex = 0 Then
                lblErr.Text = "Please Select Destination Department!!"
            ElseIf ddl_OriginPerson.SelectedIndex = 0 Then
                lblErr.Text = "Please Select Origin Person!!"
            ElseIf ddl_DestPerson.SelectedIndex = 0 Then
                lblErr.Text = "Please Select Destination Person!!"
            Else
                Dim cmd As New System.Data.SqlClient.SqlCommand
                cmd.CommandType = Data.CommandType.StoredProcedure
                cmd.CommandText = "TapeLogMaster_SaveRecord"
                cmd.Connection = Con

                Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDate", Data.SqlDbType.DateTime)
                p1.Value = txt_date.Text

                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogOriginDept", Data.SqlDbType.Int)
                p2.Value = ddl_OriginDept.SelectedValue

                Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDestinationDept", Data.SqlDbType.Int)
                p3.Value = ddl_DestDept.SelectedValue

                Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDestContactPerson", Data.SqlDbType.Int)
                p4.Value = ddl_DestPerson.SelectedValue

                Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogOrginContactPerson", Data.SqlDbType.Int)
                p5.Value = ddl_OriginPerson.SelectedValue

                Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogCreatedBy", Data.SqlDbType.Text)
                p6.Value = UserID

                If Con.State = ConnectionState.Closed Then
                    Con.Open()
                End If
                cmd.ExecuteNonQuery()
                Con.Close()

                StrCommand = "select Max(LogID) from Dealing.TapeLogMaster"
                Dim Cmd_Max = New SqlClient.SqlCommand(StrCommand)
                Dim LogID As Integer
                If Con.State = ConnectionState.Closed Then
                    Con.Open()
                End If
                Cmd_Max.Connection = Con
                LogID = Cmd_Max.ExecuteScalar().ToString
                Con.Close()

                Dim U As Integer
                For U = 0 To DgItem.Rows.Count - 1

                    Dim MyTextBox As TextBox = CType(DgItem.Rows(U).Cells(0).Controls(1), TextBox)

                    If MyTextBox.Text <> "" Then

                        Dim qty1 As String
                        Dim txt1 As TextBox
                        txt1 = CType(DgItem.Rows(U).Cells(0).FindControl("txt_Karachi"), TextBox)
                        qty1 = txt1.Text

                        Dim qty2 As String
                        Dim txt2 As TextBox
                        txt2 = CType(DgItem.Rows(U).Cells(1).FindControl("txt_DubaiUnBranded"), TextBox)
                        qty2 = txt2.Text

                        Dim qty3 As String
                        Dim txt3 As TextBox
                        txt3 = CType(DgItem.Rows(U).Cells(2).FindControl("txt_DubaiBranded"), TextBox)
                        qty3 = txt3.Text

                        Dim qty4 As String
                        Dim txt4 As TextBox
                        txt4 = CType(DgItem.Rows(U).Cells(3).FindControl("txt_Description"), TextBox)
                        qty4 = txt4.Text

                        Dim qty5 As String
                        Dim txt5 As TextBox
                        txt5 = CType(DgItem.Rows(U).Cells(4).FindControl("txt_Duration"), TextBox)
                        qty5 = txt5.Text

                        Dim cmd_Detail As New System.Data.SqlClient.SqlCommand
                        cmd_Detail.CommandType = Data.CommandType.StoredProcedure
                        cmd_Detail.CommandText = "TapeLogDetail_SaveRecord"
                        cmd_Detail.Connection = Con

                        Dim R1 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogMasterId", Data.SqlDbType.Int)
                        R1.Value = LogID

                        Dim R2 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
                        R2.Value = qty1

                        Dim R3 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
                        R3.Value = qty2

                        Dim R4 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@DBXBranded", Data.SqlDbType.Text)
                        R4.Value = qty3

                        Dim R5 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Description", Data.SqlDbType.Text)
                        R5.Value = qty4

                        Dim R6 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Duration", Data.SqlDbType.Text)
                        R6.Value = qty5

                        If Con.State = ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd_Detail.ExecuteNonQuery()
                        Con.Close()
                    End If
                Next

                ''''''''''''''' Add More Records '''''''''''''''''''''''''
                Dim C As Integer
                For C = 0 To dg_AddMore.Rows.Count - 1

                    Dim MyTextBox As TextBox = CType(dg_AddMore.Rows(C).Cells(0).Controls(1), TextBox)

                    If MyTextBox.Text <> "" Then

                        Dim qty1 As String
                        Dim txt1 As TextBox
                        txt1 = CType(dg_AddMore.Rows(C).Cells(0).FindControl("txt_Karachi"), TextBox)
                        qty1 = txt1.Text

                        Dim qty2 As String
                        Dim txt2 As TextBox
                        txt2 = CType(dg_AddMore.Rows(C).Cells(1).FindControl("txt_DubaiUnBranded"), TextBox)
                        qty2 = txt2.Text

                        Dim qty3 As String
                        Dim txt3 As TextBox
                        txt3 = CType(dg_AddMore.Rows(C).Cells(2).FindControl("txt_DubaiBranded"), TextBox)
                        qty3 = txt3.Text

                        Dim qty4 As String
                        Dim txt4 As TextBox
                        txt4 = CType(dg_AddMore.Rows(C).Cells(3).FindControl("txt_Description"), TextBox)
                        qty4 = txt4.Text

                        Dim qty5 As String
                        Dim txt5 As TextBox
                        txt5 = CType(dg_AddMore.Rows(C).Cells(4).FindControl("txt_Duration"), TextBox)
                        qty5 = txt5.Text

                        'Dim G As Integer
                        'G = DgItem.Rows(C).Cells(6).Text

                        Dim cmd_Detail As New System.Data.SqlClient.SqlCommand
                        cmd_Detail.CommandType = Data.CommandType.StoredProcedure
                        cmd_Detail.CommandText = "TapeLogDetail_SaveRecord"
                        cmd_Detail.Connection = Con

                        'Dim R7 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogDetailId", Data.SqlDbType.Int)
                        'R7.Value = G

                        Dim R1 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogMasterId", Data.SqlDbType.Int)
                        R1.Value = LogID

                        Dim R2 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
                        R2.Value = qty1

                        Dim R3 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
                        R3.Value = qty2

                        Dim R4 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@DBXBranded", Data.SqlDbType.Text)
                        R4.Value = qty3

                        Dim R5 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Description", Data.SqlDbType.Text)
                        R5.Value = qty4

                        Dim R6 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Duration", Data.SqlDbType.Text)
                        R6.Value = qty5

                        If Con.State = ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd_Detail.ExecuteNonQuery()
                        Con.Close()
                    End If
                Next

                lblErr.Text = "Record has been Saved!!"
                dt_Save.Clear()
                dt_Update.Clear()
                dg_AddMore.DataSource = dt_Update
                dg_AddMore.DataBind()
                txt_Rows.Text = "0"
                InitailizeGrid()

            End If

        Else

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "TapeLogMaster_SaveRecord"
            cmd.Connection = Con

            Dim p7 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogId", Data.SqlDbType.Int)
            p7.Value = txtId.Text

            Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDate", Data.SqlDbType.DateTime)
            p1.Value = txt_date.Text

            Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogOriginDept", Data.SqlDbType.Int)
            p2.Value = ddl_OriginDept.SelectedValue

            Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDestinationDept", Data.SqlDbType.Int)
            p3.Value = ddl_DestDept.SelectedValue

            Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogDestContactPerson", Data.SqlDbType.Int)
            p4.Value = ddl_DestPerson.SelectedValue

            Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogOrginContactPerson", Data.SqlDbType.Int)
            p5.Value = ddl_OriginPerson.SelectedValue

            Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@LogCreatedBy", Data.SqlDbType.Text)
            p6.Value = 3

            If Con.State = ConnectionState.Closed Then
                Con.Open()
            End If
            cmd.ExecuteNonQuery()
            Con.Close()

            ''''***************************************************************''''
            Dim U As Integer
            For U = 0 To DgItem.Rows.Count - 1

                Dim MyTextBox As TextBox = CType(DgItem.Rows(U).Cells(0).Controls(1), TextBox)

                If MyTextBox.Text <> "" Then

                    Dim qty1 As String
                    Dim txt1 As TextBox
                    txt1 = CType(DgItem.Rows(U).Cells(0).FindControl("txt_Karachi"), TextBox)
                    qty1 = txt1.Text

                    Dim qty2 As String
                    Dim txt2 As TextBox
                    txt2 = CType(DgItem.Rows(U).Cells(1).FindControl("txt_DubaiUnBranded"), TextBox)
                    qty2 = txt2.Text

                    Dim qty3 As String
                    Dim txt3 As TextBox
                    txt3 = CType(DgItem.Rows(U).Cells(2).FindControl("txt_DubaiBranded"), TextBox)
                    qty3 = txt3.Text

                    Dim qty4 As String
                    Dim txt4 As TextBox
                    txt4 = CType(DgItem.Rows(U).Cells(3).FindControl("txt_Description"), TextBox)
                    qty4 = txt4.Text

                    Dim qty5 As String
                    Dim txt5 As TextBox
                    txt5 = CType(DgItem.Rows(U).Cells(4).FindControl("txt_Duration"), TextBox)
                    qty5 = txt5.Text

                    Dim G As Integer
                    G = DgItem.Rows(U).Cells(6).Text

                    Dim cmd_Detail As New System.Data.SqlClient.SqlCommand
                    cmd_Detail.CommandType = Data.CommandType.StoredProcedure
                    cmd_Detail.CommandText = "TapeLogDetail_SaveRecord"
                    cmd_Detail.Connection = Con

                    Dim R7 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogDetailId", Data.SqlDbType.Int)
                    R7.Value = G

                    Dim R1 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogMasterId", Data.SqlDbType.Int)
                    R1.Value = txtId.Text

                    Dim R2 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
                    R2.Value = qty1

                    Dim R3 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
                    R3.Value = qty2

                    Dim R4 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@DBXBranded", Data.SqlDbType.Text)
                    R4.Value = qty3

                    Dim R5 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Description", Data.SqlDbType.Text)
                    R5.Value = qty4

                    Dim R6 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Duration", Data.SqlDbType.Text)
                    R6.Value = qty5

                    If Con.State = ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd_Detail.ExecuteNonQuery()
                    Con.Close()
                End If
            Next

            ''''''''''''''' Add More Records '''''''''''''''''''''''''
            Dim C As Integer
            For C = 0 To dg_AddMore.Rows.Count - 1

                Dim MyTextBox As TextBox = CType(dg_AddMore.Rows(C).Cells(0).Controls(1), TextBox)

                If MyTextBox.Text <> "" Then

                    Dim qty1 As String
                    Dim txt1 As TextBox
                    txt1 = CType(dg_AddMore.Rows(C).Cells(0).FindControl("txt_Karachi"), TextBox)
                    qty1 = txt1.Text

                    Dim qty2 As String
                    Dim txt2 As TextBox
                    txt2 = CType(dg_AddMore.Rows(C).Cells(1).FindControl("txt_DubaiUnBranded"), TextBox)
                    qty2 = txt2.Text

                    Dim qty3 As String
                    Dim txt3 As TextBox
                    txt3 = CType(dg_AddMore.Rows(C).Cells(2).FindControl("txt_DubaiBranded"), TextBox)
                    qty3 = txt3.Text

                    Dim qty4 As String
                    Dim txt4 As TextBox
                    txt4 = CType(dg_AddMore.Rows(C).Cells(3).FindControl("txt_Description"), TextBox)
                    qty4 = txt4.Text

                    Dim qty5 As String
                    Dim txt5 As TextBox
                    txt5 = CType(dg_AddMore.Rows(C).Cells(4).FindControl("txt_Duration"), TextBox)
                    qty5 = txt5.Text

                    'Dim G As Integer
                    'G = DgItem.Rows(C).Cells(6).Text

                    Dim cmd_Detail As New System.Data.SqlClient.SqlCommand
                    cmd_Detail.CommandType = Data.CommandType.StoredProcedure
                    cmd_Detail.CommandText = "TapeLogDetail_SaveRecord"
                    cmd_Detail.Connection = Con

                    'Dim R7 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogDetailId", Data.SqlDbType.Int)
                    'R7.Value = G

                    Dim R1 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@LogMasterId", Data.SqlDbType.Int)
                    R1.Value = txtId.Text

                    Dim R2 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeKhi", Data.SqlDbType.Text)
                    R2.Value = qty1

                    Dim R3 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@TapeDbx", Data.SqlDbType.Text)
                    R3.Value = qty2

                    Dim R4 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@DBXBranded", Data.SqlDbType.Text)
                    R4.Value = qty3

                    Dim R5 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Description", Data.SqlDbType.Text)
                    R5.Value = qty4

                    Dim R6 As Data.SqlClient.SqlParameter = cmd_Detail.Parameters.Add("@Duration", Data.SqlDbType.Text)
                    R6.Value = qty5

                    If Con.State = ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd_Detail.ExecuteNonQuery()
                    Con.Close()
                End If
            Next

            '''''''''''''''''''''''''''''''''''''''
            lblErr.Text = "Record has been Saved!!"
            dt_Save.Clear()
            dt_Update.Clear()
          
            dg_AddMore.DataSource = dt_Update
            dg_AddMore.DataBind()
            lnk_MoreRecords.Visible = False
            InitailizeGrid()
        End If
        clrscr()

    End Sub

    Protected Sub ddl_OriginDept_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_OriginDept.SelectedIndexChanged
        lblErr.Text = String.Empty
    End Sub

    Protected Sub bttnShowRows_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnShowRows.Click
        InitailizeGrid()
    End Sub

    Protected Sub lnkMaterialSheet_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("LogSheet_Search.aspx")
    End Sub

    Protected Sub LnkHomepage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub lnk_MoreRecords_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnk_MoreRecords.Click

        dg_AddMore.Visible = True
        Update_Grid()
    End Sub

    Private Sub Update_Grid()
        dt_Update.Clear()
        dt_Update.Columns.Clear()

        Dim LogMasterId As DataColumn = New DataColumn("LogMasterId")
        LogMasterId.DataType = System.Type.GetType("System.Int32")
        dt_Update.Columns.Add(LogMasterId)

        Dim TapeKhi As DataColumn = New DataColumn("TapeKhi")
        TapeKhi.DataType = System.Type.GetType("System.String")
        dt_Update.Columns.Add(TapeKhi)

        Dim TapeDbx As DataColumn = New DataColumn("TapeDbx")
        TapeDbx.DataType = System.Type.GetType("System.String")
        dt_Update.Columns.Add(TapeDbx)

        Dim DBXBranded As DataColumn = New DataColumn("DBXBranded")
        DBXBranded.DataType = System.Type.GetType("System.String")
        dt_Update.Columns.Add(DBXBranded)

        Dim Description As DataColumn = New DataColumn("Description")
        Description.DataType = System.Type.GetType("System.String")
        dt_Update.Columns.Add(Description)

        Dim Duration As DataColumn = New DataColumn("Duration")
        Duration.DataType = System.Type.GetType("System.String")
        dt_Update.Columns.Add(Duration)

        Dim LogDetailId As DataColumn = New DataColumn("LogDetailId")
        LogDetailId.DataType = System.Type.GetType("System.Int32")
        dt_Update.Columns.Add(LogDetailId)

        Dim h As Integer
        For h = 0 To 5
            Dim Row(h) As DataRow
            Row(h) = dt_Update.NewRow()
            Row(h).Item("LogDetailId") = System.DBNull.Value
            Row(h).Item("LogMasterId") = System.DBNull.Value
            Row(h).Item("TapeKhi") = System.DBNull.Value
            Row(h).Item("TapeDbx") = System.DBNull.Value
            Row(h).Item("DBXBranded") = System.DBNull.Value
            Row(h).Item("Description") = System.DBNull.Value
            Row(h).Item("Duration") = System.DBNull.Value
            dt_Update.Rows.Add(Row(h))
        Next

        dg_AddMore.DataSource = dt_Update
        dg_AddMore.DataBind()

        dg_AddMore.Columns(5).Visible = False
        dg_AddMore.Columns(6).Visible = False



    End Sub

    Private Sub clrscr()
        txt_date.Text = String.Empty
        ddl_OriginDept.SelectedIndex = 0
        ddl_DestDept.SelectedIndex = 0
        ddl_OriginPerson.SelectedIndex = 0
        ddl_DestPerson.SelectedIndex = 0
        txt_Rows.Text = String.Empty
    End Sub

End Class
