Imports System.Data

Partial Class TapeContent_FrmArchiveEntry_EntryFormNew
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim Dt_SaveKeyword As New DataTable
    Dim Dt_SaveKeyword_2 As New DataTable
    Dim dt_ProgramInfo As New DataTable


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try

            If Not Page.IsPostBack = True Then
                txt_UrduScript.Attributes.Add("onkeypress", "search()")
                txt_UrduScript.Attributes.Add("Dir", "Rtl")

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else

                End If


                BindCombo()
                txtEntryDate.Text = Date.Now.ToString("dd-MMM-yyyy")


            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub LnkShowKeyBoard_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkShowKeyBoard.Click

        If LnkShowKeyBoard.Text = "Show Keyboard" Then
            ImgKeyBoard.Visible = True
            LnkShowKeyBoard.Text = "Hide Keyboard"
        Else
            ImgKeyBoard.Visible = False
            LnkShowKeyBoard.Text = "Show Keyboard"
        End If

    End Sub

    Private Sub BindCombo()
        Try
            ddl_Channel.DataTextField = "ContentTypeName"
            ddl_Channel.DataValueField = "ContentTypeID"
            ddl_Channel.DataSource = New BusinessFacade.ContentType().GetRecords()
            ddl_Channel.DataBind()
            ddl_Channel.SelectedIndex = "1"

            ddl_ProgramMaster.DataTextField = "ProgramMasterName"
            ddl_ProgramMaster.DataValueField = "ProgramMasterID"
            ddl_ProgramMaster.DataSource = New BusinessFacade.ProgramMaster().GetRecords()
            ddl_ProgramMaster.DataBind()

            Fill_Guest()
            Fill_Host()
            Fill_Topics()

            lstKeyword.DataTextField = "NewsKeyword"
            lstKeyword.DataValueField = "NewsKeywordID"
            lstKeyword.DataSource = New BusinessFacade.NewsKeyword().GetRecords()
            lstKeyword.DataBind()

            Fill_ProgramInfo_Table(1)

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Private Sub Fill_Guest()

        ddl_Guest1.DataTextField = "EntertainmentKeyword"
        ddl_Guest1.DataValueField = "EntertainmentKeywordID"
        ddl_Guest1.DataSource = New BusinessFacade.Guest().GetRecords()
        ddl_Guest1.DataBind()

        ddl_Guest2.DataTextField = "EntertainmentKeyword"
        ddl_Guest2.DataValueField = "EntertainmentKeywordID"
        ddl_Guest2.DataSource = New BusinessFacade.Guest().GetRecords()
        ddl_Guest2.DataBind()

        ddl_Guest3.DataTextField = "EntertainmentKeyword"
        ddl_Guest3.DataValueField = "EntertainmentKeywordID"
        ddl_Guest3.DataSource = New BusinessFacade.Guest().GetRecords()
        ddl_Guest3.DataBind()

        ddl_Guest3.DataTextField = "EntertainmentKeyword"
        ddl_Guest3.DataValueField = "EntertainmentKeywordID"
        ddl_Guest3.DataSource = New BusinessFacade.Guest().GetRecords()
        ddl_Guest3.DataBind()

        ddl_Guest4.DataTextField = "EntertainmentKeyword"
        ddl_Guest4.DataValueField = "EntertainmentKeywordID"
        ddl_Guest4.DataSource = New BusinessFacade.Guest().GetRecords()
        ddl_Guest4.DataBind()

    End Sub

    Private Sub Fill_Host()

        ddl_Host1.DataTextField = "EntertainmentKeyword"
        ddl_Host1.DataValueField = "EntertainmentKeywordID"
        ddl_Host1.DataSource = New BusinessFacade.Host().GetRecords()
        ddl_Host1.DataBind()

        ddl_Host2.DataTextField = "EntertainmentKeyword"
        ddl_Host2.DataValueField = "EntertainmentKeywordID"
        ddl_Host2.DataSource = New BusinessFacade.Host().GetRecords()
        ddl_Host2.DataBind()

        ddl_Host3.DataTextField = "EntertainmentKeyword"
        ddl_Host3.DataValueField = "EntertainmentKeywordID"
        ddl_Host3.DataSource = New BusinessFacade.Host().GetRecords()
        ddl_Host3.DataBind()

        ddl_Host3.DataTextField = "EntertainmentKeyword"
        ddl_Host3.DataValueField = "EntertainmentKeywordID"
        ddl_Host3.DataSource = New BusinessFacade.Host().GetRecords()
        ddl_Host3.DataBind()

        ddl_Host4.DataTextField = "EntertainmentKeyword"
        ddl_Host4.DataValueField = "EntertainmentKeywordID"
        ddl_Host4.DataSource = New BusinessFacade.Host().GetRecords()
        ddl_Host4.DataBind()

    End Sub

    Private Sub Fill_Topics()

        ddl_Topic1.DataTextField = "EntertainmentKeyword"
        ddl_Topic1.DataValueField = "EntertainmentKeywordID"
        ddl_Topic1.DataSource = New BusinessFacade.Topic().GetRecords()
        ddl_Topic1.DataBind()

        ddl_Topic2.DataTextField = "EntertainmentKeyword"
        ddl_Topic2.DataValueField = "EntertainmentKeywordID"
        ddl_Topic2.DataSource = New BusinessFacade.Topic().GetRecords()
        ddl_Topic2.DataBind()

        ddl_Topic3.DataTextField = "EntertainmentKeyword"
        ddl_Topic3.DataValueField = "EntertainmentKeywordID"
        ddl_Topic3.DataSource = New BusinessFacade.Topic().GetRecords()
        ddl_Topic3.DataBind()

        ddl_Topic3.DataTextField = "EntertainmentKeyword"
        ddl_Topic3.DataValueField = "EntertainmentKeywordID"
        ddl_Topic3.DataSource = New BusinessFacade.Topic().GetRecords()
        ddl_Topic3.DataBind()

        ddl_Topic4.DataTextField = "EntertainmentKeyword"
        ddl_Topic4.DataValueField = "EntertainmentKeywordID"
        ddl_Topic4.DataSource = New BusinessFacade.Topic().GetRecords()
        ddl_Topic4.DataBind()

    End Sub


    Private Sub Fill_ProgramInfo_Table(ByVal TapeContentID As Integer)

        Dim Qry_progInfo As String = "SELECT SOTID,SNo,SotText,StartTime,EndTime,Keyword1ID,Keyword1,SubContentTypeKW1,Keyword2ID,Keyword2,SubContentTypeKW2,Keyword3ID," & _
                                     " Keyword3,SubContentTypeKW3,Keyword4ID,Keyword4,SubContentTypeKW4,Keyword5ID,Keyword5,SubContentTypeKW5,todaysdate," & _
                                     " TranscriberName,ClipPath FROM ArchiveDAMSMSR_SOTDetails "

        Dim da_progInfo As New System.Data.SqlClient.SqlDataAdapter(Qry_progInfo, Con)
        Dim ds_prognfo As New DataSet
        da_progInfo.Fill(ds_prognfo, "Table_ProgInfo")
        dt_ProgramInfo = ds_prognfo.Tables("Table_ProgInfo")

        '        ViewState("ProgramInfo_Table") = dt_ProgramInfo

        BindGrid_ProgramInfo()

    End Sub

    Private Sub BindGrid_ProgramInfo()

        dg_ProgramInfo.DataSource = dt_ProgramInfo
        dg_ProgramInfo.DataBind()
        dg_ProgramInfo.Visible = True

    End Sub

    Private Sub FillStartTime()

        Dim FillTime As String = dg_ProgramInfo.Rows(dg_ProgramInfo.Rows.Count - 1).Cells(8).Text


        Dim Start As Array = FillTime.Split(":")

    End Sub

    Private Sub Clrscr_ProgramInfo()
        txt_UrduScript.Text = String.Empty
    End Sub

End Class


