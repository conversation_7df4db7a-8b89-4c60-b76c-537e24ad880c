<%@ Page Language="VB" AutoEventWireup="false" CodeFile="LocalReminers_Testing.aspx.vb" Inherits="ReminderService_LocalReminers_Testing" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:TextBox ID="txtFrom" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="16px" Style="z-index: 113; left: 88px; position: absolute;
            top: 80px" Width="537px"></asp:TextBox>
        <asp:TextBox ID="txtDepartmentLogo" runat="server" Style="z-index: 118; left: 376px;
            position: absolute; top: 624px" Visible="False" Width="40px"></asp:TextBox>
        <asp:TextBox ID="txtTestBody" runat="server" Height="24px" Style="z-index: 115; left: 248px;
            position: absolute; top: 624px" TextMode="MultiLine" Visible="False" Width="48px"></asp:TextBox>
        <asp:Label ID="lbMessage" runat="server" BackColor="#C0FFC0" Font-Bold="True" Font-Names="Latha"
            Font-Size="20pt" Font-Strikeout="False" ForeColor="DarkGreen" Style="z-index: 114;
            left: 32px; position: absolute; top: 24px" Visible="False">.. Your Email has been sent to the Desired Reciepants ..</asp:Label>
        <asp:Button ID="btnClose" runat="server" Style="z-index: 112; left: 688px; position: absolute;
            top: 32px" Text="Close Window" Visible="False" />
        <asp:TextBox ID="txtBody" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="296px" Style="z-index: 110; left: 32px; position: absolute;
            top: 312px" TextMode="MultiLine" Width="1000px"></asp:TextBox>
        <asp:Label ID="Label5" runat="server" Font-Bold="True" Style="z-index: 109; left: 32px;
            position: absolute; top: 264px">Subject</asp:Label>
        <asp:TextBox ID="txtSubject" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="16px" Style="z-index: 108; left: 88px; position: absolute;
            top: 264px" Width="536px"></asp:TextBox>
        <asp:Label ID="Label4" runat="server" Font-Bold="True" Style="z-index: 107; left: 32px;
            position: absolute; top: 168px">Cc</asp:Label>
        <asp:Label ID="Label3" runat="server" Font-Bold="True" Style="z-index: 105; left: 32px;
            position: absolute; top: 128px">To</asp:Label>
        &nbsp;&nbsp;
        <asp:TextBox ID="txtBCC" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="16px" Style="z-index: 103; left: 88px; position: absolute;
            top: 216px" Width="537px"></asp:TextBox>
        <asp:TextBox ID="txtCC" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="16px" Style="z-index: 101; left: 88px; position: absolute;
            top: 168px" Width="537px"></asp:TextBox>
        <asp:Label ID="Label2" runat="server" Font-Bold="True" Style="z-index: 106; left: 32px;
            position: absolute; top: 216px">Bcc</asp:Label>
        <asp:TextBox ID="txtTo" runat="server" BackColor="WhiteSmoke" Font-Names="Verdana"
            Font-Size="X-Small" Height="16px" Style="z-index: 102; left: 88px; position: absolute;
            top: 120px" Width="537px"></asp:TextBox>
        <asp:Label ID="Label1" runat="server" Font-Bold="True" Style="z-index: 104; left: 32px;
            position: absolute; top: 80px">From</asp:Label>
        <asp:Button ID="btn" runat="server" BackColor="#FF8080" BorderColor="DarkGray" Font-Bold="True"
            ForeColor="Black" Style="z-index: 111; left: 40px; position: absolute; top: 624px"
            Text="Send Email" Width="144px" />
        <asp:TextBox ID="txtTapeDetail" runat="server" Style="z-index: 116; left: 432px;
            position: absolute; top: 624px" Visible="False" Width="48px"></asp:TextBox>
        <asp:TextBox ID="txtExtensions" runat="server" Style="z-index: 117; left: 320px;
            position: absolute; top: 624px" Visible="False" Width="40px"></asp:TextBox>
    
    </div>
    </form>
</body>
</html>
