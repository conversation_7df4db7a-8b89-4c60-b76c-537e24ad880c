Imports System.Xml

Partial Class ExcelUpload_TestXML
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim xmldoc As New XmlDocument()
        'xmldoc.Load("C:\DAMS.xml")
        'Dim rootnode As XmlNode = xmldoc.DocumentElement

        'checkNode(rootnode)


        Dim filename As String = "C:\DAMS.xml"
        'Dim tr As New XmlTextReader(filename)
        'While tr.Read()
        '    If tr.NodeType = XmlNodeType.Text Then
        '        Response.Write("Name is: " + tr.Name + "<br>")

        '        Response.Write("Value is: " + tr.Value + "<br>")
        '    End If
        'End While

        Dim reader As New XmlTextReader(filename)
        While reader.Read()
            Select Case reader.NodeType
                Case XmlNodeType.Element
                    ' The node is an Element
                    Response.Write("<br> Name= " + reader.Name)
                    Exit Select

                Case XmlNodeType.Text
                    Response.Write(" and Value is: " + reader.Value)
                    Exit Select


            End Select
        End While

    End Sub

    Private Sub checkNode(ByVal node As XmlNode)
        'get the NodeType ,node name and its value
        If node.NodeType = XmlNodeType.Element Then

            If (node.Name = "p") Then
                Response.Write(node.Value.ToString())
            Else
                'checkNode(node.Name)
            End If
        End If
    End Sub

End Class
