<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ChangePassword.aspx.vb" Inherits="ChangePassword_ChangePassword" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>DAMS &gt; Change Password</title>
     <link href="main.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <div>
        &nbsp;</div>
        <table>
            <tr>
                <td style="width: 17px">
                </td>
                <td style="width: 100px">
        <table>
            <tr>
                <td class="heading1" colspan="3" style="height: 20px; text-decoration: underline">
                    Change Password</td>
            </tr>
            <tr>
                <td style="width: 96px; height: 9px">
                </td>
                <td style="width: 100px; height: 9px">
                </td>
                <td style="width: 100px; height: 9px">
                </td>
            </tr>
            <tr>
                <td class="mytext" style="width: 96px">
                    Old Password</td>
                <td style="width: 100px">
                    <asp:TextBox ID="txtOldPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
                <td style="width: 100px">
                </td>
            </tr>
            <tr>
                <td class="mytext" style="width: 96px; height: 21px">
                    New Password</td>
                <td style="width: 100px; height: 21px">
                    <asp:TextBox ID="txtNewPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
                <td style="width: 100px; height: 21px">
                </td>
            </tr>
            <tr>
                <td class="mytext" style="width: 96px; height: 21px">
                    Confirm Password</td>
                <td style="width: 100px; height: 21px">
                    <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
                <td style="width: 100px; height: 21px">
                </td>
            </tr>
            <tr>
                <td style="width: 96px; height: 21px">
                </td>
                <td style="width: 100px; height: 21px">
                </td>
                <td style="width: 100px; height: 21px">
                </td>
            </tr>
            <tr>
                <td class="bottomMain" colspan="3" style="height: 29px">
                    &nbsp;
                    <asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Save >>"
                        Width="80px" />&nbsp;
                    <asp:Button ID="Button1" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Clear >>"
                        Width="88px" /></td>
            </tr>
            <tr>
                <td colspan="3" style="height: 22px">
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="416px"></asp:Label></td>
            </tr>
        </table>
                </td>
                <td style="width: 100px">
                </td>
            </tr>
            <tr>
                <td style="width: 17px">
                </td>
                <td style="width: 100px">
        <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
                <td style="width: 100px">
                </td>
            </tr>
            <tr>
                <td style="width: 17px">
                </td>
                <td style="width: 100px">
                </td>
                <td style="width: 100px">
                </td>
            </tr>
        </table>
    </form>
</body>
</html>
