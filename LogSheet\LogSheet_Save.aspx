<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="LogSheet_Save.aspx.vb" Inherits="LogSheet_LogSheet_Save" title="Home > Material Deposit Sheet > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table style="width: 100%">
        <tr>
            <td class="labelheading" style="width: 100%; height: 21px; text-decoration: underline">
                <asp:LinkButton ID="LnkHomepage" runat="server" CssClass="labelheading" OnClick="LnkHomepage_Click">Home</asp:LinkButton>
                &gt;
                <asp:LinkButton ID="lnkMaterialSheet" runat="server" CssClass="labelheading" OnClick="lnkMaterialSheet_Click">Material Deposit Sheet</asp:LinkButton>
                &gt; Add New
            </td>
        </tr>
        <tr>
            <td style="width: 100px; height: 68px;">
                <table class="mytext" style="width: 712px">
                    <tr class="mytext">
                        <td style="width: 191px; height: 15px">
                            Date</td>
                        <td style="width: 191px; height: 15px">
                            Origin Department</td>
                        <td style="width: 230px; height: 15px">
                            Destination Department</td>
                        <td style="width: 229px; height: 15px">
                            Origin Contact Person</td>
                        <td style="width: 230px; height: 15px">
                            Destination Contact Person</td>
                        <td style="width: 425px; height: 15px">
                            No.of Rows</td>
                        <td style="width: 84px; height: 15px">
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 191px; height: 22px">
                            <asp:TextBox ID="txt_date" runat="server" CssClass="mytext" Width="112px"></asp:TextBox></td>
                        <td style="width: 191px; height: 22px">
                            <asp:DropDownList ID="ddl_OriginDept" runat="server" CssClass="mytext" Width="168px">
                            </asp:DropDownList></td>
                        <td style="width: 230px; height: 22px">
                            <asp:DropDownList ID="ddl_DestDept" runat="server" CssClass="mytext" Width="168px">
                            </asp:DropDownList></td>
                        <td style="width: 229px; height: 22px">
                            <asp:DropDownList ID="ddl_OriginPerson" runat="server" CssClass="mytext" Width="168px">
                            </asp:DropDownList></td>
                        <td style="width: 230px; height: 22px">
                            <asp:DropDownList ID="ddl_DestPerson" runat="server" CssClass="mytext" Width="168px">
                            </asp:DropDownList></td>
                        <td style="width: 425px; height: 22px">
                            <asp:TextBox ID="txt_Rows" runat="server" CssClass="mytext" Height="16px" Width="56px">0</asp:TextBox></td>
                        <td style="width: 84px; height: 22px">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="520px" CssClass="mytext"></asp:Label></td>
        </tr>
        <tr class="mytext">
            <td class="bottomMain" style="width: 100%">
                &nbsp;<asp:Button ID="bttnShowRows" runat="server" CssClass="buttonA" Text="Show Rows"
                    Width="80px" />
                <asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Text="Save Record" Width="96px" />&nbsp;</td>
        </tr>
        <tr>
            <td style="width: 500px; height: 318px" valign="top">
                <asp:GridView ID="DgItem" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                    Width="880px">
                    <Columns>
                        <asp:TemplateField HeaderText="Karachi UnBranded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Karachi" runat="server" Text='<% # Eval("TapeKhi") %>' Width="112px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Dubai UnBranded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_DubaiUnBranded" runat="server" Text='<% # Eval("TapeDbx") %>'
                                    Width="112px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Dubai Branded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_DubaiBranded" runat="server" Text='<% # Eval("DBXBranded") %>'
                                    Width="104px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Description">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Description" runat="server" Height="32px" Text='<% # Eval("Description") %>'
                                    TextMode="MultiLine" Width="224px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Duration">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Duration" runat="server" Text='<% # Eval("Duration")%>' Width="56px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="LogMasterId">
                            <ItemStyle ForeColor="#F2F5FE" />
                            <HeaderStyle Width="1px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="LogDetailId">
                            <ItemStyle ForeColor="#F2F5FE" />
                            <HeaderStyle Width="1px" />
                        </asp:BoundField>
                    </Columns>
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
                <br />
                <asp:LinkButton ID="lnk_MoreRecords" runat="server" CssClass="mytext" OnClick="lnk_MoreRecords_Click">Add More Records</asp:LinkButton><br />
                <asp:GridView ID="dg_AddMore" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                    Width="880px">
                    <Columns>
                        <asp:TemplateField HeaderText="Karachi UnBranded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Karachi" runat="server" Text='<% # Eval("TapeKhi") %>' Width="112px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Dubai UnBranded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_DubaiUnBranded" runat="server" Text='<% # Eval("TapeDbx") %>'
                                    Width="112px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Dubai Branded">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_DubaiBranded" runat="server" Text='<% # Eval("DBXBranded") %>'
                                    Width="104px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Description">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Description" runat="server" Height="32px" Text='<% # Eval("Description") %>'
                                    TextMode="MultiLine" Width="224px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Duration">
                            <ItemTemplate>
                                <asp:TextBox ID="txt_Duration" runat="server" Text='<% # Eval("Duration")%>' Width="56px"></asp:TextBox>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="LogMasterId">
                            <ItemStyle ForeColor="#F2F5FE" />
                            <HeaderStyle Width="1px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="LogDetailId">
                            <ItemStyle ForeColor="#F2F5FE" />
                            <HeaderStyle Width="1px" />
                        </asp:BoundField>
                    </Columns>
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
        <tr>
            <td style="width: 100%; height: 26px">
                &nbsp;
                <asp:TextBox ID="txtId" runat="server" Visible="False" Width="80px"></asp:TextBox>
                <asp:TextBox ID="txt_origin_dept" runat="server" Visible="False" Width="64px"></asp:TextBox>
                <asp:TextBox ID="txt_Origin_Person" runat="server" Visible="False" Width="64px"></asp:TextBox>
                <asp:TextBox ID="txt_dest_Dept" runat="server" Visible="False" Width="80px"></asp:TextBox>
                <asp:TextBox ID="txt_Dest_Person" runat="server" Visible="False" Width="72px"></asp:TextBox>
                <asp:TextBox ID="txt_Date1" runat="server" Visible="False" Width="72px"></asp:TextBox>
                <asp:TextBox ID="txt_DetailID" runat="server" Visible="False"></asp:TextBox></td>
        </tr>
        <tr>
            <td>
                <cc1:CalendarExtender ID="CalendarExtender1" runat="server" Format="d-MMMM-yyyy"
                    TargetControlID="txt_date" CssClass="MyCalendar">
                </cc1:CalendarExtender>
                <cc1:FilteredTextBoxExtender ID="FilteredTextBoxExtender1" runat="server" FilterType="Numbers"
                    InvalidChars="1234567890" TargetControlID="txt_Rows">
                </cc1:FilteredTextBoxExtender>
                <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                    PromptText="" TargetControlID="ddl_OriginDept">
                </cc1:ListSearchExtender>
                <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                    PromptText="" TargetControlID="ddl_DestDept">
                </cc1:ListSearchExtender>
                <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                    PromptText="" TargetControlID="ddl_OriginPerson">
                </cc1:ListSearchExtender>
                <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                    PromptText="" TargetControlID="ddl_DestPerson">
                </cc1:ListSearchExtender>
            </td>
        </tr>
    </table>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

