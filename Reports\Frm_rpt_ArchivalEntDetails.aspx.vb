
Partial Class Reports_Frm_rpt_ArchivalEntDetails
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                Chk_Ignore.Checked = False
                txt_TapeNumber.Enabled = True
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        Dim Var As String
        If Chk_Ignore.Checked = True Then
            Var = "All"
        Else
            Var = txt_TapeNumber.Text
        End If
        'qryString = "ReportViewer.aspx?ReportName=" + "rpt_SummaryofProgram_New.rpt&" + "@progname=" & Var

        'reportviewerframe.Attributes.Add("src", qryString)

        'Response.Redirect(qryString)
        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=Rpt_ArchivalEntDetails.rpt&@TapeNumber=" + Var + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=Rpt_ArchivalEntDetails.rpt&@TapeNumber=" + Var + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If


        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_ArchivalEntDetails.aspx"
        ObjSave.ReportName = "Archival --> Q 4. How Can I View Entertainment Tape Details?"
        ObjSave.SaveRecord()

        ''******************************************************''

    End Sub

    Protected Sub Chk_Ignore_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Chk_Ignore.CheckedChanged
        If Chk_Ignore.Checked = True Then
            txt_TapeNumber.Enabled = False
        Else
            txt_TapeNumber.Enabled = True
        End If
    End Sub
End Class
