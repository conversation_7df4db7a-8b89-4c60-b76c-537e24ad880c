<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ArchivalEntDetails.aspx.vb" Inherits="Reports_Frm_rpt_ArchivalEntDetails" title="Other Reports > Q 4.How can I View Program Details?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > How can I View Archival Entertainment Tape Details?" Width="832px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 220px" vAlign=middle>&nbsp;</TD><TD style="WIDTH: 100px" vAlign=middle></TD><TD style="WIDTH: 100px" vAlign=middle></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px" vAlign=middle>Tape&nbsp;Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="Chk_Ignore" runat="server" Text="Ignore" Visible="False" AutoPostBack="True" __designer:wfdid="w116"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w32"></asp:Label></TD><TD style="WIDTH: 100px; HEIGHT: 21px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 63px; HEIGHT: 20px" vAlign=top><asp:TextBox id="txt_TapeNumber" runat="server" Width="216px" CssClass="mytext" Font-Size="X-Small" Font-Names="Verdana" __designer:wfdid="w117"></asp:TextBox></TD><TD style="WIDTH: 100px; HEIGHT: 20px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w33"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 100px; HEIGHT: 20px" vAlign=top></TD></TR><TR><TD style="HEIGHT: 21px"></TD><TD style="WIDTH: 100px; HEIGHT: 21px"></TD><TD style="WIDTH: 100px; HEIGHT: 21px"></TD></TR></TBODY></TABLE><cc1:AutoCompleteExtender id="AutoCompleteExtender_2" runat="server" TargetControlID="txt_TapeNumber" ServicePath="AutoComplete.asmx" ServiceMethod="GetTapeContentEnt_Lister_TapeNo" MinimumPrefixLength="1" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1"></cc1:AutoCompleteExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Archival Reports > How can I View Archival Entertainment Tape Details? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Archival Reports > How can I View Archival Entertainment Tape Details? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

