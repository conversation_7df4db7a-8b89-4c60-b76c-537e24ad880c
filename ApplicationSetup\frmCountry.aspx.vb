
Partial Class ApplicationSetup_frmCountry
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            FillGrid()
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
        End If
    End Sub


    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.Country().IsExists_Country(txt_CountryName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : Country already Exists !"
        Else
            If txt_CountryID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_Country.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()

    End Sub

    Private Sub SaveRecord()
        If txt_CountryName.Text = "" Then
            lblErr.Text = "Please Insert Country Name!!"
        Else
            Dim ObjCountry As New BusinessFacade.Country()
            ObjCountry.CountryName = txt_CountryName.Text
            ObjCountry.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
        

    End Sub

    Private Sub UpdateRecord()
        Dim ObjCountry As New BusinessFacade.Country()
        ObjCountry.CountryID = txt_CountryID.Text
        ObjCountry.CountryName = txt_CountryName.Text
        ObjCountry.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_Country.DataSource() = New BusinessFacade.Country().GetRecords()
        dg_Country.DataBind()
        ' dg_Country.Columns(0).Visible = False
    End Sub

    Protected Sub dg_Country_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_Country.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Country.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_Country.SelectedIndex.ToString
        txt_CountryID.Text = Convert.ToInt32(dg_Country.Rows(I).Cells(1).Text)
        txt_CountryName.Text = dg_Country.Rows(I).Cells(2).Text
        dg_Country.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_CountryID.Text = "" Then
                lblErr.Text = "Please Select Country First!!"
            Else
                Dim ObjCountry As New BusinessFacade.Country()
                ObjCountry.CountryID = txt_CountryID.Text
                ObjCountry.DeleteRecord(ObjCountry.CountryID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_Country.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Country is Already in Used !"
            clrscr()
            dg_Country.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_CountryID.Text = String.Empty
        txt_CountryName.Text = String.Empty
    End Sub

    'Protected Sub dg_Country_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_Country.PageIndexChanging
    '    dg_Country.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_Country.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
