<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmArchiveEntry_EntryForm.aspx.vb" Inherits="TapeContent_FrmArchiveEntry_News" title="Home > Archive Entry for News > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDataInput.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebDataInput" TagPrefix="igtxt" %>
   

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<%--<script language=javascript runat=server>

function pageLoad(sender,e)
{
restoreCurrentTabIndex();
}
</script>--%>

    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
<table width=100%>
            <tr>
                <td style="height: 17px; text-decoration: underline; width: 1203px;" class="labelheading">
                    <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                    &gt;
                    <asp:LinkButton ID="LnkArchival_News" runat="server" CssClass="labelheading">Archive Entry for News</asp:LinkButton>
                    &gt; Add New</td>
            </tr>
    <tr>
        <td align="center" style="width: 1203px; height: 17px">
            &nbsp;<asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="100%" Font-Overline="False" Font-Italic="False" Font-Size="Small" Font-Underline="False"></asp:Label></td>
    </tr>
    <tr>
        <td style="height: 29px; width: 1203px;" align="center" bgcolor="#ff6633">
            <asp:Button
                        ID="Button1" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Save >>"
                        Width="164px" /></td>
    </tr>
            <tr>
                <td style="width: 1203px">
                    <cc1:TabContainer ID="TabContainer1" runat="server" ActiveTabIndex="0" Height="750px">
                        <cc1:TabPanel ID="TabPanel1" runat="server" HeaderText="TabPanel1">
                            <ContentTemplate>
                                <table>
                                    <tr class="mytext">
                                        <td style="width: 174px; height: 13px;">
                                            Channel</td>
                                        <td style="width: 173px; height: 13px;">
                                            Department &nbsp;
                                            <asp:Image ID="Err_Department" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                        <td style="width: 175px; height: 13px;">
                                            Sub Closet</td>
                                        <td style="width: 174px; height: 13px;">
                                            Tape No &nbsp;
                                            <asp:Image ID="Err_TapeNumber" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                        <td style="width: 106px; height: 13px;">
                                        </td>
                                        <td style="width: 241px; height: 13px">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 174px; height: 20px" valign="top">
                                            <asp:DropDownList ID="ddl_Channel" runat="server" CssClass="mytext" Width="160px">
                                            </asp:DropDownList></td>
                                        <td style="width: 173px; height: 20px" valign="top">
                                            <asp:TextBox ID="txt_Department" runat="server" CssClass="mytext" Width="152px"></asp:TextBox>
                                        </td>
                                        <td style="width: 175px; height: 20px" valign="top">
                                            <asp:DropDownList ID="ddl_SubCloset" runat="server" CssClass="mytext" Width="160px">
                                            </asp:DropDownList></td>
                                        <td style="width: 174px; height: 20px" valign="top">
                                            <asp:TextBox ID="txt_TapeNumber" runat="server" Width="152px" CssClass="mytext"></asp:TextBox>
                                            <asp:LinkButton ID="lnkBulkTapes" runat="server">Add Bulk Tapes</asp:LinkButton></td>
                                        <td style="width: 106px; height: 20px" valign="top">
                                            <asp:Button ID="bttnAddNewTape" runat="server" CssClass="buttonA" Text="Add Tape No."
                                                Width="88px" /></td>
                                        <td style="width: 241px; height: 20px" valign="top">
                                            <asp:LinkButton ID="lnkCheck" runat="server">Tape Status</asp:LinkButton></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 174px; height: 14px">
                                        </td>
                                        <td style="width: 173px; height: 14px">
                                        </td>
                                        <td style="width: 175px; height: 14px">
                                        </td>
                                        <td style="width: 174px; height: 14px">
                                        </td>
                                        <td style="width: 106px; height: 14px">
                                        </td>
                                        <td style="width: 241px; height: 14px">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 174px; height: 14px;">
                                            Classification Code</td>
                                        <td style="width: 173px; height: 14px;">
                                            Special Note</td>
                                        <td style="width: 175px; height: 14px;">
                                            Location Code</td>
                                        <td style="width: 174px; height: 14px;">
                                            Recycle Turn</td>
                                        <td style="width: 106px; height: 14px;">
                                            No. Of Copies</td>
                                        <td style="width: 241px; height: 14px">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 174px; height: 30px">
                                            <asp:TextBox ID="txt_ClassificationCode" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="150px" Height="50px"></asp:TextBox></td>
                                        <td style="width: 173px; height: 30px">
                                            <asp:TextBox ID="txt_CallNo" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="150px" Height="50px"></asp:TextBox></td>
                                        <td style="width: 175px; height: 30px">
                                            <asp:TextBox ID="txt_LocationCode" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="150px" Height="50px"></asp:TextBox></td>
                                        <td style="width: 174px; height: 30px" valign="top">
                                            <asp:DropDownList ID="ddl_RecycleTurn" runat="server" CssClass="mytext"
                                                Width="160px">
                                            </asp:DropDownList></td>
                                        <td style="width: 106px; height: 30px" valign="top">
                                            <asp:DropDownList ID="ddlCopies" runat="server" CssClass="mytext" Width="40px">
                                                <asp:ListItem>1</asp:ListItem>
                                                <asp:ListItem>2</asp:ListItem>
                                            </asp:DropDownList>
                                        </td>
                                        <td style="width: 241px; height: 30px" valign="top">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="height: 23px">
                                            </td>
                                        <td style="height: 23px; width: 173px;">
                                            </td>
                                        <td style="height: 23px">
                                            </td>
                                        <td style="width: 174px; height: 23px" valign="top">
                                            <asp:GridView ID="dgTapeNumber" runat="server" AutoGenerateColumns="False" PageSize="200"
                                                Width="88px">
                                                <Columns>
                                                    <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                                                </Columns>
                                                <HeaderStyle CssClass="gridheader" />
                                            </asp:GridView>
                                        </td>
                                        <td style="width: 106px; height: 23px" valign="top">
                                        </td>
                                        <td style="width: 241px; height: 23px" valign="top">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="4">
                                            <asp:Label ID="lblErr_MasterEntry" runat="server" ForeColor="Red" Width="408px"></asp:Label></td>
                                        <td style="width: 106px">
                                        </td>
                                        <td>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="4">
                                            &nbsp;
                                        </td>
                                        <td style="width: 106px">
                                        </td>
                                        <td>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 174px">
                                            <asp:TextBox ID="txt_TapeContent_ID" runat="server" CssClass="mytext" Visible="False"
                                                Width="96px"></asp:TextBox></td>
                                        <td style="width: 173px">
                                            <asp:TextBox ID="txt_dgProgramInfo_Index" runat="server" CssClass="mytext" Visible="False"
                                                Width="64px"></asp:TextBox></td>
                                        <td style="width: 175px">
                                            <asp:TextBox ID="txt_dgKeyword_Index" runat="server" CssClass="mytext" Visible="False"
                                                Width="64px"></asp:TextBox></td>
                                        <td style="width: 174px">
                                            <asp:TextBox ID="txt_TapeTypeID" runat="server" BorderStyle="None" ForeColor="White"
                                                Width="120px" Visible="False"></asp:TextBox></td>
                                        <td style="width: 106px">
                                        <asp:DropDownList ID="ddl_ProgramChild" runat="server" CssClass="mytext" Visible="False"
                                                Width="88px">
                                        </asp:DropDownList></td>
                                        <td>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="2">
                                            <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" Enabled="True" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_TapeNo">
                                            </cc1:ListSearchExtender>
                                            <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_Channel" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_Department" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_SubCloset" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <cc1:AutoCompleteExtender 
                                                ID="AutoCompleteExtender_TapeNumber" 
                                                runat="server"
                                                CompletionInterval="1"
                                                CompletionSetCount="12" 
                                                ServiceMethod="TapeContentNews_TapeNumber"
                                                ServicePath="AutoComplete.asmx" 
                                                TargetControlID="txt_TapeNumber" DelimiterCharacters="" Enabled="True">
                                            </cc1:AutoCompleteExtender>
                                             <cc1:AutoCompleteExtender 
                                                ID="AutoCompleteExtender3" 
                                                runat="server"
                                                CompletionInterval="1"
                                                CompletionSetCount="12" 
                                                MinimumPrefixLength="1" 
                                                ServiceMethod="GetDepartment"
                                                ServicePath="AutoComplete.asmx" 
                                                TargetControlID="txt_Department" DelimiterCharacters="" Enabled="True">
                                            </cc1:AutoCompleteExtender>
                                            <asp:DropDownList ID="ddl_Department" runat="server" CssClass="mytext" Width="160px" Visible="False">
                                            </asp:DropDownList>
                                        </td>
                                        <td style="width: 175px;">
                                            <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
                                            <asp:Label ID="lblQueryString" runat="server" Visible="False"></asp:Label></td>
                                        <td style="width: 174px;">
                                            <asp:DropDownList ID="ddl_TapeNo" runat="server" CssClass="mytext" Width="120px" Visible="False">
                                            </asp:DropDownList></td>
                                        <td style="width: 106px;">
                                        </td>
                                        <td style="width: 241px">
                                        </td>
                                    </tr>
                                </table>
                                
                            </ContentTemplate>
                            <HeaderTemplate>
                                Master Level Entry
                            </HeaderTemplate>
                        </cc1:TabPanel>
                        <cc1:TabPanel ID="TabPanel2" runat="server" HeaderText="TabPanel2">
                            <HeaderTemplate>
                                Merge Tape Information
                            </HeaderTemplate>
                            <ContentTemplate>
                                <asp:UpdatePanel id="UpdatePanel4" runat="server">
                                    <contenttemplate>
<TABLE style="WIDTH: 272px"><TBODY><TR class="mytext"><TD style="WIDTH: 182px">Merge Tape No.</TD><TD style="WIDTH: 159px"></TD><TD style="WIDTH: 159px"></TD></TR><TR class="mytext"><TD style="WIDTH: 300px" vAlign=top><asp:TextBox id="txt_MergeTape" runat="server" Width="152px"></asp:TextBox>&nbsp; <asp:Button id="bttnMerge" runat="server" Text="Merge" CssClass="buttonA" Width="80px"></asp:Button></TD><TD style="WIDTH: 159px" vAlign=top></TD><TD style="WIDTH: 159px" vAlign=top></TD></TR><TR><TD class="mytext" vAlign=top colSpan=2><asp:GridView id="dg_Merge2" runat="server" Width="352px" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="MergeTapeNumber" HeaderText="Merge Tape Number"></asp:BoundField>
<asp:BoundField HtmlEncode="False" DataFormatString="{0:dd-MMM-yyyy}" DataField="MergeDate" HeaderText="Merge Date"></asp:BoundField>
</Columns>

<HeaderStyle CssClass="gridheader" Font-Underline="False"></HeaderStyle>
</asp:GridView></TD><TD style="HEIGHT: 26px" vAlign=middle colSpan=1></TD></TR><TR><TD style="HEIGHT: 26px" vAlign=middle colSpan=2><asp:GridView id="dg_Merge" runat="server" CssClass="gridContent" Width="392px" AutoGenerateColumns="False">
                        <Columns>
                            <asp:BoundField DataField="MergeTapeNumberID" >
                                <HeaderStyle Width="1px" />
                                <ItemStyle ForeColor="White" />
                            </asp:BoundField>
                            <asp:BoundField DataField="MergeTapeNumber" HeaderText="Merge Tape Number" />
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD style="HEIGHT: 26px" vAlign=middle colSpan=1></TD></TR><TR><TD style="HEIGHT: 26px" vAlign=middle colSpan=2><asp:Label id="lblErr_Merge" runat="server" ForeColor="Red" CssClass="mytext" Width="408px"></asp:Label></TD><TD style="HEIGHT: 26px" vAlign=middle colSpan=1></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender7" runat="server" TargetControlID="ddl_MergeTapeNo" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_MergeTape" runat="server" TargetControlID="txt_MergeTape" ServicePath="AutoComplete.asmx" ServiceMethod="GetMergeTapeNumbers_News" CompletionSetCount="12" CompletionInterval="1" MinimumPrefixLength="3" EnableCaching="true">
                </cc1:AutoCompleteExtender> <asp:DropDownList id="ddl_MergeTapeNo" runat="server" CssClass="mytext" Width="176px" Visible="False"></asp:DropDownList> 
</contenttemplate>
                                </asp:UpdatePanel>
                            </ContentTemplate>
                        </cc1:TabPanel>
                        <cc1:TabPanel ID="TabPanel3" runat="server" HeaderText="TabPanel3">
                            <HeaderTemplate>
                                Slug Information
                            </HeaderTemplate>
                            <ContentTemplate>
                                <table width="100%">
                                    <tr class="mytext">
                                        <td style="width: 225px; height: 21px;">
                                            ReporterSlug &nbsp;
                                            <asp:Image ID="Err_ReporterSlug" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                        <td style="width: 225px; height: 21px;">
                                            </td>
                                        <td style="width: 200px; height: 21px">
                                            Slug Date</td>
                                        <td style="width: 250px; height: 21px">
                                        </td>
                                        <td style="width: 225px; height: 21px">
                                Footage Type</td>
                                        <td style="width: 225px; height: 21px">
                                            News Keywords</td>
                                    </tr>
                                    <tr class="mytext">
                                        <td valign="top" colspan="2" style="width: 525px">
                                            <asp:TextBox ID="txt_ReporterSlug" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="344px" Height="56px" TabIndex="1"></asp:TextBox><br />
                                            <asp:TextBox ID="TextBox2" runat="server" CssClass="mytext" Height="1px" Width="408px" Visible="False"></asp:TextBox></td>
                                        <td style="width: 250px;" valign="top">
                                            <asp:TextBox ID="txtSlugDate" runat="server" CssClass="mytext" TabIndex="1" Width="120px"></asp:TextBox>
                                            <br />
                                            &nbsp;
                                            <asp:TextBox ID="txt_ProposedSlug" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="175px" Height="32px" Visible="False">N/A</asp:TextBox>
                                            <asp:TextBox ID="TextBox1"
                                                    runat="server" CssClass="mytext" Height="1px" Width="375px" Visible="False"></asp:TextBox></td>
                                        <td style="width: 250px" valign="top">
                                            <asp:TextBox ID="TextBox3" runat="server" CssClass="mytext" Height="1px" Visible="False"
                                                Width="64px"></asp:TextBox></td>
                                        <td rowspan="3" valign="top" align="left">
                                            <table style="width: 128px">
                                                <tr>
                                                    <td style="width: 12px">
                                                        1.
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="FT1" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="13"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px">
                                                        2.</td>
                                                    <td>
                                                        <asp:TextBox ID="FT2" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="15"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                        3.
                                                    </td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="FT3" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="17"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                        4.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="FT4" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="19"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                        5.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="FT5" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="21"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                        6.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="FT6" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="23"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                        7.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="FT7" runat="server" CssClass="myddl" BackColor="Window" Width="150px" TabIndex="25"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 12px; height: 16px">
                                                    </td>
                                                    <td style="height: 16px">
                                                        <asp:LinkButton ID="bttnClrFootageType" runat="server" Visible="False">Clear</asp:LinkButton></td>
                                                </tr>
                                            </table>
                                            &nbsp; &nbsp;
                                        </td>
                                        <td align="left" rowspan="3" valign="top">
                                            <table>
                                            <tr>
                                                <td>
                                                    1.
                                                </td>
                                                <td>
                                            <asp:TextBox ID="KW1" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="14"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    2.
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="KW2" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="16"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="height: 16px">
                                                    3.
                                                </td>
                                                <td style="height: 16px">
                                                    <asp:TextBox ID="KW3" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="18"></asp:TextBox>
                                                </td>
                                            </tr>
                                                <tr>
                                                    <td style="height: 16px">
                                                        4.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="KW4" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="20"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="height: 16px">
                                                        5.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="KW5" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="22"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="height: 16px">
                                                        6.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="KW6" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="24"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="height: 16px">
                                                        7.</td>
                                                    <td style="height: 16px">
                                                        <asp:TextBox ID="KW7" runat="server" CssClass="myddl" BackColor="Window" Width="180px" TabIndex="26"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="height: 16px">
                                                    </td>
                                                    <td style="height: 16px">
                                                        <asp:LinkButton ID="LinkButton1" runat="server" OnClick="LinkButton1_Click" TabIndex="27">Add New Keyword</asp:LinkButton></td>
                                                </tr>
                                        </table>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td>
                                            English Script</td>
                                        <td>
                                            </td>
                                        <td>
                                            Urdu Slug</td>
                                        <td>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td valign="top" colspan="2">
                                            <asp:TextBox ID="txt_EnglishScript" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="344px" Height="56px" TabIndex="2"></asp:TextBox>
                                            &nbsp;</td>
                                        <td valign="top">
                                            <asp:TextBox ID="txt_UrduScript" runat="server" CssClass="mytext" TextMode="MultiLine"
                                                Width="256px" Height="56px" Font-Bold="False" Font-Size="Medium" TabIndex="3">N/A</asp:TextBox>
                                            <br />
                                            <br />
                                            <br />
                                            <br />
                                            <br />
                                            <br />
                                            <asp:LinkButton ID="LnkShowKeyBoard" runat="server" TabIndex="3">Show Keyboard</asp:LinkButton></td>
                                        <td valign="top" style="width: 250px">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="3">
                                            <table border="0" cellpadding="0" cellspacing="0" style="width: 632px">
                                                <tr>
                                                    <td style="width: 117px; height: 19px">
                                                        Start Time&nbsp; &nbsp;<asp:Image ID="Err_StratTime1" runat="server" ImageUrl="~/Images/error.gif"
                                                            Visible="False" />
                                                    </td>
                                                    <td style="width: 118px; height: 19px">
                                                        End Time&nbsp;
                                                        <asp:Image ID="Err_EndTime1" runat="server" ImageUrl="~/Images/error.gif" Visible="False" />
                                                    </td>
                                                    <td style="width: 122px; height: 19px">
                                                        Date</td>
                                                    <td style="width: 122px; height: 19px">
                                                        <asp:Label ID="Label1" runat="server" Text="Duration" Visible="False"></asp:Label></td>
                                                    <td style="width: 77px; height: 19px">
                                                        <asp:TextBox ID="txt_EndTime" runat="server" Visible="False" Width="8px"></asp:TextBox>
                                                        <asp:TextBox ID="txt_StartTime" runat="server" Visible="False" Width="8px"></asp:TextBox>
                                                        <asp:TextBox ID="txt_TimeDuration" runat="server" Visible="False" Width="16px"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 117px; height: 21px">
                                                     <igtxt:WebMaskEdit ID="txt_StartTime1" runat="server" CssClass="mytext" HorizontalAlign="Left"
                                InputMask="##:##:##:##" Width="105px">
                            </igtxt:WebMaskEdit>
                                                       </td>
                                                    <td style="width: 118px; height: 21px">
                                                    <igtxt:WebMaskEdit ID="txt_Endtime1" runat="server" CssClass="mytext" HorizontalAlign="Left"
                                InputMask="##:##:##:##" Width="101px">
                            </igtxt:WebMaskEdit>
                                                       </td>
                                                    <td style="width: 122px; height: 21px">
                                                        <asp:TextBox ID="txtEntryDate" runat="server" Width="96px" CssClass="mytext" TabIndex="12"></asp:TextBox></td>
                                                    <td style="width: 122px; height: 21px">
                                                        <asp:TextBox ID="DUR_1" runat="server" CssClass="myddl" Enabled="False" Width="16px" Visible="False"></asp:TextBox>&nbsp;
                                                        <asp:TextBox ID="DUR_2" runat="server" CssClass="myddl" Enabled="False" Width="16px" Visible="False"></asp:TextBox>&nbsp;
                                                        <asp:TextBox ID="DUR_3" runat="server" CssClass="myddl" Enabled="False" Width="16px" Visible="False"></asp:TextBox>&nbsp;
                                                        <asp:TextBox ID="DUR_4" runat="server" CssClass="myddl" Enabled="False" Width="16px" Visible="False"></asp:TextBox></td>
                                                    <td style="width: 77px; height: 21px">
                                                        <asp:Button ID="bttnCalculateDuration" runat="server" CssClass="buttonA" Text="Calculate Duration"
                                                            Width="120px" Visible="False" /></td>
                                                </tr>
                                                <tr>
                                                <td>
                                                <asp:TextBox ID="END1" runat="server" CssClass="myddl" Width="16px" TabIndex="4" Visible ="False"></asp:TextBox>
                                                       
                                                        <asp:TextBox ID="END2" runat="server" CssClass="myddl" Width="16px" TabIndex="5" Visible ="False"></asp:TextBox>
                                                        
                                                        <asp:TextBox ID="END3" runat="server" CssClass="myddl" Width="16px" TabIndex="6" Visible ="False"></asp:TextBox>
                                                       
                                                        <asp:TextBox ID="END4" runat="server" CssClass="myddl" Width="16px" TabIndex="7" Visible ="False"></asp:TextBox>
                                                </td>
                                                <td>
                                                 <asp:TextBox ID="ST1" runat="server" CssClass="myddl" Width="16px" TabIndex="8" Visible ="False"></asp:TextBox>
                                                       
                                                        <asp:TextBox ID="ST2" runat="server" CssClass="myddl" Width="16px" TabIndex="9" Visible ="False"></asp:TextBox>
                                                     
                                                        <asp:TextBox ID="ST3" runat="server" CssClass="myddl" Width="16px" TabIndex="10" Visible ="False"></asp:TextBox>
                                                  
                                                        <asp:TextBox ID="ST4" runat="server" CssClass="myddl" Width="16px" TabIndex="11" Visible ="False"></asp:TextBox>
                                                </td>
                                                </tr>
                                                
                                                <tr>
                                                    <td style="width: 117px; height: 13px">
                                                    </td>
                                                    <td style="width: 118px; height: 13px">
                                                    </td>
                                                    <td style="width: 122px; height: 13px">
                                                    </td>
                                                    <td style="width: 122px; height: 13px">
                                                    </td>
                                                    <td style="width: 77px; height: 13px">
                                                    </td>
                                                </tr>
                                            </table>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender1" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="END1" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender2" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="END2" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender3" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="END3" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender4" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="END4" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender5" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="ST1" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender6" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="ST2" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender7" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="ST3" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:MaskedEditExtender ID="MaskedEditExtender8" runat="server" Mask="99" MaskType="Number"
                                                TargetControlID="ST4" CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True">
                                            </cc1:MaskedEditExtender>
                                            <cc1:CalendarExtender ID="CalendarExtender1" runat="server" Format="dd-MMM-yyyy"
                                                TargetControlID="txtEntryDate" Enabled="True" CssClass="MyCalendar">
                                            </cc1:CalendarExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW1" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW1">
                                            </cc1:AutoCompleteExtender><cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW2" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW2">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW3" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW3">
                                            </cc1:AutoCompleteExtender><cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW4" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW4">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW5" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW5">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW6" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW6">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_KW7" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="KW7">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_1" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT1">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_2" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT2">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_3" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT3">
                                            </cc1:AutoCompleteExtender><cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_4" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT4">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_5" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT5">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_6" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT6">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_FT_7" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" DelimiterCharacters="" Enabled="True" MinimumPrefixLength="1"
                                                ServiceMethod="FootageType_GetRecords_AutoComplete" ServicePath="AutoComplete.asmx"
                                                TargetControlID="FT6">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:CalendarExtender ID="CalendarExtender_SlugDate" runat="server" Enabled="True"
                                                Format="dd-MMM-yyyy" TargetControlID="txtSlugDate">
                                            </cc1:CalendarExtender>
                                        </td>
                                        <td colspan="1" style="width: 250px;">
                                        </td>
                                        <td rowspan="1" valign="top">
                                            <table style="width: 159%">
                                                <tr>
                                                    <td>
                                            Reporter &nbsp;
                                            <asp:Image ID="Err_reporter" runat="server" ImageUrl="~/Images/error.gif" Visible="False" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <asp:TextBox ID="txt_Reporter" runat="server" Width="168px" CssClass="mytext" BackColor="Window" TabIndex="28"></asp:TextBox></td>
                                                </tr>
                                            </table>
                                                        </td>
                                        <td rowspan="1" valign="top">
                                            <table style="width: 216px">
                                                <tr>
                                                    <td>
                                            Camera Man &nbsp;
                                            <asp:Image ID="Err_CameraMan" runat="server" ImageUrl="~/Images/error.gif" Visible="False" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td valign="top" style="height: 22px">
                                                        <asp:TextBox ID="txt_CameraMan" runat="server" Width="185px" CssClass="mytext" BackColor="Window" TabIndex="29"></asp:TextBox></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="3">
                                            <table style="width: 688px">
                                                <tr>
                                                    <td style="width: 100px">
                                                        <asp:Label ID="Label2" runat="server" Text="Low Resolution File" Width="100%"></asp:Label>
                                                    </td>
                                                    <td style="width: 100px">
                                                        <asp:Label ID="Label4" runat="server" Text="High Resolution File" Width="100%"></asp:Label>
                                                    </td>
                                                    <td style="width: 100px">
                                                        <asp:Label ID="Label5" runat="server" Text="File Path" Width="104px"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="50">
                                            <asp:TextBox ID="txtLowResFileName" runat="server" CssClass="mytext" Height="30px"
                                                TextMode="MultiLine" Width="152px">N/A</asp:TextBox>
                                                    </td>
                                                    <td style="width: 47px" valign="top">
                                            <asp:TextBox ID="txtHighResFileName" runat="server" CssClass="mytext" Height="30px"
                                                TextMode="MultiLine" Width="152px">N/A</asp:TextBox></td>
                                                    <td style="width: 100px" valign="top">
                                            <asp:TextBox ID="txtFiePath" runat="server" CssClass="mytext" Height="30px" TextMode="MultiLine"
                                                Width="176px">N/A</asp:TextBox>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td colspan="1" style="width: 250px">
                                        </td>
                                        <td rowspan="1" valign="top">
                                        </td>
                                        <td rowspan="1" valign="top">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="3">
                                                            <asp:Image ID="ImgKeyBoard" runat="server" ImageUrl="~/Images/UrduKeyboard.gif" Visible="False" /></td>
                                        <td colspan="1" style="width: 250px">
                                        </td>
                                        <td rowspan="1" valign="top">
                                        </td>
                                        <td rowspan="1" valign="top">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td class="bottomMain" colspan="6" style="height: 28px" valign="middle">
                                            &nbsp;
                                            <asp:Button ID="bttnSaveTable" runat="server" CssClass="buttonA" Text="Save Slug"
                                                Width="120px" Font-Bold="True" Visible="False" />&nbsp;
                                            <asp:Button ID="bttnSaveProgramInfo" runat="server" CssClass="buttonA" Font-Bold="True"
                                                Text="Add Slug" Width="104px" TabIndex="30" />
                                            <asp:Button ID="AddKeyword" runat="server" Text="Enter Keyword & Footage" CssClass="buttonA" Font-Bold="True" TabIndex="31" />
                                            <asp:Button ID="bttnClearSlug" runat="server" CssClass="buttonA" Text="Clear"
                                                Width="56px" Font-Bold="True" Height="24px" TabIndex="32" />
                                            <asp:Button ID="bttnSave_Footage" runat="server" CssClass="buttonA" Text="Save Footage"
                                                Width="128px" Font-Bold="True" Visible="False" /></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="6" valign="top" style="height: 574px">
                                                        <asp:Panel ID="Panel1" runat="server" BackColor="LightSteelBlue" BorderColor="#E0E0E0"
                                                            Width="100%">
                                                            &nbsp;
                                                            <asp:Image ID="Image3" runat="server" />
                                                            <asp:Label ID="Label3" runat="server" CssClass="heading1" Font-Bold="True" Font-Names="Arial"
                                                                Font-Size="Medium" Text="- - Slugs --" Width="512px"></asp:Label></asp:Panel>
                                            <asp:Panel ID="Panel2" runat="server" Width="100%">
                                                <table style="width: 100%; height: 100%">
                                                    <tr>
                                                        <td colspan="2" style="height: 15px" valign="top">
                                                            &nbsp;</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" valign="top">
                                            <asp:Panel ID="pnlScroll" runat="server" Height="150px" ScrollBars="Both" Width="100%">
                                                <asp:GridView ID="dg_programInfo" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                    CssClass="gridContent" PageSize="15" Width="2600px">
                                                    <Columns>
                                                        <asp:BoundField DataField="ReporterSlug" HeaderText="Reporter Slug">
                                                            <HeaderStyle HorizontalAlign="Center" Width="800px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="ProposedSlug" HeaderText="Proposed Slug">
                                                            <HeaderStyle HorizontalAlign="Center" Width="50px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="ReporterID">
                                                            <ControlStyle Width="0px" />
                                                            <ItemStyle ForeColor="White" />
                                                            <HeaderStyle Width="0px" />
                                                            <FooterStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="CameraManID">
                                                            <ItemStyle ForeColor="White" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="EnglishScript" HeaderText="English Script">
                                                            <HeaderStyle HorizontalAlign="Center" Width="1000px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="UrduScript" HeaderText="Urdu Slug">
                                                            <HeaderStyle HorizontalAlign="Center" Width="250px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="StartTime" HeaderText="Start Time" />
                                                        <asp:BoundField DataField="EndTime" HeaderText="End Time" />
                                                        <asp:BoundField DataField="Duration" HeaderText="Duration" />
                                                        <asp:BoundField DataField="TapeContentDetail_News_ID">
                                                            <ItemStyle ForeColor="White" />
                                                            <HeaderStyle Width="1px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="TapeSlugID">
                                                            <ItemStyle ForeColor="White" />
                                                            <HeaderStyle Width="1px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="Reporter" HeaderText="Reporter">
                                                            <HeaderStyle Width="180px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="CameraMan" HeaderText="CameraMan">
                                                            <HeaderStyle Width="180px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="EntryDate" HeaderText="Entry Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False" >
                                                            <HeaderStyle Width="150px" />
                                                        </asp:BoundField>
                                                        <asp:CommandField ShowDeleteButton="True" />
                                                        <asp:BoundField DataField="HighResolution" HeaderText="HighResolution" />
                                                        <asp:BoundField DataField="LowResolution" HeaderText="LowResolution" />
                                                        <asp:BoundField DataField="FilePath" HeaderText="FilePath" />
                                                    </Columns>
                                                    <HeaderStyle CssClass="gridheader" />
                                                    <AlternatingRowStyle CssClass="AlternateRows" />
                                                </asp:GridView>
                            </asp:Panel>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 50%; height: 167px;" valign="top">
                                            <asp:Panel ID="pnlScroll2" runat="server" Height="130px" ScrollBars="Both" Width="100%">
                                                <asp:GridView ID="dg_KeyWord_2" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                    CssClass="gridContent" OnSelectedIndexChanged="dg_KeyWord_2_SelectedIndexChanged">
                                                    <Columns>
                                                        <asp:BoundField DataField="TapeSlugID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="NewsKeywordID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="SlugVsKeywordID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="TapeSlug" HeaderText="Tape Slug">
                                                            <HeaderStyle Width="1000px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="NewsKeyword" HeaderText="News Keyword">
                                                            <HeaderStyle Width="275px" />
                                                        </asp:BoundField>
                                                        <asp:CommandField ShowDeleteButton="True" />
                                                    </Columns>
                                                    <HeaderStyle CssClass="gridheader" />
                                                    <AlternatingRowStyle CssClass="AlternateRows" />
                                                </asp:GridView>
                                                &nbsp;
                                            </asp:Panel>
                                                        </td>
                                                        <td style="width: 50%; height: 167px;" valign="top">
                                                            <asp:Panel ID="Panel3" runat="server" Height="130px" ScrollBars="Both" Width="100%">
                                                <asp:GridView ID="dg_Footage" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                                                    BorderStyle="None" CssClass="gridContent" Width="600px" Height="72px">
                                                    <Columns>
                                                        <asp:BoundField DataField="FootageType" HeaderText="Footage Type">
                                                            <ItemStyle Width="205px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="FootageTypeID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="TapeContentFootageID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="TapeContentID">
                                                            <ItemStyle ForeColor="White" Width="0px" />
                                                            <HeaderStyle Width="0px" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="TapeSlug" HeaderText="TapeSlug" >
                                                            <HeaderStyle Width="1000px" />
                                                        </asp:BoundField>
                                                        <asp:CommandField ShowDeleteButton="True" />
                                                    </Columns>
                                                    <HeaderStyle CssClass="gridheader" />
                                                </asp:GridView>
                                                            </asp:Panel>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </asp:Panel>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="6" style="height: 16px" valign="top">
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender1" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" ServiceMethod="Reporter_GetRecords_AutoComplete"
                                                ServicePath="AutoComplete.asmx" TargetControlID="txt_Reporter" DelimiterCharacters="" Enabled="True">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender2" runat="server" CompletionInterval="1"
                                                CompletionSetCount="12" ServiceMethod="Cameraman_GetRecords_AutoComplete"
                                                ServicePath="AutoComplete.asmx" TargetControlID="txt_CameraMan" DelimiterCharacters="" Enabled="True">
                                            </cc1:AutoCompleteExtender>
                                            <cc1:ListSearchExtender ID="ListSearchExtender5" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_Reporter" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <cc1:ListSearchExtender ID="ListSearchExtender6" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="ddl_CameraMan" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <cc1:collapsiblepanelextender 
                                                id="CollapsiblePanelExtender1" 
                                                runat="server" 
                                                collapsecontrolid="Panel1"
                                                collapsed="True" 
                                                collapsedimage="~/Images/Collapse.gif" 
                                                collapsedtext="-- Show Slugs --"
                                                expandcontrolid="Panel1" 
                                                expandedimage="~/Images/expand.gif" 
                                                expandedtext="-- Hide Slugs --"
                                                imagecontrolid="Image3" 
                                                suppresspostback="True"
                                                textlabelid="Label3"
                                                targetcontrolid="Panel2" Enabled="True">
                                            </cc1:collapsiblepanelextender>
                                            &nbsp;&nbsp;
                                        </td>
                                    </tr>
                                </table>
                            </ContentTemplate>
                        </cc1:TabPanel>
                    </cc1:TabContainer>
                                                            </td>
            </tr>
            <tr class="mytext" >
                <td style="height: 29px; width: 1203px;">
                    &nbsp;&nbsp;
                    <asp:Button CssClass="buttonA" ID="bttnSaveFinal" runat="server" Text="Save Record" Width="96px" Font-Bold="True" Visible="False" />&nbsp;
                    &nbsp;<asp:Button CssClass="buttonA" ID="bttnCancel" runat="server" Text="Cancel" Width="72px" Visible="False" />
                    <asp:Button CssClass="buttonA" ID="bttnSave" runat="server" Text="Save Record" Width="96px" Visible="False" />&nbsp;</td>
            </tr>
            <tr>
                <td style="height: 16px; width: 1203px;">
                                                        <asp:ListBox ID="lstFootageType" runat="server" Height="48px" SelectionMode="Multiple"
                                                Width="184px" CssClass="mytext" Visible="False"></asp:ListBox><asp:ListBox ID="lstKeyword" runat="server" Height="48px" SelectionMode="Multiple"
                                                Width="150px" CssClass="mytext" Visible="False"></asp:ListBox>
                                            <cc1:ListSearchExtender ID="ListSearchExtender10" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="lstKeyword" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            <asp:DropDownList ID="ddl_Reporter" runat="server" CssClass="mytext" Visible="False"
                                                Width="48px">
                                            </asp:DropDownList><asp:DropDownList ID="ddl_CameraMan" runat="server" CssClass="mytext" Visible="False"
                                                Width="40px">
                                            </asp:DropDownList>
                                            <cc1:ListSearchExtender ID="ListSearchExtender9" runat="server" PromptPosition="Bottom"
                                                PromptText="" TargetControlID="lstFootageType" Enabled="True">
                                            </cc1:ListSearchExtender>
                                            </td>
            </tr>
        </table>
                                <asp:UpdatePanel id="UpdatePanel2" runat="server" Visible="False">
                                    <contenttemplate>
<TABLE style="WIDTH: 448px"><TBODY><TR class="mytext"><TD style="WIDTH: 160px">Key Type</TD><TD>Key Word</TD><TD style="WIDTH: 100px"></TD></TR><TR class="mytext"><TD style="WIDTH: 160px" vAlign=top><asp:DropDownList id="ddl_KeywordType" runat="server" Width="152px" CssClass="mytext">
                                            </asp:DropDownList></TD><TD><asp:TextBox id="txt_KeyWord" runat="server" CssClass="mytext"></asp:TextBox>&nbsp; </TD><TD style="WIDTH: 100px"><asp:Button id="bttnSave_Keyword" runat="server" Text="Save KeyWord" Width="96px" CssClass="buttonA"></asp:Button></TD></TR></TBODY></TABLE><TABLE><TBODY><TR><TD style="WIDTH: 565px"><asp:GridView id="dg_KeyWord" runat="server" Width="544px" CssClass="gridContent" AutoGenerateColumns="False" AutoGenerateSelectButton="True">
                                                <HeaderStyle CssClass="gridheader" />
                                                <AlternatingRowStyle CssClass="AlternateRows" />
                                                <Columns>
                                                    <asp:BoundField DataField="KeyType" HeaderText="Key Type" />
                                                    <asp:BoundField DataField="KeyWord" HeaderText="Key Word" />
                                                    <asp:BoundField DataField="EntertainmentKeywordID" >
                                                        <HeaderStyle Width="1px" />
                                                        <ItemStyle ForeColor="White" />
                                                    </asp:BoundField>
                                                </Columns>
                                            </asp:GridView> <asp:TextBox id="txt_TapeContentID" runat="server" Width="96px" Visible="False"></asp:TextBox> <asp:TextBox id="txt_dgFootage_Index" runat="server" Width="96px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
                                </asp:UpdatePanel>
                                <asp:UpdatePanel id="UpdatePanel1" runat="server" Visible="False">
                                    <contenttemplate>
<TABLE style="WIDTH: 288px"><TBODY><TR class="mytext"><TD style="WIDTH: 355px; HEIGHT: 6px"></TD><TD style="HEIGHT: 6px"></TD></TR><TR class="mytext"><TD style="WIDTH: 355px; HEIGHT: 17px">Footage Type</TD><TD style="HEIGHT: 17px"></TD></TR><TR class="mytext"><TD style="WIDTH: 355px" vAlign=top></TD><TD style="WIDTH: 161px"></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender8" runat="server" TargetControlID="ddl_FootageType" PromptPosition="Bottom" PromptText></cc1:ListSearchExtender>&nbsp; <asp:DropDownList id="ddl_FootageType" runat="server" CssClass="mytext" Width="144px" Visible="False"></asp:DropDownList> <asp:TextBox id="txt_Footage" runat="server" CssClass="mytext" Width="144px" Visible="False"></asp:TextBox> 
</contenttemplate>
                                </asp:UpdatePanel>
</asp:Content>

