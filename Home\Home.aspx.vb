Imports System
Imports System.Data

Partial Class Home_Home
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then

                '    Response.Write("Wrong")
                'Else

                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)
                End If

                lblBaseStation.text = CInt(Request.Cookies("userinfo")("BaseStationID"))

                BindGrid()
                Marquee()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Sub Marquee()
        Dim NewTicker As String = GetNewsTicker()
        Dim mydiv As System.Web.UI.HtmlControls.HtmlGenericControl
        mydiv = New System.Web.UI.HtmlControls.HtmlGenericControl("DIV")
        mydiv.InnerHtml = NewTicker
        td_Ticker.Controls.Add(mydiv)
    End Sub

    Public Function GetNewsTicker() As String

        Dim dtTicker As New DataTable
        Dim ObjTicker As New BusinessFacade.Reports()
        dtTicker = ObjTicker.GetNewsTicker()

        Dim strDiv As String = String.Empty

        strDiv += "<marquee scrollamount=""3"" scrolldelay=""1"" onmouseover=""this.stop();"" onmouseout=""this.start();"">"
        For I As Integer = 0 To dtTicker.Rows.Count - 1
            Dim RedirectForm As String = ""
            Dim TickerText As String = dtTicker.Rows(I)(1).ToString()
            strDiv += "<a class=""AnchorMarquee"" href=" + RedirectForm + " target=_blank>" + TickerText + "</a>&nbsp;&nbsp;*&nbsp;&nbsp;"
        Next
        strDiv += " </marquee>"
        Return strDiv

    End Function


    Private Sub BindGrid()
        Dim Obj As New BusinessFacade.Reports()
        GridView1.DataSource = Obj.GetMostViewReports()
        GridView1.Columns(1).Visible = True
        GridView1.DataBind()
        GridView1.Columns(1).Visible = False

        Dim Obj2 As New BusinessFacade.Reports()
        GridView2.DataSource = Obj2.GetMostSearchedElements()
        GridView2.Columns(1).Visible = True
        GridView2.Columns(2).Visible = True
        GridView2.DataBind()
        GridView2.Columns(1).Visible = False
        GridView2.Columns(2).Visible = False

        Dim obj3 As New BusinessFacade.Reports()
        Dim ds As New Data.DataSet
        ds = obj3.GetSpotLightSummary()
        'lblGrandBlankIssued.Text = ds.Tables(0).Rows(0)(0)
        'lblGrandArchivalIssued.Text = ds.Tables(1).Rows(0)(0)
        'lblGrandBlankReturned.Text = ds.Tables(2).Rows(0)(0)
        'lblGrandArchivalReturned.Text = ds.Tables(3).Rows(0)(0)
        'lblYesterdayBlankIssued.Text = ds.Tables(4).Rows(0)(0)
        'lblYesterdayArchivalIssued.Text = ds.Tables(5).Rows(0)(0)
        'lblYesterdayBlankReturned.Text = ds.Tables(6).Rows(0)(0)
        'lblYesterdayArchivalReturned.Text = ds.Tables(7).Rows(0)(0)
    End Sub

    Protected Sub lnkPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkPassword.Click

        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        ''script = script + "var mywindow = window.open('../ChangePassword/ChangePassword.aspx" + "', 'mywindow'); "
        script = script + "var mywindow = window.open('../ChangePassword/ChangePassword.aspx', 'Report', 'width=280, height=225, left=150, top=150, menubar=no, status=no, location=no, toolbar=no, scrollbars=No, resizable=No'); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim objManager As JangSalesPortal.SecurityLayer.SecurityManager = JangSalesPortal.SecurityLayer.SecurityManager.CreateInstance
        Dim ticket As JangSalesPortal.SecurityLayer.AuthenticationTicket
        ticket = objManager.Authenticate(lbl_UserName.Text, txtOldPassword.Text)
        If Not ticket Is Nothing Then
            If txtNewPassword.Text.ToLower = txtConfirmPassword.Text.ToLower Then
                Dim EncryptedPassword As String = New SecurityManagerMethods.Encryption().EncryptText(txtNewPassword.Text)

                Dim ObjChangePassword As New BusinessFacade.Country()
                ObjChangePassword.LoginID = lbl_UserName.Text
                ObjChangePassword.Password = EncryptedPassword
                ObjChangePassword.ChangePassword()

                lblErr.Text = "Password has been Change Successfully !!"
            Else
                lblErr.Text = "New Password is not valid !!"
            End If
        Else
            lblErr.Text = "Login Id / Password is not valid"
        End If
    End Sub

    Protected Sub lnkEntSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkEntSearch.Click
        Response.Write("<script type='text/javascript'>detailedresults=window.open('../SearchEngine/SearchEnt.aspx" & "');</script>")
    End Sub

    Protected Sub lnkNewsSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkNewsSearch.Click
        Response.Write("<script type='text/javascript'>detailedresults=window.open('../SearchEngine/SearchNews.aspx" & "');</script>")
    End Sub

  
End Class
