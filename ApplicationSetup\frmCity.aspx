<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmCity.aspx.vb" Inherits="ApplicationSetup_frmCity" title="Home > City > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_City" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR class="mytext"><TD style="WIDTH: 3px; HEIGHT: 15px"></TD><TD style="TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LinkButton1" onclick="LinkButton1_Click" runat="server" CssClass="labelheading" __designer:wfdid="w16">Home</asp:LinkButton>&nbsp;&gt;&nbsp; City &gt; Add New</TD></TR><TR><TD style="WIDTH: 3px"></TD><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD>Country Name</TD><TD><asp:DropDownList id="ddl_Country" runat="server" CssClass="mytext" Width="160px" AppendDataBoundItems="True"></asp:DropDownList></TD><TD>City Short Name</TD><TD><asp:TextBox id="txt_CityShortName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD></TR><TR class="mytext"><TD>City Name</TD><TD><asp:TextBox id="txt_CityName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD><TD>Is OSR</TD><TD><asp:DropDownList id="ddl_IsOSR" runat="server" CssClass="mytext" Width="160px">
                                <asp:ListItem Value="1">True</asp:ListItem>
                                <asp:ListItem Value="0">False</asp:ListItem>
                            </asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 29px"></TD><TD style="WIDTH: 100%; HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD></TD><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w6" Font-Bold="True"></asp:Label></TD></TR><TR><TD></TD><TD vAlign=top><asp:GridView id="dg_City" runat="server" CssClass="gridContent" Width="768px" PageSize="25" AllowPaging="True" AutoGenerateSelectButton="True" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="CityID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="CountryID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="CountryName" HeaderText="Country Name"></asp:BoundField>
<asp:BoundField DataField="CityName" HeaderText="City Name"></asp:BoundField>
<asp:BoundField DataField="CityShortName" HeaderText="City Short Name"></asp:BoundField>
<asp:BoundField DataField="IsOSR" HeaderText="Is OSR"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 12px"></TD><TD style="HEIGHT: 12px"><asp:Label id="lblAuditHistory" runat="server" CssClass="labelheading" __designer:wfdid="w3" Visible="False">Audit History - City</asp:Label></TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 12px"></TD><TD style="HEIGHT: 12px"><asp:GridView id="dgAuditHistory" runat="server" CssClass="gridContent" __designer:wfdid="w1" Width="100%" PageSize="25" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD style="WIDTH: 3px; HEIGHT: 12px"></TD><TD style="HEIGHT: 12px"><asp:TextBox id="txt_CityID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:TextBox id="txt_CityID_SM" runat="server" CssClass="mytext" Width="152px" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w3" Visible="False"></asp:Label></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w9" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

