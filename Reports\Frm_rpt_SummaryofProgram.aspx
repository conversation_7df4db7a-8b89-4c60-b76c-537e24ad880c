<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_SummaryofProgram.aspx.vb" Inherits="Reports_Frm_rpt_SummaryofProgram" title="Other Reports > Q 4.How can I View Program Details?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports > How can I View Program Details?" Width="640px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 220px" vAlign=middle>&nbsp;</TD><TD style="WIDTH: 95px" vAlign=middle></TD><TD style="WIDTH: 150px" vAlign=middle></TD><TD style="WIDTH: 150px" vAlign=middle></TD><TD vAlign=middle></TD><TD style="WIDTH: 100px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 220px; HEIGHT: 21px" vAlign=bottom>Program Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;<asp:CheckBox id="Chk_Ignore" runat="server" Text="Ignore" Visible="False" __designer:wfdid="w116" AutoPostBack="True"></asp:CheckBox></TD><TD style="WIDTH: 95px" vAlign=bottom>Station <asp:CheckBox id="chkStation" runat="server" Text="Ignore" __designer:dtid="1970324836974612" __designer:wfdid="w8" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="WIDTH: 150px" vAlign=bottom>From Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="All Dates" __designer:wfdid="w2"></asp:CheckBox></TD><TD style="WIDTH: 150px; HEIGHT: 21px" vAlign=bottom>To Date</TD><TD style="HEIGHT: 21px" vAlign=bottom><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w50"></asp:Label></TD><TD style="WIDTH: 100px; HEIGHT: 21px" vAlign=bottom></TD></TR><TR class="mytext"><TD style="WIDTH: 220px" vAlign=top><asp:TextBox id="txt_ProgramName" runat="server" Width="216px" CssClass="mytext" Font-Size="X-Small" Font-Names="Verdana" __designer:wfdid="w117"></asp:TextBox></TD><TD style="WIDTH: 95px" vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="7318349394477092" Width="88px" CssClass="mytext" __designer:wfdid="w9">
                                </asp:DropDownList></TD><TD style="WIDTH: 150px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="145px" CssClass="mytext" __designer:wfdid="w1"></asp:TextBox></TD><TD style="WIDTH: 150px" vAlign=top><asp:TextBox id="txt_ToDate" runat="server" Width="144px" CssClass="mytext" __designer:wfdid="w3"></asp:TextBox></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w51"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 100px" vAlign=top></TD></TR><TR><TD style="WIDTH: 220px; HEIGHT: 21px"></TD><TD style="WIDTH: 95px; HEIGHT: 21px"></TD><TD style="WIDTH: 150px; HEIGHT: 21px"></TD><TD style="WIDTH: 150px; HEIGHT: 21px"></TD><TD style="HEIGHT: 21px"></TD><TD style="WIDTH: 100px; HEIGHT: 21px"></TD></TR></TBODY></TABLE><cc1:AutoCompleteExtender id="AutoCompleteExtender_2" runat="server" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="Program" ServicePath="AutoComplete.asmx" TargetControlID="txt_ProgramName">
                </cc1:AutoCompleteExtender> <cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w4" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender><cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w5" TargetControlID="txt_ToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Other Reports > How can I View Program Details? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Other Reports > How can I View Program Details? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

