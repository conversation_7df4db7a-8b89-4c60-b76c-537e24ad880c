Partial Class LoginPassword
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        Dim objManager As JangSalesPortal.SecurityLayer.SecurityManager = JangSalesPortal.SecurityLayer.SecurityManager.CreateInstance
        Dim ticket As JangSalesPortal.SecurityLayer.AuthenticationTicket
        ticket = objManager.Authenticate(lbl_UserName.Text, txtOldPassword.Text)
        If Not ticket Is Nothing Then
            If txtNewPassword.Text.ToLower = txtConfirmPassword.Text.ToLower Then
                Dim EncryptedPassword As String = New SecurityManagerMethods.Encryption().EncryptText(txtNewPassword.Text)

                Dim ObjChangePassword As New BusinessFacade.Country()
                ObjChangePassword.LoginID = lbl_UserName.Text
                ObjChangePassword.Password = EncryptedPassword
                ObjChangePassword.ChangePassword()

                Clrscr()
                lblErr.Text = "Password has been Change Sucessfully !!"
            Else
                lblErr.Text = "New Password is not valid !!"
            End If
        Else
            lblErr.Text = "Login Id / Password is not valid"
        End If
    End Sub

    Private Sub Clrscr()
        'lbl_UserName.Text = String.Empty
        txtOldPassword.Text = String.Empty
        txtNewPassword.Text = String.Empty
        txtConfirmPassword.Text = String.Empty
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        Clrscr()
        lblErr.Text = String.Empty
    End Sub
End Class
