Imports System.Data

Partial Class SearchEngine_NewsTapeNumberDetails
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim DS1 As DataSet

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Dim TapeNumber As String
        TapeNumber = Request.QueryString("TapeNumber")

        Dim ReportSlug As String
        ReportSlug = Request.QueryString("ReportSlug")

        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetNewsSearchEngine_TapeDetails"
        cmd.Connection = Con
        cmd.CommandTimeout = 0

        Dim TapeNumber1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
        TapeNumber1.Value = TapeNumber

        Dim ReportSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReportSlug", Data.SqlDbType.Text)
        ReportSlug1.Value = ReportSlug

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS = New DataSet

        da.SelectCommand = cmd
        da.Fill(DS, "Table0")

        If DS.Tables("Table0").Rows.Count > 0 Then

            Dim I As Integer
            For I = 0 To DS.Tables("Table0").Rows.Count - 1
                Dim J As String = I + 1
                If I <> DS.Tables("Table0").Rows.Count - 1 Then
                    lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)("Keyword").ToString + ",</b><br>"
                Else
                    lblKeyword.Text = lblKeyword.Text + "<b>" + J + " : " + DS.Tables("Table0").Rows(I)("Keyword").ToString + "</b><br>"
                End If
            Next

            Dim K As Integer
            For K = 0 To DS.Tables("Table0").Rows.Count - 1
                Dim L As String = K + 1
                If K <> DS.Tables("Table0").Rows.Count - 1 Then
                    lblFootageTypes.Text = lblFootageTypes.Text + "<b>" + L + " : " + DS.Tables("Table0").Rows(K)("FootageTypes").ToString + ",</b><br>"
                Else
                    lblFootageTypes.Text = lblFootageTypes.Text + "<b>" + L + " : " + DS.Tables("Table0").Rows(K)("FootageTypes").ToString + "</b><br>"
                End If
            Next

            lblTapeNumber.Text = DS.Tables("Table0").Rows(0)("TapeNumber").ToString
            lblSubCloset.Text = DS.Tables("Table0").Rows(0)("SubCloset").ToString
            lblReportSlug.Text = DS.Tables("Table0").Rows(0)("ReportSlug").ToString
            lblProposedSlug.Text = DS.Tables("Table0").Rows(0)("ProposedSlug").ToString
            lblReporter.Text = DS.Tables("Table0").Rows(0)("Reporter").ToString
            lblCameraMan.Text = DS.Tables("Table0").Rows(0)("CameraMan").ToString
            lblisAvailable.Text = DS.Tables("Table0").Rows(0)("isAvailable").ToString
            lblEntryDate.Text = DS.Tables("Table0").Rows(0)("EntryDate").ToString
            lblTapeType.Text = DS.Tables("Table0").Rows(0)("TapeType").ToString
            lblTapeStatus.Text = DS.Tables("Table0").Rows(0)("TapeStatus").ToString
            lblStartTime.Text = DS.Tables("Table0").Rows(0)("StartTime").ToString
            lblEndTime.Text = DS.Tables("Table0").Rows(0)("EndTime").ToString
            lblRecordID.Text = DS.Tables("Table0").Rows(0)("RecordID").ToString
        End If
        lblEngScript.Visible = True
        '*********************** Store Procedure **********************'

        Dim cmd1 As New System.Data.SqlClient.SqlCommand
        cmd1.CommandType = Data.CommandType.StoredProcedure
        cmd1.CommandText = "GetEnglishScript"
        cmd1.Connection = Con
        cmd1.CommandTimeout = 0

        Dim RecordID As Data.SqlClient.SqlParameter = cmd1.Parameters.Add("@RecordID", Data.SqlDbType.Int)
        RecordID.Value = lblRecordID.Text
        'RecordID.Value = Request.QueryString("RecordID")

        Dim da1 As New System.Data.SqlClient.SqlDataAdapter
        DS1 = New DataSet

        da1.SelectCommand = cmd1
        da1.Fill(DS1)
        'lblEngScript.Text = DS.Tables(0).Rows(0)(0)
        lblEngScript.Text = DS1.Tables(0).Rows(0)(0)


    End Sub

    Protected Sub GetEnglishScript_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles GetEnglishScript.Click
        lblEngScript.Visible = True
        '*********************** Store Procedure **********************'

        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetEnglishScript"
        cmd.Connection = Con
        cmd.CommandTimeout = 0

        Dim RecordID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RecordID", Data.SqlDbType.Int)
        RecordID.Value = lblRecordID.Text
        'RecordID.Value = Request.QueryString("RecordID")

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS = New DataSet

        da.SelectCommand = cmd
        da.Fill(DS)
        'lblEngScript.Text = DS.Tables(0).Rows(0)(0)
        lblEngScript.Text = DS.Tables(0).Rows(0)(0)

    End Sub
End Class
