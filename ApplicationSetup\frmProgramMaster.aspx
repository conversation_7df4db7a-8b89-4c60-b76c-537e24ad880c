<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmProgramMaster.aspx.vb" Inherits="ApplicationSetup_frmProgramMaster" title="Home > Program Master > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebSchedule" TagPrefix="igsch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w11" CssClass="labelheading">Home</asp:LinkButton>&nbsp;&gt; Program Master &gt; Add New</TD></TR><TR><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD>Program Master</TD><TD style="WIDTH: 144px"><asp:TextBox id="txt_ProgramMasterName" runat="server" CssClass="mytext" Width="140px"></asp:TextBox></TD><TD>Sub Title</TD><TD><asp:TextBox id="txt_SubTitle" runat="server" CssClass="mytext" Width="140px"></asp:TextBox></TD><TD>Start Time </TD><TD><asp:TextBox id="wdc_starttime" runat="server" CssClass="mytext" Width="88px"></asp:TextBox></TD></TR><TR class="mytext"><TD style="HEIGHT: 26px">Program Abstract</TD><TD style="WIDTH: 144px; HEIGHT: 26px"><asp:TextBox id="txt_ProgramAbstract" runat="server" CssClass="mytext" Width="140px" Height="30px" TextMode="MultiLine"></asp:TextBox></TD><TD style="HEIGHT: 26px">Alias</TD><TD style="HEIGHT: 26px"><asp:TextBox id="txt_Alias" runat="server" __designer:wfdid="w3" CssClass="mytext" Width="140px" TextMode="MultiLine"></asp:TextBox></TD><TD style="HEIGHT: 26px">End Time</TD><TD style="HEIGHT: 26px"><asp:TextBox id="wdc_endtime" runat="server" CssClass="mytext" Width="88px"></asp:TextBox></TD></TR><TR class="mytext"><TD>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD style="WIDTH: 144px"></TD><TD>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD></TD><TD>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="392px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnChildRecord" runat="server" Text="Insert Child Record" CssClass="buttonA" Width="136px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w13" Font-Bold="True"></asp:Label></TD></TR><TR><TD><asp:GridView id="dg_ProgramMaster" runat="server" CssClass="gridContent" Width="100%" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25" AllowPaging="True"><Columns>
<asp:BoundField DataField="ProgramMasterID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="ProgramMasterName" HeaderText="Prog.Master Name" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="SubTitle" HeaderText="Sub Title"></asp:BoundField>
<asp:BoundField DataField="ProgramAbstract" HeaderText="Prog.Abstract"></asp:BoundField>
<asp:BoundField DataField="ProducedBy" HeaderText="Produced By"></asp:BoundField>
<asp:BoundField DataField="startime" HeaderText="Start Time"></asp:BoundField>
<asp:BoundField DataField="Endtime" HeaderText="End Time"></asp:BoundField>
<asp:BoundField DataField="Alias" HeaderText="Alias"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD><asp:Label id="lblAuditHistory" runat="server" __designer:wfdid="w3" CssClass="labelheading" Visible="False">Audit History - Program Master</asp:Label></TD></TR><TR><TD><asp:GridView id="dgAuditHistory" runat="server" __designer:wfdid="w4" CssClass="gridContent" Width="100%" AutoGenerateColumns="False" PageSize="25"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD><TABLE class="mytext" __designer:dtid="2814749767106564"><TBODY><TR __designer:dtid="2814749767106565"><TD style="WIDTH: 200px; HEIGHT: 26px" class="labelheading" __designer:dtid="2814749767106566">Search Program Master</TD><TD style="WIDTH: 100px; HEIGHT: 26px" __designer:dtid="2814749767106567"></TD></TR><TR __designer:dtid="2814749767106568"><TD style="WIDTH: 180px; HEIGHT: 13px" __designer:dtid="2814749767106569">Program Master&nbsp;Name</TD><TD style="WIDTH: 100px; HEIGHT: 13px" __designer:dtid="2814749767106570"></TD></TR><TR __designer:dtid="2814749767106571"><TD style="WIDTH: 100px; HEIGHT: 22px" __designer:dtid="2814749767106572"><asp:TextBox id="txt_SearchProgramMaster" runat="server" __designer:dtid="2814749767106573" __designer:wfdid="w4" CssClass="mytext" Width="184px"></asp:TextBox></TD><TD style="WIDTH: 100px; HEIGHT: 22px" __designer:dtid="2814749767106574"><asp:LinkButton id="lnkSearch" onclick="lnkSearch_Click" runat="server" __designer:dtid="2814749767106575" __designer:wfdid="w5">Search</asp:LinkButton></TD></TR></TBODY></TABLE></TD></TR><TR><TD><asp:TextBox id="txt_ProgramMasterID" runat="server" Width="48px" Visible="False"></asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:TextBox id="wdc_EndTime_vc" runat="server" Width="140px" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w91" Visible="False"></asp:Label> <asp:TextBox id="wdc_startTime_vc" runat="server" Width="140px" Visible="False"></asp:TextBox> <asp:TextBox id="txt_ProducedBy" runat="server" CssClass="mytext" Width="140px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE><cc1:MaskedEditExtender id="MaskedEditExtender1" runat="server" __designer:wfdid="w19" Mask="99:99:99" TargetControlID="wdc_starttime" MaskType="Time"></cc1:MaskedEditExtender> <cc1:MaskedEditExtender id="MaskedEditExtender2" runat="server" __designer:wfdid="w90" Mask="99:99:99" TargetControlID="wdc_endtime" MaskType="Time"></cc1:MaskedEditExtender> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w2" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

