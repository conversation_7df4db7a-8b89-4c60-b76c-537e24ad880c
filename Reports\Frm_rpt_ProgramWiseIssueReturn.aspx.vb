Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ProgramWiseIssueReturn
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                ChkProgram.Checked = True
                txtEmployee.Enabled = False
                txtProgrmaName.Enabled = False
                txtFromdate.Enabled = False
                txtToDate.Enabled = False
               
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ProgramWiseTapeIssue_New.rpt"

            Dim Prog As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String
            Dim ProgramChildID As Integer
            Dim ObjProgram As New BusinessFacade.ProgramChild()
            ObjProgram.ProgramChildName = txtProgrmaName.Text
            ProgramChildID = ObjProgram.GetProgramChildIDID_byProgramName(ObjProgram.ProgramChildName)

            If ChkProgram.Checked = True Then
                Prog = "-1"
            Else
                Prog = ProgramChildID
                'Prog = ddlProgram.SelectedValue
            End If

            If chkEmployee.Checked = True Then
                Emp = "All"
            Else
                Emp = txtEmployee.Text
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ProgramWiseTapeIssue_New.rpt&" + "@progname=" & Prog & "&@emp=" & Emp & "&@fromdate=" & FromDate & "&@todate=" & ToDate

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ProgramWiseTapeIssue_New.rpt&@progname=" + Prog + "&@emp=" + Emp + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_ProgramWiseIssueReturn.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = False Then
            txtEmployee.Enabled = True
        End If
        If chkEmployee.Checked = True Then
            txtEmployee.Enabled = False
        End If
    End Sub

    Protected Sub ChkProgram_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If ChkProgram.Checked = True Then
            txtProgrmaName.Enabled = False
        End If
        If ChkProgram.Checked = False Then
            txtProgrmaName.Enabled = True
        End If
    End Sub

    Protected Sub chkIgnoredate_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkIgnoredate.Checked = False Then
            txtFromdate.Enabled = True
            txtToDate.Enabled = True
        End If
        If chkIgnoredate.Checked = True Then
            txtFromdate.Enabled = False
            txtToDate.Enabled = False
        End If
    End Sub
End Class
