<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmTapeLirary.aspx.vb" Inherits="frmTapeLirary" title="Home > Tape Number" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_ContentType" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w1" CssClass="labelheading" Width="24px">Home</asp:LinkButton>&nbsp;&nbsp;&nbsp;&nbsp; &gt; Tape Number</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD>Tape Number</TD><TD><asp:TextBox id="txtTapeNumber" runat="server" CssClass="mytext" ReadOnly="True"></asp:TextBox></TD><TD style="WIDTH: 4px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD style="WIDTH: 60px">Tape Type</TD><TD style="WIDTH: 4px"><asp:DropDownList id="ddl_TapeType" runat="server" __designer:dtid="1970324836974623" __designer:wfdid="w1" CssClass="mytext" Width="152px">
                                </asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="608px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Update" CssClass="buttonA" Width="64px"></asp:Button>&nbsp; <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button> </TD></TR><TR><TD><asp:GridView id="dg" runat="server" CssClass="gridContent" Width="504px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25"><Columns>
<asp:BoundField DataField="TapeNumber" HeaderText="TapeNumber"></asp:BoundField>
<asp:BoundField ApplyFormatInEditMode="True" DataField="TapeType" HeaderText="TapeType"></asp:BoundField>
<asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD style="HEIGHT: 22px"></TD></TR><TR><TD style="HEIGHT: 22px"><TABLE style="WIDTH: 576px" class="mytext" __designer:dtid="2251799813685311"><TBODY><TR __designer:dtid="2251799813685312"><TD style="WIDTH: 200px; HEIGHT: 26px" class="labelheading" __designer:dtid="2251799813685313">Search Tape Number</TD><TD style="WIDTH: 100px; HEIGHT: 26px" __designer:dtid="2251799813685314"></TD></TR><TR __designer:dtid="2251799813685315"><TD style="WIDTH: 200px; HEIGHT: 13px" __designer:dtid="2251799813685316">Tape Number</TD><TD style="WIDTH: 100px; HEIGHT: 13px" __designer:dtid="2251799813685317"></TD></TR><TR __designer:dtid="2251799813685318"><TD style="WIDTH: 200px" __designer:dtid="2251799813685319"><asp:TextBox id="txt_Search" runat="server" __designer:dtid="2251799813685320" __designer:wfdid="w2" CssClass="mytext" Width="112px" BackColor="#FFE0C0"></asp:TextBox>&nbsp; <asp:LinkButton id="lnkSearch" onclick="lnkSearch_Click" runat="server" __designer:dtid="2251799813685322" __designer:wfdid="w3">Search</asp:LinkButton></TD><TD style="WIDTH: 100px" __designer:dtid="2251799813685321"></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w11" TargetControlID="bttnSave" ConfirmText="Do you want to Update !"></cc1:ConfirmButtonExtender> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w4" Visible="False"></asp:Label> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

