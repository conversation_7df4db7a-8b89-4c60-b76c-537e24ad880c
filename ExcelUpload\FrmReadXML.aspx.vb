Imports System.Xml

Partial Class ExcelUpload_FrmReadXML
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim textReader As New XmlTextReader("C:\DAMS.xml")
        ReadNewsLetterXml("C:\DAMS.xml")
    
    End Sub

    Public Sub ReadNewsLetterXml(ByVal path As String)
        Dim Document As New System.Xml.XmlDocument()
        Document.Load(path)

        If Document.HasChildNodes Then
            ' This line below will enable you to read your full xml file

            ' Uncomment the line below if you want to read article nodes

            'ReadXmlNodes(Document.SelectSingleNode("newsMessage/newsItem/contentSet/inlineXML/html/body/p").ChildNodes)


            ' Uncomment the line below if you want to read comic nodes
            ' ReadXmlNodes(Document.SelectSingleNode("newsletter/items[@value='comics']").ChildNodes);

            ReadXmlNodes(Document.ChildNodes)
        End If
    End Sub

    Public Sub ReadXmlNodes(ByVal nodeList As System.Xml.XmlNodeList)
        For Each n As System.Xml.XmlNode In nodeList
            If n.ChildNodes.Count = 0 Then
                Response.Write(n.InnerText)
                Response.Write("<br>")
            Else
                If n.Attributes.Count > 0 Then

                    Dim PTag As String = n.Attributes(0).Value
                    Response.Write("<b>" & PTag & "</b><br>")

                    'Response.Write("<b>" & n.Attributes(0).Value & "</b><br>")
                End If
                ReadXmlNodes(n.ChildNodes)
            End If
        Next
    End Sub

    'Public Sub ReadXmlNodes(ByVal nodeList As System.Xml.XmlNodeList)
    '    For Each n As System.Xml.XmlNode In nodeList
    '        If n.ChildNodes.Count = 0 Then
    '            Response.Write(n.InnerText)
    '            Response.Write("<br>")
    '        Else
    '            If n.Attributes.Count > 0 Then

    '                Response.Write("<b>" & n.Attributes(0).Value & "</b><br>")
    '            End If
    '            ReadXmlNodes(n.ChildNodes)
    '        End If
    '    Next
    'End Sub
End Class
