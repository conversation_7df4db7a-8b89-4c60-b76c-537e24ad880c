Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ListOfKeyWord
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                Chk_EntKW.Checked = True
                ' txtEntKeywords.Enabled = False

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ListofKeywordWiseReport_New2.rpt"
            Dim KeyWords As String = txtEntKeywords.Text

            Dim Slug1 As String = "null"
            Dim Slug2 As String = "null"
            Dim Slug3 As String = "null"
            Dim Slug4 As String = "null"
            Dim Slug5 As String = "null"



            Dim stringItems() As String = KeyWords.Split("~")
            Dim myArrayList As New ArrayList
            Dim k As Integer
            k = stringItems.Length


            If Chk_EntKW.Checked = True Then
                Slug1 = "All"
            ElseIf txtEntKeywords.Text.Length = 0 Then
                Slug1 = "null"
            Else

                If k <> 0 Then
                    Slug1 = stringItems(0).ToString
                Else
                    Slug1 = "null"
                End If
            End If


            If k > 1 And k < 3 Then
                Slug2 = stringItems(1).ToString
                Slug3 = "null"
                Slug4 = "null"
                Slug5 = "null"

            End If
            If k > 2 And k < 4 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = "null"
                Slug5 = "null"
            End If

            If k > 3 And k < 5 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = stringItems(3).ToString
                Slug5 = "null"
            End If

            If k > 4 And k < 6 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = stringItems(3).ToString
                Slug5 = stringItems(4).ToString
            End If


            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofKeywordWiseReport_New2.rpt&" + "@ReporterSlug1=" & Slug1 & "&@ReporterSlug2=" & Slug2 & "&@ReporterSlug3=" & Slug3 & "&@ReporterSlug4=" & Slug4 & "&@ReporterSlug5=" & Slug5 & "&@ArchiveType=-1"

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_New2.rpt&@ReporterSlug1=" + Slug1 + "&@ReporterSlug2=" + Slug2 + "&@ReporterSlug3=" + Slug3 + "&@ReporterSlug4=" + Slug4 + "&@ReporterSlug5=" + Slug5 + "&@ArchiveType=-1" + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)


        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub ChkKeywords_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        'If Chk_EntKW.Checked = True Then
        '    txtEntKeywords.Enabled = False
        'Else
        '    txtEntKeywords.Enabled = True
        'End If
    End Sub


    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If ddlContentType.SelectedValue = "Ent" Then
            Dim Slug1 As String = ""
            If chk_EntKW.Checked = True Then
                Slug1 = ""
            Else
                Slug1 = txtEntKeywords.Text
            End If

            Slug1 = txtEntKeywords.Text

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_Ent_old.rpt&@KeyWord1=" + Slug1 + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "

                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ListofKeywordWiseReport_Ent_old.rpt&@KeyWord1=" + Slug1 + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "

                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If

        Else
            Dim Slug1 As String = ""
            If chk_NewKW.Checked = True Then
                Slug1 = ""
            Else
                Slug1 = txtNewKeywords.Text
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue


            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_New2_old.rpt&@ReporterSlug1=" + Slug1 + "&@ReporterSlug2=" + Slug1 + "&@ReporterSlug3=" + Slug1 + "&@ReporterSlug4=" + Slug1 + "&@ReporterSlug5=" + Slug1 + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ListofKeywordWiseReport_New2.rpt&@ReporterSlug1=" + Slug1 + "&@ReporterSlug2=" + Slug1 + "&@ReporterSlug3=" + Slug1 + "&@ReporterSlug4=" + Slug1 + "&@ReporterSlug5=" + Slug1 + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If

        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_ListOfKeyWords.aspx"
        ObjSave.ReportName = "Other Report --> Q 2.How Can I View Keyword Wise Report?"
        ObjSave.SaveRecord()

        ''******************************************************''

        'If chk_EntKW.Checked = False And txtEntKeywords.Text <> "" And chk_NewKW.Checked = True And txtNewKeywords.Text = "" Then

        '    Dim Slug1 As String = ""
        '    Slug1 = txtEntKeywords.Text
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_Ent.rpt&@KeyWord1=" + Slug1 + "', 'mywindow'); "

        '    script = script + "}</script>"

        '    Page.RegisterClientScriptBlock("test", script)
        'ElseIf chk_EntKW.Checked = True And txtEntKeywords.Text = "" And chk_NewKW.Checked = True And txtNewKeywords.Text = "" Then

        '    Dim Slug1 As String = ""
        '    Slug1 = ""
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_Ent.rpt&@KeyWord1=" + Slug1 + "', 'mywindow'); "

        '    script = script + "}</script>"

        '    Page.RegisterClientScriptBlock("test", script)

        'ElseIf chk_NewKW.Checked = False And txtNewKeywords.Text <> "" And chk_EntKW.Checked = True And txtEntKeywords.Text = "" Then

        '    Dim Slug1 As String = ""
        '    Slug1 = txtNewKeywords.Text
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_New2.rpt&@KeyWord1=" + Slug1 + "', 'mywindow'); "
        '    script = script + "}</script>"

        '    Page.RegisterClientScriptBlock("test", script)
        'ElseIf chk_NewKW.Checked = True And txtNewKeywords.Text = "" And chk_EntKW.Checked = True And txtEntKeywords.Text = "" Then

        '    Dim Slug1 As String = ""
        '    Slug1 = ""
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onload=function OpenReport() {"
        '    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_New2.rpt&@KeyWord1=" + Slug1 + "', 'mywindow'); "
        '    script = script + "}</script>"

        '    Page.RegisterClientScriptBlock("test", script)

        'End If

        '  If ddlContentType.SelectedItem.Text = "Ent" Then
        '    ''''''''''''''''''''''''''''''''''''
        '    Dim Description As String = txtEntKeywords.Text


        '    Dim Slug1 As String = ""
        '    Dim Slug2 As String = ""
        '    Dim Slug3 As String = ""
        '    Dim Slug4 As String = ""
        '    Dim Slug5 As String = ""


        '    ''*****************************''
        '    If chk_EntKW.Checked = True Then
        '        Slug1 = "All"
        '        Slug2 = "All"
        '        Slug3 = "All"
        '        Slug4 = "All"
        '        Slug5 = "All"
        '    Else

        '        Dim stringItems() As String = Description.Split("~")
        '        Dim myArrayList As New ArrayList
        '        Dim k As Integer
        '        k = stringItems.Length

        '        If txtEntKeywords.Text = "" Then
        '            Slug1 = ""
        '            Slug1 = ""
        '            Slug2 = ""
        '            Slug3 = ""
        '            Slug4 = ""
        '            Slug5 = ""
        '        End If

        '        If k <> 0 Then
        '            Slug1 = stringItems(0).ToString
        '        End If


        '        If k = 1 Then
        '            Slug2 = stringItems(0).ToString
        '            Slug3 = stringItems(0).ToString
        '            Slug4 = stringItems(0).ToString
        '            Slug5 = stringItems(0).ToString
        '        End If

        '        If k > 1 And k < 3 Then
        '            Slug2 = stringItems(1).ToString
        '            Slug3 = stringItems(1).ToString
        '            Slug4 = stringItems(1).ToString
        '            Slug5 = stringItems(1).ToString

        '        End If
        '        If k > 2 And k < 4 Then
        '            Slug2 = stringItems(1).ToString
        '            Slug3 = stringItems(2).ToString
        '            Slug4 = stringItems(2).ToString
        '            Slug5 = stringItems(2).ToString
        '        End If

        '        If k > 3 And k < 5 Then
        '            Slug2 = stringItems(1).ToString
        '            Slug3 = stringItems(2).ToString
        '            Slug4 = stringItems(3).ToString
        '            Slug5 = stringItems(3).ToString
        '        End If

        '        If k > 4 And k < 6 Then
        '            Slug2 = stringItems(1).ToString
        '            Slug3 = stringItems(2).ToString
        '            Slug4 = stringItems(3).ToString
        '            Slug5 = stringItems(4).ToString
        '        End If

        '    End If

        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onload=function OpenReport() {"
        'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_Ent.rpt&@KeyWord1=" + Slug1 + "&@KeyWord2=" + Slug2 + "&@KeyWord3=" + Slug3 + "&@KeyWord4=" + Slug4 + "&@KeyWord5=" + Slug5 + "', 'mywindow'); "

        'script = script + "}</script>"

        'Page.RegisterClientScriptBlock("test", script)
        'Else
        'Dim Description As String = txtEntKeywords.Text


        'Dim Slug1 As String = ""
        'Dim Slug2 As String = ""
        'Dim Slug3 As String = ""
        'Dim Slug4 As String = ""
        'Dim Slug5 As String = ""


        ' ''*****************************''
        'If chk_EntKW.Checked = True Then
        '    Slug1 = ""
        '    Slug2 = ""
        '    Slug3 = ""
        '    Slug4 = ""
        '    Slug5 = ""
        'Else

        '    Dim stringItems() As String = Description.Split("~")
        '    Dim myArrayList As New ArrayList
        '    Dim k As Integer
        '    k = stringItems.Length

        '    If txtEntKeywords.Text = "" Then
        '        Slug1 = ""
        '        Slug1 = ""
        '        Slug2 = ""
        '        Slug3 = ""
        '        Slug4 = ""
        '        Slug5 = ""
        '    End If

        '    If k <> 0 Then
        '        Slug1 = stringItems(0).ToString
        '    End If


        '    If k = 1 Then
        '        Slug2 = stringItems(0).ToString
        '        Slug3 = stringItems(0).ToString
        '        Slug4 = stringItems(0).ToString
        '        Slug5 = stringItems(0).ToString
        '    End If

        '    If k > 1 And k < 3 Then
        '        Slug2 = stringItems(1).ToString
        '        Slug3 = stringItems(1).ToString
        '        Slug4 = stringItems(1).ToString
        '        Slug5 = stringItems(1).ToString

        '    End If
        '    If k > 2 And k < 4 Then
        '        Slug2 = stringItems(1).ToString
        '        Slug3 = stringItems(2).ToString
        '        Slug4 = stringItems(2).ToString
        '        Slug5 = stringItems(2).ToString
        '    End If

        '    If k > 3 And k < 5 Then
        '        Slug2 = stringItems(1).ToString
        '        Slug3 = stringItems(2).ToString
        '        Slug4 = stringItems(3).ToString
        '        Slug5 = stringItems(3).ToString
        '    End If

        '    If k > 4 And k < 6 Then
        '        Slug2 = stringItems(1).ToString
        '        Slug3 = stringItems(2).ToString
        '        Slug4 = stringItems(3).ToString
        '        Slug5 = stringItems(4).ToString
        '    End If

        'End If

        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onload=function OpenReport() {"
        'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofKeywordWiseReport_New2.rpt&@KeyWord1=" + Slug1 + "&@KeyWord2=" + Slug2 + "&@KeyWord3=" + Slug3 + "&@KeyWord4=" + Slug4 + "&@KeyWord5=" + Slug5 + "', 'mywindow'); "
        'script = script + "}</script>"

        'Page.RegisterClientScriptBlock("test", script)
        'End If



    End Sub

    Protected Sub chk_NewKW_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        'If chk_NewKW.Checked = True Then
        '    txtNewKeywords.Enabled = False
        'Else
        '    txtNewKeywords.Enabled = True
        'End If
    End Sub
End Class
