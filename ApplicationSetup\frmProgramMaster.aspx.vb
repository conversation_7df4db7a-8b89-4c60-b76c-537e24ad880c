
Partial Class ApplicationSetup_frmProgramMaster
    Inherits System.Web.UI.Page
    Dim I As Integer
    Dim dt As New Data.DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            ViewState("Search") = Nothing

            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            FillGrid()
        End If
    End Sub


    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.ProgramMaster().IsExists_ProgramMaster(txt_ProgramMasterName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : ProgramMaster already Exists !"
        Else
            If txt_ProgramMasterID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        ClearAuditHistory()
    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserName As String
        Dim objUserName As New BusinessFacade.Employee()
        objUserName.EmployeeID = UserID
        UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)


        If txt_ProgramMasterName.Text = "" Then
            lblErr.Text = "Please Enter Program Master Name!"
        Else
            Dim ObjProgramMaster As New BusinessFacade.ProgramMaster()
            ObjProgramMaster.ProgramMasterName = txt_ProgramMasterName.Text

            If txt_SubTitle.Text = "" Then
                ObjProgramMaster.SubTitle = " "
            Else
                ObjProgramMaster.SubTitle = txt_SubTitle.Text
            End If

            If txt_ProgramAbstract.Text = "" Then
                ObjProgramMaster.ProgramAbstract = " "
            Else
                ObjProgramMaster.ProgramAbstract = txt_ProgramAbstract.Text
            End If

            ObjProgramMaster.ProducedBy = UserName

            ObjProgramMaster.startTime_vc = wdc_startTime_vc.Text
            ObjProgramMaster.EndTime_vc = wdc_EndTime_vc.Text
            ObjProgramMaster.startime = wdc_starttime.Text
            ObjProgramMaster.Endtime = wdc_endtime.Text

            If txt_Alias.Text = "" Then
                ObjProgramMaster.Alias = " "
            Else
                ObjProgramMaster.Alias = txt_Alias.Text
            End If
            ObjProgramMaster.UserID = UserID
            ObjProgramMaster.SaveRecord()
            lblErr.Text = "Record has been Saved!!"
            FillGrid()
        End If

    End Sub

    Private Sub UpdateRecord()
        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        Dim ObjProgramMaster As New BusinessFacade.ProgramMaster()
        ObjProgramMaster.ProgramMasterID = txt_ProgramMasterID.Text
        ObjProgramMaster.ProgramMasterName = txt_ProgramMasterName.Text
        ObjProgramMaster.SubTitle = txt_SubTitle.Text
        ObjProgramMaster.ProgramAbstract = txt_ProgramAbstract.Text
        ObjProgramMaster.ProducedBy = UserID
        ObjProgramMaster.startTime_vc = wdc_startTime_vc.Text
        ObjProgramMaster.EndTime_vc = wdc_EndTime_vc.Text
        ObjProgramMaster.startime = wdc_starttime.Text
        ObjProgramMaster.Endtime = wdc_endtime.Text
        ObjProgramMaster.Alias = txt_Alias.Text
        ObjProgramMaster.UserID = UserID
        ObjProgramMaster.UpdateRecord()
        lblErr.Text = "Record has been Updated!!"
        If Not ViewState("Search") Is Nothing Then
            fillgrid_search()
        Else
            FillGrid()
        End If
    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.ProgramMaster().GetRecords()
        dg_ProgramMaster.DataSource() = dt
        dg_ProgramMaster.Columns(0).Visible = True
        dg_ProgramMaster.DataBind()
        dg_ProgramMaster.Columns(0).Visible = False
        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_ProgramMaster.SelectedIndexChanged
        I = dg_ProgramMaster.SelectedIndex.ToString
        txt_ProgramMasterID.Text = Convert.ToInt32(dg_ProgramMaster.Rows(I).Cells(1).Text)
        txt_ProgramMasterName.Text = dg_ProgramMaster.Rows(I).Cells(2).Text
        txt_SubTitle.Text = dg_ProgramMaster.Rows(I).Cells(3).Text
        txt_ProgramAbstract.Text = dg_ProgramMaster.Rows(I).Cells(4).Text
        txt_ProducedBy.Text = dg_ProgramMaster.Rows(I).Cells(5).Text
        wdc_starttime.Text = dg_ProgramMaster.Rows(I).Cells(6).Text
        wdc_endtime.Text = dg_ProgramMaster.Rows(I).Cells(7).Text
        txt_Alias.Text = dg_ProgramMaster.Rows(I).Cells(8).Text
        dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.ProgramMaster()
        ObjAudit.ProgramMasterID = txt_ProgramMasterID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_ProgramMaster()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_ProgramMasterID.Text = "" Then
                lblErr.Text = "Please Select Record!!"
            Else
                Dim ObjProgramMaster As New BusinessFacade.ProgramMaster()
                ObjProgramMaster.ProgramMasterID = txt_ProgramMasterID.Text
                ObjProgramMaster.DeleteRecord(ObjProgramMaster.ProgramMasterID)
                FillGrid()
                lblErr.Text = "Record has been Deleted!!"
                clrscr()
                dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Program Master is Already in Used !"
            clrscr()
            dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_ProgramMasterID.Text = String.Empty
        txt_ProgramMasterName.Text = String.Empty
        txt_SubTitle.Text = String.Empty
        txt_ProgramAbstract.Text = String.Empty
        txt_ProducedBy.Text = String.Empty
        txt_Alias.Text = String.Empty
       
    End Sub

    Protected Sub bttnChildRecord_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnChildRecord.Click
        If txt_ProgramMasterID.Text = "" Then
            lblErr.Text = "Please Select Master Record!!"
        Else
            Dim K As Integer
            K = txt_ProgramMasterID.Text
            Response.Redirect("frmProgramChild.aspx?ProgramMasterID=" & K)
        End If

    End Sub

    Protected Sub dg_ProgramMaster_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_ProgramMaster.PageIndexChanging
        dg_ProgramMaster.PageIndex = e.NewPageIndex()
        dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        If txt_SearchProgramMaster.Text = "" Then
            FillGrid()
        Else
            fillgrid_search()
        End If
        clrscr()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_ProgramMaster.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        txt_SearchProgramMaster.Text = String.Empty
        ViewState("Search") = Nothing
        FillGrid()
        ClearAuditHistory()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

   
    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        ViewState("Search") = Nothing
        fillgrid_search()
    End Sub

    Private Sub fillgrid_search()
        Dim objSearch As New BusinessFacade.ProgramMaster()
        objSearch.ProgramMasterName = txt_SearchProgramMaster.Text
        dt = objSearch.GetSingleProgramMasterRecord(objSearch.ProgramMasterName)

        If dt.Rows(0).Item(0).ToString <> "0" Then
            dg_ProgramMaster.DataSource = dt
            dg_ProgramMaster.Columns(0).Visible = True
            dg_ProgramMaster.DataBind()
            dg_ProgramMaster.Columns(0).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
        Else
            lblErr.Text = "Search Results: Program Master is not valid !!"
        End If
        ViewState("Search") = dt
    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
