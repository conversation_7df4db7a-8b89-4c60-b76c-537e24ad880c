Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_GrandOutStandingTapes
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkEmployee.Checked = True
                TxtEmployee.Enabled = False
                chkIgnoredate.Checked = True

                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub


    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String

            Dept = ddlDepartment.SelectedValue

            If chkEmployee.Checked = True Then
                Emp = "-1"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    Emp = EmployeeID.ToString
                Else
                    Emp = "-1"
                End If

            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            Dim Chart As String
            Chart = Me.ddlChart.SelectedValue

            If ddlPDF.SelectedValue = "PDF" Then

                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=GrandOutStandingTapes.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Graph=" + Chart + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)

            Else

                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_Excel.aspx?ReportName=GrandOutStandingTapes.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Graph=" + Chart + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)

            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_GrandOutStandingTapes.aspx"
            ObjSave.ReportName = "Other Reports --> Q 8. How can I View Sum of all Outstanding Tapes Emplotee/Department wise ?"
            ObjSave.SaveRecord()

            ''******************************************************''
        Catch ex As Exception
            Throw
        End Try

    End Sub



    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub

End Class
