
Partial Class ApplicationSetup_frmStockStatus
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_StockStatusID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_StockStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_StockStatus.Text = "" Then
            lblErr.Text = "Please insert Stock Status!!"
        Else
            Dim ObjStockStatus As New BusinessFacade.StockStatus()
            ObjStockStatus.StockStatus = txt_StockStatus.Text
            ObjStockStatus.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjStockStatus As New BusinessFacade.StockStatus()
        ObjStockStatus.StockStatusID = txt_StockStatusID.Text
        ObjStockStatus.StockStatus = txt_StockStatus.Text
        ObjStockStatus.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_StockStatus.DataSource() = New BusinessFacade.StockStatus().GetRecords()
        dg_StockStatus.DataBind()
        'dg_StockStatus.Columns(0).Visible = False
    End Sub

    Protected Sub dg_StockStatus_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_StockStatus.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_StockStatus.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_StockStatus.SelectedIndex.ToString
        txt_StockStatusID.Text = Convert.ToInt32(dg_StockStatus.Rows(I).Cells(1).Text)
        txt_StockStatus.Text = dg_StockStatus.Rows(I).Cells(2).Text
        dg_StockStatus.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        If txt_StockStatusID.Text = "" Then
            lblErr.Text = "Please Select Stock Status !!"
        Else
            Dim ObjStockStatus As New BusinessFacade.StockStatus()
            ObjStockStatus.StockStatusID = txt_StockStatusID.Text
            ObjStockStatus.DeleteRecord(ObjStockStatus.StockStatusID)
            FillGrid()
            Clrscr()
            lblErr.Text = "Record has been Deleted!!"
            dg_StockStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End If
    End Sub

    Private Sub Clrscr()
        txt_StockStatus.Text = String.Empty
        txt_StockStatusID.Text = String.Empty
    End Sub

    'Protected Sub dg_StockStatus_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_StockStatus.PageIndexChanging
    '    dg_StockStatus.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_StockStatus.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
