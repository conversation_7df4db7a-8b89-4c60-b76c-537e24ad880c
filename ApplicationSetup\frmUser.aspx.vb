
Partial Class ApplicationSetup_frmUser
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_EmployeeName.DataSource = New BusinessFacade.Employee().GetRecords()
        ddl_EmployeeName.DataTextField = "EmployeeName"
        ddl_EmployeeName.DataValueField = "EmployeeID"
        ddl_EmployeeName.DataBind()
        ddl_EmployeeName.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_UserID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_User.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()

    End Sub

    Private Sub SaveRecord()
        If ddl_EmployeeName.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Employee Name!!"
        Else
            Dim ObjUser As New BusinessFacade.User()
            ObjUser.EmployeeID = ddl_EmployeeName.SelectedValue
            ObjUser.SaveRecord()
            FillGrid()
        End If

    End Sub

    Private Sub UpdateRecord()
        Dim ObjUser As New BusinessFacade.User()
        ObjUser.UserID = txt_UserID.Text
        ObjUser.EmployeeID = Convert.ToInt32(ddl_EmployeeName.SelectedValue)
        ObjUser.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_User.DataSource() = New BusinessFacade.User().GetRecords()
        dg_User.DataBind()
        dg_User.Columns(0).Visible = False
        dg_User.Columns(1).Visible = False
    End Sub

    'Protected Sub dg_User_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_User.RowCreated
    '    e.Row.Cells(1).Visible = False
    '    e.Row.Cells(2).Visible = False
    'End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_User.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_User.SelectedIndex.ToString
        txt_UserID.Text = Convert.ToInt32(dg_User.Rows(I).Cells(1).Text)
        ddl_EmployeeName.SelectedValue = dg_User.Rows(I).Cells(2).Text
        dg_User.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        If txt_UserID.Text = "" Then
            lblErr.Text = "Please Select Record!!"
        Else
            Dim ObjUser As New BusinessFacade.User()
            ObjUser.UserID = txt_UserID.Text
            ObjUser.DeleteRecord(ObjUser.UserID)
            FillGrid()
            clrscr()
            lblErr.Text = "Record has been Deleted"
        End If
    End Sub

    Private Sub clrscr()
        txt_UserID.Text = String.Empty
        ddl_EmployeeName.SelectedIndex = "0"

    End Sub

    Protected Sub dg_User_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_User.PageIndexChanging
        dg_User.PageIndex = e.NewPageIndex()
        FillGrid()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_User.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
