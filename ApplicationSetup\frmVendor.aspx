<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmVendor.aspx.vb" Inherits="ApplicationSetup_frmVendor" title="Home > Vendor > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w20">Home</asp:LinkButton> &gt; Vendor &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD>Vendor Name</TD><TD style="WIDTH: 194px"><asp:TextBox id="txt_VendorName" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></TD><TD>Vendor Address</TD><TD><asp:TextBox id="txt_VendorAddress" runat="server" CssClass="mytext" Width="176px" TextMode="MultiLine" Height="32px"></asp:TextBox></TD></TR><TR class="mytext"><TD>City Name</TD><TD style="WIDTH: 194px"><asp:DropDownList id="ddl_City" runat="server" CssClass="mytext" Width="184px">
                            </asp:DropDownList></TD><TD>Phone</TD><TD><asp:TextBox id="txt_Phone" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></TD></TR><TR class="mytext"><TD>Mobile</TD><TD style="WIDTH: 194px"><asp:TextBox id="txt_Mobile" runat="server" CssClass="mytext" Width="176px"></asp:TextBox></TD><TD></TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="544px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:Label id="lblMsg" runat="server" ForeColor="White" __designer:wfdid="w71" Width="160px" Font-Bold="True" Visible="False">Do You want to Delete :</asp:Label>&nbsp;&nbsp;<asp:Button id="btnYes" runat="server" Text="Yes" __designer:dtid="281496451547140" CssClass="buttonA" __designer:wfdid="w69" Visible="False"></asp:Button> <asp:Button id="btnNo" runat="server" Text="No" __designer:dtid="281496451547141" CssClass="buttonA" __designer:wfdid="w70" Width="32px" Visible="False"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_Vendor" runat="server" CssClass="gridContent" Width="816px" AllowPaging="True" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="VendorID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="VendorName" HeaderText="Vendor Name" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="VendorAddress" HeaderText="Vendor Address"></asp:BoundField>
<asp:BoundField DataField="CityID" Visible="False">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="CityName" HeaderText="City Name"></asp:BoundField>
<asp:BoundField DataField="Phone" HeaderText="Phone"></asp:BoundField>
<asp:BoundField DataField="Mobile" HeaderText="Mobile"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD><asp:TextBox id="txt_VendorID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

