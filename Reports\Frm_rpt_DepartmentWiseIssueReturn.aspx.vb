Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_DepartmentWiseIssueReturn
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkDepartment.Checked = True
                chkIgnoredate.Checked = True
                ddlDepartment.Enabled = False

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ListofTapeReturns_New.rpt"

            Dim Dept As String
            Dim FromDate As String
            Dim ToDate As String


            If chkDepartment.Checked = True Then
                Dept = "-1"
            Else
                Dept = ddlDepartment.SelectedValue
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofTapeReturns_New.rpt&" + "@deptName=" & Dept & "&@fromdate=" & FromDate & "&@todate=" & ToDate

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofTapeReturns_New.rpt&@deptName=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)


            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_DepartmentWiseIssueReturn.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub chkDepartment_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkDepartment.Checked = True Then
            ddlDepartment.Enabled = False
        Else
            ddlDepartment.Enabled = True
        End If
    End Sub

    Protected Sub chkIgnoredate_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)

    End Sub
End Class
