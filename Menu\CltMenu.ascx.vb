Imports System.Data
Imports System.Data.SqlClient
Imports Daab = Microsoft.ApplicationBlocks.Data

Partial Class Menu_CltMenu
    Inherits System.Web.UI.UserControl


    Dim RootDT As New DataTable
    Dim ChildDT As New DataTable
    Dim SubChildDT As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            'Dim STRCON As String = "server=CCS-SERVER\ROSHNI;Database=Security_Manager;uid=sa;pwd=**********"
            Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

            Dim Con As SqlConnection = New SqlConnection(STRCON)
            RootDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select id,menu_name,menu_webAddress from application_menus where application_id = 25 and menu_isactive =1 and menu_master=000 Order by TreeSortOrder").Tables(0)
            'RootDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and menumaster=000 and LoginID = '" + Request.Cookies("userinfo")("username") + "'Order by TreeSortOrder").Tables(0)
            Dim item As MenuItem
            Dim mitem As New MenuItem

            Dim i As Integer = 0
            For i = 0 To RootDT.Rows.Count - 1
                item = New MenuItem
                item.Text = RootDT.Rows(i)("Menu_Name").ToString
                item.Value = RootDT.Rows(i)("ID").ToString
                getChild(RootDT.Rows(i)("ID").ToString, item)
                Menu.Items.Add(item)
            Next
        End If
    End Sub

    Public Sub getChild(ByVal id As String, ByVal root As MenuItem)
        'Dim STRCON As String = "server=CCS-SERVER\ROSHNI;Database=Security_Manager;uid=sa;pwd=**********"
        Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

        Dim Con As SqlConnection = New SqlConnection(STRCON)

        '  ChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select id,menu_name,menu_webAddress from application_menus where application_id = 25 and menu_isactive =1 and menu_master=" & id + " Order by TreeSortOrder").Tables(0)
        ChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and LoginID = '" + Request.Cookies("userinfo")("username") + "' and menuParentID = " & id + " Order by TreeSortOrder").Tables(0)
        Dim rot As MenuItem
        Dim i As Integer = 0
        For i = 0 To ChildDT.Rows.Count - 1
            rot = New MenuItem
            rot.Text = ChildDT.Rows(i)("Menu_Name").ToString
            rot.Value = ChildDT.Rows(i)("ID").ToString
            rot.NavigateUrl = ChildDT.Rows(i)("menu_webAddress").ToString
            getSubChild(ChildDT.Rows(i)("ID").ToString, rot)

            root.ChildItems.Add(rot)
        Next
    End Sub

    Public Sub getSubChild(ByVal id As String, ByVal root As MenuItem)
        ' Dim STRCON As String = "server=CCS-SERVER\ROSHNI;Database=Security_Manager;uid=sa;pwd=**********"
        Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

        Dim Con As SqlConnection = New SqlConnection(STRCON)

        'SubChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select id,menu_name,menu_webAddress from application_menus where application_id = 25 and menu_isactive =1 and menu_master=" & id + " Order by TreeSortOrder").Tables(0)
        SubChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and LoginID = '" + Request.Cookies("userinfo")("username") + "' and menuParentID = " & id + " Order by TreeSortOrder").Tables(0)

        Dim subrot As MenuItem
        Dim i As Integer = 0
        For i = 0 To SubChildDT.Rows.Count - 1
            subrot = New MenuItem
            subrot.Text = SubChildDT.Rows(i)("Menu_Name").ToString
            subrot.Value = SubChildDT.Rows(i)("ID").ToString
            subrot.NavigateUrl = SubChildDT.Rows(i)("menu_webAddress").ToString
            root.ChildItems.Add(subrot)
        Next
    End Sub
End Class









'Imports System.Data
'Imports System.Data.SqlClient
'Imports Daab = Microsoft.ApplicationBlocks.Data

'Partial Class Menu_CltMenu
'    Inherits System.Web.UI.UserControl


'    Dim RootDT As DataTable
'    Dim ChildDT As DataTable
'    Dim SubChildDT As DataTable

'    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
'        If Not Page.IsPostBack Then

'            RootDT = DirectCast(Cache.[Get]("RootDT"), DataTable)
'            ChildDT = DirectCast(Cache.[Get]("ChildDT"), DataTable)
'            SubChildDT = DirectCast(Cache.[Get]("SubChildDT"), DataTable)

'            Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

'            Dim Con As SqlConnection = New SqlConnection(STRCON)

'            If RootDT Is Nothing Then

'                RootDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and menumaster=000 and LoginID = '" + Request.Cookies("userinfo")("username") + "'Order by TreeSortOrder").Tables(0)
'                Cache.Insert("RootDT", RootDT)
'            End If


'            ''***********************************''
'            ''***********************************''
'            ''***********************************''
'            If ChildDT Is Nothing Then
'                ChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuParentID, menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and LoginID = '" + Request.Cookies("userinfo")("username") + "'" + " Order by TreeSortOrder").Tables(0)
'                SubChildDT = ChildDT
'                Cache.Insert("ChildDT", ChildDT)
'                Cache.Insert("SubChildDT", SubChildDT)
'            End If


'            ''***********************************''

'            Dim item As MenuItem
'            Dim mitem As New MenuItem

'            Dim i As Integer = 0
'            For i = 0 To RootDT.Rows.Count - 1
'                item = New MenuItem
'                item.Text = RootDT.Rows(i)("Menu_Name").ToString
'                item.Value = RootDT.Rows(i)("ID").ToString
'                getChild(RootDT.Rows(i)("ID").ToString, item)
'                menu.Items.Add(item)
'            Next
'        End If
'    End Sub

'    Public Sub getChild(ByVal id As String, ByVal root As MenuItem)

'        Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

'        Dim Con As SqlConnection = New SqlConnection(STRCON)

'        ' ChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and LoginID = '" + Request.Cookies("userinfo")("username") + "' and menuParentID = " & id + " Order by TreeSortOrder").Tables(0)
'        Dim rot As MenuItem
'        Dim i As Integer = 0
'        For i = 0 To ChildDT.Rows.Count - 1
'            If ChildDT.Rows(i)("menuParentID").ToString = id Then
'                rot = New MenuItem
'                rot.Text = ChildDT.Rows(i)("Menu_Name").ToString
'                rot.Value = ChildDT.Rows(i)("ID").ToString
'                rot.NavigateUrl = ChildDT.Rows(i)("menu_webAddress").ToString
'                getSubChild(ChildDT.Rows(i)("ID").ToString, rot)

'                root.ChildItems.Add(rot)
'            End If

'        Next
'    End Sub

'    Public Sub getSubChild(ByVal id As String, ByVal root As MenuItem)

'        Dim STRCON As String = System.Configuration.ConfigurationSettings.AppSettings("CCSSerevrConnectionString")

'        Dim Con As SqlConnection = New SqlConnection(STRCON)

'        ' SubChildDT = Daab.SqlHelper.ExecuteDataset(Con, CommandType.Text, "select menuCode id, MenuName menu_name, MenuLink menu_webAddress, * from dbo.VU_UserApplicationMenus where applicationid = 25 and menuisShow =1 and LoginID = '" + Request.Cookies("userinfo")("username") + "' and menuParentID = " & id + " Order by TreeSortOrder").Tables(0)

'        Dim subrot As MenuItem
'        Dim i As Integer = 0
'        For i = 0 To SubChildDT.Rows.Count - 1
'            If SubChildDT.Rows(i)("menuParentID").ToString = id Then
'                subrot = New MenuItem
'                subrot.Text = SubChildDT.Rows(i)("Menu_Name").ToString
'                subrot.Value = SubChildDT.Rows(i)("ID").ToString
'                subrot.NavigateUrl = SubChildDT.Rows(i)("menu_webAddress").ToString
'                root.ChildItems.Add(subrot)
'            End If

'        Next
'    End Sub
'End Class



