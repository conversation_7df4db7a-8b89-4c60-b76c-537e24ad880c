
Partial Class ApplicationSetup_frmTapeType
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            FillGrid()
        End If

        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onbeforeunload = function() {"
        'script = script + "return ""Closing the page now may result in data loss."";"
        ''return "Closing the page now may result in data loss.";
        ''Dim I As String = "return ""Closing the page now may result in data loss."";"
        'script = script + "}</script>"

        'Page.RegisterClientScriptBlock("test", script)
    End Sub


    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.TapeType().IsExists_TapeType(txt_TapeType.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : TapeType already Exists !"
        Else
            If txt_TapeTypeID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_TapeType.Text = "" Then
            lblErr.Text = "Please Insert Tape Type!!"
        ElseIf ddl_IsActive.SelectedIndex = "0" Then
            lblErr.Text = "Please Select IsActive!!"
        Else
            Dim ObjTapeType As New BusinessFacade.TapeType()
            ObjTapeType.TapeType = txt_TapeType.Text
            ObjTapeType.Description = txt_Description.Text
            ObjTapeType.EntryDate = WDC_EntryDate.Text
            ObjTapeType.UOM = txt_UOM.Text

            If txt_OpeningBal.Text = "" Then
                ObjTapeType.OpeningBal = 0
            Else
                ObjTapeType.OpeningBal = txt_OpeningBal.Text
            End If

            If txt_CurrentBal.Text = "" Then
                ObjTapeType.CurrentBal = 0
            Else
                ObjTapeType.CurrentBal = txt_CurrentBal.Text
            End If

            If txt_ReOrderLevel.Text = "" Then
                ObjTapeType.ReOrderLevel = 0
            Else
                ObjTapeType.ReOrderLevel = txt_ReOrderLevel.Text
            End If
            If txt_Cost.Text = "" Then
                ObjTapeType.Cost = 0
            Else
                ObjTapeType.Cost = txt_Cost.Text
            End If

            ObjTapeType.IsActive = ddl_IsActive.SelectedValue
            ObjTapeType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
        

    End Sub

    Private Sub UpdateRecord()
        Dim ObjTapeType As New BusinessFacade.TapeType()
        ObjTapeType.TapeTypeID = txt_TapeTypeID.Text
        ObjTapeType.TapeType = txt_TapeType.Text
        ObjTapeType.Description = txt_Description.Text
        ObjTapeType.EntryDate = WDC_EntryDate.Text
        ObjTapeType.UOM = txt_UOM.Text

        If txt_OpeningBal.Text = "&nbsp;" Or txt_OpeningBal.Text = "" Then
            ObjTapeType.OpeningBal = 0
        Else
            ObjTapeType.OpeningBal = txt_OpeningBal.Text
        End If

        If txt_CurrentBal.Text = "&nbsp;" Or txt_CurrentBal.Text = "" Then
            ObjTapeType.CurrentBal = 0
        Else
            ObjTapeType.CurrentBal = txt_CurrentBal.Text
        End If

        If txt_ReOrderLevel.Text = "&nbsp;" Or txt_ReOrderLevel.Text = "" Then
            ObjTapeType.ReOrderLevel = 0
        Else
            ObjTapeType.ReOrderLevel = txt_ReOrderLevel.Text
        End If


        ObjTapeType.Cost = txt_Cost.Text
        ObjTapeType.IsActive = ddl_IsActive.SelectedValue
        ObjTapeType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_TapeType.DataSource() = New BusinessFacade.TapeType().GetRecords()
        dg_TapeType.Columns(0).Visible = True
        dg_TapeType.DataBind()
        dg_TapeType.Columns(0).Visible = False
    End Sub


    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_TapeType.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_TapeType.SelectedIndex.ToString
        txt_TapeTypeID.Text = Convert.ToInt32(dg_TapeType.Rows(I).Cells(1).Text)
        txt_TapeType.Text = dg_TapeType.Rows(I).Cells(2).Text
        txt_Description.Text = dg_TapeType.Rows(I).Cells(3).Text
        WDC_EntryDate.Text = Convert.ToString(dg_TapeType.Rows(I).Cells(4).Text)
        txt_UOM.Text = dg_TapeType.Rows(I).Cells(5).Text
        txt_OpeningBal.Text = dg_TapeType.Rows(I).Cells(6).Text
        txt_CurrentBal.Text = dg_TapeType.Rows(I).Cells(7).Text
        txt_ReOrderLevel.Text = dg_TapeType.Rows(I).Cells(8).Text
        txt_Cost.Text = dg_TapeType.Rows(I).Cells(9).Text

        If dg_TapeType.Rows(I).Cells(10).Text = "False" Then
            ddl_IsActive.SelectedIndex = "1"
        Else
            ddl_IsActive.SelectedIndex = "0"
        End If

        'ddl_IsActive.SelectedIndex = ddl_IsActive.Items.FindByValue(dg_TapeType.Rows(I).Cells(10).Text).Value
        dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_TapeTypeID.Text = "" Then
                lblErr.Text = "Please select Tape Type!!"
            Else
                Dim ObjTapeType As New BusinessFacade.TapeType()
                ObjTapeType.TapeTypeID = txt_TapeTypeID.Text
                ObjTapeType.DeleteRecord(ObjTapeType.TapeTypeID)
                FillGrid()
                Clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This TapeType is Already in Used !"
            Clrscr()
            dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub Clrscr()
        txt_TapeType.Text = String.Empty
        txt_TapeTypeID.Text = String.Empty
        txt_Description.Text = String.Empty
        txt_UOM.Text = String.Empty
        txt_OpeningBal.Text = String.Empty
        txt_CurrentBal.Text = String.Empty
        txt_ReOrderLevel.Text = String.Empty
        txt_Cost.Text = String.Empty

    End Sub

    Protected Sub dg_TapeType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_TapeType.PageIndexChanging
        dg_TapeType.PageIndex = e.NewPageIndex()
        dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        FillGrid()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_TapeType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty

    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        'Dim script As String
        'script = "<script language='javascript' type='text/javascript'>"
        'script = script + "window.onbeforeunload = function() {"
        'script = script + "return ""Closing the page now may result in data loss."";"
        ''return "Closing the page now may result in data loss.";
        ''Dim I As String = "return ""Closing the page now may result in data loss."";"
        'script = script + "}</script>"

        'Page.RegisterClientScriptBlock("test", script)
    End Sub
End Class
