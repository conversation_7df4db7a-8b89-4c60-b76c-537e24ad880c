<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_MergeTapeReport.aspx.vb" Inherits="Frm_rpt_MergeTapeReport" title="Archival Reports > Q5. How can I view Merge Tape Details ?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports >  How Can I View Merge Tape Details?" Width="656px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE><TBODY><TR class="mytext"><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px" vAlign=middle>Content Type&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD style="HEIGHT: 21px" vAlign=middle>Tape Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkTapeNumber" runat="server" Text="Ignore" Font-Bold="False" __designer:wfdid="w73" AutoPostBack="True" OnCheckedChanged="chkEmployee_CheckedChanged"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>From Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Bold="False" __designer:wfdid="w74"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>To Date</TD><TD style="HEIGHT: 21px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w30"></asp:Label></TD></TR><TR class="mytext"><TD vAlign=top><asp:DropDownList id="ddlContentType" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w75" AutoPostBack="True" DataValueField="ContentTypeID" DataTextField="ContentTypeName" DataSourceID="dsContentType">
                                </asp:DropDownList><asp:SqlDataSource id="dsContentType" runat="server" __designer:wfdid="w76" SelectCommand="SELECT [ContentTypeID], [ContentTypeName] FROM ApplicationSetup.ContentType&#13;&#10;union &#13;&#10;select -1 as [ContentTypeID], '---- Any Type ----' as [ContentTypeName] &#13;&#10;FROM ApplicationSetup.ContentType&#13;&#10;order by [ContentTypeName]" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>"></asp:SqlDataSource> </TD><TD vAlign=top>&nbsp;<asp:TextBox id="txtTapeNumber" runat="server" __designer:wfdid="w2"></asp:TextBox></TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w79"></asp:TextBox></TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w80"></asp:TextBox></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w31"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w81" TargetControlID="ddlContentType" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:ListSearchExtender id="ListSearchExtender2" runat="server" __designer:wfdid="w82" TargetControlID="ddlTapeNumber" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w83" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w84" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_TapeNo_2" runat="server" TargetControlID="txtTapeNumber" ServicePath="AutoComplete.asmx" ServiceMethod="GetArchivedTapeNumber" MinimumPrefixLength="3" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1">
                </cc1:AutoCompleteExtender> <asp:DropDownList id="ddlTapeNumber" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w77" Visible="False"></asp:DropDownList><BR />
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form (Archival Reports >  How Can I View Merge Tape Details?) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form (Archival Reports >  How Can I View Merge Tape Details?) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

