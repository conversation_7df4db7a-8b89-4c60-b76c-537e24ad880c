<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_EmployeeOutstandingSummary.aspx.vb" Inherits="Frm_rpt_EmployeeOutstandingSummary" title="Home > Reports > Blank Tapes > Q 2. How Can I View Employee Outstanding Summary?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Blank Tapes Reports  >  Q 1. How Can I View Employee wise Tape Type Outstanding Summary?" Width="856px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD vAlign=middle>&nbsp;</TD><TD vAlign=middle></TD><TD style="FONT-SIZE: 8pt; FONT-FAMILY: Arial" vAlign=middle></TD><TD vAlign=middle></TD><TD vAlign=middle></TD><TD vAlign=middle></TD></TR><TR class="mytext"><TD vAlign=middle>Department Name</TD><TD vAlign=middle>Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; <asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" __designer:wfdid="w69" OnCheckedChanged="chkEmployee_CheckedChanged1" autopostback="true"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; FONT-FAMILY: Arial" vAlign=middle>From Date &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" __designer:wfdid="w70"></asp:CheckBox></TD><TD vAlign=middle>To Date</TD><TD vAlign=middle>Station</TD><TD vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" __designer:wfdid="w5"></asp:Label></TD></TR><TR class="mytext"><TD vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="200px" CssClass="mytext" __designer:wfdid="w71" AutoPostBack="True" DataValueField="DepartmentID" DataTextField="DepartmentName" DataSourceID="dsDepartment"></asp:DropDownList><asp:SqlDataSource id="dsDepartment" runat="server" __designer:wfdid="w72" SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>">
                                </asp:SqlDataSource>&nbsp; </TD><TD vAlign=top><asp:TextBox id="TxtEmployee" runat="server" __designer:dtid="3377699720527948" Width="192px" CssClass="mytext" __designer:wfdid="w3" EnableViewState="False"></asp:TextBox></TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w75"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w76"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="562949953421399" Width="88px" CssClass="mytext" __designer:wfdid="w3"></asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w4"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w77" PromptPosition="Bottom" PromptText TargetControlID="ddlDepartment"></cc1:ListSearchExtender><cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w79" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w80" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" __designer:wfdid="w4" TargetControlID="TxtEmployee" ServicePath="AutoComplete.asmx" ServiceMethod="GetEmployee" MinimumPrefixLength="3" CompletionInterval="1" EnableCaching="true" CompletionSetCount="12">
</cc1:AutoCompleteExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td style="width: 460px">
                            </td>
                        </tr>
                        <tr>
                            <td class="bottomMain" style="width: 460px; height: 25px;">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form (Blank Tapes Reports > Q 1. How Can I View Employee wise Tape Type Outstanding Summary?)--"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form (Blank Tapes Reports > Q 1. How Can I View Employee wise Tape Type Outstanding Summary?)--"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

