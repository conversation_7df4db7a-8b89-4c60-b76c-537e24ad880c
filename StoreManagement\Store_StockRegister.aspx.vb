
Partial Class StoreManagement_Store_StockRegister
    Inherits System.Web.UI.Page
    Dim DS As New System.Data.DataSet
    Dim strCommand As String
    ' Dim connStr As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim objStore As New BusinessFacade.StoreManagement

        If Not Page.IsPostBack Then

            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")

                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)

            End If

            BindCombo()
            FillGrid()
            Dim strID As String = Request("ID").ToString()
            If strID <> -1 Then

                DS = objStore.GetRecord_Master(strID)

                If DS.Tables(0).Rows.Count > 0 Then
                    txt_PRNo.Text = DS.Tables(0).Rows(0)("PRNo").ToString()
                    txt_SIRNO.Text = DS.Tables(0).Rows(0)("SIRNo").ToString()
                    ddl_City_Issued.SelectedValue = DS.Tables(0).Rows(0)("CityID").ToString()
                    ddl_StockStatus.SelectedValue = DS.Tables(0).Rows(0)("StockStatusID").ToString()
                    ddl_RequisitionSource.SelectedValue = DS.Tables(0).Rows(0)("RequisitionSourceID").ToString()
                    txtRemarks.Text = DS.Tables(0).Rows(0)("Remarks").ToString()
                    txtEntryDate.Text = DS.Tables(0).Rows(0)("RequisitionDate")

                    lblAuditHistory.Visible = True
                    Dim ObjAudit As New BusinessFacade.StockRegister()
                    ObjAudit.StockRegisterID = strID
                    dgAuditHistory.DataSource = ObjAudit.AuditHistory_StockRegister()
                    dgAuditHistory.DataBind()

                End If

            End If
        End If
    End Sub

    Private Sub BindCombo()

        Dim objCity As New BusinessFacade.City()
        DS = objCity.GetRecords_DataSet


        ddl_City_Issued.DataSource = DS.Tables(0)
        ddl_City_Issued.DataTextField = "CityName"
        ddl_City_Issued.DataValueField = "CityID"
        ddl_City_Issued.DataBind()
        ddl_City_Issued.Items.Insert(0, "--Select--")

        ''''''''''''''''''''''''''''''''''''''''''''''
        Dim objStockStatus As New BusinessFacade.StockStatus()
        DS = objStockStatus.GetRecords

        ddl_StockStatus.DataSource = DS.Tables(0)
        ddl_StockStatus.DataTextField = "StockStatus"
        ddl_StockStatus.DataValueField = "StockStatusID"
        ddl_StockStatus.DataBind()
        ddl_StockStatus.Items.Insert(0, "--Select--")

        ''''''''''''''''''''''''''''''''''''
        Dim objReqSource As New BusinessFacade.RequisitionSource()
        DS = objReqSource.GetRecords

        ddl_RequisitionSource.DataSource = DS.Tables(0)
        ddl_RequisitionSource.DataTextField = "RequisitionSource"
        ddl_RequisitionSource.DataValueField = "RequisitionSourceID"
        ddl_RequisitionSource.DataBind()
        ddl_RequisitionSource.Items.Insert(0, "--Select--")


    End Sub

    Private Sub FillGrid()
        Dim strID As String = Request("ID").ToString()
        If strID <> -1 Then
            Dim objStockRegisterDetail As New BusinessFacade.StockRegisterDetail()
            DS = objStockRegisterDetail.GetRecords(strID)
            If DS.Tables(0).Rows.Count = 0 Then
                DS = objStockRegisterDetail.GetRecords(Convert.ToInt32(-1))
            End If
            dg.DataSource = DS.Tables(0)
            dg.DataBind()

            dg.Columns(0).Visible = False
            dg.Columns(3).Visible = False

        Else
            Dim objStockRegisterDetail As New BusinessFacade.StockRegisterDetail()
            DS = objStockRegisterDetail.GetRecords(Convert.ToInt32(strID))

            dg.DataSource = DS.Tables(0)
            dg.DataBind()
            dg.Columns(0).Visible = False
            dg.Columns(3).Visible = False


        End If

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click


        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


        '''''''''''''' Save Master Record '''''''''''''''''''
        'If txt_PRNo.Text = "" Then
        '    lblErr.Text = "Please Enter Ref. No !!"
        'Else
        If ddl_City_Issued.SelectedIndex = "0" Then
            lblErr.Text = "Please Select City !!"
        ElseIf ddl_StockStatus.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Stock Status !!"
        ElseIf ddl_RequisitionSource.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Requisition Source"
        Else

            Dim strID As String = Request("ID").ToString()
            If strID <> -1 Then

                Dim objStockRegister As New BusinessFacade.StockRegister()
                objStockRegister.StockRegisterID = Convert.ToInt32(strID)
                objStockRegister.PRNo = txt_PRNo.Text
                objStockRegister.SIRNo = txt_SIRNO.Text
                objStockRegister.CityID = ddl_City_Issued.SelectedValue
                objStockRegister.StockStatusID = ddl_StockStatus.SelectedValue
                objStockRegister.RequisitionSourceID = ddl_RequisitionSource.SelectedValue
                objStockRegister.Remarks = txtRemarks.Text
                objStockRegister.EntryDate = txtEntryDate.Text
                objStockRegister.UserID = UserID
                objStockRegister.UpdateRecord()

                Dim objStockRegisterDetail As New BusinessFacade.StockRegisterDetail()
                objStockRegisterDetail.GetRecord(Convert.ToInt32(strID))
                Dim SRID As Integer = objStockRegisterDetail.StockRegisterID

                Dim P As Integer
                For P = 0 To dg.Rows.Count - 1
                    'Dim Quantity As String
                    Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
                    If MyTextBox.Text <> "" Then

                        objStockRegisterDetail.TapeTypeID = dg.Rows(P).Cells(0).Text
                        objStockRegisterDetail.Qty = MyTextBox.Text
                        objStockRegisterDetail.StockRegisterID = strID
                        objStockRegisterDetail.StockRegisterDetailID = Convert.ToInt32(dg.Rows(P).Cells(3).Text)
                        If Convert.ToInt32(dg.Rows(P).Cells(3).Text) <> 0 Then

                            objStockRegisterDetail.UpdateRecord()
                        Else
                            objStockRegisterDetail.SaveRecord()
                        End If

                    End If
                Next
                lblErr.Text = "Record has been Updated!!"
                dg.DataSource = Nothing
                dg.DataBind()
                dgAuditHistory.DataSource = Nothing
                dgAuditHistory.DataBind()
                lblAuditHistory.Visible = False
                Clrscr()
            Else

                Dim objStockRegister As New BusinessFacade.StockRegister()
                objStockRegister.PRNo = txt_PRNo.Text
                objStockRegister.SIRNo = txt_SIRNO.Text
                objStockRegister.CityID = ddl_City_Issued.SelectedValue
                objStockRegister.StockStatusID = ddl_StockStatus.SelectedValue
                objStockRegister.RequisitionSourceID = ddl_RequisitionSource.SelectedValue
                objStockRegister.Remarks = txtRemarks.Text
                objStockRegister.EntryDate = txtEntryDate.Text
                objStockRegister.UserID = UserID
                Dim ID As Integer = objStockRegister.SaveRecords()

                '    ''''''''''''''''''''''''''''''''''''''''''''''
                Dim objStockRegisterDetail As New BusinessFacade.StockRegisterDetail()

                Dim P As Integer
                For P = 0 To dg.Rows.Count - 1
                    'Dim Quantity As String
                    Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
                    If MyTextBox.Text <> "" Then

                        objStockRegisterDetail.TapeTypeID = dg.Rows(P).Cells(0).Text
                        objStockRegisterDetail.Qty = MyTextBox.Text
                        objStockRegisterDetail.StockRegisterID = ID
                        If objStockRegisterDetail.Qty > 0 Then
                            objStockRegisterDetail.SaveRecord()
                        End If

                    End If
                Next
                lblErr.Text = "Record has been Saved !!"
                Clrscr()
                '    '''''''''''''''''''''''''''

            End If
        End If

    End Sub

    Protected Sub bttnCencel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCencel.Click
        lblErr.Text = String.Empty
        Clrscr()
    End Sub

    Private Sub Clrscr()
        txt_PRNo.Text = String.Empty
        ddl_City_Issued.SelectedIndex = "0"
        ddl_StockStatus.SelectedIndex = "0"
        ddl_RequisitionSource.SelectedIndex = "0"
        txtRemarks.Text = String.Empty
        txtEntryDate.Text = String.Empty
        txt_SIRNO.Text = String.Empty

        Dim P As Integer
        For P = 0 To dg.Rows.Count - 1
            Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
            If MyTextBox.Text <> "" Then
                MyTextBox.Text = "0"
            End If
        Next
    End Sub

    Protected Sub LnkStoreManagement_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkStoreManagement.Click
        Response.Redirect("StoreManagement.aspx")
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
