Imports Microsoft.VisualBasic
Imports DAAB = Microsoft.ApplicationBlocks.Data.SqlHelper

Public Class Country

    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    'Private strConnection As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

    Private m_CountryID As Integer
    Private m_CountryName As String


#Region " Object Properties "

    Public Property CountryID() As Integer
        Get
            Return m_CountryID
        End Get
        Set(ByVal value As Integer)
            m_CountryID = value
        End Set
    End Property
    Public Property CountryName() As String
        Get
            Return m_CountryName
        End Get
        Set(ByVal value As String)
            m_CountryName = value
        End Set
    End Property


#End Region


#Region " Data Manipulation Methods "

    Public Sub SaveRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "Country_SaveRecord", DBNull.Value, m_CountryName)
        Catch ex As Exception
            Throw
        End Try
    End Sub


    Public Sub UpdateRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "Country_SaveRecord", m_CountryID, m_CountryName)
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub DeleteRecord(ByVal id As Integer)
        Try
            DAAB.ExecuteNonQuery(objConnection, "Country_DeleteRecord", id)
        Catch ex As Exception
            Throw
        End Try
    End Sub


#End Region


#Region " Data Retrieval Methods "

    Public Sub GetRecord(ByVal id As Integer)
        Try
            Dim dtbl As Data.DataTable = DAAB.ExecuteDataset(objConnection, "Country_GetRecords", id).Tables(0)
            If (Not dtbl Is Nothing) And (dtbl.Rows.Count > 0) Then
                m_CountryID = dtbl.Rows(0)("CountryID")
                m_CountryName = dtbl.Rows(0)("CountryName")
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    'You may overload this method for retrieving records by using parameters and filters, 
    'e.g., GetRecords(filter1) or GetRecords(filter1, filter2) etc.
    Public Function GetRecords() As Data.DataTable
        Try
            Return DAAB.ExecuteDataset(objConnection, "Country_GetRecords", DBNull.Value).Tables(0)
        Catch ex As Exception
            Throw
        End Try
    End Function


#End Region

End Class
