<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmRecycleTurn.aspx.vb" Inherits="ApplicationSetup_frmRecycleTurn" title="Home > Recycle Turn > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="WIDTH: 100%; HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="lnkHomePage" onclick="lnkHomePage_Click" runat="server" __designer:wfdid="w12" CssClass="labelheading">Home</asp:LinkButton>&nbsp; &gt; Recycle Turn &gt; Add New</TD></TR><TR><TD style="WIDTH: 357px"><TABLE><TBODY><TR class="mytext"><TD>Recycle Turn</TD><TD><asp:TextBox id="txt_RecycleTurn" runat="server" CssClass="mytext"></asp:TextBox></TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True" Width="288px"></asp:Label></TD></TR><TR><TD style="WIDTH: 357px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD style="WIDTH: 357px"><asp:GridView id="dg_RecycleTurn" runat="server" CssClass="gridContent" Width="392px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="RecycleTurnID" HeaderText="RecycleTurnID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="RecycleTurn" HeaderText="Recycle Turn" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD style="WIDTH: 357px"><asp:TextBox id="txt_RecycleTurnID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w3" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

