
Partial Class Login1
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.txtLoginID.Focus()

    End Sub
    Protected Sub btnLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnLogin.Click

        Dim asdf As String
        Dim objManager As JangSalesPortal.SecurityLayer.SecurityManager = JangSalesPortal.SecurityLayer.SecurityManager.CreateInstance
        Dim ticket As JangSalesPortal.SecurityLayer.AuthenticationTicket
        ticket = objManager.Authenticate(txtLoginID.Text, txtpassword.Text)
        If Not ticket Is Nothing Then
            Session("ApplicationTicket") = ticket
            'Response.Redirect("Home.aspx")
            Response.Redirect("~/Home/Home.aspx")
        Else
            ' Response.Write("BHANDDDDDDD")
            lblErr.Text = "Please Try Again !!"
            Me.txtpassword.Focus()
        End If
    End Sub

    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click
        txtLoginID.Text = String.Empty
        txtpassword.Text = String.Empty
        lblErr.Text = String.Empty
    End Sub
End Class
