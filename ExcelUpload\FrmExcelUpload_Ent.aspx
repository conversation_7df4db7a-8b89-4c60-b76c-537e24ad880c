<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmExcelUpload_Ent.aspx.vb" Inherits="ExcelUpload_FrmExcelUpload_Ent" title="Excel Uploading Screen for Entertainment" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>


<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">

  <table style="width: 100%">
        <tr>
            <td style="width: 100%; height: 28px;">
                <asp:LinkButton ID="LinkButton1" runat="server" CssClass="labelheading" Font-Underline="True">Home</asp:LinkButton>
                <asp:Label ID="Label5" runat="server" CssClass="labelheading" Font-Bold="True" Font-Size="Medium"
                    Text=">> Upload Data For Entertainment" Font-Underline="True"></asp:Label></td>
        </tr>
        <tr>
            <td align="center" style="width: 100%" valign="middle">
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
        </tr>
       <tr>
           <td style="width: 100%; height: 32px; background-color: lightsteelblue">
               <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Names="Consolas"
                   Font-Size="Large" ForeColor="Navy" Text=": : Generate New Tape Number : :"></asp:Label></td>
       </tr>
        <tr>
            <td style="width: 100%">
                <table class="mytext">
                    <tr>
                        <td style="width: 100px">
                            Tape Type</td>
                        <td style="width: 100px">
                            Tape Number</td>
                        <td style="width: 100px">
                            Station</td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddl_TapeType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext"></asp:TextBox></td>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddlStation" runat="server" CssClass="myddl" Width="107px">
                            </asp:DropDownList></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
       <tr>
           <td class="bottomMain" style="width: 100%; height: 28px">
               &nbsp;<asp:Button ID="bttnTapeNumber" runat="server" Text="Generate Tape Number" Width="179px" CssClass="buttonA" Font-Bold="True" />
               <asp:Button
                                ID="bttnClear_TapeNumber" runat="server" Text="Clear" Width="120px" CssClass="buttonA" Font-Bold="True" /></td>
       </tr>
       <tr>
           <td style="width: 100%">
           </td>
       </tr>
       <tr>
           <td style="width: 100%; height: 30px; background-color: lightsteelblue">
               <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Consolas"
                   Font-Size="Larger" ForeColor="Navy" Text=": : Excel Upload Area For Entertainment : :"></asp:Label></td>
       </tr>
        <tr>
            <td style="width: 100%">
                <table style="width: 807px" class="mytext">
                    <tr>
                        <td colspan="1" style="height: 26px">
                            File Path</td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 26px">
                            <asp:FileUpload ID="FileUpload" runat="server" Width="627px" accept=".xlsx" /></td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 21px">
                <asp:Label ID="lblFilePath" runat="server" Visible="False"></asp:Label>
                <asp:Label ID="lblFileName" runat="server" Visible="False"></asp:Label></td>
                    </tr>
                </table>
                </td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 29px;">
                &nbsp;
                            <asp:Button ID="bttnLoadExcel" runat="server" Text="Load Excel" CssClass="buttonA" Font-Bold="True" Width="126px" />&nbsp;<asp:Button
                                ID="bttnSave" runat="server" Text="Save" Width="90px" CssClass="buttonA" Font-Bold="True" />&nbsp;
                <asp:Button
                                ID="bttnClear_ExcelArea" runat="server" Text="Clear" Width="97px" CssClass="buttonA" Font-Bold="True" /></td>
        </tr>
        <tr>
            <td style="width: 100px; height: 21px;">
                &nbsp;
            </td>
        </tr>
        <tr>
            <td valign="top">
                <asp:GridView ID="dgExcel" runat="server" AutoGenerateColumns="False" Width="100%" CssClass="gridContent">
                    <Columns>
                        <asp:BoundField DataField="S No" HeaderText="S No" />
                        <asp:BoundField DataField="Entry Date" HeaderText="Entry Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False"  />
                        <asp:BoundField DataField="On-Air Date" HeaderText="On-Air Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False"  />
                        <asp:BoundField DataField="Tape Number" HeaderText="Tape Number" />
                        <asp:BoundField DataField="Program Name" HeaderText="Program Name" />
                        <asp:BoundField DataField="Tape Type" HeaderText="Tape Type" />
                        <asp:BoundField DataField="Episode No" HeaderText="Episode No" />
                        <asp:BoundField DataField="Part No" HeaderText="Part No" />
                        <asp:BoundField DataField="Urdu Script" HeaderText="Urdu Script" />
                        <asp:BoundField DataField="Note Area" HeaderText="Note Area" />
                        <asp:BoundField DataField="Abstract" HeaderText="Abstract" />
                        <asp:BoundField DataField="Keytype" HeaderText="Keytype" />
                        <asp:BoundField DataField="Keyword" HeaderText="Keyword" />
                        <asp:BoundField DataField="Start Time" HeaderText="Start Time" />
                        <asp:BoundField DataField="End Time" HeaderText="End Time" />
                        
                    </Columns>
                    <AlternatingRowStyle CssClass="gridAlternate" />
                    <HeaderStyle BackColor="#C0C0FF" />
                </asp:GridView>
            </td>
        </tr>
       <tr>
           <td style="width: 100%; background-color: lightsteelblue">
               <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Names="Consolas"
                   Font-Size="Large" ForeColor="Navy" Text=" :  : Sample Excel Format for Upload Data :  :"></asp:Label></td>
       </tr>
       <tr>
           <td style="width: 100%">
           </td>
       </tr>
        <tr>
            <td style="width: 100%; height: 17px;"><asp:Button ID="Button1" runat="server" Text="Download Sample" CssClass="buttonA" Font-Bold="True" Width="144px" />
            <%--<a class="Link" href=Sample.xls>Download Sample Format</a> --%>
             
                &nbsp;<asp:Button ID="bttnDownload" runat="server" CssClass="buttonA" Font-Bold="True"
                    Text="Download Sample Format" Visible="False" Width="192px" />
                <asp:LinkButton ID="lnkSample" runat="server" Font-Size="11pt" Visible="False">Download Sample Format</asp:LinkButton></td>
                  <asp:Label ID="lbl_UserName" runat="server" Visible="True"></asp:Label></tr>
    </table>
    
</asp:Content>

