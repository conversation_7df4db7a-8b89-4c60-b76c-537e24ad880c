Partial Class ApplicationSetup_frmNewsKeyword
    Inherits System.Web.UI.Page
    Dim I As Integer
    Dim dt As New Data.DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_SubContentType.DataSource = New BusinessFacade.SubContentType().GetRecords()
        ddl_SubContentType.DataTextField = "SubContentTypeName"
        ddl_SubContentType.DataValueField = "SubContentTypeID"
        ddl_SubContentType.DataBind()
        ddl_SubContentType.Items.Insert(0, "--Select--")

        ''''''''''''''''''''''''''''''''''''''''''''
        lstNews.DataSource = New BusinessFacade.KeyType().KeyType_News_GetRecords()
        lstNews.DataTextField = "KeyType"
        lstNews.DataValueField = "KeyTypeID"
        lstNews.DataBind()
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.NewsKeyword().IsExists_NewsKeyword(txt_NewsKeyword.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : News Keyword already exists !"
        Else
            If txt_NewsKeywordID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        ClearAuditHistory()
        
    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        If txt_NewsKeyword.Text = "" Then
            lblErr.Text = "Please insert News Keyword!!"
        ElseIf ddl_SubContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please select SubContent Type!!"
        Else
            Dim U As Integer
            For U = 0 To lstNews.Items.Count - 1
                If lstNews.Items(U).Selected = True Then
                    Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
                    objNewsKeyword.NewsKeyword = txt_NewsKeyword.Text
                    objNewsKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
                    objNewsKeyword.TKeytype = lstNews.Items(U).Text
                    objNewsKeyword.UserID = UserID
                    objNewsKeyword.SaveRecord()
                    'Else
                    'Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
                    'objNewsKeyword.NewsKeyword = txt_NewsKeyword.Text
                    'objNewsKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
                    'objNewsKeyword.TKeytype = ""
                    'objNewsKeyword.SaveRecord()
                End If
                FillGrid()
                lblErr.Text = "Record has been Saved!!"
            Next

        End If
    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        If lstNews.SelectedIndex <> "-1" Then
            Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
            objNewsKeyword.NewsKeywordID = txt_NewsKeywordID.Text
            objNewsKeyword.NewsKeyword = txt_NewsKeyword.Text
            objNewsKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objNewsKeyword.TKeytype = lstNews.SelectedItem.Text
            objNewsKeyword.UserID = UserID
            objNewsKeyword.UpdateRecord()
            If Not ViewState("Search") Is Nothing Then
                fillgrid_search()
            Else
                FillGrid()
            End If
            lblErr.Text = "Record has been Updated!!"
        Else
            Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
            objNewsKeyword.NewsKeywordID = txt_NewsKeywordID.Text
            objNewsKeyword.NewsKeyword = txt_NewsKeyword.Text
            objNewsKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objNewsKeyword.TKeytype = ""
            objNewsKeyword.UpdateRecord()
            If Not ViewState("Search") Is Nothing Then
                fillgrid_search()
            Else
                FillGrid()
            End If
            lblErr.Text = "Record has been Updated!!"
        End If

    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.NewsKeyword().GetRecords()
        dg_NewsKeyword.DataSource() = dt
        dg_NewsKeyword.Columns(0).Visible = True
        dg_NewsKeyword.Columns(2).Visible = True
        dg_NewsKeyword.DataBind()
        dg_NewsKeyword.Columns(0).Visible = False
        dg_NewsKeyword.Columns(2).Visible = False
        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_NewsKeyword.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_NewsKeyword.SelectedIndex.ToString
        txt_NewsKeywordID.Text = Convert.ToInt32(dg_NewsKeyword.Rows(I).Cells(1).Text)
        txt_NewsKeyword.Text = dg_NewsKeyword.Rows(I).Cells(2).Text
        ddl_SubContentType.SelectedValue = Convert.ToInt32(dg_NewsKeyword.Rows(I).Cells(3).Text)
        If dg_NewsKeyword.Rows(I).Cells(5).Text <> "&nbsp;" Then
            lstNews.SelectedIndex = 0
            lstNews.SelectedItem.Text = dg_NewsKeyword.Rows(I).Cells(5).Text
        End If

        dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.NewsKeyword()
        ObjAudit.NewsKeywordID = txt_NewsKeywordID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_NewsKeyword()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_NewsKeywordID.Text = "" Then
                lblErr.Text = "Please Select News Keyword!!"
            Else
                Dim objNewsKeyword As New BusinessFacade.NewsKeyword()
                objNewsKeyword.NewsKeywordID = txt_NewsKeywordID.Text
                objNewsKeyword.DeleteRecord(objNewsKeyword.NewsKeywordID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This NewsKeyword is Already in Used !"
            clrscr()
            dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_NewsKeyword.Text = String.Empty
        txt_NewsKeywordID.Text = String.Empty
        ddl_SubContentType.SelectedIndex = 0
        txt_TKeytype.Text = String.Empty

    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        FillGrid()
        ClearAuditHistory()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub dg_NewsKeyword_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs)

        dg_NewsKeyword.PageIndex = e.NewPageIndex()
        If txt_SearchKW.Text = "" Then
            FillGrid()
        Else
            fillgrid_search()
        End If
        dg_NewsKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty

    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        ViewState("Search") = Nothing
        fillgrid_search()
        ClearAuditHistory()
    End Sub

    Private Sub fillgrid_search()

        Dim objSearch As New BusinessFacade.NewsKeyword()
        objSearch.NewsKeyword = txt_SearchKW.Text
        dt = objSearch.GetSingleNewsKeyword(objSearch.NewsKeyword)

        If dt.Rows(0).Item(0).ToString <> "0" Then
            dg_NewsKeyword.DataSource = dt
            dg_NewsKeyword.Columns(0).Visible = True
            dg_NewsKeyword.Columns(2).Visible = True
            dg_NewsKeyword.DataBind()
            dg_NewsKeyword.Columns(0).Visible = False
            dg_NewsKeyword.Columns(2).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
        Else
            lblErr.Text = "Search Results: KeyWord is not valid !!"
        End If
        ViewState("Search") = dt
    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
