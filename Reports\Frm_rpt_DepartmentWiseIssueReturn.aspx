<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_DepartmentWiseIssueReturn.aspx.vb" Inherits="Frm_rpt_DepartmentWiseIssueReturn" title="Home > Reports > Archival Reports > Q 1. How Can I View History of Return Tapes?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > Q 1. How Can I View History of Return Tapes?" Width="720px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="WIDTH: 185px" vAlign=middle></TD><TD style="WIDTH: 185px" vAlign=middle></TD><TD vAlign=middle></TD><TD vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 185px; HEIGHT: 21px" vAlign=middle>Department Name&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="chkDepartment" runat="server" Text="Ignore" OnCheckedChanged="chkDepartment_CheckedChanged" AutoPostBack="True" __designer:wfdid="w3"></asp:CheckBox></TD><TD style="WIDTH: 185px" vAlign=middle>From Date &nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" OnCheckedChanged="chkIgnoredate_CheckedChanged" __designer:wfdid="w4"></asp:CheckBox></TD><TD vAlign=middle>To Date</TD><TD vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 200px" vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w5" DataValueField="DepartmentID" DataTextField="DepartmentName" DataSourceID="dsDepartment"></asp:DropDownList> <asp:SqlDataSource id="dsDepartment" runat="server" __designer:wfdid="w6" SelectCommand="Select DepartmentID,DepartmentName From ApplicationSetup.Department order by DepartmentName" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>">
                                </asp:SqlDataSource></TD><TD style="WIDTH: 204px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w7"></asp:TextBox>&nbsp;&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w8"></asp:TextBox>&nbsp;&nbsp; </TD><TD style="HEIGHT: 18px" vAlign=top></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w9" TargetControlID="ddlDepartment" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w10" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w11" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender>&nbsp; 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100px">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Archival Reports > Q 1. How Can I View History of Return Tapes? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Archival Reports > Q 1. How Can I View History of Return Tapes? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
    &nbsp;
</asp:Content>

