<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="frmOSRReminders.aspx.vb" Inherits="ReminderService_frmOSRReminders"
    Title="OSR Reminders" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td class="labelheading" style="width: 100%; text-decoration: underline">
                <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; OSR Reminder Service</td>
        </tr>
        <tr>
            <td>
                <table style="width: 740px">
                    <tr class="mytext">
                        <td style="height: 22px">
                            Employee &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            <asp:CheckBox ID="Chk_Employee" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            Department &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;
                            <asp:CheckBox ID="chkDepartment" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            From Date&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                            <asp:CheckBox ID="Chk_Date" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            To Date
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td>
                            <asp:TextBox ID="txtEmployee" runat="server" CssClass="mytext" Width="208px"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txt_DepartmentName" runat="server" CssClass="mytext" Width="175px"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtFromDate" runat="server" CssClass="mytext"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext"></asp:TextBox></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr class="mytext">
            <td class="bottomMain">
                &nbsp;
                <asp:Button ID="bttnSearch" runat="server" CssClass="buttonA" Font-Bold="True" Text="Search"
                    Width="80px" />&nbsp;
                <asp:Button ID="bttnSendMail" runat="server" CssClass="buttonA" Font-Bold="True"
                    Text="Send Email" Width="104px" />&nbsp;
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Font-Bold="True" Text="Clear"
                    Width="88px" />
            </td>
        </tr>
        <tr class="mytext">
            <td style="height: 68px">
                <table style="width: 588px">
                    <tr class="mytext">
                        <td>
                            CC</td>
                        <td>
                            BCC</td>
                        <td>
                            Employee Email Address</td>
                        <td>
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 178px">
                            <asp:TextBox ID="txt_CC" runat="server" CssClass="mytext" Width="150px"></asp:TextBox></td>
                        <td style="width: 163px">
                            <asp:TextBox ID="txt_BCC" runat="server" CssClass="mytext" Width="150px"></asp:TextBox></td>
                        <td align="center" style="width: 160px" valign="middle">
                            <asp:TextBox ID="txt_EmailAddress" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                        <td style="width: 77px; color: #ff0033">
                            <asp:LinkButton ID="LinkButton1" runat="server" Width="93px">Get Email Address</asp:LinkButton></td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 178px">
                        </td>
                        <td style="width: 163px">
                        </td>
                        <td align="left" style="width: 160px" valign="top">
                            eg:&nbsp; <EMAIL></td>
                        <td style="width: 150px; color: #ff0033">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" CssClass="mytext" Font-Bold="True" Font-Size="Small"
                    ForeColor="Red" Width="704px"></asp:Label></td>
        </tr>
        <tr class="mytext">
            <td>
                <asp:GridView ID="dg_search" runat="server" AllowPaging="True" AutoGenerateColumns="False"
                    CssClass="gridContent" Width="863px" PageSize="50">
                    <Columns>
                        <asp:TemplateField HeaderText="S.#">
                            <ItemTemplate>
                                <%#Container.DataItemIndex + 1 %>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="EmployeeID" HeaderText="EmployeeID" />
                        <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" />
                        <asp:BoundField DataField="DepartmentName" HeaderText="Department Name" />
                        <asp:BoundField DataField="TotalQuantity" HeaderText="Total Quantity" />
                        <asp:BoundField DataField="TotalReminders" HeaderText="Total Reminders" />
                        <asp:TemplateField HeaderText="Check">
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle HorizontalAlign="Center" />
                            <HeaderTemplate>
                                Check
                            </HeaderTemplate>
                            <ItemTemplate>
                                <asp:CheckBox ID="Chk_Tape" runat="server" onclick="Check_Click(this)" />
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="DepartmentID" HeaderText="DepartmentID" />
                    </Columns>
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
    </table>
    <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
    </cc1:ToolkitScriptManager>
    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="MyCalendar"
        Format="dd-MMM-yyyy" TargetControlID="txtFromDate">
    </cc1:CalendarExtender>
    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" Format="dd-MMM-yyyy"
        TargetControlID="txtToDate">
    </cc1:CalendarExtender>
    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Employee" runat="server" CompletionInterval="1"
        CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetActiveEmployee"
        ServicePath="AutoComplete.asmx" TargetControlID="txtEmployee">
    </cc1:AutoCompleteExtender>
    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Department" runat="server" CompletionInterval="1"
        CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="3" ServiceMethod="GetDepartment"
        ServicePath="AutoComplete.asmx" TargetControlID="txt_DepartmentName">
    </cc1:AutoCompleteExtender>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label><asp:Label
        ID="lblEmployeeName" runat="server" Visible="False"></asp:Label>
</asp:Content>
