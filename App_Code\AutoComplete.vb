Imports System.Web
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Collections.Generic
Imports AjaxControlToolkit
Imports System.Data
Imports System.Data.SqlClient
Imports DAAB = Microsoft.ApplicationBlocks.Data

<WebService(Namespace:="http://tempuri.org/")> _
<WebServiceBinding(ConformsTo:=WsiProfiles.BasicProfile1_1)> _
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
<System.Web.Script.Services.ScriptService()> _
Public Class AutoComplete
    Inherits System.Web.Services.WebService
    Dim StrCon As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(StrCon)

    <WebMethod()> _
    Public Function Guest(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeNo", New SqlParameter("@TapeNo", prefixText)).Tables(0)

        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            Dim st As String = datafile
            items.Add("#" + datafile)

        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function Program(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetProgramChild", New SqlParameter("@ProgramChildName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("ProgramChildName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function NoteArea(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetNoteArea", New SqlParameter("@NoteArea", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("NoteArea"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function Abstract(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetAbstract", New SqlParameter("@Abstract", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("Abstract"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
        Public Function EntKeywords(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "Get_Entertainment_Keywords", New SqlParameter("@EntertainmentKeyword", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EntertainmentKeyword"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
        Public Function GetKeyTypes(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetKeyTypes", New SqlParameter("@KeyType", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("KeyType"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
        Public Function GetReporterSlug(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetReporterSlug", New SqlParameter("@ReporterSlug", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeSlug"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
       Public Function GetProposedSlug(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetProposedSlug", New SqlParameter("@ProposedSlug", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("ProposedSlug"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
       Public Function GetReporter(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetReporter", New SqlParameter("@Reporter", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
      Public Function GetCameraMan(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetCameraMan", New SqlParameter("@CameraMan", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
     Public Function GetTapeType(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeType", New SqlParameter("@TapeType", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeType"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function GetNewKeywords(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetNewKeywords", New SqlParameter("@NewsKeyword", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("NewsKeyword"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function GetNewKeyTypes(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetNewKeyTypes", New SqlParameter("@NewsKeyTypes", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("KeyType"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function TapeContent_TapeNumer(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "TapeContent_TapeNumber_GetRecords_2", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
     Public Function TapeContent_ProgramChild(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "ProgramChild_GetRecords_AutoComplete", New SqlParameter("@ProgramChildName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("ProgramChildName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function TapeContentNews_TapeNumber(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "TapeContentNews_TapeNumber_GetRecords_2", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function TapeContentNews_TapeNumber_SearchEngine(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "TapeContentNews_TapeNumber_GetRecords_SearchEngine", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
Public Function GetTapeNo_ArchivalReturn(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeNo_ArchivalReturn", New SqlParameter("@TapeNo", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
 Public Function TapeContentEnt_TapeNumber_GetRecords_2(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "TapeContentEnt_TapeNumber_GetRecords_2", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
   Public Function Reporter_GetRecords_AutoComplete(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "Reporter_GetRecords_AutoComplete", New SqlParameter("@ReporterName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
  Public Function Cameraman_GetRecords_AutoComplete(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "Cameraman_GetRecords_AutoComplete", New SqlParameter("@CameraMan", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
Public Function FootageType_GetRecords_AutoComplete(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "FootageType_GetRecords_AutoComplete", New SqlParameter("@FootageType", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("FootageTypeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
          Public Function GetDepartment(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetDepartment", New SqlParameter("@DepartmentName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("DepartmentName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
              Public Function GetCity(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetCity", New SqlParameter("@CityName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("CityName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
            Public Function GetEmployee(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetEmployee", New SqlParameter("@EmployeeName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
            Public Function GetBulkTapeIssuedEmployee(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetBulkTapeIssuedEmployee", New SqlParameter("@EmployeeName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetMergeTapeNumbers(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetMergeTapeNumbers", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetMergeTapeNumbers_News(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetMergeTapeNumbers_News", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetProgramName_Ent(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetProgramName_Ent", New SqlParameter("@ProgramName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("ProgNameSlug"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetSlugName_News(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetSlugName_News", New SqlParameter("@SlugName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("ProgNameSlug"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetTapeNumber_ArchivalIssue_Ent(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeNumber_ArchivalIssue_Ent", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
              Public Function GetTapeNumber_ArchivalIssue_News(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeNumber_ArchivalIssue_News", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
              Public Function GetTapeNumebrs_TapeReturn(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeNumebrs_TapeReturn", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
            Public Function GetEmployeeRecord_TapeReturn_1by1(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetEmployeeRecord_TapeReturn_1by1", New SqlParameter("@EmployeeName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
                Public Function getTapeNumber_LostDamage(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "getTapeNumber_LostDamage", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
                Public Function GetTapeContentNews_Lister_TapeNo(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeContentNews_Lister_TapeNo", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
               Public Function GetTapeContentEnt_Lister_TapeNo(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapeContentEnt_Lister_TapeNo", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
                  Public Function GetArchivedTapeNumber(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetArchivedTapeNumber", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
    Public Function GetMergeTapes_Ent(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetMergeTapes_Ent", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
 Public Function GetMergeTapes_News(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetMergeTapes_News", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
        Public Function GetTapes_Ent(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapes_Ent", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
      Public Function GetTapes_News(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetTapes_News", New SqlParameter("@TapeNumber", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            items.Add("#" + datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
      Public Function GetBlankTapes(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetBlankTapes", New SqlParameter("@TapeNo", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            Dim st As String = datafile
            items.Add("#" + datafile)

        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
          Public Function GetLocationCodeEnt(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "EntLocationCode_AutoComplete", New SqlParameter("@LocationCode", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("LocationCode"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
         Public Function GetLocationCodeNews(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "NewsLocationCode_AutoComplete", New SqlParameter("@LocationCode", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("LocationCode"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

    <WebMethod()> _
     Public Function GetBlankTapesIssuance(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetBlankTapesIssuance", New SqlParameter("@TapeNo", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("TapeNumber"))
            'i = i + 1
            Dim st As String = datafile
            items.Add("#" + datafile)

        Next
        Return items.ToArray()
    End Function


    <WebMethod()> _
        Public Function GetActiveEmployee(ByVal prefixText As String, ByVal count As Integer) As String()
        Dim dt As New DataTable

        If (count = 0) Then
            count = 10
        End If

        dt = DAAB.SqlHelper.ExecuteDataset(con, CommandType.StoredProcedure, "GetActiveEmployee", New SqlParameter("@EmployeeName", prefixText)).Tables(0)
        Dim items As New List(Of String)
        For i As Integer = 0 To dt.Rows.Count - 1
            Dim datafile
            datafile = UCase(dt.Rows(i).Item("EmployeeName"))
            'i = i + 1
            items.Add(datafile)
        Next
        Return items.ToArray()
    End Function

End Class
