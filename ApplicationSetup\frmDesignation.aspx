<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmDesignation.aspx.vb" Inherits="frmDesignation" title="Home > Designation > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w4">Home</asp:LinkButton> &gt; Designation &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 110px">Designation Name</TD><TD><asp:TextBox id="txt_DesignationName" runat="server" CssClass="mytext" Width="296px"></asp:TextBox></TD><TD style="WIDTH: 190px"></TD><TD style="WIDTH: 210px"><asp:TextBox id="txt_OracleID" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_Designation" runat="server" CssClass="gridContent" Width="512px" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="DesignationID" HeaderText="DesignationID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="Designation" HeaderText="Designation" />
                        <asp:BoundField DataField="Oracle_ID" HeaderText="Oracle_ID" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD><asp:Label id="lblAuditHistory" runat="server" CssClass="labelheading" __designer:wfdid="w3" Visible="False">Audit History - Designation</asp:Label></TD></TR><TR><TD><asp:GridView id="dgAuditHistory" runat="server" CssClass="gridContent" __designer:wfdid="w8" Width="100%" PageSize="25" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD><asp:TextBox id="txt_DesignationID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w10" Visible="False"></asp:Label></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w13" ConfirmText="Do you want to Delete !" TargetControlID="BttnDelete"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

