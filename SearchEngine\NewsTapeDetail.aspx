<%@ Page Language="VB" AutoEventWireup="false" CodeFile="NewsTapeDetail.aspx.vb"
    Inherits="SearchEngine_NewsTapeDetail" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<script language="Javascript1.2">
<!--
// please keep these lines on when you copy the source
// made by: Nicolas - http://www.javascript-page.com

//var message = "<< Print >>";

//function printpage() {
//window.print();  
//}

//document.write("<form>   <input type=button "
//+"value=\""+message+"\" onClick=\"printpage()\"></form>");

//-->
</script>
<head runat="server">
    <title>Search Engine > News Tape Details</title>
     <link href="main.css" rel="stylesheet" type="text/css" />
   
</head>
<body>
    <form id="form1" runat="server">
        <div>
            &nbsp;<table width="100%">
                <tr>
                    <td>
                        <asp:FormView ID="FormView1" runat="server" DataSourceID="SqlDataSource1" CellPadding="4" ForeColor="#333333" HeaderText="-- News Tape Details --" Width="100%" Font-Names="Arial">
                            <ItemTemplate>
                                <asp:Label ID="Label4" runat="server" Font-Bold="true" Text="Tape Number:"></asp:Label>
                                <asp:Label ID="TapeNumberLabel" Font-Size="Smaller" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:Label><br />
                                <hr />
                                <asp:Label ID="Label1" runat="server" Font-Bold="true" Text="Sub Closet:"></asp:Label>
                                <asp:Label ID="SubClosetLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("SubCloset") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label2" runat="server" Font-Bold="true" Text="Report Slug:"></asp:Label>
                                <asp:Label ID="ReportSlugLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("ReportSlug") %>'>
                                </asp:Label><br /><hr />
                                <asp:Label ID="Label3" runat="server" Font-Bold="true" Text="Proposed Slug:"></asp:Label>
                                <asp:Label ID="ProposedSlugLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("ProposedSlug") %>'>
                                </asp:Label><br /><hr />
                                <asp:Label ID="Label5" runat="server" Font-Bold="true" Text="Reporter Name:"></asp:Label>
                                <asp:Label ID="ReporterLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("Reporter") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label6" runat="server" Font-Bold="true" Text="Camera Man Name:"></asp:Label>
                                <asp:Label ID="CameraManLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("CameraMan") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label7" runat="server" Font-Bold="true" Text="Available:"></asp:Label>
                                <asp:Label ID="isAvailableLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("isAvailable") %>'>
                                </asp:Label><br /><hr />
                                <asp:Label ID="Label8" runat="server" Font-Bold="true" Text="Entry Date:"></asp:Label>
                                <asp:Label ID="EntryDateLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("EntryDate") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label9" runat="server" Font-Bold="true" Text="Tape Type:"></asp:Label>
                                <asp:Label ID="TapeTypeLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("TapeType") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label10" runat="server" Font-Bold="true" Text="Tape Status:"></asp:Label>
                                <asp:Label ID="TapeStatusLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("TapeStatus") %>'>
                                </asp:Label><br /><hr />
                                <asp:Label ID="Label11" runat="server" Font-Bold="true" Text="Start Time:"></asp:Label>
                                <asp:Label ID="StartTimeLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("StartTime") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label12" runat="server" Font-Bold="true" Text="End Time:"></asp:Label>
                                <asp:Label ID="EndTimeLabel" runat="server" Font-Size="Smaller" Text='<%# Bind("EndTime") %>'></asp:Label><br /><hr />
                             </ItemTemplate>
                            <EditItemTemplate>
                                TapeNumber:
                                <asp:TextBox ID="TapeNumberTextBox" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:TextBox><br />
                                SubCloset:
                                <asp:TextBox ID="SubClosetTextBox" runat="server" Text='<%# Bind("SubCloset") %>'>
                                </asp:TextBox><br />
                                ReportSlug:
                                <asp:TextBox ID="ReportSlugTextBox" runat="server" Text='<%# Bind("ReportSlug") %>'>
                                </asp:TextBox><br />
                                ProposedSlug:
                                <asp:TextBox ID="ProposedSlugTextBox" runat="server" Text='<%# Bind("ProposedSlug") %>'>
                                </asp:TextBox><br />
                                Reporter:
                                <asp:TextBox ID="ReporterTextBox" runat="server" Text='<%# Bind("Reporter") %>'>
                                </asp:TextBox><br />
                                CameraMan:
                                <asp:TextBox ID="CameraManTextBox" runat="server" Text='<%# Bind("CameraMan") %>'>
                                </asp:TextBox><br />
                                isAvailable:
                                <asp:TextBox ID="isAvailableTextBox" runat="server" Text='<%# Bind("isAvailable") %>'>
                                </asp:TextBox><br />
                                EntryDate:
                                <asp:TextBox ID="EntryDateTextBox" runat="server" Text='<%# Bind("EntryDate") %>'>
                                </asp:TextBox><br />
                                TapeType:
                                <asp:TextBox ID="TapeTypeTextBox" runat="server" Text='<%# Bind("TapeType") %>'>
                                </asp:TextBox><br />
                                TapeStatus:
                                <asp:TextBox ID="TapeStatusTextBox" runat="server" Text='<%# Bind("TapeStatus") %>'>
                                </asp:TextBox><br />
                                StartTime:
                                <asp:TextBox ID="StartTimeTextBox" runat="server" Text='<%# Bind("StartTime") %>'>
                                </asp:TextBox><br />
                                EndTime:
                                <asp:TextBox ID="EndTimeTextBox" runat="server" Text='<%# Bind("EndTime") %>'>
                                </asp:TextBox><br />
                                <asp:LinkButton ID="UpdateButton" runat="server" CausesValidation="True" CommandName="Update"
                                    Text="Update">
                                </asp:LinkButton>
                                <asp:LinkButton ID="UpdateCancelButton" runat="server" CausesValidation="False" CommandName="Cancel"
                                    Text="Cancel">
                                </asp:LinkButton>
                            </EditItemTemplate>
                            <InsertItemTemplate>
                                TapeNumber:
                                <asp:TextBox ID="TapeNumberTextBox" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:TextBox><br />
                                SubCloset:
                                <asp:TextBox ID="SubClosetTextBox" runat="server" Text='<%# Bind("SubCloset") %>'>
                                </asp:TextBox><br />
                                ReportSlug:
                                <asp:TextBox ID="ReportSlugTextBox" runat="server" Text='<%# Bind("ReportSlug") %>'>
                                </asp:TextBox><br />
                                ProposedSlug:
                                <asp:TextBox ID="ProposedSlugTextBox" runat="server" Text='<%# Bind("ProposedSlug") %>'>
                                </asp:TextBox><br />
                                Reporter:
                                <asp:TextBox ID="ReporterTextBox" runat="server" Text='<%# Bind("Reporter") %>'>
                                </asp:TextBox><br />
                                CameraMan:
                                <asp:TextBox ID="CameraManTextBox" runat="server" Text='<%# Bind("CameraMan") %>'>
                                </asp:TextBox><br />
                                isAvailable:
                                <asp:TextBox ID="isAvailableTextBox" runat="server" Text='<%# Bind("isAvailable") %>'>
                                </asp:TextBox><br />
                                EntryDate:
                                <asp:TextBox ID="EntryDateTextBox" runat="server" Text='<%# Bind("EntryDate") %>'>
                                </asp:TextBox><br />
                                TapeType:
                                <asp:TextBox ID="TapeTypeTextBox" runat="server" Text='<%# Bind("TapeType") %>'>
                                </asp:TextBox><br />
                                TapeStatus:
                                <asp:TextBox ID="TapeStatusTextBox" runat="server" Text='<%# Bind("TapeStatus") %>'>
                                </asp:TextBox><br />
                                StartTime:
                                <asp:TextBox ID="StartTimeTextBox" runat="server" Text='<%# Bind("StartTime") %>'>
                                </asp:TextBox><br />
                                EndTime:
                                <asp:TextBox ID="EndTimeTextBox" runat="server" Text='<%# Bind("EndTime") %>'>
                                </asp:TextBox><br />
                                <asp:LinkButton ID="InsertButton" runat="server" CausesValidation="True" CommandName="Insert"
                                    Text="Insert">
                                </asp:LinkButton>
                                <asp:LinkButton ID="InsertCancelButton" runat="server" CausesValidation="False" CommandName="Cancel"
                                    Text="Cancel">
                                </asp:LinkButton>
                            </InsertItemTemplate>
                            <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                            <EditRowStyle BackColor="#2461BF" />
                            <RowStyle BackColor="#EFF3FB" />
                            <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
                            <HeaderStyle BackColor="#507CD1" Font-Bold="True" Font-Names="Arial" Font-Size="Large"
                                ForeColor="White" HorizontalAlign="Center" />
                        </asp:FormView>
                        <asp:SqlDataSource ID="SqlDataSource1" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString6 %>"
                            SelectCommand="GetNewsSearchEngine_TapeDetails" SelectCommandType="StoredProcedure">
                            <SelectParameters>
                                <asp:QueryStringParameter Name="TapeNumber" QueryStringField="TapeNumber" Type="String" />
                                <asp:QueryStringParameter DefaultValue="" Name="ReportSlug" QueryStringField="ReportSlug"
                                    Type="String" />
                            </SelectParameters>
                        </asp:SqlDataSource>
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Label ID="Label20" runat="server" Font-Bold="True" Text="Added & Modified By :" Font-Names="Arial"></asp:Label>
                        <hr />
                        <table width="100%">
                            <tr>
                                <td style="width: 100px">
                                    <asp:Label ID="Label21" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Smaller"
                                        Width="94px">Added By :</asp:Label></td>
                                <td style="width: 96%">
                                    <asp:Label ID="lblAddedBy" runat="server" Font-Names="Arial" Font-Size="Smaller"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 100px">
                                    <asp:Label ID="Label22" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Smaller"
                                        Width="94px">Modified By :</asp:Label></td>
                                <td style="width: 96%">
                                    <asp:Label ID="lblModifiedBy" runat="server" Font-Names="Arial" Font-Size="Smaller"></asp:Label></td>
                            </tr>
                        </table>
                        <hr />
                        &nbsp;<asp:Label ID="Label13" runat="server" Font-Bold="True" Font-Names="Arial"
                            Text="Keywords :"></asp:Label>
                        <table width="100%">
                            <tr>
                                <td style="width: 100px">
                                </td>
                                <td style="width: 96%">
                                    <asp:Label ID="lblKeyword" runat="server" Font-Size="Smaller" Font-Names="Arial"></asp:Label></td>
                            </tr>
                        </table>
                        <hr />
                        <asp:Label ID="Label16" runat="server" Font-Bold="True" Text="Footages :" Font-Names="Arial"></asp:Label>&nbsp;
                        <table width="100%">
                            <tr>
                                <td style="width: 100px; height: 21px;">
                                </td>
                                <td style="width: 96%; height: 21px;">
                                    <asp:Label ID="lblFootages" runat="server" Font-Size="Smaller" Font-Names="Arial"></asp:Label></td>
                                                                </tr>
                        </table><hr />
                        <asp:Label ID="Label14" runat="server" Font-Bold="True" Font-Names="Arial" Text="Availability :"></asp:Label></td>
                </tr>
                <tr>
                    <td style="height: 48px">
                        <table width="100%">
                            <tr>
                                <td style="width: 100px; height: 21px;">
                                </td>
                                <td style="width: 96%; height: 21px;">
                                    <asp:Label ID="lblAvailability" runat="server" Font-Names="Arial" Font-Size="Smaller"></asp:Label></td>
                            </tr>
                        </table>
                        <hr />
                        <asp:Label ID="Label18" runat="server" Font-Bold="True" Font-Names="Arial" Text="Damage (Yes/No):"></asp:Label></td>
                </tr>
                <tr>
                    <td style="height: 48px">
                        <table width="100%">
                            <tr>
                                <td style="width: 100px; height: 21px;">
                                </td>
                                <td style="width: 96%; height: 21px;">
                                    <asp:Label ID="lblIsDamage" runat="server" Font-Names="Arial" Font-Size="Smaller"></asp:Label></td>
                            </tr>
                        </table>
                        <hr />
                        <asp:Label ID="Label15" runat="server" Font-Bold="True" Font-Names="Arial" Text="Urdu Script :"></asp:Label></td>
                </tr>
                <tr>
                    <td style="height: 48px">
                        <table width="100%">
                            <tr>
                                <td style="height: 21px;" colspan="2">
                                    <asp:Label ID="lblUrduScript" runat="server" Font-Names="Arial Narrow" Font-Size="Large" Font-Bold="False" Visible="False"></asp:Label>
                                    <asp:TextBox ID="txtUrduScript" runat="server" CssClass="mytextUrdu" Font-Size="Medium"
                                        Height="150px" TabIndex="3" TextMode="MultiLine" Width="100%" BackColor="#F2F5FE" BorderStyle="None"></asp:TextBox></td>
                            </tr>
                        </table>
                        <hr />
                        <asp:Label ID="Label17" runat="server" Font-Bold="True" Text="English Script :" Font-Names="Arial"></asp:Label></td>
                </tr>
                <tr>
                    <td>
                        <asp:TextBox ID="lblEngScript" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                            Height="200px" TextMode="MultiLine" Width="100%"></asp:TextBox>
                            </td>
                </tr>
                <tr>
                    <td style="width: 100%; height: 29px;" class="bottomMain">
                        &nbsp;
                        &nbsp;
                      <asp:Button ID="Button1" OnClientClick="window.print()" runat="server" Text="Print Page" CssClass="buttonA" Font-Bold="True" />
                        <asp:Button ID="bttnEditSlug" runat="server" Text="Edit Slug" CssClass="buttonA" Font-Bold="True" />
                        <asp:Button ID="bttnReminder" runat="server" Text="Send Reminder" CssClass="buttonA" Font-Bold="True" Enabled="False" Width="128px" />&nbsp;<asp:Button
                            ID="bttnEditMaster" runat="server" CssClass="buttonA" Font-Bold="True" Text="Goto Tape Entry Screen" />
                        <asp:Label ID="lblEmployeeID" runat="server" Visible="False"></asp:Label>
                        <asp:LinkButton ID="lnkPlayVideo" runat="server" Visible="False">Play Video</asp:LinkButton><asp:Label
                            ID="lblEmployeeName" runat="server" Visible="False"></asp:Label><asp:Label ID="lbl_UserName"
                                runat="server" Visible="False"></asp:Label>
                        <asp:Button ID="bttnPlayVideo" runat="server" Text="Play Video" CssClass="buttonA" Font-Bold="True" Width="128px" Visible="False" />
                        <asp:Label ID="lblMasterID" runat="server" Visible="False"></asp:Label></td>
                </tr>
                <tr>
                    <td style="width: 100%; height: 20px">
                        </td>
                </tr>
            </table>
    </div>
    </form>
</body>
</html>
