<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="LoginPassword.aspx.vb" Inherits="LoginPassword" title="Untitled Page" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table>
        <tr>
            <td class="labelheading" colspan="3" style="height: 20px; text-decoration: underline">
                Change Password</td>
        </tr>
        <tr>
            <td style="width: 135px; height: 9px">
            </td>
            <td style="width: 100px; height: 9px">
            </td>
            <td style="width: 100px; height: 9px">
            </td>
        </tr>
        <tr>
            <td class="mytext" style="width: 135px">
                Old Password</td>
            <td style="width: 100px">
                <asp:TextBox ID="txtOldPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            <td style="width: 100px">
            </td>
        </tr>
        <tr>
            <td class="mytext" style="width: 135px; height: 21px">
                New Password</td>
            <td style="width: 100px; height: 21px">
                <asp:TextBox ID="txtNewPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            <td style="width: 100px; height: 21px">
            </td>
        </tr>
        <tr>
            <td class="mytext" style="width: 135px; height: 21px">
                Confirm Password</td>
            <td style="width: 100px; height: 21px">
                <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="mytext" TextMode="Password"></asp:TextBox></td>
            <td style="width: 100px; height: 21px">
            </td>
        </tr>
        <tr>
            <td style="width: 135px; height: 21px">
            </td>
            <td style="width: 100px; height: 21px">
            </td>
            <td style="width: 100px; height: 21px">
            </td>
        </tr>
        <tr>
            <td class="bottomMain" colspan="3" style="height: 29px">
                &nbsp;
                <asp:Button ID="bttnSave" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Save >>"
                    Width="80px" />&nbsp;
                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Clear >>"
                    Width="88px" /></td>
        </tr>
        <tr>
            <td colspan="3" style="height: 21px">
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="416px"></asp:Label></td>
        </tr>
    </table>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

