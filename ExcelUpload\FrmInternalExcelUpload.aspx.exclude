<%@ Page Language="VB" AutoEventWireup="false" CodeFile="FrmInternalExcelUpload.aspx.vb" Inherits="FrmInternalExcelUpload" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%; height: 28px;">
                <asp:LinkButton ID="LinkButton1" runat="server" CssClass="labelheading" Font-Underline="True">Home</asp:LinkButton>
                <asp:Label ID="Label5" runat="server" CssClass="labelheading" Font-Bold="True" Font-Size="Medium"
                    Text=">> Upload Data" Font-Underline="True"></asp:Label></td>
        </tr>
        <tr>
            <td align="center" style="width: 100%" valign="middle">
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr>
            <td style="width: 100%">
                <table>
                    <tr>
                        <td style="width: 100px">
                            Tape Type</td>
                        <td style="width: 100px">
                            Tape Number</td>
                        <td style="width: 100px">
                            Station</td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddl_TapeType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></td>
                        <td style="width: 100px">
                            <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext"></asp:TextBox></td>
                        <td style="width: 100px">
                            <asp:DropDownList ID="ddlStation" runat="server" CssClass="myddl" Width="107px">
                            </asp:DropDownList></td>
                        <td style="width: 100px">
                            <asp:Button ID="bttnTapeNumber" runat="server" Text="Generate Tape Number" Width="161px" /></td>
                    </tr>
                    <tr>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                        <td style="width: 100px">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <table style="width: 807px">
                    <tr>
                        <td colspan="1" style="height: 26px">
                            File Path</td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 26px">
                            <asp:FileUpload ID="FileUpload" runat="server" Width="627px" /></td>
                    </tr>
                    <tr>
                        <td colspan="1" style="height: 21px">
                <asp:Label ID="lblFilePath" runat="server"></asp:Label>
                <asp:Label ID="lblFileName" runat="server"></asp:Label></td>
                    </tr>
                </table>
                </td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 26px;">
                &nbsp;
                            <asp:Button ID="bttnLoadExcel" runat="server" Text="Load Excel" CssClass="buttonA" Font-Bold="True" Width="126px" />&nbsp;<asp:Button
                                ID="bttnSave" runat="server" Text="Save" Width="120px" CssClass="buttonA" Font-Bold="True" />
                </td>
        </tr>
        <tr>
            <td style="width: 100px; height: 21px;">
                &nbsp;
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:GridView ID="dgExcel" runat="server" AutoGenerateColumns="False" Width="100%" CssClass="gridContent">
                    <Columns>
                        <asp:BoundField DataField="S No" HeaderText="S No" />
                        <asp:BoundField DataField="Entry Date" HeaderText="Entry Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False"  />
                        <asp:BoundField DataField="Reporter Slug" HeaderText="Reporter Slug" />
                        <asp:BoundField DataField="Tape Number" HeaderText="Tape Number" />
                        <asp:BoundField DataField="Tape Type" HeaderText="Tape Type" />
                        <asp:BoundField DataField="Start Time" HeaderText="Start Time" />
                        <asp:BoundField DataField="End Time" HeaderText="End Time" />
                        <asp:BoundField DataField="Keyword" HeaderText="Keyword" />
                        <asp:BoundField DataField="Reporter" HeaderText="Reporter" />
                    </Columns>
                    <AlternatingRowStyle CssClass="gridAlternate" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:LinkButton ID="lnkSample" runat="server">Download Sample Format</asp:LinkButton></td>
        </tr>
    </table>
    </form>
</body>
</html>
