<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmChangeEntKeyword.aspx.vb" Inherits="ApplicationSetup_frmChangeEntKeyword" title="Home > Replace keywords " %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Country" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w2">Home</asp:LinkButton> &gt;&nbsp;Update Entertainment&nbsp;Keywords&nbsp;&gt; Add New</TD></TR><TR><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD></TD><TD colSpan=2></TD><TD colSpan=1></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px"></TD><TD style="HEIGHT: 22px" colSpan=2>Keyword</TD><TD style="HEIGHT: 22px" colSpan=1></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px"></TD><TD style="HEIGHT: 22px" colSpan=2><asp:TextBox id="txtSearch" runat="server" CssClass="mytext" __designer:wfdid="w6" Width="264px"></asp:TextBox></TD><TD style="HEIGHT: 22px" colSpan=1><asp:Button id="bttnSearch" onclick="bttnSearch_Click" runat="server" Text="Search" CssClass="buttonA" __designer:wfdid="w10" Font-Bold="True"></asp:Button></TD></TR><TR class="mytext"><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px" colSpan=2></TD><TD style="HEIGHT: 13px" colSpan=1></TD></TR><TR class="mytext"><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px">Old Word</TD><TD style="HEIGHT: 13px">New Word</TD><TD style="HEIGHT: 13px"></TD></TR><TR class="mytext"><TD></TD><TD><asp:TextBox id="txtOldWord" runat="server" CssClass="mytext" __designer:wfdid="w1"></asp:TextBox></TD><TD><asp:TextBox id="txtNewWord" runat="server" CssClass="mytext" __designer:wfdid="w2"></asp:TextBox></TD><TD></TD></TR><TR class="mytext"><TD></TD><TD></TD><TD></TD><TD></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Width="424px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;&nbsp; <asp:Button id="bttnChange" onclick="bttnChange_Click" runat="server" Text="Change Keyword" CssClass="buttonA" __designer:wfdid="w5" Width="120px" Font-Bold="True"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg" runat="server" ForeColor="Black" CssClass="gridContent" __designer:wfdid="w8" Width="100%" AllowPaging="True" PageSize="2000" AutoGenerateColumns="False" OnPageIndexChanging="dg_PageIndexChanging">
<RowStyle ForeColor="Black"></RowStyle>

<EmptyDataRowStyle ForeColor="Transparent"></EmptyDataRowStyle>
<Columns>
<asp:BoundField ApplyFormatInEditMode="True" DataField="EntertainmentKeywordID" HeaderText="EntertainmentKeywordID" Visible="False"></asp:BoundField>
<asp:BoundField ApplyFormatInEditMode="True" DataField="EntertainmentKeyword" HeaderText="EntertainmentKeyword"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

