<%@ Master Language="VB" CodeFile="MasterPage.master.vb" Inherits="MasterPage" %>
<%@ Register TagPrefix="uc1" TagName="ctlMenu" Src="Menu/CltMenu.ascx" %>

<%@ Register Assembly="Infragistics2.WebUI.UltraWebNavigator.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebNavigator" TagPrefix="ignav" %>
<%@ Register Src="footer.ascx" TagName="footer" TagPrefix="uc2" %>
<%@ Register Src="header.ascx" TagName="header" TagPrefix="uc1" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>Untitled Page</title>
     <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" /> 
     <link href="main.css" rel="stylesheet" type="text/css" />
     <script language="javascript" src="dic.js" type="text/javascript"></script>
      <script type="text/javascript" language=javascript>
      
//      function saveCurrentTabIndex()
//        {
//            var tabcontainer = $get('').control;
//            var tabIndex = $get('');
//        if (tabcontainer != null)
//            {
//                tabIndex.value = tabcontainer.get_activeTabIndex();
//            }
//         } 

//    function restoreCurrentTabIndex()
//        {
//            var tabIndex = $get('');
//            if (tabIndex.value > -1)
//            {
//                var tabcontainer = $find('');
//                var index = tabcontainer.get_activeTabIndex();
//            if (index != tabIndex.value)
//                {
//                tabcontainer.set_activeTabIndex(tabIndex.value);
//                }
//            }
//        }

        function popWin(llInp) 
        {          
              window.open("NewTapeNumber.aspx","aa","bb","cc","width=1500,height=800");			
            
            return false;		
        }
        
        function popWin_2(llInp) 
        {         
              window.open("NewTapeNumber.aspx","aa","width=1000,height=550");			
            
            return false;		
        }
        
          function popWin_News(llInp) 
        {          
              window.open("NewTapeNumber_News.aspx","aa","width=1000,height=550");			
            
            return false;		
        }
        
          function popWin_Bulk(llInp) 
        {
              window.open("BulkNewTapeNumber.aspx","aa","width=1000,height=550");			
            
            return false;		
        }
       
       
         function mainValues()
    {
    	var lsString;			
    	lsString=window.document.form1['txt_TapeNumber'].value;		
    	window.opener.parent.form1['txt_TapeNumber'].value=lsString;			
    	
    	var lsString2;			
    	lsString2=window.document.form1['txt_SRDID'].value;		
    	window.opener.parent.form1['txt_SRDID'].value=lsString2;
    	
    	var lsString3;			
    	lsString3=window.document.form1['txt_TapeTypeID'].value;		
    	window.opener.parent.form1['txt_TapeTypeID'].value=lsString3;
    	    	   	
    	window.close();		
    }
    
//     window.onbeforeunload = function() {
//    return "Closing the page now may result in data loss.";
//};
      
  </script>
    
  <script type = "text/javascript">
        function Check_Click(obj)
        {
            var row = obj.parentNode.parentNode;
            if(obj.checked)
            {
                row.style.backgroundColor = "Wheat";
            }
            else
            {    
                   row.style.backgroundColor = "#f2f5fE";
            }              
        }
    </script>

   
<script type = "text/javascript">
function MouseEvents(objRef, evt)
{
    var checkbox = objRef.getElementsByTagName("input")[0];
   if (evt.type == "mouseover")
   {
        objRef.style.backgroundColor = "#C5D5FC";
   }
   else
   {
        if (checkbox.checked)
        {
            objRef.style.backgroundColor = "Wheat";
        }
        else if(evt.type == "mouseout")
        {
                if(objRef.rowIndex % 2 == 0)
                {
                   objRef.style.backgroundColor = "#F2F5FE";
                }
                else
                {
                    objRef.style.backgroundColor = "#F2F5FE";
                }          
        }   
   }
}
</script>  

<script type = "text/javascript"> 
function checkAll(objRef) 
{ 
    var GridView = objRef.parentNode.parentNode.parentNode; 
    var inputList = GridView.getElementsByTagName("input"); 
    for (var i=0;i<inputList.length;i++) 
    { 
        var row = inputList[i].parentNode.parentNode; 
        if(inputList[i].type == "checkbox"  && objRef != inputList[i]) 
        { 
            if (objRef.checked) 
            { 
                row.style.backgroundColor = "Wheat"; 
                inputList[i].checked=true; 
            } 
            else 
            { 
                if(row.rowIndex % 2 == 0) 
                { 
                   row.style.backgroundColor = "#F2F5FE"; 
                } 
                else 
                { 
                   row.style.backgroundColor = "#F2F5FE"; 
                } 
                inputList[i].checked=false; 
            } 
        } 
    } 
} 
</script> 


</head>
<body leftmargin="0" topmargin="0" bottommargin="0" rightmargin="0">
    <form id="form1" runat="server">     
    <input type="hidden" id="tabIndexHidden" runat="server">
        <table style="width: 100%; height: 100%" width="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                
                <td align="left" style="width: 70%; background-repeat: repeat-y;" valign="top" width="70%">
                    <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; height: 100%" width="100%">
                        <tr style="height:100%">
                            <td style="width: 100%">
                                <%--<uc1:header ID="Header1" runat="server" />--%>
                                <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%">
                                    <tr>
      
                                          <td valign="middle" width = "18" background = "../Images/header1_left.gif" style="background-repeat: no-repeat; height: 85px;">
                                              &nbsp;</td>
                                          <td valign="top" background = "../Images/header1_center.gif" style="background-repeat: repeat-x; height: 85px; font-weight: bold; font-size: 75%; text-transform: uppercase; color: #ffffff; font-family: Arial;">
                                              &nbsp;</td>
                                          <td valign="bottom" align="right" width = "744" background = "../Images/header1_right.gif" style="background-repeat: no-repeat; height: 85px; font-weight: bold; font-size: 125%; color: #ffffff; font-family: Arial;">
                                              <br />
                                              <br />
                                              <br />
                                              DIGITAL ARCHIVE
                                              MANAGEMENT SYSTEM
                                              &nbsp;</td>
                                        
                                    </tr>
                                    
                                </table>
                            </td>
                        </tr>
                        <tr>
                            
                            <td width = "100%" valign="top">
                                <uc1:ctlMenu id="CtlMenu1" runat="server">
                                </uc1:ctlMenu>
                                
                            </td>
                            
                        </tr>
                        <tr>
                                    <td align="right" colspan="3" style="height: 16px; color: #ffff66;" class="WelcomeLabel">
                                        <asp:Label ID="lblUserName" runat="server" Text="Label" ForeColor="Yellow"></asp:Label>&nbsp;
                                        &nbsp;</td>
                                    </tr>
                        <tr style="height:60%">
                            <td align="left">
                                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                                   
                            </asp:ContentPlaceHolder>
                            </td>
                        </tr>
                        <tr style="height:10%"><td style="width: 818px">
                            <uc2:footer ID="Footer1" runat="server" />
                        </td></tr>
                    </table>                    
                </td>
                
            </tr>
        </table>
             
         
    </form>
   
</body>
</html>
