Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_DepartmentWiseBlankTapesSummary
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()
        Dim ObjStation1 As New BusinessFacade.TapeType()
        ddl_TapeType.DataSource = ObjStation1.Gettapetype()
        ddl_TapeType.DataTextField = "TapeTypeMaster"
        ddl_TapeType.DataValueField = "TapeTypeMaster"
        ddl_TapeType.DataBind()
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            
            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String



            Dept = ddlDepartment.SelectedValue.ToString


            'If chkEmployee.Checked = True Then
            '    Emp = "-1"
            'Else
            '    Emp = ddlEmployee.SelectedValue.ToString
            'End If

            If chkEmployee.Checked = True Then
                Emp = "-1"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    ' Emp = ddlEmployee.SelectedValue.ToString
                    Emp = EmployeeID.ToString
                Else
                    Emp = "-1"
                End If

            End If


            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue
            Dim tapetype As String
            tapetype = ddl_TapeType.SelectedValue
            Dim blanktype As String
            blanktype = ddlPendings.SelectedValue




            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_Grand_out_standing_tapes.rpt&@EmployeeID=" + Emp + "&@DepartmentId=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@isBlank=" + blanktype + "&@TapeTypeMaster=" + tapetype + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_Grand_out_standing_tapes.rpt&@EmployeeID=" + Emp + "&@DepartmentId=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@isBlank=" + blanktype + "&@TapeTypeMaster=" + tapetype + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If
            
            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "GrandOutstandingtapes.aspx"
            ObjSave.ReportName = "Reminder service  >  Q 2. How can I view outstanding tape numbers according to tape types?"
            ObjSave.SaveRecord()

            ''******************************************************''



        Catch ex As Exception
            Throw
        End Try

    End Sub



    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub

    

    Protected Sub chkEmployee_CheckedChanged1(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub
End Class
