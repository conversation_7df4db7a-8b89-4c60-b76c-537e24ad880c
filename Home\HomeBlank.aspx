<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="HomeBlank.aspx.vb" Inherits="Home_HomeBlank" title="Archive Management System > Home Page" %>

<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="DCWC" %>

<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="DCWC" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table width="100%">
        <tr>
            <td style="width: 428px; height: 21px">
                <asp:Image ID="Image2" runat="server" ImageUrl="~/Images/Archive.jpg" Height="485px" /></td>
            <td style="width: 100%;" valign="top">
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="hometext">
                        </td>
                        <td class="hometext">
                Digital Archive
                Management System is designed to provide central repository to record and retrieve
                data related to all types of contents/ programs and news reports with smart search
                options, so producers, archive department and any other stake holder can also easily
                search their required content / videos.</td>
                    </tr>
                    <tr>
                        <td style="height: 1px">
                        </td>
                    <td style="height:1px"></td>
                    </tr>
                    <tr>
                        <td align="left" style="height: 221px">
                        </td>
                        <td align="left" style="height: 221px">
                            <table style="width: 100%">
                                <tr>
                                    <td style="width: 50%" valign="top">
                                        &nbsp;</td>
                                    <td style="width: 50%" valign="top">
                                        &nbsp;</td>
                                </tr>
                                <tr>
                                    <td style="width: 50%; height: 61px;" align="center">
                                        &nbsp;
                                    </td>
                                    <td style="width: 50%; height: 61px;" align="center" valign="top">
                                        &nbsp;
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            <asp:Label ID="lblBaseStation" runat="server" Visible="False"></asp:Label></td>
        </tr>
        <tr>
            <td style="width: 428px; height: 21px;">
                </td>
            <td style="width: 100px; height: 21px;" valign="top">
                &nbsp;</td>
        </tr>
    </table>
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    &nbsp;
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

