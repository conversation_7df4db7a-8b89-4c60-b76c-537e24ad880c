Partial Class frmTicker
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            FillGrid()

        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_TickerID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If

        dg.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()

    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        If Trim(txt_Ticker.Text) = "" Then
            lblErr.Text = "Please Enter Ticker!!"
        Else
            Dim ObjTicker As New BusinessFacade.Reports()
            ObjTicker.Insert_Tickers(txt_Ticker.Text, UserID)
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If

    End Sub

    Private Sub UpdateRecord()
        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        If Trim(txt_Ticker.Text) = "" Then
            lblErr.Text = "Please Enter Ticker!!"
        Else
            Dim ObjTicker As New BusinessFacade.Reports()
            ObjTicker.Update_Tickers(txt_TickerID.Text, txt_Ticker.Text, UserID)
            FillGrid()
            lblErr.Text = "Record has been Updated!!"
        End If
    End Sub

    Private Sub FillGrid()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim Obj As New BusinessFacade.Reports()
        dg.DataSource = Obj.GetTickers(UserID)
        dg.Columns(0).Visible = True
        dg.DataBind()
        dg.Columns(0).Visible = False

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_TickerID.Text = "" Then
                lblErr.Text = "Please Select Record !!"
            Else

                Dim ObjTicker As New BusinessFacade.Reports()
                ObjTicker.Delete_Tickers(txt_TickerID.Text)
                FillGrid()
                dg.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                Clrscr()
                lblErr.Text = "Record has been Deleted !!"

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Clrscr()
        txt_TickerID.Text = String.Empty
        txt_Ticker.Text = String.Empty
        dg.SelectedIndex = -1
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        'dg.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub dg_RowCommand(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles dg.RowCommand
        If e.CommandName = "Status" Then
            Dim rows As GridViewRow = CType(CType(e.CommandSource, Control).NamingContainer, GridViewRow)
            Dim RowID As Integer = rows.RowIndex
            Dim TickerID As String = dg.Rows(RowID).Cells(1).Text

            Dim Chk As CheckBox = DirectCast(rows.FindControl("ChkStatus"), CheckBox)
            Dim Status As Integer
            If Chk.Checked = True Then
                Status = 1
            Else
                Status = 0
            End If

            ''***********************************''
            ''********** Update Status **********''
            ''***********************************''
            Dim Obj As New BusinessFacade.Reports()
            Obj.Update_TickersStatus(TickerID, Status)
            FillGrid()

            ''***********************************''

            ScriptManager.RegisterStartupScript(Me, Me.GetType(), "callFunctionsStartupScript", "ShowAlert();", True)

        End If
    End Sub


    Protected Sub dg_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg.SelectedIndexChanged
        Dim Index As Integer = dg.SelectedIndex.ToString()
        txt_TickerID.Text = dg.Rows(Index).Cells(1).Text
        txt_Ticker.Text = dg.Rows(Index).Cells(2).Text
    End Sub

End Class
