
Partial Class ApplicationSetup_frmkeywordType
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_ContentType.DataSource = New BusinessFacade.ContentType().GetRecords()
        ddl_ContentType.DataTextField = "ContentTypeName"
        ddl_ContentType.DataValueField = "ContentTypeID"
        ddl_ContentType.DataBind()
        ddl_ContentType.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_KeywordTypeID.Text = "" Then
            SaveRecord()
        Else
            UpdateRecord()
        End If
        dg_KeywordType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_KeywordType.Text = "" Then
            lblErr.Text = "Please Insert Keyword Type!!"
        ElseIf ddl_ContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Content Type!!"
        Else
            Dim objKeywordType As New BusinessFacade.KeywordType()
            objKeywordType.KeywordType = txt_KeywordType.Text
            objKeywordType.ContentTypeID = ddl_ContentType.SelectedValue
            objKeywordType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim objKeywordType As New BusinessFacade.KeywordType()
        objKeywordType.KeywordTypeID = txt_KeywordTypeID.Text
        objKeywordType.KeywordType = txt_KeywordType.Text
        objKeywordType.ContentTypeID = ddl_ContentType.SelectedValue
        objKeywordType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_KeywordType.DataSource() = New BusinessFacade.KeywordType().GetRecords()
        dg_KeywordType.DataBind()
        'dg_KeywordType.Columns(0).Visible = False
        'dg_KeywordType.Columns(2).Visible = False

    End Sub

    Protected Sub dg_KeywordType_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_KeywordType.RowCreated
        e.Row.Cells(1).Visible = False
        e.Row.Cells(3).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_KeywordType.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_KeywordType.SelectedIndex.ToString
        txt_KeywordTypeID.Text = Convert.ToInt32(dg_KeywordType.Rows(I).Cells(1).Text)
        txt_KeywordType.Text = dg_KeywordType.Rows(I).Cells(2).Text
        ddl_ContentType.SelectedValue = Convert.ToInt32(dg_KeywordType.Rows(I).Cells(3).Text)
        dg_KeywordType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        If txt_KeywordTypeID.Text = "" Then
            lblErr.Text = "Please Select Keyword Type!!"
        Else
            Dim objKeywordType As New BusinessFacade.KeywordType()
            objKeywordType.KeywordTypeID = txt_KeywordTypeID.Text
            objKeywordType.DeleteRecord(objKeywordType.KeywordTypeID)
            FillGrid()
            clrscr()
            lblErr.Text = "Record has been Deleted!!"
            dg_KeywordType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End If
    End Sub

    Private Sub clrscr()
        txt_KeywordType.Text = String.Empty
        txt_KeywordTypeID.Text = String.Empty
        ddl_ContentType.SelectedIndex = 0
    End Sub

    'Protected Sub dg_KeywordType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_KeywordType.PageIndexChanging
    '    dg_KeywordType.PageIndex = e.NewPageIndex()
    '    FillGrid()

    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_KeywordType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
