Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ListOfPackages
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkIgnoredate.Checked = True
                ChkPropSlug.Checked = True
                chkReporter.Checked = True
                chkSlug.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ListofPackages_New2.rpt"

            Dim ReporterSlug As String
            Dim PropSlug As String
            Dim ReporterID As String
            Dim FromDate As String
            Dim ToDate As String


            If chkReporter.Checked = True Then
                ReporterID = "-1"
            Else
                ReporterID = ddlReporter.SelectedValue
            End If


            If chkSlug.Checked = True Then
                ReporterSlug = "All"
            Else
                ReporterSlug = txtReporterSlug.Text
            End If

            If ChkPropSlug.Checked = True Then
                PropSlug = "All"
            Else
                PropSlug = txtPropSlug.Text
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofPackages_New2.rpt&" + "@ReporterSlug=" & ReporterSlug & "&@ProposedSlug=" & PropSlug & "&@reporter=" & ReporterID & "&@fromdate=" & FromDate & "&@todate=" & ToDate

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofPackages_New2.rpt&@ReporterSlug=" + ReporterSlug + "&@ProposedSlug=" + PropSlug + "&@reporter=" + ReporterID + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_ListOfPackages.aspx"
            ObjSave.ReportName = "Other Reports --> Q 1. How Can I View Slugs Contain Specific Word?"
            ObjSave.SaveRecord()

            ''******************************************************''
        Catch ex As Exception
            Throw
        End Try

    End Sub

    'Protected Sub chkReporter_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
    '    'If chkReporter.Checked = True Then
    '    '    ddlReporter.Enabled = False
    '    'Else
    '    '    ddlReporter.Enabled = True
    '    'End If
    'End Sub

    'Protected Sub chkSlug_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
    '    'If chkSlug.Checked = True Then
    '    '    txtReporterSlug.Enabled = False
    '    'Else
    '    '    txtReporterSlug.Enabled = True
    '    'End If
    'End Sub

    'Protected Sub ChkPropSlug_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
    '    'If ChkPropSlug.Checked = True Then
    '    '    txtPropSlug.Enabled = False
    '    'Else
    '    '    txtPropSlug.Enabled = True
    '    'End If
    'End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        ''''''''''''''''''''''''''''''''''''
        Dim Description As String = txtReporterSlug.Text


        Dim Slug1 As String = ""
        Dim Slug2 As String = ""
        Dim Slug3 As String = ""
        Dim Slug4 As String = ""
        Dim Slug5 As String = ""

        
        ''*****************************''

        Dim ReporterSlug As String
        Dim PropSlug As String
        Dim ReporterID As String
        Dim FromDate As String
        Dim ToDate As String

        If chkReporter.Checked = True Then
            ReporterID = "-1"
        Else
            ReporterID = ddlReporter.SelectedValue
        End If


        If chkSlug.Checked = True Then
            ReporterSlug = "All"
            Slug1 = "All"
            Slug2 = "All"
            Slug3 = "All"
            Slug4 = "All"
            Slug5 = "All"
        Else
            'ReporterSlug = txtReporterSlug.Text

            Dim stringItems() As String = Description.Split("~")
            Dim myArrayList As New ArrayList
            Dim k As Integer
            k = stringItems.Length

            If txtReporterSlug.Text = "" Then
                Slug1 = ""
                Slug1 = ""
                Slug2 = ""
                Slug3 = ""
                Slug4 = ""
                Slug5 = ""
            End If

            If k <> 0 Then
                Slug1 = stringItems(0).ToString
            End If


            If k = 1 Then
                Slug2 = stringItems(0).ToString
                Slug3 = stringItems(0).ToString
                Slug4 = stringItems(0).ToString
                Slug5 = stringItems(0).ToString
            End If

            If k > 1 And k < 3 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(1).ToString
                Slug4 = stringItems(1).ToString
                Slug5 = stringItems(1).ToString

            End If
            If k > 2 And k < 4 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = stringItems(2).ToString
                Slug5 = stringItems(2).ToString
            End If

            If k > 3 And k < 5 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = stringItems(3).ToString
                Slug5 = stringItems(3).ToString
            End If

            If k > 4 And k < 6 Then
                Slug2 = stringItems(1).ToString
                Slug3 = stringItems(2).ToString
                Slug4 = stringItems(3).ToString
                Slug5 = stringItems(4).ToString
            End If

        End If

        If ChkPropSlug.Checked = True Then
            PropSlug = "All"
        Else
            PropSlug = txtPropSlug.Text
        End If

        If chkIgnoredate.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
            ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
        End If

        Dim BaseStationID As String
        If Me.chkStation.Checked = True Then
            BaseStationID = "-1"
        Else
            BaseStationID = ddlBaseStation.SelectedValue
        End If

        'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofPackages_New2.rpt&" + "@ReporterSlug=" & ReporterSlug & "&@ProposedSlug=" & PropSlug & "&@reporter=" & ReporterID & "&@fromdate=" & FromDate & "&@todate=" & ToDate

        'Response.Redirect(qryString)

        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofPackages_New2.rpt&@Slug1=" + Slug1 + "&@Slug2=" + Slug2 + "&@Slug3=" + Slug3 + "&@Slug4=" + Slug4 + "&@Slug5=" + Slug5 + "&@ProposedSlug=" + PropSlug + "&@reporter=" + ReporterID + "&@BaseStationID=" + BaseStationID + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ListofPackages_New2.rpt&@Slug1=" + Slug1 + "&@Slug2=" + Slug2 + "&@Slug3=" + Slug3 + "&@Slug4=" + Slug4 + "&@Slug5=" + Slug5 + "&@ProposedSlug=" + PropSlug + "&@reporter=" + ReporterID + "&@BaseStationID=" + BaseStationID + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_ListOfPackages.aspx"
        ObjSave.ReportName = "Other Reports --> Q 1. How Can I View Slugs Contain Specific Word?"
        ObjSave.SaveRecord()

        ''******************************************************''

    End Sub

   
   
End Class
