Imports System.Data.SqlClient
Imports System.Data
Partial Class StoreManagement_StoreManagement
    Inherits System.Web.UI.Page
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)
    Dim objTrans As SqlTransaction
    Dim strPrNo As String = ""
    Dim strSIRNo As String = ""
    Dim strCity As String = "0"
    Dim strSource As String = ""
    Dim strTapeType As String = "0"
    Dim FromDate As String = "0"
    Dim ToDate As String = "0"
    
    Public Sub BindCombo()
        Dim objCity As New City
        Dim objTapeType As New TapeType
        Dim objCountry As New Country

        'ddl_City.DataSource = objCity.GetRecords()
        'ddl_City.DataTextField = "CityName"
        'ddl_City.DataValueField = "CityID"
        'ddl_City.DataBind()

        ddl_Country.DataSource = objCountry.GetRecords()
        ddl_Country.DataTextField = "CountryName"
        ddl_Country.DataValueField = "CountryID"
        ddl_Country.DataBind()

        ddl_TapeType.DataSource = objTapeType.GetRecords()
        ddl_TapeType.DataTextField = "TapeType"
        ddl_TapeType.DataValueField = "TapeTypeID"
        ddl_TapeType.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub CheckValues()
        If txt_PrNo.Text = "" Then
            strPrNo = "All"
        Else
            strPrNo = txt_PrNo.Text
        End If
        If txt_SIRNo.Text = "" Then
            strSIRNo = "All"
        Else
            strSIRNo = txt_SIRNo.Text
        End If
        If txt_Source.Text = "" Then
            strSource = "All"
        Else
            strSource = txt_Source.Text
        End If
        If IgnorerCity.Checked Then
            strCity = "0"
        Else
            '''''''''' Get CityID '''''''''''''''
            Dim CityID As Integer
            Dim objCityID As New BusinessFacade.TapeIssuance()
            objCityID.CityName = txt_CityName.Text
            CityID = objCityID.GetCityID_byCityName(objCityID.CityName)

            strCity = CityID
        End If

        If IgnoreTapeType.Checked Then
            strTapeType = "0"
        Else
            strTapeType = ddl_TapeType.SelectedValue.ToString()
        End If

        If chkDate.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = txtFromDate.Text
            ToDate = txtToDate.Text
        End If


    End Sub

    Protected Sub ddl_Country_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_Country.SelectedIndexChanged

    End Sub

    Private Sub FillGrid()
        Try
            CheckValues()
            Dim Qry As String
            Dim cmd As New SqlCommand()
            cmd.Connection = objConnection
            cmd.CommandText = "GetData_StockRegisterMaster"
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.Parameters.AddWithValue("@PRNo", strPrNo)
            cmd.Parameters.AddWithValue("@SIRNo", strSIRNo)
            cmd.Parameters.AddWithValue("@Source", strSource)
            cmd.Parameters.AddWithValue("@City", strCity)
            cmd.Parameters.AddWithValue("@TapeType", strTapeType)
            cmd.Parameters.AddWithValue("@FromDate", FromDate)
            cmd.Parameters.AddWithValue("@ToDate", ToDate)
            Dim ad As New SqlDataAdapter(cmd)
            Dim ds As New Data.DataSet()
            ad.Fill(ds, "Stock_Register")
            'ad.Fill(ds)s
            Qry = ad.SelectCommand.CommandText
            ad.SelectCommand.CommandText = "GetData_StockRegisterDetail"
            ad.SelectCommand.Parameters.Clear()
            ad.SelectCommand.Parameters.AddWithValue("@PRNo", strPrNo)
            ad.SelectCommand.Parameters.AddWithValue("@SIRNo", strSIRNo)
            ad.SelectCommand.Parameters.AddWithValue("@Source", strSource)
            ad.SelectCommand.Parameters.AddWithValue("@City", strCity)
            ad.SelectCommand.Parameters.AddWithValue("@TapeType", strTapeType)
            ad.SelectCommand.Parameters.AddWithValue("@FromDate", FromDate)
            ad.SelectCommand.Parameters.AddWithValue("@ToDate", ToDate)
            'ad.SelectCommand.Parameters.AddWithValue("", ddl_RecordType.SelectedValue)
            ad.Fill(ds, "StockRegister_Detail")
            ds.Relations.Add(ds.Tables("Stock_Register").Columns("StockRegisterID"), ds.Tables("StockRegister_Detail").Columns("StockRegisterID"))
            grd_Result.DataSource = ds.Tables("Stock_Register").DefaultView
            grd_Result.DataBind()
            If ds.Tables(0).Rows.Count > 0 Then
                grd_Result.DataSource = ds.Tables(0)
                grd_Result.DataBind()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch.Click
        FillGrid()

      
    End Sub

    Public Sub DeleteRecord(ByVal strStoreRegisterID As Integer)

        Dim ObjDeleteMaster As New BusinessFacade.StoreManagement
        ObjDeleteMaster.DeleteRecord_Detail(strStoreRegisterID)

        Dim ObjDeleteDetail As New BusinessFacade.StoreManagement
        ObjDeleteDetail.DeleteRecord(strStoreRegisterID)


        'Dim objStore As BusinessFacade.StoreManagement
        'Try
        '    Dim Qry As String
        '    Dim cmd As New SqlCommand()
        '    cmd.Connection = objConnection
        '    Dim strDetailID As Integer


        '    Dim ds As New DataSet()

        '    ds = objStore.GetRecords_Detail(strStoreRegisterID)

        '    If ds.Tables(0).Rows.Count > 0 Then
        '        strDetailID = Convert.ToInt32(ds.Tables(0).Rows(0)("StockRegisterDetailID"))
        '        Dim ds_TapeLibrary As New DataSet()
        '        ds_TapeLibrary = objStore.GetRecords_LibraryDetail(strDetailID)

        '        If ds_TapeLibrary.Tables(0).Rows.Count > 0 Then
        '            lblMsg.Text = "This Tape is being used you cannot delete this data"
        '        Else
        '            If objConnection.State = ConnectionState.Closed Then
        '                objConnection.Open()
        '            End If
        '            objTrans = objConnection.BeginTransaction()
        'objStore.DeleteRecord_Detail(strStoreRegisterID)
        'objStore.DeleteRecord(strStoreRegisterID)
        '            objTrans.Commit()
        '            objConnection.Close()
        '            lblMsg.Text = "Record Deleted"
        '        End If
        '    Else
        '        If objConnection.State = ConnectionState.Closed Then
        '            objConnection.Open()
        '        End If
        '        objTrans = objConnection.BeginTransaction()
        '        objStore.DeleteRecord(strStoreRegisterID)
        '        objTrans.Commit()
        '        objConnection.Close()
        '        lblMsg.Text = "Record Deleted"
        '    End If
        'Catch ex As Exception
        '    objTrans.Rollback()
        '    objConnection.Close()
        '    Throw
        'End Try

    End Sub

    Protected Sub grd_Result_SelectedCellsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedCellsEventArgs) Handles grd_Result.SelectedCellsChange
        'Label1.Text = e.SelectedCells.Item(1).Value.ToString
    End Sub

    Protected Sub grd_Result_SelectedRowsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedRowsEventArgs) Handles grd_Result.SelectedRowsChange
        Dim strStockID As String = grd_Result.DisplayLayout.SelectedRows.Item(0).Cells(1).ToString 'e.SelectedRows.Item(e.SelectedRows.IndexOf(grd_Re).Cells(1).Value.ToString
        If strStockID <> "0" Then
            Response.Redirect("Store_StockRegister.aspx?ID=" & strStockID)

        End If
    End Sub

    Protected Sub grd_Result_ItemCommand(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.UltraWebGridCommandEventArgs) Handles grd_Result.ItemCommand
        Dim StockRegisterID As Integer
        Try
            If CType(e.CommandSource, ImageButton).CommandName = "Delete" Then
                StockRegisterID = CType(e.ParentControl, Infragistics.WebUI.UltraWebGrid.CellItem).Cell.Row.Cells.FromKey("StockRegisterID").Value
                If StockRegisterID <> 0 Then
                    DeleteRecord(StockRegisterID)
                    FillGrid()
                    'lblMsg.Text = "Record Deleted Successfully."
                End If
            Else
                StockRegisterID = CType(e.ParentControl, Infragistics.WebUI.UltraWebGrid.CellItem).Cell.Row.Cells.FromKey("StockRegisterID").Value
                If StockRegisterID <> 0 Then
                    Response.Redirect("Store_StockRegister.aspx?ID=" & StockRegisterID)
                End If
            End If
        Catch ex As Exception
            'Throw
            lblErr.Text = "This Record has been already in Use !"
        End Try
    End Sub

    Protected Sub btnAddNew_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnAddNew.Click
        Response.Redirect("Store_StockRegister.aspx?ID=-1")
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub grd_Result_PageIndexChanged(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.PageEventArgs) Handles grd_Result.PageIndexChanged
        grd_Result.DisplayLayout.Pager.CurrentPageIndex = e.NewPageIndex()
        FillGrid()
    End Sub
End Class
