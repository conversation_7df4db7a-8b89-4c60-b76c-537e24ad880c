<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="LostDamage.aspx.vb" Inherits="LostDamage_LostDamage" title="Home > Tape Management > Lost and Damage Tape" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <table width="100%">
            <tr>
                <td style="width: 100%; height: 18px; text-decoration: underline;" class="labelheading">
                    <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                    &gt; Lost &amp; Damage Tape &gt; Add New</td>
            </tr>
     <tr>
         <td style="width: 100px; height: 8px">
         </td>
     </tr>
            
            <tr >
                <td rowspan="" >
                    <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue">
                        <asp:Image ID="Image1" runat="server" />
                        <asp:Label ID="Label2" runat="server" Text="Lost & damage Tape By Employee" Width="560px" Font-Bold="True" CssClass="heading1"></asp:Label></asp:Panel>
                    <asp:Panel ID="ContentPanel" runat="server" Height="0px" Width="100%">
                        <table width="100%">
                            <tr>
                                <td style="height: 70px" >
                    <table style="width: 232px">
                        <tr class="mytext">
                            <td>
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 13px" >
                                Employee</td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top" >
                                <asp:TextBox ID="txtEmployee" runat="server" Width="216px" CssClass="mytext"></asp:TextBox></td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top">
                            </td>
                        </tr>
                        <asp:Label ID="Label4" runat="server" Font-Bold="True" ForeColor="Red" Width="464px"></asp:Label></table>
                                    
                    <asp:GridView ID="dg_emp" runat="server" AutoGenerateColumns="False" Width="1072px" CssClass="gridContent" AllowPaging="True" PageSize="15">
                        <Columns>
                            <asp:BoundField DataField="TapeLibraryID" Visible="False" >
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="1px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeIssuanceDate" HeaderText="Issue Date" />
                            <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" >
                                <HeaderStyle Width="200px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape No." />
                            <asp:TemplateField HeaderText="Damage">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="Chk_Damage" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Lost">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="Chk_Lost" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Cost Recoverd">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="Chk_CostRecoverd" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Disposed / Right off">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" Width="125px" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="Chk_DisposedOff" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Reason">
                                <ItemTemplate>
                                    <asp:TextBox ID="txtEmpReason" runat="server" Height="40px" TextMode="MultiLine" Width="392px"></asp:TextBox>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                                </td>
                            </tr>
                            <tr class="mytext">
                                <td style="width: 100%; height: 29px" class="bottomMain">
                                    &nbsp;
                                    <asp:Button ID="bttnSearchByEmployee" runat="server" CssClass="buttonA" Text="Search" />&nbsp;
                                    <asp:Button CssClass="buttonA" ID="bttnMarkDamage" runat="server" Text="Marked Damage" Width="120px" />&nbsp;
                                       <asp:Button
                                        CssClass="buttonA" ID="bttnClear" runat="server" Text="Clear" Width="56px" /></td>
                            </tr>
                        </table>
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="464px"></asp:Label></asp:Panel>
                    
                </td>
            </tr>
            <tr>
                <td style="width: 100%; height: 21px" valign="top">
                    <asp:Panel ID="TitlePanel_2" runat="server" Width="100%" BackColor="LightSteelBlue">
                        <asp:Image ID="Image2" runat="server" />
                        <asp:Label ID="Label3" runat="server" Font-Bold="True" Text="Lost & damage Tape By Tape Number"
                            Width="576px" CssClass="heading1"></asp:Label></asp:Panel>
                    <asp:Panel ID="ContentPanel_2" runat="server" Height="0px" Width="100%">
                        <table style="width: 100%">
                            <tr>
                                <td valign="top">
                    <table style="width: 304px">
                        <tr class="mytext">
                            <td>
                            </td>
                            <td>
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 13px" >
                                Tape No.</td>
                            <td style="height: 13px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top" >
                                <asp:TextBox ID="txt_TapeNo" runat="server" CssClass="mytext"></asp:TextBox></td>
                            <td>
                                <asp:DropDownList CssClass="mytext" ID="txt_TapeNumber" runat="server" Width="152px" Visible="False">
                                </asp:DropDownList></td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top">
                            </td>
                            <td>
                            </td>
                        </tr>
                    </table>
                    <asp:GridView ID="dg_ByTapeNo" runat="server" AutoGenerateColumns="False" Width="600px" CssClass="gridContent">
                        <Columns>
                            <asp:BoundField DataField="LibID" Visible="False" >
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="1px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="EmpID" Visible="False" >
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="1px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:TemplateField HeaderText="Reason">
                                <ItemTemplate>
                                    &nbsp;<asp:TextBox ID="txt_Reason_22" runat="server" Width="312px" Height="56px" TextMode="MultiLine"></asp:TextBox>
                                </ItemTemplate>
                                <HeaderStyle Width="120px" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Damage">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckDamage" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Lost">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckLost" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Cost Recovered">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckCostRecovered" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Disposed Off">
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    <asp:CheckBox ID="CheckDisposedOff" runat="server" />
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                                </td>
                            </tr>
                            <tr>
                                <td class="bottomMain" style="width: 100%; height: 28px" valign="middle">
                                    &nbsp;<asp:Button CssClass="buttonA" ID="bttnAdd" runat="server" Text="Search" Width="72px" />
                                    <asp:Button CssClass="buttonA" ID="bttn_Marked_TapeNo" runat="server" Text="Marked Damage" Width="120px" />
                                    <asp:Button
                                       CssClass="buttonA" ID="bttnClear_2" runat="server" Text="Clear" Width="56px" /></td>
                            </tr>
                        </table>
                                                <asp:Label ID="lblErr_2" runat="server" Font-Bold="True" ForeColor="Red" Width="488px"></asp:Label></asp:Panel>
                    <cc1:CollapsiblePanelExtender 
                        ID="CollapsiblePanelExtender2" 
                        runat="server" 
                        collapsecontrolid="TitlePanel_2"
                        collapsed="true" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (Lost & Damage By Tape Number) --"
                        expandcontrolid="TitlePanel_2" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (Lost & Damage By Tape Number) --"
                        imagecontrolid="Image2" 
                        suppresspostback="true"
                        textlabelid="Label3"
                        TargetControlID="ContentPanel_2">
                    </cc1:CollapsiblePanelExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="txt_TapeNumber">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_Employee">
                    </cc1:ListSearchExtender>
                    <cc1:CollapsiblePanelExtender 
                        ID="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="true" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (Lost & Damage By Employee) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (Lost & Damage By Employee) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label2"
                        TargetControlID="ContentPanel">
                    </cc1:CollapsiblePanelExtender>
                    <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="getTapeNumber_LostDamage"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo">
                </cc1:AutoCompleteExtender>
                                <asp:DropDownList CssClass="mytext" ID="ddl_Employee" runat="server" Width="224px" AutoPostBack="True" Visible="False">
                                </asp:DropDownList>
                
                                <asp:TextBox CssClass="mytext" ID="txt_Count" runat="server" Visible="False" Width="64px"></asp:TextBox>
                    <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtenderEmployee" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="getEmployee"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtEmployee">
                    </cc1:AutoCompleteExtender>
                </td>
            </tr>
            <tr>
                <td style="width: 100px; height: 2px">
                </td>
            </tr>
        </table>
         <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
        </cc1:ToolkitScriptManager>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

