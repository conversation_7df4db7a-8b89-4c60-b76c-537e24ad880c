Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_MergeTapeReport
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                ddlContentType.SelectedIndex = 0
                chkTapeNumber.Checked = True
                chkIgnoredate.Checked = True
                ddlTapeNumber.Enabled = False


            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click

        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "MergeTapeReport.rpt"

            Dim ContentType As String
            Dim TapeNumber As String = ""
            Dim FromDate As String
            Dim ToDate As String

            If ddlContentType.SelectedIndex = "0" Then
                ContentType = "-1"
            Else
                ContentType = ddlContentType.SelectedValue
            End If

            If chkTapeNumber.Checked = True Then
                TapeNumber = "All"
            Else
                Dim arr As Array = Split(txtTapeNumber.Text, "#")
                If arr.Length = 2 Then
                    TapeNumber = txtTapeNumber.Text
                End If
                'TapeNumber = ddlTapeNumber.SelectedItem.Text
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If


            'qryString = "ReportViewer.aspx?ReportName=" + "MergeTapeReport.rpt&" + "@TapeNumber=" & TapeNumber & "&@fromdate=" & FromDate & "&@todate=" & ToDate & "&@ContentType=" & ContentType
            'Response.Redirect(qryString)

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=MergeTapeReport.rpt&@TapeNumber=" + TapeNumber + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@ContentType=" + ContentType + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=MergeTapeReport.rpt&@TapeNumber=" + TapeNumber + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@ContentType=" + ContentType + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_MergeTapeReport.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''
        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub ddlContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlContentType.SelectedIndexChanged

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkTapeNumber.Checked = True Then
            ddlTapeNumber.Enabled = False
        Else
            ddlTapeNumber.Enabled = True
        End If
    End Sub
End Class
