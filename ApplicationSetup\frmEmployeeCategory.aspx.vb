
Partial Class ApplicationSetup_frmEmployeeCategory
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            FillGrid()
        End If
    End Sub


    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.EmployeeCategory().IsExists_EmployeeCategory(txt_EmpCategory.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : EmployeeCategory already Exists !"
        Else
            If txt_EmpCategoryID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_EmpCategory.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_EmpCategory.Text = "" Then
            lblErr.Text = "Please Insert Employee Category!!"
        Else
            Dim objEmpCategory As New BusinessFacade.EmployeeCategory()
            objEmpCategory.EmployeeCategory = txt_EmpCategory.Text
            objEmpCategory.Description = txt_Description.Text
            objEmpCategory.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If

    End Sub

    Private Sub UpdateRecord()
        Dim objEmpCategory As New BusinessFacade.EmployeeCategory()
        objEmpCategory.EmployeeCategoryID = txt_EmpCategoryID.Text
        objEmpCategory.EmployeeCategory = txt_EmpCategory.Text
        objEmpCategory.Description = txt_Description.Text
        objEmpCategory.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_EmpCategory.DataSource() = New BusinessFacade.EmployeeCategory().GetRecords()
        dg_EmpCategory.DataBind()
        ' dg_EmpCategory.Columns(0).Visible = False

    End Sub

    Protected Sub dg_EmpCategory_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_EmpCategory.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_EmpCategory_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_EmpCategory.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_EmpCategory.SelectedIndex.ToString
        txt_EmpCategoryID.Text = Convert.ToInt32(dg_EmpCategory.Rows(I).Cells(1).Text)
        txt_EmpCategory.Text = dg_EmpCategory.Rows(I).Cells(2).Text
        txt_Description.Text = dg_EmpCategory.Rows(I).Cells(3).Text
        dg_EmpCategory.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_EmpCategoryID.Text = "" Then
                lblErr.Text = "Please Select Employee Category First!!"
            Else
                Dim objEmpCategory As New BusinessFacade.EmployeeCategory()
                objEmpCategory.EmployeeCategoryID = txt_EmpCategoryID.Text
                objEmpCategory.DeleteRecord(objEmpCategory.EmployeeCategoryID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_EmpCategory.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Employee Category is Already in Used !"
            clrscr()
            dg_EmpCategory.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_EmpCategory.Text = String.Empty
        txt_EmpCategoryID.Text = String.Empty
        txt_Description.Text = String.Empty
    End Sub

    'Protected Sub dg_EmpCategory_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_EmpCategory.PageIndexChanging
    '    dg_EmpCategory.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_EmpCategory.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
