<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_DocumenterEntertainment.aspx.vb" Inherits="Reports_Frm_rpt_DocumenterEntertainment" title="Other Reports > How can I view Program Report by Documenter Name" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
  <table width="100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports >  How can I view Program Report by Documenter Name?"
                        Width="704px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="WIDTH: 164px; HEIGHT: 13px">&nbsp; </TD><TD style="WIDTH: 229px; HEIGHT: 13px"></TD><TD style="WIDTH: 191px; HEIGHT: 13px"></TD><TD style="WIDTH: 110px; HEIGHT: 13px"></TD><TD style="WIDTH: 162px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD><TD style="WIDTH: 107px; HEIGHT: 13px"></TD></TR><TR class="mytext"><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">Documenter Name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="ChkEmployee" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">Tape Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="ChkTapeNumber" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">1stKeyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chk_1stKW" runat="server" Text="Ignore" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">2ndKeyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="Chk_2ndKW" runat="server" Text="Ignore" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">Program Name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkProgram" runat="server" Text="Ignore" AutoPostBack="True" Checked="True" ></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">Station <asp:CheckBox id="chkStation" runat="server" Text="Ignore" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 21px"><asp:TextBox id="txtEmployee" runat="server" Width="178px" CssClass="mytext"></asp:TextBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial"><asp:TextBox id="txtTapeNumber" runat="server" Width="178px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 191px"><asp:TextBox id="txt1stKeywords" runat="server" Width="174px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 110px"><asp:TextBox id="txt2ndKeywords" runat="server" Width="174px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 162px"><asp:TextBox id="txtProgramName" runat="server" Width="174px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 107px"><asp:DropDownList id="ddlBaseStation" runat="server" Width="88px" CssClass="mytext">
                                </asp:DropDownList></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 210px; HEIGHT: 23px">&nbsp;</TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial; HEIGHT: 23px"></TD><TD style="WIDTH: 191px; HEIGHT: 23px"></TD><TD style="WIDTH: 110px; HEIGHT: 23px"></TD><TD style="WIDTH: 162px; HEIGHT: 23px"></TD><TD style="WIDTH: 107px; HEIGHT: 23px"></TD><TD style="WIDTH: 107px; HEIGHT: 23px"></TD></TR><TR class="mytext"><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">&nbsp;Abstract&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkSlug" runat="server" Text="Ignore" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">&nbsp;From Date &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore" Checked="True"></asp:CheckBox></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial">&nbsp;To Date</TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px"></asp:Label></TD><TD style="FONT-SIZE: 8pt; WIDTH: 229px; FONT-FAMILY: Arial"><asp:CheckBox id="ChkUrduSlug" runat="server" Text="Ignore" Width="1px" AutoPostBack="True" Checked="True" Visible="False"></asp:CheckBox></TD><TD style="WIDTH: 107px"></TD><TD style="WIDTH: 107px"></TD></TR><TR class="mytext"><TD style="WIDTH: 164px" vAlign=top><asp:TextBox id="txtAbstract" runat="server" Width="175px" Height="48px" CssClass="mytext" TextMode="MultiLine"></asp:TextBox></TD><TD style="WIDTH: 229px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="104px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 191px" vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="96px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 110px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 162px" vAlign=top><asp:TextBox id="txtUrduScript" runat="server" Width="164px" Height="48px" CssClass="mytext" TextMode="MultiLine" Visible="False"></asp:TextBox></TD><TD style="WIDTH: 107px" vAlign=top></TD><TD style="WIDTH: 107px" vAlign=top></TD></TR><TR class="mytext"><TD vAlign=top colSpan=6><cc1:CalendarExtender id="CalendarExtender1" runat="server" TargetControlID="txtFromdate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender><cc1:CalendarExtender id="CalendarExtender2" runat="server" TargetControlID="txtToDate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_NewsKeywords_1" runat="server" TargetControlID="txt1stKeywords" ServicePath="AutoComplete.asmx" ServiceMethod="EntKeywords" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_2ndKeywords" runat="server" TargetControlID="txt2ndKeywords" ServicePath="AutoComplete.asmx" ServiceMethod="EntKeywords" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_FootageTypes" runat="server" TargetControlID="txtProgramName" ServicePath="AutoComplete.asmx" ServiceMethod="Program" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1" MinimumPrefixLength="1"></cc1:AutoCompleteExtender><cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" TargetControlID="txtEmployee" ServicePath="AutoComplete.asmx" ServiceMethod="GetEmployee" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1" MinimumPrefixLength="3"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_2" runat="server" TargetControlID="txtTapeNumber" ServicePath="AutoComplete.asmx" ServiceMethod="TapeContentEnt_TapeNumber_GetRecords_2" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1" MinimumPrefixLength="1"></cc1:AutoCompleteExtender></TD><TD vAlign=top colSpan=1></TD></TR></TBODY></TABLE>
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="height: 29px">
                                &nbsp;
                                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Text="View Report" Width="88px" />
                            </td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;&nbsp;
            </td>
        </tr>
    </table>
    &nbsp;<cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server"
        CollapseControlID="TitlePanel" Collapsed="false" CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Form (Other Reports > How can I view Program Report by Documenter Name?) --"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" ExpandedText="-- Hide Form (Other Reports > How can I view Program Report by Documenter Name?) --"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>

