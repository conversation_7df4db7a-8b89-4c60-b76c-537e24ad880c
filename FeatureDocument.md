
# Feature Document: Digital Archive Management System (DAMS)

## 1. Overview

This document outlines the features of the Digital Archive Management System (DAMS), a web-based application designed to manage a physical and digital media archive. The system is built using ASP.NET and VB.NET, with a SQL Server backend.

## 2. Core Features

### 2.1. User and Access Management

*   **User Authentication:** Users can log in to the system through a dedicated login page.
*   **User Management:** Administrators can create, modify, and deactivate user accounts.
*   **Password Management:** Users can change their own passwords.

### 2.2. Organizational Structure Management

*   **Department Management:** Create and manage departments within the organization.
*   **Designation Management:** Define and manage employee designations.
*   **Employee Management:** Maintain a directory of employees, including their category and mappings to departments.

### 2.3. Location Management

*   **Country Management:** Manage a list of countries.
*   **City Management:** Manage a list of cities within countries.

### 2.4. Tape Management

*   **Tape Issuing:** Issue tapes to users or departments.
*   **Bulk Tape Creation:** Create new tape records in bulk.
*   **Bulk Tape Management:** Perform bulk updates on tape records.
*   **Tape Return:** Process the return of tapes to the archive.
*   **Tape Status Tracking:** Track the status of tapes (e.g., in library, issued, etc.).
*   **Tape Library Management:** Manage the physical tape library locations.

### 2.5. Content and Metadata Management

*   **Content Type Management:** Define and manage different types of content (e.g., news, entertainment).
*   **Sub-Content Type Management:** Define and manage sub-categories of content.
*   **Footage Type Management:** Manage different types of footage.
*   **Keyword Management:** Create and manage keywords for news and entertainment content.
*   **Keyword Mapping:** Map keywords to different key types.
*   **Program Management:** Manage master programs and their child programs.
*   **Slug Management:** Create, edit, and manage slugs for content.

### 2.6. Vendor and Requisition Management

*   **Vendor Management:** Maintain a list of vendors.
*   **Requisition Source Management:** Manage the sources from which requisitions can be made.

### 2.7. System Configuration

*   **Recycle Turn Management:** Configure the recycling turn for tapes.
*   **Stock Status Management:** Manage the stock status of items.
*   **Ticker Management:** Create and manage tickers for display on the site.

## 3. Technical Details

*   **Frontend:** ASP.NET with VB.NET code-behind.
*   **Backend:** SQL Server Database.
*   **Key Libraries:** AjaxControlToolkit, Dundas WebChart, Crystal Reports.
