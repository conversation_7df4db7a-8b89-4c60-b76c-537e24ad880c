
Partial Class ApplicationSetup_frmRecycleTurn
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.RecycleTurn().IsExists_RecycleTurn(txt_RecycleTurn.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : RecycleTurn already Exists !"
        Else
            If txt_RecycleTurnID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_RecycleTurn.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()
        If txt_RecycleTurn.Text = "" Then
            lblErr.Text = "Please insert Recycle Turn!!"
        Else
            Dim ObjRecycleTurn As New BusinessFacade.RecycleTurn()
            ObjRecycleTurn.RecycleTurn = txt_RecycleTurn.Text
            ObjRecycleTurn.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjRecycleTurn As New BusinessFacade.RecycleTurn()
        ObjRecycleTurn.RecycleTurnID = txt_RecycleTurnID.Text
        ObjRecycleTurn.RecycleTurn = txt_RecycleTurn.Text
        ObjRecycleTurn.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_RecycleTurn.DataSource() = New BusinessFacade.RecycleTurn().GetRecords()
        dg_RecycleTurn.DataBind()
        'dg_RecycleTurn.Columns(0).Visible = False

    End Sub

    Protected Sub dg_RecycleTurn_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_RecycleTurn.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_RecycleTurn.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_RecycleTurn.SelectedIndex.ToString
        txt_RecycleTurnID.Text = Convert.ToInt32(dg_RecycleTurn.Rows(I).Cells(1).Text)
        txt_RecycleTurn.Text = dg_RecycleTurn.Rows(I).Cells(2).Text
        dg_RecycleTurn.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_RecycleTurnID.Text = "" Then
                lblErr.Text = "Please select Recycle Turn First!!"
            Else
                Dim ObjRecycleTurn As New BusinessFacade.RecycleTurn()
                ObjRecycleTurn.RecycleTurnID = txt_RecycleTurnID.Text
                ObjRecycleTurn.DeleteRecord(ObjRecycleTurn.RecycleTurnID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_RecycleTurn.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention:  This Recycle Turn is Already in Used !"
            clrscr()
            dg_RecycleTurn.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
        
    End Sub

    Private Sub clrscr()
        txt_RecycleTurn.Text = String.Empty
        txt_RecycleTurnID.Text = String.Empty
    End Sub

    'Protected Sub dg_RecycleTurn_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_RecycleTurn.PageIndexChanging
    '    dg_RecycleTurn.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_RecycleTurn.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
