<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmSlug.aspx.vb" Inherits="ApplicationSetup_frmSlug" title="Home > Slugs " %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Country" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w2" CssClass="labelheading">Home</asp:LinkButton> &gt;&nbsp;Slug &gt; Add New</TD></TR><TR><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD></TD><TD colSpan=2></TD><TD colSpan=1></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px"></TD><TD style="HEIGHT: 22px" colSpan=2>Tape Slug</TD><TD style="HEIGHT: 22px" colSpan=1></TD></TR><TR class="mytext"><TD style="HEIGHT: 22px"></TD><TD style="HEIGHT: 22px" colSpan=2><asp:TextBox id="txtSearch" runat="server" __designer:wfdid="w6" CssClass="mytext" Width="264px"></asp:TextBox></TD><TD style="HEIGHT: 22px" colSpan=1><asp:Button id="bttnSearch" onclick="bttnSearch_Click" runat="server" Text="Search" __designer:wfdid="w10" CssClass="buttonA" Font-Bold="True"></asp:Button></TD></TR><TR class="mytext"><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px" colSpan=2></TD><TD style="HEIGHT: 13px" colSpan=1></TD></TR><TR class="mytext"><TD></TD><TD>Old Word</TD><TD>New Word</TD><TD></TD></TR><TR class="mytext"><TD></TD><TD><asp:TextBox id="txtOldWord" runat="server" __designer:wfdid="w1" CssClass="mytext"></asp:TextBox></TD><TD><asp:TextBox id="txtNewWord" runat="server" __designer:wfdid="w2" CssClass="mytext"></asp:TextBox></TD><TD></TD></TR><TR class="mytext"><TD></TD><TD></TD><TD></TD><TD></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Width="424px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;&nbsp; <asp:Button id="bttnChange" onclick="bttnChange_Click" runat="server" Text="Change Slugs" __designer:wfdid="w5" CssClass="buttonA" Width="120px" Font-Bold="True"></asp:Button></TD></TR><TR><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w14" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 423px"><asp:GridView id="dg" runat="server" ForeColor="Black" __designer:wfdid="w8" CssClass="gridContent" Width="100%" OnPageIndexChanging="dg_PageIndexChanging" AutoGenerateColumns="False" PageSize="2000" AllowPaging="True">
<RowStyle ForeColor="Black"></RowStyle>

<EmptyDataRowStyle ForeColor="Transparent"></EmptyDataRowStyle>
<Columns>
<asp:BoundField ApplyFormatInEditMode="True" DataField="TapeSlugID" HeaderText="TapeSlugID" Visible="False"></asp:BoundField>
<asp:BoundField ApplyFormatInEditMode="True" DataField="TapeSlug" HeaderText="Tape Slug"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

