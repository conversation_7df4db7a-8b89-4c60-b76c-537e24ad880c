Imports System.Data
Partial Class LostDamage_LostDamage
    Inherits System.Web.UI.Page
    Dim Table1 As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Dim C1 As DataColumn = New DataColumn("TapeNumber")
        C1.DataType = System.Type.GetType("System.String")
        Table1.Columns.Add(C1)

        Dim C2 As DataColumn = New DataColumn("Reason")
        C2.DataType = System.Type.GetType("System.String")
        Table1.Columns.Add(C2)

        Dim C3 As DataColumn = New DataColumn("EmpID")
        C3.DataType = System.Type.GetType("System.Int32")
        Table1.Columns.Add(C3)

        Dim C4 As DataColumn = New DataColumn("LibID")
        C4.DataType = System.Type.GetType("System.Int32")
        Table1.Columns.Add(C4)

        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                '    lbl_UserName.Text = Master.FooterText
                '    Dim Arr_UserID As Array = Split(lbl_UserName.Text, ",")
                '    lbl_UserName.Text = Arr_UserID(1)
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                BindCombo()
                txt_Count.Text = "0"
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()
        Try
            'ddl_Employee.DataTextField = "EmployeeName"
            'ddl_Employee.DataValueField = "EmployeeID"
            'ddl_Employee.DataSource = New BusinessFacade.Employee().GetRecords()
            'ddl_Employee.DataBind()
            'ddl_Employee.Items.Insert(0, "--Select--")

            'txt_TapeNumber.DataTextField = "TapeNumber"
            'txt_TapeNumber.DataValueField = "ID"
            'txt_TapeNumber.DataSource = New BusinessFacade.LostDamage().LostDamage_GetTapes()
            'txt_TapeNumber.DataBind()
            'txt_TapeNumber.Items.Insert(0, "--Select--")

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub ddl_Employee_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_Employee.SelectedIndexChanged

        If ddl_Employee.SelectedIndex <> "0" Then
            lblErr.Text = String.Empty
            Dim ObjUser As New BusinessFacade.LostDamage()
            ObjUser.EmployeeID = ddl_Employee.SelectedValue
            dg_emp.DataSource = ObjUser.LostDamageByEmployee_GetRecord(ObjUser.EmployeeID)
            dg_emp.Columns(0).Visible = True
            dg_emp.DataBind()
            dg_emp.Columns(0).Visible = False
            'dg_emp.Columns(1).Visible = False
        End If
    End Sub

    Protected Sub bttnMarkDamage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMarkDamage.Click

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''

        Try
            Dim Count As Integer
            Dim L As Integer '
            For L = 0 To dg_emp.Rows.Count - 1
                Dim ChkDamage As CheckBox = CType(dg_emp.Rows(L).Cells(4).Controls(1), CheckBox)
                Dim ChkLost As CheckBox = CType(dg_emp.Rows(L).Cells(5).Controls(1), CheckBox)
                Dim ChkCostRecovered As CheckBox = CType(dg_emp.Rows(L).Cells(6).Controls(1), CheckBox)
                Dim ChkDisposed As CheckBox = CType(dg_emp.Rows(L).Cells(7).Controls(1), CheckBox)

                If ((ChkDamage.Checked = False) And (ChkLost.Checked = False) And (ChkCostRecovered.Checked = False) And (ChkDisposed.Checked = False)) = False Then
                    Count = Count + 1
                End If
            Next

            ''***************************************''
            ''********** Get EmployeeID *************''
            ''***************************************''

            Dim EmployeeID As Integer
            Dim objEmployeeID As New BusinessFacade.TapeIssuance()
            objEmployeeID.EmployeeName = txtEmployee.Text
            EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

            If EmployeeID = "0" Then
                lblErr.Text = "Please Select Employee!!"
            ElseIf Count = 0 Then
                lblErr.Text = "Please Select Check Box!!"
            Else
                Dim K As Integer
                For K = 0 To dg_emp.Rows.Count - 1
                    Dim ChkDamage As CheckBox = CType(dg_emp.Rows(K).Cells(4).Controls(1), CheckBox)
                    Dim ChkLost As CheckBox = CType(dg_emp.Rows(K).Cells(5).Controls(1), CheckBox)
                    Dim ChkCostRecovered As CheckBox = CType(dg_emp.Rows(K).Cells(6).Controls(1), CheckBox)
                    Dim ChkDisposed As CheckBox = CType(dg_emp.Rows(K).Cells(7).Controls(1), CheckBox)
                    If ((ChkDamage.Checked = False) And (ChkLost.Checked = False) And (ChkCostRecovered.Checked = False) And (ChkDisposed.Checked = False)) = False Then

                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dg_emp.Rows(K).Cells(0).Text
                        'objValidation.TapeLibraryID = dg_emp.Rows(K).Cells("TapeLibraryId").Text
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            Dim ObjUser As New BusinessFacade.LostDamage()
                            ObjUser.LibraryID = dg_emp.Rows(K).Cells(0).Text

                            ObjUser.EmployeeID = EmployeeID

                            If ChkDamage.Checked = True Then
                                ObjUser.Damage = 1
                            Else
                                ObjUser.Damage = 0
                            End If

                            If ChkLost.Checked = True Then
                                ObjUser.Lost = 1
                            Else
                                ObjUser.Lost = 0
                            End If


                            If ChkCostRecovered.Checked = True Then
                                ObjUser.CostRecovered = 1
                            Else
                                ObjUser.CostRecovered = 0
                            End If

                            If ChkDisposed.Checked = True Then
                                ObjUser.DisposedOff = 1
                            Else
                                ObjUser.DisposedOff = 0
                            End If
                            ObjUser.UserID = UserID

                            ''**********************************''
                            Dim txt1 As TextBox
                            txt1 = CType(dg_emp.Rows(K).Cells(0).FindControl("txtEmpReason"), TextBox)
                            ObjUser.Reason = txt1.Text

                            ''**********************************''
                            ObjUser.SaveRecord()

                            ''********************************************************''
                            ''***************** Update Tape Library ******************''
                            ''********************************************************''

                            Dim ObjIsAvail_Search As New BusinessFacade.NewTapeNumber()
                            ObjIsAvail_Search.TapeLibraryID = dg_emp.Rows(K).Cells(0).Text
                            ObjIsAvail_Search.TapeLibrary_IsAvailableSearch_Update()

                            ''************************************************************''
                            ''************** IsDamage Search Engine Update ***************''
                            ''************************************************************''

                            Dim objSearchEngine_IsDamage As New BusinessFacade.LostDamage()
                            objSearchEngine_IsDamage.LibraryID = dg_emp.Rows(K).Cells(0).Text
                            objSearchEngine_IsDamage.SearchEngine_IsDamgaeUpdate(objSearchEngine_IsDamage.LibraryID)
                        End If
                        ''************ End *************''
                        ''******************************''
                    End If
                Next

                If EmployeeID <> "0" Then
                    lblErr.Text = "Record has been Saved!!"
                    Dim ObjUser1 As New BusinessFacade.LostDamage()
                    ObjUser1.EmployeeID = EmployeeID
                    dg_emp.DataSource = ObjUser1.LostDamageByEmployee_GetRecord(ObjUser1.EmployeeID)
                    dg_emp.DataBind()
                End If

                BindCombo()

            End If
        Catch ex As Exception
            Me.lblErr.Text = "Error: " & ex.Message

        End Try
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        'Dim M As Integer
        'For M = 0 To dg_emp.Rows.Count - 1
        '    Dim ChkDamage As CheckBox = CType(dg_emp.Rows(M).Cells(4).Controls(1), CheckBox)
        '    Dim ChkLost As CheckBox = CType(dg_emp.Rows(M).Cells(5).Controls(1), CheckBox)
        '    Dim ChkCostRecovered As CheckBox = CType(dg_emp.Rows(M).Cells(6).Controls(1), CheckBox)
        '    ChkLost.Checked = False
        '    ChkDamage.Checked = False
        '    ChkCostRecovered.Checked = False
        'Next

        dg_emp.DataSource = Nothing
        dg_emp.DataBind()

        lblErr.Text = String.Empty
        txtEmployee.Text = String.Empty

    End Sub

    Protected Sub bttnAdd_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAdd.Click

        lblErr_2.Text = String.Empty

        Dim arr As Array = Split(txt_TapeNo.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNo.Text = arr(1)
        End If

        Dim LibraryID As String
        Dim objTapeID As New BusinessFacade.LostDamage()
        objTapeID.TapeNo = txt_TapeNo.Text
        LibraryID = objTapeID.GetTapeID_byTapeNumber_LostDamage(objTapeID.TapeNo)

        Dim ComboSelectedValue As String
        '  ComboSelectedValue = txt_TapeNumber.SelectedValue.ToString
        ComboSelectedValue = LibraryID
        Dim str As Array = ComboSelectedValue.Split(",")

        'Response.Write(str(0) & "<br>")
        'Response.Write(str(1))
        If Session("Tape") Is Nothing Then
        Else
            Table1 = Session("Tape")
        End If

        Dim Count As Integer
        If Not Session("Tape") Is Nothing Then
            Dim K As Integer
            For K = 0 To dg_ByTapeNo.Rows.Count - 1
                If txt_TapeNo.Text = Table1.Rows(K).Item("TapeNumber").ToString Then
                    Count = Count + 1
                End If
            Next
        End If
        

        If str(0) <> 0 Then
            If Count = 0 Then
                Dim Row As DataRow
                Row = Table1.NewRow()
                'Row.Item("TapeNumber") = txt_TapeNumber.SelectedItem.Text
                Row.Item("TapeNumber") = txt_TapeNo.Text
                'Row.Item("Reason") = txt_Reason_2.Text
                Row.Item("LibID") = str(0)
                Row.Item("EmpID") = str(1)
                Table1.Rows.Add(Row)

                Table1.AcceptChanges()
                Session("Tape") = Table1
            End If
        Else
            lblErr_2.Text = "Tape Number is not valid !!"
        End If
        
        txt_TapeNo.Text = String.Empty
        'txt_TapeNumber.SelectedIndex = "0"

        BindGrid_TapeNo()
        Session("Tape") = Nothing

        'dg_ByTapeNo.DataSource = Table1
        'dg_ByTapeNo.DataBind()

        'dg_ByTapeNo.Columns(0).Visible = False
        'dg_ByTapeNo.Columns(1).Visible = False

    End Sub

    Private Sub BindGrid_TapeNo()

        dg_ByTapeNo.DataSource = Table1
        dg_ByTapeNo.Columns(0).Visible = True
        dg_ByTapeNo.Columns(1).Visible = True
        dg_ByTapeNo.DataBind()

        dg_ByTapeNo.Columns(0).Visible = False
        dg_ByTapeNo.Columns(1).Visible = False

    End Sub

    Protected Sub bttn_Marked_TapeNo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttn_Marked_TapeNo.Click

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''

        Dim Count As Integer
        Dim L As Integer
        For L = 0 To dg_ByTapeNo.Rows.Count - 1

            Dim ChkDamage As CheckBox = CType(dg_ByTapeNo.Rows(L).Cells(4).Controls(1), CheckBox)
            Dim ChkLost As CheckBox = CType(dg_ByTapeNo.Rows(L).Cells(5).Controls(1), CheckBox)
            Dim ChkCostRecovered As CheckBox = CType(dg_ByTapeNo.Rows(L).Cells(6).Controls(1), CheckBox)
            Dim ChkDisposed As CheckBox = CType(dg_ByTapeNo.Rows(L).Cells(7).Controls(1), CheckBox)

            If ((ChkDamage.Checked = False) And (ChkLost.Checked = False) And (ChkCostRecovered.Checked = False) And (ChkDisposed.Checked = False)) = False Then
                Count = Count + 1
            End If
        Next

        If Count = 0 Then
            lblErr_2.Text = "Please Select Check Box for Marking Tape!!"
        Else
            Dim H As Integer
            For H = 0 To dg_ByTapeNo.Rows.Count - 1
                Dim ChkDamage As CheckBox = CType(dg_ByTapeNo.Rows(H).Cells(4).Controls(1), CheckBox)
                Dim ChkLost As CheckBox = CType(dg_ByTapeNo.Rows(H).Cells(5).Controls(1), CheckBox)
                Dim ChkCostRecovered As CheckBox = CType(dg_ByTapeNo.Rows(H).Cells(6).Controls(1), CheckBox)
                Dim ChkDisposed As CheckBox = CType(dg_ByTapeNo.Rows(H).Cells(7).Controls(1), CheckBox)

                If ((ChkDamage.Checked = False) And (ChkLost.Checked = False) And (ChkCostRecovered.Checked = False) And (ChkDisposed.Checked = False)) = False Then

                    ''******************************''
                    ''*** BaseStation Validation ***''
                    ''******************************''

                    Dim objValidation As New BusinessFacade.NewTapeNumber()
                    objValidation.TapeLibraryID = dg_ByTapeNo.Rows(H).Cells(0).Text
                    Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID = CokieBaseStationID Then
                        Dim ObjUser As New BusinessFacade.LostDamage()
                        ObjUser.LibraryID = dg_ByTapeNo.Rows(H).Cells(0).Text
                        ObjUser.EmployeeID = dg_ByTapeNo.Rows(H).Cells(1).Text
                        Dim txt1 As TextBox
                        txt1 = CType(dg_ByTapeNo.Rows(H).Cells(0).FindControl("txt_Reason_22"), TextBox)
                        ObjUser.Reason = txt1.Text

                        ' ObjUser.Reason = dg_ByTapeNo.Rows(H).Cells(3).Text

                        If ChkDamage.Checked = True Then
                            ObjUser.Damage = 1
                        Else
                            ObjUser.Damage = 0
                        End If

                        If ChkLost.Checked = True Then
                            ObjUser.Lost = 1
                        Else
                            ObjUser.Lost = 0
                        End If

                        If ChkCostRecovered.Checked = True Then
                            ObjUser.CostRecovered = 1
                        Else
                            ObjUser.CostRecovered = 0
                        End If

                        If ChkDisposed.Checked = True Then
                            ObjUser.DisposedOff = 1
                        Else
                            ObjUser.DisposedOff = 0
                        End If
                        ObjUser.UserID = UserID
                        ObjUser.SaveRecord()

                        ''************************************************************''
                        ''****************** Update Tape Library *********************''
                        ''************************************************************''

                        Dim ObjIsAvail_Search As New BusinessFacade.NewTapeNumber()
                        ObjIsAvail_Search.TapeLibraryID = dg_ByTapeNo.Rows(H).Cells(0).Text
                        ObjIsAvail_Search.TapeLibrary_IsAvailableSearch_Update()

                        ''************************************************************''
                        ''************** IsDamage Search Engine Update ***************''
                        ''************************************************************''

                        Dim objSearchEngine_IsDamage As New BusinessFacade.LostDamage()
                        objSearchEngine_IsDamage.LibraryID = dg_ByTapeNo.Rows(H).Cells(0).Text
                        objSearchEngine_IsDamage.SearchEngine_IsDamgaeUpdate(objSearchEngine_IsDamage.LibraryID)

                        ''************************************************************''
                    End If

                    ''************ End *************''
                    ''******************************''
                End If
            Next
            lblErr_2.Text = "Record has been Saved!!"
            Table1.Clear()
            BindGrid_TapeNo()
            BindCombo()

        End If
    End Sub

    Protected Sub bttnClear_2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear_2.Click
        'Dim M As Integer
        'For M = 0 To dg_ByTapeNo.Rows.Count - 1
        '    Dim ChkDamage As CheckBox = CType(dg_ByTapeNo.Rows(M).Cells(4).Controls(1), CheckBox)
        '    Dim ChkLost As CheckBox = CType(dg_ByTapeNo.Rows(M).Cells(5).Controls(1), CheckBox)
        '    Dim ChkCostRecovered As CheckBox = CType(dg_ByTapeNo.Rows(M).Cells(6).Controls(1), CheckBox)
        '    ChkLost.Checked = False
        '    ChkDamage.Checked = False
        '    ChkCostRecovered.Checked = False
        'Next

        dg_ByTapeNo.DataSource = Nothing
        dg_ByTapeNo.DataBind()
        lblErr_2.Text = String.Empty
        txt_TapeNo.Text = String.Empty

    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub bttnSearchByEmployee_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearchByEmployee.Click
        FillGrid()
    End Sub

    Private Sub FIllGrid()
        ''***************************************''
        ''********** Get EmployeeID *************''
        ''***************************************''

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txtEmployee.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        If EmployeeID <> "0" Then
            lblErr.Text = String.Empty
            Dim ObjUser As New BusinessFacade.LostDamage()
            ObjUser.EmployeeID = EmployeeID
            dg_emp.DataSource = ObjUser.LostDamageByEmployee_GetRecord(ObjUser.EmployeeID)
            dg_emp.Columns(0).Visible = True
            dg_emp.DataBind()
            dg_emp.Columns(0).Visible = False
        End If
    End Sub

    Protected Sub dg_emp_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_emp.PageIndexChanging
        dg_emp.PageIndex = e.NewPageIndex()
        FIllGrid()
    End Sub
End Class
