<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="SearchNews.aspx.vb" Inherits="SearchEngine_SearchNews" title="Home > Seach Engine > News" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Search" runat="server">
    </asp:ScriptManager>
    <table style="width: 100%">
        <tr>
            <td class="labelheading" style="width: 100%; text-decoration: underline;">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading" OnClick="lnkHomePage_Click">Home</asp:LinkButton>&nbsp;
                &gt;Advanced Search &gt; News</td>
        </tr>
        <tr>
            <td class="mytext" style="width: 100%; height: 17px;">
                Find Record that have....<asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Names="Calibri"
                    Font-Size="Medium" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr>
            <td class="mytext" style="width: 100%; height: 21px">
                <table>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label7" runat="server" ForeColor="Navy" Text="Tape Number" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 211px">
                            <asp:TextBox ID="txt_TapeNo_1" runat="server" CssClass="mytext" Width="190px" TabIndex="1"></asp:TextBox></td>
                        <td style="width: 100px">
                            <asp:Label ID="Label8" runat="server" ForeColor="Navy" Text="Tape Type"></asp:Label></td>
                        <td style="width: 138px">
                            <asp:TextBox ID="txtTapeType1" runat="server" CssClass="mytext" Width="190px" TabIndex="1"></asp:TextBox></td>
                        <td style="width: 47px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 15px;">
                        </td>
                        <td style="width: 211px; height: 15px;">
                        </td>
                        <td style="width: 100px; height: 15px;">
                        </td>
                        <td style="width: 138px; height: 15px;">
                        </td>
                        <td style="width: 47px; height: 15px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label9" runat="server" ForeColor="Navy" Text="Reporter Slug" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td colspan="3">
                            <asp:TextBox ID="AllReporterSlug" runat="server" CssClass="mytext" Width="509px" Height="40px" TextMode="MultiLine" TabIndex="2"></asp:TextBox></td>
                        <td colspan="1" style="width: 47px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 15px;">
                        </td>
                        <td colspan="3" style="height: 15px">
                        </td>
                        <td colspan="1" style="height: 15px; width: 47px;">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label10" runat="server" ForeColor="Navy" Text="Urdu Slug" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td colspan="3">
                            <asp:TextBox ID="txtUrduScript" runat="server" CssClass="mytext" Height="40px" TextMode="MultiLine"
                                Width="508px" TabIndex="3" Font-Size="Medium"></asp:TextBox></td>
                        <td colspan="1" style="width: 47px">
                            </td>
                    </tr>
                    <tr>
                        <td style="width: 86px">
                        </td>
                        <td colspan="3">
                        </td>
                        <td colspan="1" style="width: 47px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label11" runat="server" ForeColor="Navy" Text="English Script" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td colspan="3">
                            <asp:TextBox ID="AllEnglishScript" runat="server" CssClass="mytext" Width="509px" Height="40px" TextMode="MultiLine" TabIndex="4"></asp:TextBox></td>
                        <td colspan="1" style="width: 47px">
                            <asp:TextBox ID="AllProposedSlug" runat="server" CssClass="mytext" Width="24px" Height="40px" TextMode="MultiLine" Visible="False" ReadOnly="True"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 15px">
                        </td>
                        <td style="width: 211px; height: 15px">
                        </td>
                        <td style="width: 100px; height: 15px">
                        </td>
                        <td style="width: 138px; height: 15px">
                        </td>
                        <td style="width: 47px; height: 15px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label12" runat="server" ForeColor="Navy" Text="Keyword 1" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 211px; height: 22px">
                            <asp:TextBox ID="txt_NewsKeyword_1" runat="server" Width="190px" CssClass="mytext" TabIndex="5"></asp:TextBox></td>
                        <td style="width: 86px;">
                            <asp:Label ID="Label14" runat="server" ForeColor="Navy" Text="Footage Type 1" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 138px; height: 22px">
                            <asp:TextBox ID="txt_NewsKeyTypes_1" runat="server" CssClass="mytext" Width="190px" TabIndex="6"></asp:TextBox></td>
                        <td style="width: 47px; height: 22px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 15px;">
                        </td>
                        <td style="height: 15px; width: 211px;">
                        </td>
                        <td style="width: 100px; height: 15px;">
                        </td>
                        <td style="height: 15px; width: 138px;">
                        </td>
                        <td style="height: 15px; width: 47px;">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label13" runat="server" ForeColor="Navy" Text="Keyword 2" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 211px">
                            <asp:TextBox ID="txt_NewsKeyword_2" runat="server" Width="190px" CssClass="mytext" TabIndex="7"></asp:TextBox></td>
                        <td style="width: 86px;">
                            <asp:Label ID="Label20" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"
                                ForeColor="Navy" Text="Footage Type 2"></asp:Label></td>
                        <td style="width: 138px">
                            <asp:TextBox ID="txt_NewsKeyTypes_2" runat="server" CssClass="mytext" Width="190px" TabIndex="8"></asp:TextBox></td>
                        <td style="width: 47px">
                            </td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 15px">
                        </td>
                        <td style="width: 211px; height: 15px">
                        </td>
                        <td style="width: 100px; height: 15px">
                        </td>
                        <td style="width: 138px; height: 15px">
                        </td>
                        <td style="width: 47px; height: 15px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label16" runat="server" ForeColor="Navy" Text="From Date" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 211px; height: 13px">
                            <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" Width="101px" TabIndex="9"></asp:TextBox></td>
                        <td style="width: 86px;">
                            <asp:Label ID="Label15" runat="server" ForeColor="Navy" Text="Reporter Name" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 138px; height: 13px">
                            <asp:TextBox ID="txtReporterName" runat="server" CssClass="mytext" Width="190px" TabIndex="10"></asp:TextBox></td>
                        <td style="width: 47px; height: 13px">
                            <asp:CheckBox ID="chkReporter" runat="server" Checked="True" Text="Ignore" Visible="False" TabIndex="15" /></td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 13px">
                        </td>
                        <td style="width: 211px; height: 13px">
                            &nbsp;</td>
                        <td style="width: 100px; height: 13px">
                        </td>
                        <td style="width: 138px; height: 13px">
                        </td>
                        <td style="width: 47px; height: 13px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px;">
                            <asp:Label ID="Label17" runat="server" ForeColor="Navy" Text="To Date" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 211px; height: 13px">
                            <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext" Width="101px" TabIndex="11"></asp:TextBox></td>
                        <td style="width: 86px;">
                            <asp:Label ID="Label19" runat="server" ForeColor="Navy" Text="Base Station" Font-Bold="True" Font-Names="Arial" Font-Size="8pt"></asp:Label></td>
                        <td style="width: 138px; height: 13px">
                            <asp:DropDownList ID="ddlBaseStation" runat="server" CssClass="mytext" Width="88px" TabIndex="12">
                                <asp:ListItem Value="-1">IGNORE</asp:ListItem>
                                <asp:ListItem Value="1">KARACHI</asp:ListItem>
                                <asp:ListItem Value="3">LAHORE</asp:ListItem>
                                <asp:ListItem Value="4">ISLAMABAD</asp:ListItem>
                                <asp:ListItem Value="5">Peshawar</asp:ListItem>
                                <asp:ListItem Value="6">DUBAI</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 47px; height: 13px">
                        <asp:CheckBox ID="chkDate" runat="server" Checked="True" Text="Ignore" Visible="False" TabIndex="16" /></td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 13px">
                        </td>
                        <td style="width: 211px; height: 13px">
                        </td>
                        <td style="width: 100px; height: 13px">
                        </td>
                        <td style="width: 138px; height: 13px">
                        </td>
                        <td style="width: 47px; height: 13px">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 86px; height: 13px;">
                            <asp:Label ID="Label18" runat="server" ForeColor="Navy" Text="Result Per Page" Font-Bold="True" Font-Names="Arial" Font-Size="8pt" Width="93px"></asp:Label></td>
                        <td style="width: 211px; height: 13px">
                            <asp:DropDownList ID="ddl_ResultPerPage" runat="server" CssClass="mytext" Width="56px" TabIndex="13">
                                <asp:ListItem>2000</asp:ListItem>
                                <asp:ListItem>50</asp:ListItem>
                                <asp:ListItem>100</asp:ListItem>
                                <asp:ListItem>200</asp:ListItem>
                                <asp:ListItem>300</asp:ListItem>
                                <asp:ListItem>400</asp:ListItem>
                                <asp:ListItem>500</asp:ListItem>
                                <asp:ListItem>600</asp:ListItem>
                                <asp:ListItem>700</asp:ListItem>
                                <asp:ListItem>800</asp:ListItem>
                                <asp:ListItem>900</asp:ListItem>
                                <asp:ListItem>1000</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 13px">
                        </td>
                        <td style="width: 138px; height: 13px">
                        </td>
                        <td style="width: 47px; height: 13px">
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td valign="top">
            </td>
        </tr>
        <tr>
            <td style="width: 100px">
            <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="TapeContentNews_TapeNumber_SearchEngine"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_TapeNo_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Guest"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_TapeNo_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ReporterSlug_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporterSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterSlug_1">
                </cc1:AutoCompleteExtender>
                   <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ReporterSlug_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporterSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterSlug_2">
                </cc1:AutoCompleteExtender>
                   <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ReporterSlug_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporterSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterSlug_3">
                </cc1:AutoCompleteExtender>
                   <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ReporterSlug_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporterSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterSlug_4">
                </cc1:AutoCompleteExtender>
                   <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ReporterSlug_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporterSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ReporterSlug_5">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProposedSlug_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetProposedSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProposedSlug_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProposedSlug_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetProposedSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProposedSlug_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProposedSlug_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetProposedSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProposedSlug_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProposedSlug_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetProposedSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProposedSlug_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProposedSlug_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetProposedSlug"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProposedSlug_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeywords_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyword_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeywords_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyword_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeywords_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyword_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeywords_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyword_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeywords_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyword_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeyTypes_1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="FootageType_GetRecords_AutoComplete"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyTypes_1">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeyTypes_2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="FootageType_GetRecords_AutoComplete"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyTypes_2">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeyTypes_3" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="FootageType_GetRecords_AutoComplete"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyTypes_3">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeyTypes_4" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="FootageType_GetRecords_AutoComplete"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyTypes_4">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_NewsKeyTypes_5" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="FootageType_GetRecords_AutoComplete"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_NewsKeyTypes_5">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtenderTapeType" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="1" 
                    ServiceMethod="GetTapeType"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtTapeType1">
                </cc1:AutoCompleteExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_Tape_1" runat="server"
                    TargetControlID="txt_TapeNo_1" WatermarkCssClass="watermarked" WatermarkText="Enter Tape No !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_RS_1" runat="server" TargetControlID="AllReporterSlug"
                    WatermarkCssClass="watermarked2" WatermarkText="Enter Reporter Slug !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_PS1" runat="server" TargetControlID="AllProposedSlug"
                    WatermarkCssClass="watermarked2" WatermarkText="Enter Proposed Slug !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KW1" runat="server" TargetControlID="txt_NewsKeyword_1"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keyword !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KT1" runat="server" TargetControlID="txt_NewsKeyTypes_1"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keytype !">
                </cc1:TextBoxWatermarkExtender><cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KT2" runat="server" TargetControlID="txt_NewsKeyTypes_2"
                    WatermarkCssClass="watermarked" WatermarkText="Enter Keytype !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderEng1" runat="server" TargetControlID="AllEnglishScript" WatermarkCssClass="watermarked2" WatermarkText="Enter English Script !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderReporter" runat="server" TargetControlID="txtReporterName" WatermarkCssClass="watermarked" WatermarkText="Enter Reporter !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderEntryDate" runat="server" TargetControlID="txtEntryDate" WatermarkCssClass="watermarked" WatermarkText="Enter Entry Date !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderTapeType" runat="server"
                    TargetControlID="txtTapeType1" WatermarkCssClass="watermarked" WatermarkText="Enter TapeType !!">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender1" runat="server" TargetControlID="txtUrduScript"
                    WatermarkCssClass="watermarked2" WatermarkText="Enter Urdu Script !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtender_KW2" runat="server" TargetControlID="txt_NewsKeyword_2"
                    WatermarkCssClass="watermarked" WatermarkText="Enter 2nd Keyword !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:CalendarExtender ID="CalendarExtenderToDate" runat="server" Format="dd-MMM-yyyy"
                    TargetControlID="txtToDate" CssClass="MyCalendar">
                </cc1:CalendarExtender>
                <cc1:CalendarExtender ID="CalendarExtenderDate" runat="server" Format="dd-MMM-yyyy"
                    TargetControlID="txtEntryDate" CssClass="MyCalendar">
                </cc1:CalendarExtender>
                <cc1:TextBoxWatermarkExtender ID="TextBoxWatermarkExtenderToDate" runat="server"
                    TargetControlID="txtToDate" WatermarkCssClass="watermarked" WatermarkText="Enter To Date !">
                </cc1:TextBoxWatermarkExtender>
                <cc1:AutoCompleteExtender                    
                    ID="AutoCompleteExtender_Reporter" 
                    runat="server" 
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetReporter"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtReporterName">
                </cc1:AutoCompleteExtender>
                <asp:CheckBox ID="Chk_TapeNumber" runat="server" Text="Ignore" Visible="False" /><asp:CheckBox ID="Chk_ReporterSlug" runat="server" Text="Ignore" Visible="False" /><asp:CheckBox ID="Chk_ProposedSlug" runat="server" Text="Ignore" Visible="False" /><asp:CheckBox ID="Chk_KW" runat="server" Text="Ignore" Visible="False" /><asp:CheckBox ID="Chk_KT" runat="server" Text="Ignore" Visible="False" /></td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 29px" valign="middle">
                &nbsp;&nbsp;
                <asp:Button ID="bttnNext" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Search Records >>"
                    Width="160px" TabIndex="12" />&nbsp;
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Clear >>"
                    Width="96px" TabIndex="13" />&nbsp;
                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< Show Keyboard >>"
                    Width="152px" TabIndex="14" />&nbsp;
                <asp:Button ID="btnView" runat="server" CssClass="buttonA" Font-Bold="True" Text="<< View Tapes >>"
                    Width="130px" TabIndex="13" Visible="False" />&nbsp;</td>
        </tr>
        <tr>
            <td valign="middle">
                <asp:Image ID="Image1" runat="server" ImageUrl="~/Images/UrduKeyboard.gif" Visible="False" /></td>
        </tr>
        <tr>
            <td valign="middle">
                <table class="mytext" style="width: 1088px">
                    <tr>
                        <td style="width: 256px; height: 26px">
                            </td>
                        <td style="width: 652px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label2" runat="server" Text="Tape No" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 26px">
                            </td>
                        <td style="width: 43px; height: 26px">
                            <asp:DropDownList ID="Tape_option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_2" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 33px; height: 26px">
                            <asp:DropDownList ID="Tape_option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>Or</asp:ListItem>
                                <asp:ListItem>And</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 117px; height: 26px">
                            <asp:DropDownList ID="Tape_option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>Or</asp:ListItem>
                                <asp:ListItem>And</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="Tape_option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>Or</asp:ListItem>
                                <asp:ListItem>And</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_TapeNo_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 256px; height: 26px">
                            </td>
                        <td style="width: 652px; height: 26px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label1" runat="server" Text="Reporter Slug" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 26px">
                            <asp:TextBox ID="txt_ReporterSlug_1" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 43px; height: 26px">
                            <asp:DropDownList ID="ReporterSlug_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 26px">
                            <asp:TextBox ID="txt_ReporterSlug_2" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 33px; height: 26px">
                            <asp:DropDownList ID="ReporterSlug_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_ReporterSlug_3" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 117px; height: 26px">
                            <asp:DropDownList ID="ReporterSlug_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_ReporterSlug_4" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 26px">
                            <asp:DropDownList ID="ReporterSlug_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 26px">
                            <asp:TextBox ID="txt_ReporterSlug_5" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 256px; height: 20px">
                            </td>
                        <td style="width: 652px; height: 20px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label3" runat="server" Text="Proposed Slug" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 20px">
                            <asp:TextBox ID="txt_ProposedSlug_1" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 43px; height: 20px">
                            <asp:DropDownList ID="ProposedSlug_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 20px">
                            <asp:TextBox ID="txt_ProposedSlug_2" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 33px; height: 20px">
                            <asp:DropDownList ID="ProposedSlug_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_ProposedSlug_3" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 117px; height: 20px">
                            <asp:DropDownList ID="ProposedSlug_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_ProposedSlug_4" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 20px">
                            <asp:DropDownList ID="ProposedSlug_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_ProposedSlug_5" runat="server" Width="128px" CssClass="mytext" TextMode="Password" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 256px; height: 20px">
                            </td>
                        <td style="width: 652px; height: 20px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label4" runat="server" Text="Key words" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 20px">
                            </td><td style="width: 43px; height: 20px">
                                <asp:DropDownList ID="KW_Option_1" runat="server" CssClass="mytext" Visible="False">
                                    <asp:ListItem>And</asp:ListItem>
                                    <asp:ListItem>Or</asp:ListItem>
                                </asp:DropDownList></td>
                        <td style="width: 100px; height: 20px">
                            </td><td style="width: 33px; height: 20px">
                                <asp:DropDownList ID="KW_Option_2" runat="server" CssClass="mytext" Visible="False">
                                    <asp:ListItem>And</asp:ListItem>
                                    <asp:ListItem>Or</asp:ListItem>
                                </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyword_3" runat="server" Width="128px" CssClass="mytext" Visible="False"></asp:TextBox></td><td style="width: 117px; height: 20px">
                                <asp:DropDownList ID="KW_Option_3" runat="server" CssClass="mytext" Visible="False">
                                    <asp:ListItem>And</asp:ListItem>
                                    <asp:ListItem>Or</asp:ListItem>
                                </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyword_4" runat="server" Width="128px" CssClass="mytext" Visible="False"></asp:TextBox></td><td style="width: 29px; height: 20px">
                                <asp:DropDownList ID="KW_Option_4" runat="server" CssClass="mytext" Visible="False">
                                    <asp:ListItem>And</asp:ListItem>
                                    <asp:ListItem>Or</asp:ListItem>
                                </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyword_5" runat="server" Width="128px" CssClass="mytext" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 256px; height: 20px">
                            </td>
                        <td style="width: 652px; height: 20px; font-weight: bold; text-transform: uppercase; text-decoration: underline;">
                            <asp:Label ID="Label5" runat="server" Text="Footage Types" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 20px">
                            </td>
                        <td style="width: 43px; height: 20px">
                            <asp:DropDownList ID="KT_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 20px">
                            </td>
                        <td style="width: 33px; height: 20px">
                            <asp:DropDownList ID="KT_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyTypes_3" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 117px; height: 20px">
                            <asp:DropDownList ID="KT_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyTypes_4" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 29px; height: 20px">
                            <asp:DropDownList ID="KT_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 20px">
                            <asp:TextBox ID="txt_NewsKeyTypes_5" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td style="width: 256px; height: 19px">
                        </td>
                        <td style="font-weight: bold; width: 652px; height: 19px; text-decoration: underline">
                            <asp:Label ID="Label6" runat="server" Text="English Script" Visible="False"></asp:Label></td>
                        <td style="width: 74px; height: 19px">
                            <asp:TextBox ID="txtEnglishScript1" runat="server" CssClass="mytext"
                                TextMode="Password" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 43px; height: 19px">
                            <asp:DropDownList ID="EnglishScript_Option_1" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 100px; height: 19px">
                            <asp:TextBox ID="txtEnglishScript2" runat="server" CssClass="mytext"
                                TextMode="Password" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 33px; height: 19px">
                            <asp:DropDownList ID="EnglishScript_Option_2" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 19px">
                            <asp:TextBox ID="txtEnglishScript3" runat="server" CssClass="mytext"
                                TextMode="Password" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 117px; height: 19px">
                            <asp:DropDownList ID="EnglishScript_Option_3" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>And</asp:ListItem>
                                <asp:ListItem>Or</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 19px">
                            <asp:TextBox ID="txtEnglishScript4" runat="server" CssClass="mytext"
                                TextMode="Password" Width="128px" Visible="False"></asp:TextBox></td>
                        <td style="width: 111px; height: 19px">
                            <asp:DropDownList ID="EnglishScript_Option_4" runat="server" CssClass="mytext" Visible="False">
                                <asp:ListItem>Or</asp:ListItem>
                                <asp:ListItem>And</asp:ListItem>
                            </asp:DropDownList></td>
                        <td style="width: 111px; height: 19px">
                            <asp:TextBox ID="txtEnglishScript5" runat="server" CssClass="mytext"
                                TextMode="Password" Width="128px" Visible="False"></asp:TextBox></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</asp:Content>

