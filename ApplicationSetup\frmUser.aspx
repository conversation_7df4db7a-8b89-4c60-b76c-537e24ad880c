<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmUser.aspx.vb" Inherits="ApplicationSetup_frmUser" title="Home > User > Add New" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 17px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" runat="server" CssClass="labelheading" __designer:wfdid="w19" OnClick="LnkHomePage_Click">Home</asp:LinkButton> &gt; User &gt; Add New</TD></TR><TR><TD><TABLE><TBODY><TR class="mytext"><TD>Employee Name</TD><TD><asp:DropDownList id="ddl_EmployeeName" runat="server" Width="168px" CssClass="mytext">
                            </asp:DropDownList></TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="512px" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" Width="64px" CssClass="buttonA"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" Width="64px" CssClass="buttonA"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" Width="64px" CssClass="buttonA"></asp:Button></TD></TR><TR><TD vAlign=top><asp:GridView id="dg_User" runat="server" Width="440px" CssClass="gridContent" AllowPaging="True" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="UserID" >
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="EmployeeID" >
                            <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                            <HeaderStyle Width="0px" />
                        </asp:BoundField>
                        <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" >
                            <HeaderStyle Width="225px" />
                        </asp:BoundField>
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD style="HEIGHT: 22px"><asp:TextBox id="txt_UserID" runat="server" Width="48px" CssClass="mytext" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

