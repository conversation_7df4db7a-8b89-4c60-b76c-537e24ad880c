
Partial Class Home
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
        If Session("ApplicationTicket") Is Nothing Then

            Response.Write("Wrong")
        Else

            obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            Master.FooterText = obj.UserLoginID

        End If
    End Sub
End Class
