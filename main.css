/*
    Project: CrystalX
    URL: http://www.nuvio.cz
    
    Output device: screen, projection
    
    Author: Vit <PERSON> (<EMAIL>); Nuvio (www.nuvio.cz)
    Last revision: 2006-12-05, 12:00 GMT+1

    Structure:
        display | position | float | overflow | width | height | border | margin | padding | background | align | font
*/
/*
* {min-height:10px;}
*/
body
{
	border: 0;
	margin: 0;
	padding: 0;
	background: #F2F5FE url( "bg.gif" ) 0 0 repeat-x;
}


.TitlePanel
{
	background:LightSteelBlue;
	border-color:#E0E0E0;
	border-style:ridge;
	width:99%;
}

.Test
{
	background-color:Blue;
}

	
.mytext
{
		
	/*width:534px;
	height:74px;*/
	font:normal 11px/11px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	/*float:left;*/
}

.myddl
{
		
	/*width:534px;
	height:74px;*/
	font:normal 11px/11px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	}	
		
	
.heading1
{
width:597px;
	font:bold 16px/16px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	padding:0 0 0 10px;	
}	
	
.bottomMain{
	width:100%;
	height:29px;
	background-color:#5774C2;
	color:#000;
	border-top:#DAD9C1 solid 2px;
	border-bottom:#FFFDE7 solid 1px;
	padding:0px 0 0px 0;
}

.design{
	display:block;
	position:absolute;
	font:bold 12px/20px Arial, Helvetica, sans-serif;
	color:#7DC012;
	background-color:inherit;
}

.buttonA
{
background-color:#EBEBD1;
}

.gridheader
{
	background:url(Images/ig_cal_blue1.gif) ;
	padding:0 0 0 12px;
	color:#3F3D1F;
	font:normal 12px/21px Arial, Helvetica, sans-serif;
	/*text-decoration:underline;*/
}

.gridContent
{
		
	/*width:534px;
	height:74px;*/
	width:inherit;
	font:normal 12px/12px Arial, Helvetica, sans-serif;
	background-color:inherit;
	float:left;
}	
	
.popup
{
	background: #F2F5FE url( "bg.gif" ) 0 0 repeat-x;
}

.hometext
{
	display:block;
	padding:0px;
	font:bold 13px/24px Arial, Helvetica, sans-serif;
	color:#233B9B;
	text-decoration:none;	
}

.points
{
	float:left;
	padding:3px;
	color:#233B9B;
	background-color:inherit;
	font:normal 12px/20px Arial, Helvetica, sans-serif;	
}


.WelcomeLabel
{
	background-color:#89A2DA;
	font:normal 14px/14px Arial, Helvetica, sans-serif;
}



.labelheading
{
	font:bold 16px/16px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	}
	

.watermarked {
	height:20px;
	/*width:128px;*/
	/*padding:2px 0 0 2px;*/
	/*border:1px solid #BEBEBE;*/
	background-color:#F1F4FE;
	font-size:x-small;
	color:Gray;
}	

.watermarked2 {
	height:40px;
	background-color:#F1F4FE;
	font-size:x-small;
	color:Gray;
}	

.modalBackground {
	background-color:Gray;
	filter:alpha(opacity=70);
	opacity:0.7;
}

.modalPopup {
	background-color:#ffffdd;
	border-width:3px;
	border-style:solid;
	border-color:Gray;
	padding:3px;
	width:250px;
}

.FooterLink
{
	font-family: Arial;
	font-size: 9pt;
	font-weight: normal;
	font-style: normal;
	color: midnightblue;
	background-color: lightsteelblue;
}


/*
.MyCalendar .ajax__calendar_container
{
	border: 1px solid #646464;
	background-color: lightblue;
	color: maroon;
}
*/

.MyCalendar .ajax__calendar_container   {    background-color: #e2e2e2;     border:solid 1px #cccccc;}.MyCalendar .ajax__calendar_header  {    background-color: #ffffff;     margin-bottom: 4px;}.MyCalendar .ajax__calendar_title,.MyCalendar .ajax__calendar_next,.MyCalendar .ajax__calendar_prev    {    color: #004080;     padding-top: 3px;}.MyCalendar .ajax__calendar_body    {    background-color: #e9e9e9;     border: solid 1px #cccccc;}.MyCalendar .ajax__calendar_dayname {    text-align:center;     font-weight:bold;     margin-bottom: 4px;     margin-top: 2px;}.MyCalendar .ajax__calendar_day {    text-align:center;}.MyCalendar .ajax__calendar_hover .ajax__calendar_day,.MyCalendar .ajax__calendar_hover .ajax__calendar_month,.MyCalendar .ajax__calendar_hover .ajax__calendar_year,.MyCalendar .ajax__calendar_active  {    color: #004080;     font-weight: bold;     background-color: #ffffff;}.MyCalendar .ajax__calendar_today   {    font-weight:bold;}.MyCalendar .ajax__calendar_other,.MyCalendar .ajax__calendar_hover .ajax__calendar_today,.MyCalendar .ajax__calendar_hover .ajax__calendar_title {    color: #bbbbbb;}

.AnchorMarquee
{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 9pt;
	text-decoration: none;
	color: #990000;
}