<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="Frm_rpt_proc_Keyword_KeyType_Programwisereport.aspx.vb" Inherits="Frm_rpt_proc_Keyword_KeyType_Programwisereport"
    Title="Archival Reports > How can I view Keyword, Key type and Program wise report ?" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > How can I view Keyword, Key type and Program wise report ?"
                        Width="824px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE style="WIDTH: 70%" cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 13px"></TD><TD style="WIDTH: 667px; HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD><TD style="WIDTH: 400px; HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px">Entertainment Keyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chk_EntKW" runat="server" Text="Ignore" __designer:dtid="1970324836974612" AutoPostBack="True" __designer:wfdid="w2" Checked="True"></asp:CheckBox></TD><TD style="WIDTH: 667px; HEIGHT: 21px">Keytype&nbsp;&nbsp;<asp:CheckBox id="Chk_KT" runat="server" Text="Ignore" __designer:dtid="1970324836974612" AutoPostBack="True" __designer:wfdid="w158" Checked="True"></asp:CheckBox></TD><TD style="HEIGHT: 21px">Program Name&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="ChkProgram" runat="server" Text="Ignore" AutoPostBack="True" Checked="True"></asp:CheckBox></TD><TD style="WIDTH: 400px; HEIGHT: 21px">From Date&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" AutoPostBack="True" Checked="True">
</asp:CheckBox></TD><TD style="HEIGHT: 21px">To Date</TD><TD style="WIDTH: 50%">Complete/Distinct&nbsp;&nbsp;&nbsp; </TD><TD style="WIDTH: 50%">Station</TD><TD style="WIDTH: 50%"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w2"></asp:Label></TD></TR><TR class="mytext"><TD style="WIDTH: 400px" vAlign=top><asp:TextBox id="txtEntKeywords" runat="server" __designer:dtid="1970324836974615" Width="256px" CssClass="mytext" __designer:wfdid="w1"></asp:TextBox></TD><TD style="WIDTH: 667px" vAlign=top><asp:TextBox id="txtKT" runat="server" Width="160px" CssClass="mytext" __designer:wfdid="w3"></asp:TextBox></TD><TD style="WIDTH: 400px" vAlign=top><asp:TextBox id="txtProgrmaName" runat="server" Width="175px" CssClass="mytext"></asp:TextBox></TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="136px" CssClass="mytext"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="120px" CssClass="mytext" AutoPostBack="True"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:DropDownList id="ddlComplete" runat="server" CssClass="mytext" __designer:wfdid="w1"><asp:ListItem>Complete</asp:ListItem>
<asp:ListItem>Distinct</asp:ListItem>
</asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="1407374883553313" Width="88px" CssClass="mytext" __designer:wfdid="w1">
                                </asp:DropDownList></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w3"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:CalendarExtender id="CalendarExtender1" runat="server" TargetControlID="txtFromdate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" TargetControlID="txtToDate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Ent_KW1" runat="server" TargetControlID="txtEntKeywords" __designer:wfdid="w4" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="EntKeywords" ServicePath="AutoComplete.asmx"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_KeyType_1" runat="server" __designer:dtid="2251799813685410" TargetControlID="txtKT" __designer:wfdid="w157" CompletionInterval="1" CompletionSetCount="12" ServiceMethod="GetKeyTypes" ServicePath="AutoComplete.asmx" DelimiterCharacters="" Enabled="True"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_ProgramName" runat="server" TargetControlID="txtProgrmaName" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="Program" ServicePath="AutoComplete.asmx" MinimumPrefixLength="1">
                    </cc1:AutoCompleteExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
            </td>
        </tr>
    </table>
    <cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server" CollapseControlID="TitlePanel"
        Collapsed="false" CollapsedImage="~/Images/Collapse.gif" 
        CollapsedText="--Show Form ( Archival Reports > How can I view Keyword, Key type and Program wise report ? )--"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" 
        ExpandedText="--Hide Form ( Archival Reports > How can I view Keyword, Key type and Program wise report ? )--"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>
