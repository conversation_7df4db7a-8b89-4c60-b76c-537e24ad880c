<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_EmployeeWiseOutstandingTapes.aspx.vb" Inherits="Frm_rpt_EmployeeWiseOutstandingTapes" title="Archival Reports > Q 2. How Can I View List of OutStanding Archival Tapes?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > Q 2. How Can I View List of OutStanding Archival Tapes?" Width="776px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 5px" vAlign=middle>&nbsp;</TD><TD style="HEIGHT: 5px" vAlign=middle></TD><TD style="HEIGHT: 5px" vAlign=middle></TD><TD style="HEIGHT: 5px" vAlign=middle></TD><TD style="HEIGHT: 5px" vAlign=middle></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px" vAlign=middle>Department Name</TD><TD style="HEIGHT: 21px" vAlign=middle>Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" AutoPostBack="True" OnCheckedChanged="chkEmployee_CheckedChanged" __designer:wfdid="w36"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>From Date &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Size="X-Small" Font-Names="Verdana" Font-Bold="False" __designer:wfdid="w37"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>To Date</TD><TD style="HEIGHT: 21px" vAlign=middle></TD></TR><TR class="mytext"><TD vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="200px" CssClass="mytext" AutoPostBack="True" __designer:wfdid="w38" DataSourceID="dsDepartment" DataTextField="DepartmentName" DataValueField="DepartmentID">
                                </asp:DropDownList><asp:SqlDataSource id="dsDepartment" runat="server" __designer:wfdid="w39" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;">
                                </asp:SqlDataSource>&nbsp; </TD><TD vAlign=top><asp:DropDownList id="ddlEmployee" runat="server" Width="200px" CssClass="mytext" __designer:wfdid="w40" DataSourceID="dsEmployee" DataTextField="Employeename" DataValueField="EmployeeID">
                            </asp:DropDownList> <asp:SqlDataSource id="dsEmployee" runat="server" __designer:wfdid="w41" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommand="Employee_GetRecordsDepartmentwise" SelectCommandType="StoredProcedure">
                                    <SelectParameters>
                                        <asp:ControlParameter ControlID="ddlDepartment" Name="DepartmentID" PropertyName="SelectedValue"
                                            Type="Int32" DefaultValue="0" />
                                    </SelectParameters>
                                </asp:SqlDataSource>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w42"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w43"></asp:TextBox>&nbsp; </TD><TD vAlign=top></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:dtid="281474976710669" __designer:wfdid="w44" PromptPosition="Bottom" PromptText="" TargetControlID="ddlDepartment">
    </cc1:ListSearchExtender> <cc1:ListSearchExtender id="ListSearchExtender2" runat="server" __designer:dtid="281474976710670" __designer:wfdid="w45" PromptPosition="Bottom" PromptText="" TargetControlID="ddlEmployee">
    </cc1:ListSearchExtender> <cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w46" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w47" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Height="24px"
                                    Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Archival Reports > Q 2. How Can I View List of OutStanding Archival Tapes? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Archival Reports > Q 2. How Can I View List of OutStanding Archival Tapes? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
                                       
            </td>
        </tr>
    </table>
    &nbsp;
</asp:Content>

