<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmKeywordsMapping.aspx.vb" Inherits="ApplicationSetup_FrmKeywordsMapping" title="Untitled Page" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table width="100%">
        <tr>
            <td class="labelheading" style="height: 21px; text-decoration: underline">
                <asp:ScriptManager id="ScriptManager1" runat="server">
                </asp:ScriptManager>
                &nbsp;<asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading" >Home</asp:LinkButton>
                &gt; Keywords Mapping</td>
        </tr>
        <tr>
            <td style="height: 71px">
                <table style="width: 592px">
                    <tr class="mytext">
                        <td colspan="5" style="font-weight: bold; font-size: 10pt; height: 13px">
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td colspan="5" style="font-weight: bold; font-size: 10pt; height: 13px;">
                            <asp:Label ID="Label1" runat="server" BackColor="Transparent" Font-Size="Small"
                                ForeColor="Maroon" Text="Entertainment Keywords" Width="160px" Height="16px" BorderColor="LightCoral" BorderStyle="Dotted"></asp:Label></td>
                    </tr>
                    <tr class="mytext">
                        <td colspan="5" style="font-weight: bold; font-size: 10pt; height: 13px">
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 106px">
                            Old Keyword</td>
                        <td style="width: 148px">
                            <asp:TextBox ID="txtOldkeyword" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                        <td style="width: 22px">
                        </td>
                        <td style="width: 107px">
                            New Keyword</td>
                        <td style="width: 22px">
                            <asp:TextBox ID="txtNewKeyword" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 106px">
                            &nbsp;</td>
                        <td style="width: 148px">
                        </td>
                        <td style="width: 22px">
                        </td>
                        <td style="width: 107px">
                        </td>
                        <td style="width: 22px">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="576px"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain" style="height: 29px">
                &nbsp;<asp:Button ID="bttnMappKeyword" runat="server" CssClass="buttonA" Text="~ Map Keywords ~" Width="136px" Font-Bold="True" Enabled="False" />&nbsp;
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Text="~ Clear ~" Width="72px" Font-Bold="True" />
                </td>
        </tr>
        <tr>
            <td>
                &nbsp;</td>
        </tr>
        <tr>
            <td>
                <table style="width: 592px">
                    <tr class="mytext">
                        <td colspan="5" style="font-weight: bold; font-size: 10pt; height: 13px;">
                            <asp:Label ID="Label2" runat="server" BackColor="Transparent" Font-Size="Small"
                                ForeColor="Maroon" Text="News Keywords" Width="104px" Height="16px" BorderColor="LightCoral" BorderStyle="Dotted"></asp:Label></td>
                    </tr>
                    <tr class="mytext">
                        <td colspan="5" style="font-weight: bold; font-size: 10pt; height: 13px">
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 106px">
                            Old Keyword</td>
                        <td style="width: 148px">
                            <asp:TextBox ID="txtOldkeyword_News" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                        <td style="width: 22px">
                        </td>
                        <td style="width: 107px">
                            New Keyword</td>
                        <td style="width: 22px">
                            <asp:TextBox ID="txtNewKeyword_News" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 106px">
                            &nbsp;</td>
                        <td style="width: 148px">
                        </td>
                        <td style="width: 22px">
                        </td>
                        <td style="width: 107px">
                        </td>
                        <td style="width: 22px">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr_2" runat="server" Font-Bold="True" ForeColor="Red" Width="592px"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain">
                &nbsp;<asp:Button ID="bttnMappKeyword_News" runat="server" CssClass="buttonA" Text="~ Map Keywords ~" Width="136px" Font-Bold="True" Enabled="False" />&nbsp;
                <asp:Button ID="bttnClear_News" runat="server" CssClass="buttonA" Text="~ Clear ~" Width="72px" Font-Bold="True" /></td>
        </tr>
        <tr>
            <td>
                <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Ent_KW1" runat="server" CompletionInterval="1"
                    CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="3" ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" TargetControlID="txtOldkeyword">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Ent_KW2" runat="server" CompletionInterval="1"
                    CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="3" ServiceMethod="EntKeywords"
                    ServicePath="AutoComplete.asmx" TargetControlID="txtNewKeyword">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender1" 
                    runat="server" 
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtOldkeyword_News">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender2" 
                    runat="server" 
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetNewKeywords"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtNewKeyword_News">
                </cc1:AutoCompleteExtender>
                <br />
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender2" runat="server" ConfirmText="Do you want to Map Employee !"
                    TargetControlID="bttnMappKeyword">
                </cc1:ConfirmButtonExtender><cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Map Employee !"
                    TargetControlID="bttnMappKeyword_News">
                </cc1:ConfirmButtonExtender>
            </td>
        </tr>
        <tr>
            <td style="height: 11px">
                <asp:TextBox ID="txt_ClosetMasterID" runat="server" CssClass="mytext" Visible="False"
                    Width="48px"></asp:TextBox>
                <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
        </tr>
    </table>
</asp:Content>

