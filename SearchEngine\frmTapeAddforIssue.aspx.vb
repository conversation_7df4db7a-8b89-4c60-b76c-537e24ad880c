
Partial Class SearchEngine_frmTapeAddforIssue
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not IsPostBack Then
                FillGrid()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub FillGrid()
        Dim cls As New BusinessFacade.SearchEngine
        dg_Search.DataSource = cls.GetSETapeCollection(Request.Cookies("userinfo")("username").ToString)
        dg_Search.DataBind()
    End Sub


    Protected Sub dg_Search_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_Search.RowDeleting
        Try
            'lblErr.Text = dg_Search.Rows(e.RowIndex).Cells(2).Text
            'lblErr.Visible = True
            DeleteRecord(dg_Search.Rows(e.RowIndex).Cells(2).Text)

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub DeleteRecord(ByVal ID As String)
        Dim cls As New BusinessFacade.SearchEngine

        cls.DeleteSETapeCollection(ID)
        FillGrid()

    End Sub

End Class
