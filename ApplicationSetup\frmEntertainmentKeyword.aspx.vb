
Partial Class ApplicationSetup_frmEntertainmentKeyword
    Inherits System.Web.UI.Page
    Dim I As Integer
    Dim dt As New Data.DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            ViewState("Search") = Nothing

            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()

        ddl_SubContentType.DataSource = New BusinessFacade.SubContentType().GetRecords()
        ddl_SubContentType.DataTextField = "SubContentTypeName"
        ddl_SubContentType.DataValueField = "SubContentTypeID"
        ddl_SubContentType.DataBind()
        ddl_SubContentType.Items.Insert(0, "--Select--")

        '''''''''''''''''''''''''''''''''''''''''''''''''''
        lstKeyType.DataTextField = "KeyType"
        lstKeyType.DataValueField = "KeyTypeID"
        lstKeyType.DataSource = New BusinessFacade.KeyType().KeyType_Ent_GetRecords()
        lstKeyType.DataBind()


    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        Dim IsExists As String
        IsExists = New BusinessFacade.EntertainmentKeyword().IsExists_EntKeyword(txt_EntertainmentKeyword.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention: Keyword already exists !"
        Else
            If txt_EntKeywordID.Text = "" Then
                SaveRecord()
            Else
                'UpdateRecord()

                ''******************************''
                ''*** BaseStation Validation ***''
                ''******************************''

                Dim objValidation As New BusinessFacade.EntertainmentKeyword()
                objValidation.EntertainmentKeywordID = txt_EntKeywordID.Text
                Dim BaseStationID As Integer = objValidation.EntKW_BaseStationValidation()
                Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                If BaseStationID = CokieBaseStationID Then
                    UpdateRecord()
                Else
                    lblErr.Text = "You are not allowed to Edit this Record!!"
                End If
                ''************ End *************''
                ''******************************''

            End If
        End If

        dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        ClearAuditHistory()

    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        Dim K As Integer
        Dim Cnt As Integer = 0
        For K = 0 To lstKeyType.Items.Count - 1
            If lstKeyType.Items(K).Selected = True Then
                Cnt = Cnt + 1
            End If
        Next

        ''****************************************''


        If txt_EntertainmentKeyword.Text = "" Then
            lblErr.Text = "Please Insert Entertainment Keyword!!"
        ElseIf ddl_SubContentType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select SubContent Type!!"
        ElseIf Cnt > 0 Then
            Dim U As Integer
            For U = 0 To lstKeyType.Items.Count - 1
                If lstKeyType.Items(U).Selected = True Then
                    Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
                    objEntKeyword.EntertainmentKeyword = txt_EntertainmentKeyword.Text
                    objEntKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
                    If lstKeyType.SelectedIndex = "-1" Then
                        objEntKeyword.TKeytype = ""
                    Else
                        objEntKeyword.TKeytype = lstKeyType.Items(U).Text
                    End If
                    objEntKeyword.UserID = UserID
                    objEntKeyword.SaveRecord()
                End If
                FillGrid()
                lblErr.Text = "Record has been Saved!!"
            Next
        ElseIf Cnt = 0 Then
            Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
            objEntKeyword.EntertainmentKeyword = txt_EntertainmentKeyword.Text
            objEntKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objEntKeyword.TKeytype = ""
            objEntKeyword.UserID = UserID
            objEntKeyword.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        If lstKeyType.SelectedIndex <> "-1" Then
            Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
            objEntKeyword.EntertainmentKeywordID = txt_EntKeywordID.Text
            objEntKeyword.EntertainmentKeyword = txt_EntertainmentKeyword.Text
            objEntKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objEntKeyword.TKeytype = lstKeyType.SelectedItem.Text
            objEntKeyword.UserID = UserID
            objEntKeyword.UpdateRecord()
            If Not ViewState("Search") Is Nothing Then
                fillgrid_search()
            Else
                FillGrid()
            End If
            lblErr.Text = "Record has been Updated!!"
            ClearAuditHistory()
        Else
            Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
            objEntKeyword.EntertainmentKeywordID = txt_EntKeywordID.Text
            objEntKeyword.EntertainmentKeyword = txt_EntertainmentKeyword.Text
            objEntKeyword.SubContentTypeID = ddl_SubContentType.SelectedValue
            objEntKeyword.TKeytype = ""
            objEntKeyword.UserID = UserID
            objEntKeyword.UpdateRecord()
            If Not ViewState("Search") Is Nothing Then
                fillgrid_search()
            Else
                FillGrid()
            End If
            lblErr.Text = "Record has been Updated!!"

        End If

    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.EntertainmentKeyword().GetRecords()
        dg_EntKeyword.DataSource() = dt
        dg_EntKeyword.Columns(0).Visible = True
        dg_EntKeyword.Columns(1).Visible = True
        dg_EntKeyword.DataBind()
        dg_EntKeyword.Columns(0).Visible = False
        dg_EntKeyword.Columns(1).Visible = False
        lblTotalRecords.Text = "Total Records : " & Convert.ToString(dt.Rows.Count)

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_EntKeyword.SelectedIndexChanged

        lblErr.Text = String.Empty
        I = dg_EntKeyword.SelectedIndex.ToString
        txt_EntKeywordID.Text = Convert.ToInt32(dg_EntKeyword.Rows(I).Cells(1).Text)
        ddl_SubContentType.SelectedValue = Convert.ToInt32(dg_EntKeyword.Rows(I).Cells(2).Text)
        txt_EntertainmentKeyword.Text = dg_EntKeyword.Rows(I).Cells(4).Text
        If dg_EntKeyword.Rows(I).Cells(5).Text <> "&nbsp;" Then
            lstKeyType.SelectedIndex = 0
            lstKeyType.SelectedItem.Text = dg_EntKeyword.Rows(I).Cells(5).Text
        End If
        dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.EntertainmentKeyword()
        ObjAudit.EntertainmentKeywordID = txt_EntKeywordID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_EntertainmentKeyword()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_EntKeywordID.Text = "" Then
                lblErr.Text = "Please Select Entertainment Keyword First!!"
            Else
                Dim objEntKeyword As New BusinessFacade.EntertainmentKeyword()
                objEntKeyword.EntertainmentKeywordID = txt_EntKeywordID.Text
                objEntKeyword.DeleteRecord(objEntKeyword.EntertainmentKeywordID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Keyword is already in Used !"
            dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            clrscr()
        End Try   
    End Sub

    Private Sub clrscr()
        txt_EntertainmentKeyword.Text = String.Empty
        txt_EntKeywordID.Text = String.Empty
        ddl_SubContentType.SelectedIndex = 0
        txt_TKeytype.Text = String.Empty
        lstKeyType.ClearSelection()

    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        txt_SearchKW.Text = String.Empty
        ViewState("Search") = Nothing
        FillGrid()
        ClearAuditHistory()

    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub dg_EntKeyword_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs)
        dg_EntKeyword.PageIndex = e.NewPageIndex()
        If txt_SearchKW.Text = "" Then
            FillGrid()
        Else
            fillgrid_search()
        End If
        clrscr()
        dg_EntKeyword.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        ClearAuditHistory()

    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        ViewState("Search") = Nothing
        fillgrid_search()
    End Sub

    Private Sub fillgrid_search()

        Dim objSearch As New BusinessFacade.EntertainmentKeyword()
        objSearch.EntertainmentKeyword = txt_SearchKW.Text
        dt = objSearch.GetSingleEntKeyword(objSearch.EntertainmentKeyword)

        If dt.Rows(0).Item(0).ToString <> "0" Then
            dg_EntKeyword.DataSource = dt
            dg_EntKeyword.Columns(0).Visible = True
            dg_EntKeyword.Columns(1).Visible = True
            dg_EntKeyword.DataBind()
            dg_EntKeyword.Columns(0).Visible = False
            dg_EntKeyword.Columns(1).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
        Else
            lblErr.Text = "Search Results: KeyWord is not valid !!"
        End If
        ViewState("Search") = dt
    End Sub


    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
