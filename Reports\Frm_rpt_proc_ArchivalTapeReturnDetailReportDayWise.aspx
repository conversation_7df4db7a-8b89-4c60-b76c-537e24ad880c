<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_proc_ArchivalTapeReturnDetailReportDayWise.aspx.vb" Inherits="Frm_rpt_proc_ArchivalTapeReturnDetailReportDayWise" title="Archival Reports > How can I view Return Date wise Archival Tape Issue & Receive Report?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Blank Reports > How can I view Blank Tape Issue & Receive Report Return Date  wise ?" Width="824px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE style="WIDTH: 29%" cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 21px">From Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="All Dates" __designer:wfdid="w64"></asp:CheckBox></TD><TD style="WIDTH: 153px; HEIGHT: 21px">To Date</TD><TD style="WIDTH: 153px; HEIGHT: 21px">Station</TD><TD style="WIDTH: 91px; HEIGHT: 21px"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w56"></asp:Label></TD><TD>&nbsp;&nbsp;&nbsp; </TD></TR><TR class="mytext"><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="144px" CssClass="mytext" __designer:wfdid="w65"></asp:TextBox> </TD><TD style="WIDTH: 153px" vAlign=top><asp:TextBox id="txt_ToDate" runat="server" Width="144px" CssClass="mytext" __designer:wfdid="w125"></asp:TextBox></TD><TD style="WIDTH: 153px" vAlign=top><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="562949953421399" Width="88px" CssClass="mytext" __designer:wfdid="w4"><asp:ListItem Value="1" __designer:dtid="562949953421401">KARACHI</asp:ListItem>
<asp:ListItem Value="3" __designer:dtid="562949953421402">LAHORE</asp:ListItem>
<asp:ListItem Value="4" __designer:dtid="562949953421403">ISLAMABAD</asp:ListItem>
<asp:ListItem Value="5" __designer:dtid="562949953421404">Peshawar</asp:ListItem>
<asp:ListItem Value="-1" __designer:dtid="562949953421400">IGNORE</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 91px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w57"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 35%" vAlign=top></TD></TR></TBODY></TABLE><cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w67" Format="dd-MMM-yyyy" TargetControlID="txtFromdate"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w126" Format="dd-MMM-yyyy" TargetControlID="txt_ToDate"></cc1:CalendarExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td style="height: 7px">
                            </td>
                        </tr>
                        <tr>
                            <td class="bottomMain" style="height: 29px">
                                &nbsp;<asp:Button ID="Button1" runat="server" CssClass="buttonA" OnClick="Button1_Click"
                                    Text="View Report" Font-Bold="True" /></td>
                        </tr>
                    </table>
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Archival Reports > How can I view Return Date wise Archival Tape Issue & Receive Report? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Archival Reports > How can I view Return Date wise Archival Tape Issue & Receive Report? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

