Imports System
Imports System.Data
Imports System.Data.SqlClient

Partial Class FrmDepartmentMapping
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")
            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)
        End If


        'If (lbl_UserName.Text.ToLower <> "turab.ali") Or (lbl_UserName.Text.ToLower <> "sobia.aziz") Then
        '    bttnMappDepartment.Enabled = False
        '    lblErr.Text = "You are not Authorize for Department Mapping !"
        'End If

        If Not Page.IsPostBack = True Then
            bttnMappDepartment.Enabled = False
            lblErr.Text = "You are not Authorize for Department Mapping !"

            If (lbl_UserName.Text.ToLower = "turab.ali") Or (lbl_UserName.Text.ToLower = "sobia.aziz") Then
                bttnMappDepartment.Enabled = False
                lblErr.Text = ""
            End If

        End If

        Dim connStr As String
        connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")

    End Sub

    Protected Sub bttnMappEmployee_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappDepartment.Click
        Try

            ''********************************************************''
            ''******************Get Old DepartmentID  ****************''
            ''********************************************************''

            Dim OldDepartmentID As Integer
            Dim objOldDeptID As New BusinessFacade.TapeIssuance()
            objOldDeptID.DepartmentName = txtOldDepartment.Text
            OldDepartmentID = objOldDeptID.GetDepartmentID_byDepartmentName(objOldDeptID.DepartmentName)


            ''*******************************************************''
            ''******************Get New DepartmentID ****************''
            ''*******************************************************''

            Dim NewDepartmentID As Integer
            Dim objNewDeptID As New BusinessFacade.TapeIssuance()
            objNewDeptID.DepartmentName = txtNewDepartment.Text
            NewDepartmentID = objNewDeptID.GetDepartmentID_byDepartmentName(objOldDeptID.DepartmentName)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If (OldDepartmentID <> "0") And (NewDepartmentID <> "0") And (OldDepartmentID <> NewDepartmentID) Then

                ''****************************************************''
                ''*********** Insert in Footage Type Mapping *********''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "Proc_DepartmentMapping " & OldDepartmentID & "," & NewDepartmentID & "," & UserID
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr.Text = "Department has been Mapped Successfully."

                If Not Page.IsPostBack Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmDepartmentMapping.aspx" + ";"""
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                End If
                txtNewDepartment.Text = String.Empty
                txtOldDepartment.Text = String.Empty
            ElseIf (OldDepartmentID = "0") Or (NewDepartmentID = "0") Then
                lblErr.Text = "Please Select a Valid Department"
            ElseIf OldDepartmentID = NewDepartmentID Then
                lblErr.Text = "Old and New Department must be different from each other!"
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        txtNewDepartment.Text = String.Empty
        txtOldDepartment.Text = String.Empty
        lblErr.Text = String.Empty
    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onbeforeunload = function() {"
        script = script + "return ""Closing the page now may result in data loss."";"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)
    End Sub

End Class
