Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_EmployeeWiseOutstandingTapes
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            If Not Page.IsPostBack Then
                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                ddlEmployee.Enabled = False
            End If
            

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ListofArchivalTapeEmployeeWise_New.rpt"

            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String


            
            Dept = ddlDepartment.SelectedValue


            If chkEmployee.Checked = True Then
                Emp = "-1"
            Else
                Emp = ddlEmployee.SelectedValue
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofArchivalTapeEmployeeWise_New.rpt&" + "@emp=" & Emp & "&@dept=" & Dept & "&@fromdate=" & FromDate & "&@todate=" & ToDate

            'Response.Redirect(qryString)


            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ListofArchivalTapeEmployeeWise_New.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_EmployeeWiseOutstandingTapes.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''
        Catch ex As Exception
            Throw
        End Try

    End Sub



    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            ddlEmployee.Enabled = False
        Else
            ddlEmployee.Enabled = True
        End If
    End Sub
End Class
