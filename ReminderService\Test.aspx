<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Test.aspx.vb" Inherits="ReminderService_Test" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Send Email Reminder</title>
</head>
<body>
    <form id="form1" runat="server">
        
        <!--
        <iframe id="reportviewerframe" src="http://finosys/EmailService/EmailService/Sender.aspx?EmailID=<%=Request.QueryString("EmailID")%> &From=<%= Request.QueryString("From")%> &To=<%= Request.QueryString("To")%> &CC=<%= Request.QueryString("CC")%> &BCC=<%= Request.QueryString("BCC")%> &Subject=<%= Request.QueryString("Subject")%> &UserName=<%= Request.QueryString("UserName")%> &EmployeeName=<%= Request.QueryString("EmployeeName")%> &TapeDetail=<%= Request.QueryString("TapeDetail")%>" 
         height="800px" width="800">
        </iframe>
       --> 
        <iframe id="reportviewerframe" src="http://roshni/EmailService/Sender.aspx?EmailID=<%=Request.QueryString("EmailID")%> &From=<%= Request.QueryString("From")%> &To=<%= Request.QueryString("To")%> &CC=<%= Request.QueryString("CC")%> &BCC=<%= Request.QueryString("BCC")%> &Subject=<%= Request.QueryString("Subject")%> &UserName=<%= Request.QueryString("UserName")%> &EmployeeName=<%= Request.QueryString("EmployeeName")%> &TapeDetail=<%= Request.QueryString("TapeDetail")%>" 
         height="800px" width="800">
        </iframe>
       
        &nbsp;
    </form>
</body>
</html>