<%@ Page Language="VB" AutoEventWireup="false" CodeFile="LoginMain.aspx.vb" Inherits="LoginMain" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Archive Management System &gt; Login</title>
    <link href="StyleSheet.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <div style="width: 100%; height: 100%">
        &nbsp;&nbsp;
        <table border="0" cellpadding="1" cellspacing="0" style="width: 528px; height: 136px; left: 22%; position: absolute; top: 35%;">
            <tr>
                <td align="center" colspan="1" rowspan="6" valign="middle">
                    <asp:Image ID="Image1" runat="server" ImageUrl="~/Images/Lock.png"
                        Width="120px" /></td>
                <td align="left" colspan="2" style="height: 23px">
                    <strong><span style="font-size: 14pt; font-family: Arial; background-color: #000000; color: #ffff66;">
                        <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Large"
                            ForeColor="Yellow" Width="408px"> Archive Management System - Login</asp:Label></span></strong></td>
            </tr>
            <tr>
                <td align="left" colspan="2" style="height: 18px; background-color: #f5f5f5">
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Small"
                        ForeColor="Red" Width="392px"></asp:Label></td>
            </tr>
            <tr style="font-size: 10pt; color: #000080">
                <td align="right" bgcolor="whitesmoke" style="font-weight: bold; font-size: x-small; color: navy; font-family: verdana" colspan="2">
                </td>
            </tr>
            <tr style="font-size: 10pt; color: #000080">
                <td align="right" bgcolor="whitesmoke" style="font-weight: bold; font-size: x-small;
                    width: 143px; color: navy; font-family: verdana">
                    <span style="color: navy"><strong style="font-weight: bold; font-size: x-small; color: navy;
                        font-family: verdana">Login Id &nbsp; &nbsp; </strong></span></td>
                <td align="left" bgcolor="whitesmoke" style="width: 70px">
        <asp:TextBox ID="txtLoginID" runat="server" Width="150px"></asp:TextBox></td>
            </tr>
            <tr>
                <td align="right" bgcolor="whitesmoke" style="font-weight: bold; font-size: x-small;
                    width: 143px; color: navy; font-family: verdana; height: 23px">
                    <span style="font-size: 10pt; color: navy"><strong style="font-weight: bold; font-size: x-small;
                        color: navy; font-family: verdana">Password&nbsp; </strong></span></td>
                <td align="left" bgcolor="whitesmoke" style="width: 70px; height: 23px">
        <asp:TextBox ID="txtpassword" runat="server" TextMode="Password" Width="150px"></asp:TextBox></td>
            </tr>
            <tr>
                <td bgcolor="whitesmoke" style="width: 143px; height: 14px" valign="top">
                </td>
                <td align="left" bgcolor="whitesmoke" style="font-size: x-small; width: 70px; height: 14px" valign="top">
                    <br />
                    <table style="width: 274px">
                        <tbody>
                            <tr>
                                <td align="center" style="width: 66px">
        <asp:Button ID="btnLogin" runat="server" Text="Login" Height="24px" Width="72px" CssClass="buttonA" /></td>
                                <td align="left" style="width: 161px"><asp:Button ID="bttnCancel" runat="server" Text="Cancel" Height="24px" Width="64px" CssClass="buttonA" /></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </table>
    </div>
        <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
        </cc1:ToolkitScriptManager>
        &nbsp;
    </form>
</body>
</html>
