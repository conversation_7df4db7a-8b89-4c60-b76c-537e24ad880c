<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="Frm_rpt_ProgramWiseIssueReturn.aspx.vb" Inherits="Frm_rpt_ProgramWiseIssueReturn"
    Title="Other Reports > Q 2. How Can I View Program Title Wise Tape Issue And Return?" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
            </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue"
                    BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports > Q 2. How Can I View Program Title Wise Tape Issue And Return?"
                        Width="824px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE style="WIDTH: 70%" cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 13px">&nbsp;</TD><TD style="HEIGHT: 13px"></TD><TD style="WIDTH: 400px; HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD><TD style="HEIGHT: 13px"></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px">Employee Name &nbsp; &nbsp; &nbsp;&nbsp; <asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" OnCheckedChanged="chkEmployee_CheckedChanged" AutoPostBack="True"></asp:CheckBox></TD><TD style="HEIGHT: 21px">Program Name&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="ChkProgram" runat="server" Text="Ignore" OnCheckedChanged="ChkProgram_CheckedChanged" AutoPostBack="True"></asp:CheckBox></TD><TD style="WIDTH: 400px; HEIGHT: 21px">From Date &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" OnCheckedChanged="chkIgnoredate_CheckedChanged" AutoPostBack="True"></asp:CheckBox></TD><TD style="HEIGHT: 21px">To Date</TD><TD style="WIDTH: 50%">&nbsp; &nbsp; &nbsp; </TD></TR><TR class="mytext"><TD vAlign=top><asp:TextBox id="txtEmployee" runat="server" Width="175px" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 400px" vAlign=top><asp:TextBox id="txtProgrmaName" runat="server" Width="175px" CssClass="mytext"></asp:TextBox></TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" AutoPostBack="True"></asp:TextBox>&nbsp; </TD><TD vAlign=top></TD></TR></TBODY></TABLE><cc1:CalendarExtender id="CalendarExtender1" runat="server" TargetControlID="txtFromdate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" TargetControlID="txtToDate" CssClass="MyCalendar" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" TargetControlID="txtEmployee" ServicePath="AutoComplete.asmx" ServiceMethod="GetEmployee" MinimumPrefixLength="1" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1">
                    </cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_ProgramName" runat="server" TargetControlID="txtProgrmaName" ServicePath="AutoComplete.asmx" ServiceMethod="Program" MinimumPrefixLength="1" EnableCaching="true" CompletionSetCount="12" CompletionInterval="1">
                    </cc1:AutoCompleteExtender>
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
            </td>
        </tr>
    </table>
    <cc1:CollapsiblePanelExtender ID="CollapsiblePanelExtender1" runat="server" CollapseControlID="TitlePanel"
        Collapsed="false" CollapsedImage="~/Images/Collapse.gif" CollapsedText="--Show Form ( Other Reports > Q 2. How Can I View Program Title Wise Tape Issue And Return? )--"
        ExpandControlID="TitlePanel" ExpandedImage="~/Images/expand.gif" ExpandedText="--Hide Form ( Other Reports > Q 2. How Can I View Program Title Wise Tape Issue And Return? )--"
        ImageControlID="Image1" SuppressPostBack="true" TextLabelID="Label1" TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
</asp:Content>
