
Partial Class MasterPage
    Inherits System.Web.UI.MasterPage

    Public Property FooterText() As String
        Get
            Return lblUserName.Text
        End Get
        Set(ByVal value As String)
            Dim A As String = "Welcome,"
            lblUserName.Text = A & value
        End Set
    End Property
    
    Protected Sub CtlMenu1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles CtlMenu1.Load

    End Sub


    'Protected Sub lnkPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkPassword.Click
    '    Dim script As String
    '    script = "<script language='javascript' type='text/javascript'>"
    '    script = script + "window.onload=function OpenReport() {"
    '    ''script = script + "var mywindow = window.open('../ChangePassword/ChangePassword.aspx" + "', 'mywindow'); "
    '    script = script + "var mywindow = window.open('../ChangePassword/ChangePassword.aspx', 'Report', 'width=280, height=225, left=150, top=150, menubar=no, status=no, location=no, toolbar=no, scrollbars=No, resizable=No'); "
    '    script = script + "}</script>"

    '    Page.RegisterClientScriptBlock("test", script)


    'End Sub
End Class

