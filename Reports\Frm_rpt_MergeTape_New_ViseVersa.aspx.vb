Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_MergeTape_New_ViseVersa
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                ddlTapeNumber.Enabled = False


            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub


    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If ddlContentType.SelectedValue = "Ent" Then
            Dim LibraryID As String

            If chkMergeEnt.Checked = True Then
                LibraryID = "-1"
            Else
                Dim TapeNumber As String = ""
                Dim arr As Array = Split(txtMergeTapeNumber_Ent.Text, "#")
                If arr.Length = 2 Then
                    TapeNumber = arr(1)
                End If

                ''****************************************''
                ''********* Get TapeLibraryID ************''
                ''****************************************''


                Dim objLibID As New BusinessFacade.TapeIssuance()
                objLibID.TapeNumber = TapeNumber
                LibraryID = Convert.ToString(objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber))
            End If
            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=Rpt_MergeTapes_Ent2.rpt&@OldTapeLibraryID=" + LibraryID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=Rpt_MergeTapes_Ent2.rpt&@OldTapeLibraryID=" + LibraryID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If
            
        Else
            Dim LibraryID As String

            If chkMergeNews.Checked = True Then
                LibraryID = "-1"
            Else
                Dim TapeNumber As String = ""
                Dim arr As Array = Split(txtMergeTapeNumber_News.Text, "#")
                If arr.Length = 2 Then
                    TapeNumber = arr(1)
                End If

                ''****************************************''
                ''********* Get TapeLibraryID ************''
                ''****************************************''


                Dim objLibID As New BusinessFacade.TapeIssuance()
                objLibID.TapeNumber = TapeNumber
                LibraryID = Convert.ToString(objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber))
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=Rpt_MergeTapes_News2.rpt&@OldTapeLibraryID=" + LibraryID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=Rpt_MergeTapes_News2.rpt&@OldTapeLibraryID=" + LibraryID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If
            
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_MergeTape_New_ViseVersa.aspx"
        ObjSave.ReportName = "Archival --> Q 11. How Can I View Merged Tape Detail?"
        ObjSave.SaveRecord()

        ''******************************************************''
    End Sub
End Class
