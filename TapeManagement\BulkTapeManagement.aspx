<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="BulkTapeManagement.aspx.vb" Inherits="TapeManagement_BulkTapeManagement" title="Home > Tape Management > Blank Tape Issuacne" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<%@ Register Assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.WebSchedule" TagPrefix="igsch" %>
    
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<table width="100%" style="vertical-align:top">
            <tr>
                <td style="height: 21px; text-decoration: underline;" class="labelheading" >
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager><asp:LinkButton ID="LinkButton1" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                    &gt; Tape Management &gt; Add New</td>
            </tr>
            <tr>
                <td valign="top">
                    <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                        &nbsp;<asp:Image ID="Image1" runat="server" />
                        <asp:Label ID="Label3" runat="server" Text="Issue Bulk Tape (Show form..)" Width="456px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                    <asp:Panel ID="ContentPanel" runat="server" Width="100%">
<TABLE width="100%">
<TBODY>
    <tr class="mytext">
        <td style="width: 161px; height: 5px">
        </td>
        <td style="width: 160px; height: 2px">
        </td>
        <td style="width: 160px; height: 2px">
        </td>
        <td style="width: 160px; height: 2px">
        </td>
        <td style="width: 160px; height: 2px">
        </td>
        <td style="width: 162px; height: 4px">
        </td>
    </tr>
<TR class="mytext">
<TD style="WIDTH: 161px; HEIGHT: 17px">Ref No. &nbsp;
    <asp:Image ID="Err_RefNo" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></TD><TD style="WIDTH: 160px; HEIGHT: 17px">Department &nbsp;
    <asp:Image ID="Err_Department" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></TD><TD style="WIDTH: 160px; HEIGHT: 17px">City &nbsp;
    <asp:Image ID="Err_City" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></TD><TD style="WIDTH: 160px; HEIGHT: 17px">Employee &nbsp;
    <asp:Image ID="Err_Employee_Bulk" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></TD>
    <td style="width: 160px; height: 17px">
        Entry Date &nbsp;
        <asp:Image ID="ErrDate" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
    <TD style="WIDTH: 162px; HEIGHT: 17px">
        <asp:TextBox ID="txtBulkTapeIssuanceID" runat="server" Width="56px" Visible="False"></asp:TextBox></TD>
</TR><TR><TD style="WIDTH: 161px; HEIGHT: 26px"><asp:TextBox CssClass="mytext" id="txt_RefNo" runat="server" Width="152px"></asp:TextBox></TD><TD style="WIDTH: 160px; HEIGHT: 26px" class="mytext">
    <asp:TextBox ID="txt_DepartmentName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD>
    <TD style="WIDTH: 160px; HEIGHT: 26px" class="mytext">
    <asp:TextBox ID="txt_CityName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD><TD style="WIDTH: 160px; HEIGHT: 26px" class="mytext">
    <asp:TextBox ID="txt_EmployeeName" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></TD>
    <td style="width: 160px; height: 26px">
        <asp:TextBox ID="txtEntryDate" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
    <TD style="WIDTH: 30%; HEIGHT: 26px">
        <asp:DropDownList CssClass="mytext" id="DeptIssueorNot" runat="server" Width="56px" Visible="False">
                                    <asp:ListItem Value="1">Yes</asp:ListItem>
                                    <asp:ListItem Value="0">No</asp:ListItem>
                                </asp:DropDownList></TD>
</TR><TR><TD style="HEIGHT: 4px" colSpan=6>
    &nbsp;<asp:Label id="lblErr" runat="server" Width="488px" Font-Bold="True" ForeColor="Red"></asp:Label>
    <asp:TextBox id="txt_MasterID" runat="server" Width="64px" Visible="False" Height="1px"></asp:TextBox></TD>
                                </TR>
    <tr class="mytext">
        <td class="bottomMain" colspan="6" style="height: 29px">
            &nbsp;<asp:Button CssClass="buttonA" id="bttnSave" runat="server" Width="80px" Text="Save" Font-Bold="True"></asp:Button>
            <asp:Button CssClass="buttonA" id="bttnCencel" runat="server" Text="Clear" Width="72px" Font-Bold="True"></asp:Button>
            <asp:Button CssClass="buttonA" id="bttnPrint" runat="server" Width="80px" Text="Print" Visible="False" Font-Bold="True"></asp:Button>&nbsp;<asp:Button ID="Button2" runat="server" Text="BulkIssue Email" /></td>
    </tr>
    <TR><TD style="HEIGHT: 4px" colSpan=6><asp:GridView id="dg" runat="server" Width="616px" PageSize="20" CssClass="gridContent" AutoGenerateColumns="False">
                        <Columns>
                            <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" ></asp:BoundField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" ></asp:BoundField>
                            <asp:TemplateField HeaderText="Quantity">
                                <ItemTemplate>
                                    <asp:TextBox ID="txt_Quantity" runat="server" Width="32px"></asp:TextBox>
                                    <cc1:FilteredTextBoxExtender ID="FilteredTextBoxExtender1" runat="server" TargetControlID="txt_Quantity" ValidChars="1234567890">
                                    </cc1:FilteredTextBoxExtender>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="TotalQuantity" HeaderText="Total Quantity" />
                            <asp:BoundField DataField="IssuedQuanity" HeaderText="Issued Quanity" />
                            <asp:BoundField DataField="RemainingQuantity" HeaderText="Remaining Quantity" />
                        </Columns>
                        <HeaderStyle CssClass="gridheader"  />
                        <AlternatingRowStyle CssClass="AlternateRows"  />
                    </asp:GridView><asp:DropDownList CssClass="mytext" id="ddl_Name_Issued" runat="server" Width="144px" Visible="False">
                                </asp:DropDownList>
        <asp:DropDownList CssClass="mytext" id="ddl_City_Issued" runat="server" Width="152px" Visible="False">
                                </asp:DropDownList>
        <asp:DropDownList CssClass="mytext" id="ddl_Employee" runat="server" Width="152px" Visible="False">
                            </asp:DropDownList>
        <asp:GridView id="Dg_Edit" runat="server" Width="416px" PageSize="20" CssClass="gridContent" AutoGenerateColumns="False">
            <Columns>
                <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" />
                <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                <asp:TemplateField HeaderText="Quantity">
                    <ItemTemplate>
                        <asp:TextBox ID="txt_Quantity" Text='<%# Bind("Quantity") %>'  runat="server" Width="32px"></asp:TextBox>
                        <cc1:FilteredTextBoxExtender ID="FilteredTextBoxExtender1" runat="server" TargetControlID="txt_Quantity" ValidChars="1234567890">
                        </cc1:FilteredTextBoxExtender>
                    </ItemTemplate>
                    <HeaderStyle HorizontalAlign="Center" />
                    <ItemStyle HorizontalAlign="Center" />
                </asp:TemplateField>
            </Columns>
            <HeaderStyle CssClass="gridheader"  />
            <AlternatingRowStyle CssClass="AlternateRows"  />
        </asp:GridView>
    </TD>
                                </TR></TBODY></TABLE><BR />
                    </asp:Panel>
                    
                    
                </td>
            </tr>
            <tr>
                <td valign="top">
                    <asp:Panel ID="TitlePanel_2" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                        <asp:Image ID="Image2" runat="server" />
                        <asp:Label ID="Label4" runat="server" Text="One by One Issue Tape  (Show form..)" Width="544px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label><br />
                    </asp:Panel>
                    <asp:Panel ID="ContentPanel_2" runat="server" Width="100%">
                        <table width="100%" style="top: 0px" cellpadding="0" cellspacing="0">
                            <tr>
                                <td colspan="5" rowspan="2" style="height: 79px">
                                    <table style="width: 496px">
                                        <tr class="mytext">
                                            <td style="width: 85px;">
                                            </td>
                                            <td style="width: 85px;">
                                            </td>
                                            <td style="width: 85px;">
                                            </td>
                                            <td style="width: 85px;">
                                            </td>
                                            <td style="width: 132px;">
                                            </td>
                                            <td>
                                                &nbsp;</td>
                                        </tr>
                                        <tr class="mytext">
                                            <td style="width: 85px; height: 13px;">
                                                Employee &nbsp;
                                                <asp:Image ID="Err_Employee" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                            <td>
                                                Program &nbsp;&nbsp;
                                                <asp:Image ID="Err_Program" runat="server" ImageUrl="~/Images/error.gif" Visible="False" />
                                                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:LinkButton
                                                    ID="LnkAddProgram" runat="server">Add New Program</asp:LinkButton>
                                                &nbsp;&nbsp;
                                            </td>
                                            <td style="width: 120px; height: 13px">
                                                Entry Date&nbsp;
                                                <asp:Image ID="ErrDate_1By1" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                            <td style="width: 120px; height: 13px">
                                                <asp:Label ID="lblTapeNo" runat="server" Text="Tape Number"></asp:Label>
                                                <asp:Image ID="Err_TapeNo" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                            <td align="center" style="width: 132px; height: 13px">
                                                <asp:Label ID="Label1" runat="server" Text="Issue as Recycle Tape" Width="120px"></asp:Label></td>
                                            <td align="center" rowspan="2" valign="bottom">
                                                <asp:Label ID="lblMsg_Recycle" runat="server" Font-Bold="False" Font-Names="Calibri"
                                                    Font-Size="9pt" ForeColor="RoyalBlue" Text="Attention : if Recycle Tape check is checked then These Tapes will be marked as recycled in Issuance" Width="204px" BackColor="#FFFFC0"></asp:Label></td>
                                        </tr>
                                        <tr class="mytext">
                                            <td>
                                                <asp:TextBox ID="txt_Employee_1By1" runat="server" CssClass="mytext" Width="216px"></asp:TextBox></td>
                                            <td>
                                                <asp:TextBox ID="txt_ProgramChild_1by1" runat="server" CssClass="mytext" Width="224px"></asp:TextBox></td>
                                            <td>
                                                <asp:TextBox ID="txtEntryDate_1By1" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                                            <td>
                                                <asp:TextBox ID="txt_TapeNumber" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                                            <td align="center" style="width: 132px">
                                                <asp:CheckBox ID="chkIsRecycle" runat="server" BorderColor="Gray"
                                                    BorderStyle="None" /></td>
                                        </tr>
                                    </table>
                                    <asp:Label ID="lblErr_2" runat="server" Font-Bold="True" ForeColor="Red" Width="496px" Font-Size="Small"></asp:Label>
                                    <asp:TextBox ID="txtSave" runat="server" Visible="False" Width="88px"></asp:TextBox></td>
                            </tr>
                            <tr>
                            </tr>
                            <tr>
                                <td class="bottomMain" colspan="5" style="height: 29px">
                                    &nbsp;<asp:Button ID="bttnAddTapeNO" runat="server" CssClass="buttonA" Text="~ Add New Tape ~" Height="24px" Width="128px" Font-Bold="True" />
                                    <asp:Button ID="bttnSave_1By1" runat="server" CssClass="buttonA" Text="~ Save ~"
                                        Width="88px" Font-Bold="True" />&nbsp;<asp:Button ID="bttnCancel_1By1" runat="server" CssClass="buttonA"
                                            Text="~ Cancel ~" Font-Bold="True" Width="88px" />&nbsp;<asp:Button ID="Button1" runat="server" CssClass="buttonA"
                                            Text="~ Email ~" Font-Bold="True" Width="88px" Visible="False" />
                                    &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td colspan="5" style="height: 4px">
                                    <asp:GridView ID="dg_tapeNumber" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                                        Width="464px">
                                        <Columns>
                                            <asp:BoundField DataField="StockRegisterDetailID" Visible="False">
                                                <ItemStyle ForeColor="#F2F5FE" />
                                                <HeaderStyle Width="1px" />
                                            </asp:BoundField>
                                            <asp:BoundField DataField="TapeTypeID" Visible="False">
                                                <ItemStyle ForeColor="#F2F5FE" />
                                                <HeaderStyle Width="1px" />
                                            </asp:BoundField>
                                            <asp:TemplateField HeaderText="Tape Number">
                                                <ItemTemplate>
                                                    <asp:TextBox ID="txt_Karachi" runat="server" Text='<% # Eval("TapeNumber") %>' Width="112px" ReadOnly="True"></asp:TextBox>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                                            <asp:BoundField DataField="StationID" HeaderText="StationID" />
                                        </Columns>
                                        <HeaderStyle CssClass="gridheader" />
                                    </asp:GridView>
                                    <asp:TextBox ID="txt_MasterID_1By1" runat="server" BackColor="Transparent" BorderStyle="None"
                                        ForeColor="Gainsboro" Visible="False" Width="64px"></asp:TextBox>
                                    <asp:TextBox ID="test" runat="server" BackColor="Transparent" BorderStyle="None"
                                        ForeColor="Gainsboro" Width="48px"></asp:TextBox>
                                    <asp:TextBox ID="txt_TapeTypeID" runat="server" BackColor="Transparent" BorderStyle="None"
                                        ForeColor="Gainsboro" Width="56px"></asp:TextBox>
                                    <asp:TextBox ID="txt_SRDID" runat="server" BackColor="Transparent" BorderStyle="None"
                                        ForeColor="Gainsboro" Width="64px"></asp:TextBox>
                                                <asp:DropDownList ID="ddl_Employee_1By1" runat="server" CssClass="mytext" Width="160px" Visible="False">
                                                </asp:DropDownList>
                                                <asp:DropDownList ID="ddl_Program_1by1" runat="server" CssClass="mytext" Width="152px" Visible="False">
                                                </asp:DropDownList></td>
                            </tr>
                        </table>
                        &nbsp;
                    </asp:Panel>
                    &nbsp;
                    <cc1:CollapsiblePanelExtender 
                        ID="CollapsiblePanelExtender2" 
                        runat="server"
                        collapsecontrolid="TitlePanel_2"
                        collapsed="true" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (One By One Tape Issuance) --"
                        expandcontrolid="TitlePanel_2" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (One By One Tape Issuance) --"
                        imagecontrolid="Image2" 
                        suppresspostback="true"
                        textlabelid="Label4"
                        TargetControlID="ContentPanel_2"> 
                        
                        
                    </cc1:CollapsiblePanelExtender>
                                    <cc1:AutoCompleteExtender 
                                    ID="AutoCompleteExtender1" 
                                    runat="server" 
                                    CompletionInterval="1"
                                    CompletionSetCount="12" 
                                    EnableCaching="true" 
                                    MinimumPrefixLength="3" 
                                    ServiceMethod="Guest"
                                    ServicePath="AutoComplete.asmx" TargetControlID="txt_TapeNumber">
                                    </cc1:AutoCompleteExtender>
                    
                    <cc1:CollapsiblePanelExtender 
                        ID="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="true" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (Bulk Tape Issuance) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (Bulk Tape Issuance) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label3" 
                        TargetControlID="ContentPanel">
                    </cc1:CollapsiblePanelExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_Name_Issued">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_City_Issued">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_Employee">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender4" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="DeptIssueorNot">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender5" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_Employee_1By1">
                    </cc1:ListSearchExtender>
                    <cc1:ListSearchExtender ID="ListSearchExtender6" runat="server" PromptPosition="Bottom"
                        PromptText="" TargetControlID="ddl_Program_1by1">
                    </cc1:ListSearchExtender>
                     <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Department" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetDepartment"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_DepartmentName">
                </cc1:AutoCompleteExtender>
                 <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_City" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetCity"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_CityName">
                </cc1:AutoCompleteExtender>
                    <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Employee" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetEmployee"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_EmployeeName">
                </cc1:AutoCompleteExtender>
                     <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_Employee_1by1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetEmployee"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_Employee_1By1">
                </cc1:AutoCompleteExtender>
                     <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_ProgramChild_1by1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_ProgramChild_1by1">
                </cc1:AutoCompleteExtender>
                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" Format="dd-MMM-yyyy" TargetControlID="txtEntryDate" CssClass="MyCalendar">
                    </cc1:CalendarExtender>
                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtEntryDate_1By1" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                    </cc1:CalendarExtender>
                    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
            </tr>
            <tr>
                <td>
                    &nbsp;</td>
            </tr>
        </table>
</asp:Content>

