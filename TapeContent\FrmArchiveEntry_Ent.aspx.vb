Imports System.Data
Imports System.Data.SqlClient

Partial Class TapeContent_FrmArchiveEntry_Ent
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim objCommand As New SqlClient.SqlCommand
    Dim Dt_SaveKeyword As New DataTable
    Dim dt_ProgramInfo As New DataTable
    Dim dt_MergeTapeNumber As New DataTable
    Dim dt_Test_ProgramInfo As New DataTable
    Dim Dt_Keyword_2 As New DataTable
    Dim dt_BulkTapeNumber As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' bttnAddNEwTape.Attributes.Add("onClick", "return popWin_2('" & txt_TapeTypeID.ID & "');")
        'bindTapeCombo()
        Try
            If Not Page.IsPostBack = True Then

                txt_UrduScript.Attributes.Add("onkeypress", "search()")
                txt_UrduScript.Attributes.Add("Dir", "Rtl")

                txt_starttime1.Text = "00:00:00:00"
                txt_Endtime1.Text = "00:00:00:00"

                txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
                txtonairdate.Text = Date.Now().ToString("dd-MMM-yyyy")

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                BindCombo()
                If Not Request.QueryString.Get("TapeContentID") = "" Then
                    txt_TapeContentID.Text = Request.QueryString.Get("TapeContentID").ToString
                    Me.BindEditCombo(txt_TapeContentID.Text)
                    Me.Fill_KeyWord_Table(txt_TapeContentID.Text)
                    Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                    Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                    Me.Fill_MergeInformation(txt_TapeContentID.Text)
                    lblQueryString.Text = Request.QueryString.Get("TapeContentID").ToString
                Else

                End If

            End If

            'If Page.IsPostBack = True Then
            '    If Request.Form("bttnSaveProg_2") Is Nothing Then
            '        Dim script As String
            '        script = "<script language='javascript' type='text/javascript'>"
            '        script = script + "window.onbeforeunload = function() {"
            '        script = script + "return ""Closing the page now may result in data loss."";"
            '        script = script + "}</script>"
            '        Page.RegisterClientScriptBlock("test", script)
            '    End If
            'End If



            'Dim script As String
            'script = "<script language='javascript' type='text/javascript'>"
            'script = script + "window.onbeforeunload = function() {"
            'script = script + "return ""Closing the page now may result in data loss."";"
            'script = script + "}</script>"
            'Page.RegisterClientScriptBlock("test", script)
            txtBttnProgram.Text = "0"
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Fill_ProgramInformation_Table(ByVal TapeContentID As Integer)
        Try
            'Dim qry_4 As String = "SELECT [TapeContentDetailID],[TapeContentID],[TapeLibraryID],[SubClosetID],a.[ProgramChildID],[EpisodeNo],[PartNo],[NoteArea] as 'Note',[Abstract],[ProducedBy],[startTime_vc] as 'StartTime',[EndTime_vc] as 'EndTime',a.[Duration],[EntryDate],[NocNo] as 'Note',[Item_id],[MergedId],[ProducedBYName],[ProductionStatus], [EntryDate], a.[ProgramChildID] , b.ProgramChildName [Program]FROM [DAMS_NewDB].[ArchivalManagement].[TapeContentDetail] a inner join ApplicationSetup.ProgramChild b on b.ProgramChildID = a.ProgramChildID where TapeContentID = " & txt_TapeContentID.Text
            'Dim da_4 As New System.Data.SqlClient.SqlDataAdapter
            'Dim ds_4 As New DataSet
            'da_4.Fill(ds_4, "ProgramInfo_Table_2")
            'dt_Test_ProgramInfo = ds_4.Tables("ProgramInfo_Table_2")

            'ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
            'BindGrid_ProgramInfo_2()

            Dim objConn As New SqlClient.SqlConnection
            objConn.ConnectionString = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
            Dim objCommand As SqlClient.SqlCommand
            Dim da_4 As New System.Data.SqlClient.SqlDataAdapter
            Dim ds_4 As New DataSet
            objConn.Open()
            objCommand = New SqlClient.SqlCommand("ProgramInformation_GetRecords", objConn)
            objCommand.Parameters.Add("@TapeContentID", SqlDbType.Int).Value = Convert.ToInt32(txt_TapeContentID.Text)
            objCommand.CommandTimeout = 0

            objCommand.CommandType = CommandType.StoredProcedure
            da_4.SelectCommand = objCommand
            da_4.Fill(ds_4, "ProgramInfo_Table_2")
            dt_Test_ProgramInfo = ds_4.Tables("ProgramInfo_Table_2")
            ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
            BindGrid_ProgramInfo_2()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Fill_MergeInformation(ByVal TapeContentID As Integer)
        Try

            Dim objMergeDetail As New BusinessFacade.TapeContent()
            objMergeDetail.TapeContentID = txt_TapeContentID.Text
            dg_Merge2.DataSource = objMergeDetail.getEntMergeTapes()
            dg_Merge2.DataBind()

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Fill_KeyWord_Table(ByVal TapeContentID As Integer)
        Try
            Dim qry_3 As String = "select a.EntertainmentKeywordID, a.SubContentTypeID, a.EntertainmentKeyword as 'KeyWord', a.TKeytype as 'KeyType' from ApplicationSetup.EntertainmentKeyword a inner join ArchivalManagement.TapeContentKeywords b on a.EntertainmentKeywordID = b.EntertainmentKeyWordID where b.TapeContentID = " & txt_TapeContentID.Text
            Dim da_3 As New System.Data.SqlClient.SqlDataAdapter(qry_3, Con)
            Dim ds_3 As New DataSet
            da_3.Fill(ds_3, "Table_KeyWord")
            Dt_SaveKeyword = ds_3.Tables("Table_KeyWord")
            ViewState("KeyWordTable") = Dt_SaveKeyword
            Bind_dgKeyWord()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Fill_KeyWord_Table_2(ByVal TapeContentID As Integer)
        Try
            Dim objget As New BusinessFacade.TapeContent()
            objget.TapeContentID = txt_TapeContentID.Text
            Dt_Keyword_2 = objget.Achival_Ent_Keywords_GetRecords()
            ViewState("KeyWordTable_2") = Dt_Keyword_2
            Bind_dgKeyWord_2()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindEditCombo(ByVal TapeContentID As Integer)
        Try
            Dim qry As String = "SELECT TapeContentID,DepartmentID,ContentTypeID,Isnull(ClassificationCode,'N/A') [ClassificationCode],Isnull(CallNo,'N/A') [CallNo],Isnull(LocationCode,'N/A') [LocationCode],isnull(Copies,0) [Copies], Isnull(LowResolutionFileName,'N/A') [LowResolutionFileName], Isnull(HighResolutionFileName,'N/A') [HighResolutionFileName],isnull(FilePath,'N/A') [FilePath] FROM [DAMS_NewDB].[ArchivalManagement].[TapeContent] where TapeContentID = " & txt_TapeContentID.Text
            Dim da As New System.Data.SqlClient.SqlDataAdapter(qry, Con)
            Dim ds As New DataSet
            da.Fill(ds)
            If ds.Tables(0).Rows.Count > 0 Then

                Me.ddl_Channel.SelectedIndex = Me.ddl_Channel.Items.IndexOf(Me.ddl_Channel.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("ContentTypeID"))))

                txt_ClassificationCode.Text = ds.Tables(0).Rows(0)("ClassificationCode")
                txt_CallNo.Text = ds.Tables(0).Rows(0)("CallNo")
                txt_LocationCode.Text = ds.Tables(0).Rows(0)("LocationCode")

                txtLowResFileName.Text = ds.Tables(0).Rows(0)("LowResolutionFileName")
                txtHighResFileName.Text = ds.Tables(0).Rows(0)("HighResolutionFileName")
                txtFiePath.Text = ds.Tables(0).Rows(0)("FilePath")

                If ds.Tables(0).Rows(0)("Copies") <> 0 Then
                    ddlCopies.SelectedItem.Text = ds.Tables(0).Rows(0)("Copies")
                End If

                Dim DeptID As Integer
                If ds.Tables(0).Rows(0)("DepartmentID").ToString = "" Then
                    DeptID = 3
                Else
                    DeptID = ds.Tables(0).Rows(0)("DepartmentID").ToString
                End If

                Dim ObjDepartmentName As New BusinessFacade.TapeContent()
                ObjDepartmentName.DepartmentID = DeptID
                Me.txt_Department.Text = ObjDepartmentName.GetDepartmetnName_by_DepartmentID(ObjDepartmentName.DepartmentID)

            End If

            '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

            Dim qry_2 As String = "select a.* from ArchivalManagement.TapeContentDetail a inner join ArchivalManagement.TapeContent b on a.TapeContentID = b.TapeContentID Where a.TapeContentID = " & txt_TapeContentID.Text
            Dim da_2 As New System.Data.SqlClient.SqlDataAdapter(qry_2, Con)
            Dim ds_2 As New DataSet
            da_2.Fill(ds_2)
            If ds_2.Tables(0).Rows.Count > 0 Then

                Me.ddl_TapeNo.SelectedIndex = Me.ddl_TapeNo.Items.IndexOf(Me.ddl_TapeNo.Items.FindByValue(CStr(ds_2.Tables(0).Rows(0)("TapeLibraryID"))))

                Me.ddl_ProgramChild.SelectedIndex = Me.ddl_ProgramChild.Items.IndexOf(Me.ddl_ProgramChild.Items.FindByValue(CStr(ds_2.Tables(0).Rows(0)("ProgramChildID"))))

                Dim TapeLib_ID As Integer = ds_2.Tables(0).Rows(0)("TapeLibraryID").ToString
                Dim objTapeNumber As New BusinessFacade.TapeContent()
                objTapeNumber.TapeLibraryID = TapeLib_ID
                Me.txt_TapeNumber.Text = objTapeNumber.GetTapeNumber_byTapeLibraryID(objTapeNumber.TapeLibraryID)

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        ddl_Channel.DataTextField = "ContentTypeName"
        ddl_Channel.DataValueField = "ContentTypeID"
        ddl_Channel.DataSource = New BusinessFacade.ContentType().GetRecords()
        ddl_Channel.DataBind()

        ddl_SubCloset.DataTextField = "SubClosetName"
        ddl_SubCloset.DataValueField = "SubClostID"
        ddl_SubCloset.DataSource = New BusinessFacade.SubCloset().GetRecords()
        ddl_SubCloset.DataBind()

        ddl_KeywordType.DataTextField = "KeywordType"
        ddl_KeywordType.DataValueField = "KeywordTypeID"
        ddl_KeywordType.DataSource = New BusinessFacade.KeywordType().GetRecords()
        ddl_KeywordType.DataBind()

        ddl_ProductionStatus.DataTextField = "ProductionStatus"
        ddl_ProductionStatus.DataValueField = "ProductionStatusID"
        ddl_ProductionStatus.DataSource = New BusinessFacade.ProductionStatus().GetRecords()
        ddl_ProductionStatus.DataBind()

        ddl_Keyword.DataTextField = "EntertainmentKeyword"
        ddl_Keyword.DataValueField = "EntertainmentKeywordID"
        ddl_Keyword.DataSource = New BusinessFacade.EntertainmentKeyword().GetRecords()
        ddl_Keyword.DataBind()

        'lstKeyword.DataTextField = "EntertainmentKeyword"
        'lstKeyword.DataValueField = "EntertainmentKeywordID"
        'lstKeyword.DataSource = New BusinessFacade.EntertainmentKeyword().GetRecords()
        'lstKeyword.DataBind()

        'lstKeyType.DataTextField = "KeyType"
        'lstKeyType.DataValueField = "KeyTypeID"
        'lstKeyType.DataSource = New BusinessFacade.KeyType().GetRecords()
        'lstKeyType.DataBind()

        ddl_RecycleTurn.DataTextField = "RecycleTurn"
        ddl_RecycleTurn.DataValueField = "RecycleTurnID"
        ddl_RecycleTurn.DataSource = New BusinessFacade.RecycleTurn().GetRecords()
        ddl_RecycleTurn.DataBind()

        txt_title.Text = txt_ProgramChild.Text

    End Sub

    Private Sub bindTapeCombo()
        Try
            ddl_TapeNo.DataTextField = "TapeNumber"
            ddl_TapeNo.DataValueField = "TapeLibraryID"
            ddl_TapeNo.DataSource = New BusinessFacade.TapeContent().TapeContent_TapeNumber_GetRecords()
            ddl_TapeNo.DataBind()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub ddl_TapeNo_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_TapeNo.SelectedIndexChanged

    End Sub

    Protected Sub bttnSave_Keyword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_Keyword.Click
        Try
            Dim col1 As DataColumn = New DataColumn("KeyType")
            col1.DataType = System.Type.GetType("System.String")
            Dt_SaveKeyword.Columns.Add(col1)

            Dim col2 As DataColumn = New DataColumn("KeyWord")
            col2.DataType = System.Type.GetType("System.String")
            Dt_SaveKeyword.Columns.Add(col2)

            Dim col3 As DataColumn = New DataColumn("EntertainmentKeywordID")
            col3.DataType = System.Type.GetType("System.Int32")
            Dt_SaveKeyword.Columns.Add(col3)


            '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
            If ViewState("KeyWordTable") Is Nothing Then
            Else
                Dt_SaveKeyword = ViewState("KeyWordTable")
            End If

            If txt_dgKeyword_Index.Text <> "" Then

                Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).BeginEdit()
                Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).Item("KeyType") = ddl_KeywordType.SelectedItem.ToString
                Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).Item("KeyWord") = txt_title.Text
                Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).EndEdit()
                txt_dgKeyword_Index.Text = String.Empty

            Else
                Dim Rw As DataRow
                Rw = Dt_SaveKeyword.NewRow()
                Rw.Item("KeyType") = ddl_KeywordType.SelectedItem.ToString
                Rw.Item("KeyWord") = txt_title.Text
                Rw.Item("EntertainmentKeywordID") = DBNull.Value


                Dt_SaveKeyword.Rows.Add(Rw)

            End If
            '''''''''''''''''''''''''''''''''''''''''

            ViewState("KeyWordTable") = Dt_SaveKeyword

            ddl_KeywordType.SelectedIndex = "0"
            txt_title.Text = String.Empty

            Bind_dgKeyWord()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Bind_dgKeyWord()
        dg_KeyWord.DataSource = Dt_SaveKeyword
        dg_KeyWord.DataBind()
    End Sub

    Protected Sub bttnSaveProgramInfo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveProgramInfo.Click
        Try
            Dim col1_ps As DataColumn = New DataColumn("Program")
            col1_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("Program")

            Dim col2_ps As DataColumn = New DataColumn("PartNo")
            col2_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("PartNo")

            Dim col3_ps As DataColumn = New DataColumn("ProductionStatus")
            col3_ps.DataType = System.Type.GetType("System.Int32")
            dt_ProgramInfo.Columns.Add("ProductionStatus")

            Dim col4_ps As DataColumn = New DataColumn("Note")
            col4_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("Note")

            Dim col5_ps As DataColumn = New DataColumn("EpisodeNo")
            col5_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("EpisodeNo")

            Dim col6_ps As DataColumn = New DataColumn("Abstract")
            col6_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("Abstract")

            Dim col7_ps As DataColumn = New DataColumn("Duration")
            col7_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("Duration")

            Dim col8_ps As DataColumn = New DataColumn("StartTime")
            col8_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("StartTime")

            Dim col9_ps As DataColumn = New DataColumn("EndTime")
            col9_ps.DataType = System.Type.GetType("System.String")
            dt_ProgramInfo.Columns.Add("EndTime")

            Dim col10_ps As DataColumn = New DataColumn("TapeContentDetailID")
            col10_ps.DataType = System.Type.GetType("System.Int32")
            dt_ProgramInfo.Columns.Add("TapeContentDetailID")

            If ViewState("ProgramInfo_Table") Is Nothing Then
            Else
                dt_ProgramInfo = ViewState("ProgramInfo_Table")
            End If

            ''''''''''''     '''''''''''     '''''''''''     ''''''''''
            If txt_dgProgramInfo_Index.Text <> "" Then

                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Program") = txt_title.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("PartNo") = txt_PartNo.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProductionStatus") = ddl_ProductionStatus.SelectedValue
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Note") = txt_Note.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EpisodeNo") = txt_EpisodesNo.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Abstract") = txt_Abstract.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = txt_TimeDuration.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_EndTime.Text
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
                txt_dgProgramInfo_Index.Text = String.Empty

            Else

                Dim row_PS As DataRow
                row_PS = dt_ProgramInfo.NewRow()
                row_PS.Item("Program") = txt_title.Text
                row_PS.Item("PartNo") = txt_PartNo.Text
                row_PS.Item("ProductionStatus") = ddl_ProductionStatus.SelectedValue
                row_PS.Item("Note") = txt_Note.Text
                row_PS.Item("EpisodeNo") = txt_EpisodesNo.Text
                row_PS.Item("Abstract") = txt_Abstract.Text
                row_PS.Item("Duration") = txt_TimeDuration.Text
                row_PS.Item("StartTime") = txt_StartTime.Text
                row_PS.Item("EndTime") = txt_EndTime.Text
                row_PS.Item("TapeContentDetailID") = DBNull.Value
                dt_ProgramInfo.Rows.Add(row_PS)

            End If

            ViewState("ProgramInfo_Table") = dt_ProgramInfo
            BindGrid_ProgramInfo()
            Clrscr_ProgramInfo()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindGrid_ProgramInfo()
        dg_programInfo.DataSource = dt_ProgramInfo
        dg_programInfo.DataBind()
    End Sub

    Private Sub Clrscr_ProgramInfo()
        txt_title.Text = String.Empty
        'txt_PartNo.Text = String.Empty
        ddl_ProductionStatus.SelectedIndex = "0"
        txt_Note.Text = String.Empty
        'txt_EpisodesNo.Text = String.Empty
        txt_Abstract.Text = String.Empty
        txt_StartTime.Text = String.Empty
        txt_EndTime.Text = String.Empty
        txt_TimeDuration.Text = String.Empty
        txtToEpisode.Enabled = True


        ST1.Text = "00"
        ST2.Text = "00"
        ST3.Text = "00"
        ST4.Text = "00"
        END1.Text = "00"
        END2.Text = "00"
        END3.Text = "00"
        END4.Text = "00"
        DUR_1.Text = "00"
        DUR_2.Text = "00"
        DUR_3.Text = "00"
        DUR_4.Text = "00"

        txt_UrduScript.Text = "N/A"
        'If lstKeyType.SelectedIndex = -1 And lstKeyword.SelectedIndex = -1 Then
        '    txt_ProgramChild.Text = String.Empty
        'End If

    End Sub

    Private Sub BindMergeTapeNumber()
        dg_Merge.DataSource = dt_MergeTapeNumber
        dg_Merge.DataBind()

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Try
            Err_Department.Visible = False
            Err_TapeNumber.Visible = False
            lblErr_MasterEntry.Text = String.Empty
            lblErr_ProgramInfo.Text = String.Empty

            ''****************************************''

            Dim arr As Array = Split(txt_TapeNumber.Text, "#")
            If arr.Length = 2 Then
                txt_TapeNumber.Text = arr(1)
            End If

            ''****************************************''
            ''********* Get TapeLibraryID ************''
            ''****************************************''

            Dim LibraryID As Integer
            Dim objLibID As New BusinessFacade.TapeIssuance()
            objLibID.TapeNumber = txt_TapeNumber.Text
            LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
            '''''''''''''''''''''''''''''''''''''''''''''''

            ''****************************************''
            ''********** Get DepartmentID ************''
            ''****************************************''

            Dim DepartmentID As Integer
            Dim objDeptID As New BusinessFacade.TapeIssuance()
            objDeptID.DepartmentName = txt_Department.Text
            DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserName As String
            Dim objUserName As New BusinessFacade.Employee()
            objUserName.EmployeeID = UserID
            UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)


            If DepartmentID = 0 Then
                lblErr_MasterEntry.Text = "Please Select Department !!"
                Err_Department.Visible = True
            ElseIf LibraryID = 0 Then
                lblErr_MasterEntry.Text = "Please Select Tape Number !!"
                Err_TapeNumber.Visible = True
                'ElseIf dg_TestProgram.Rows.Count = 0 Then
                '    lblErr_ProgramInfo.Text = "There is no record in Program Information Setion !!"
            Else
                Dim TapeContentID As Integer

                ''******************************************''
                ''********** Tape Content Save *************''
                ''******************************************''
                If txt_TapeContentID.Text = "" Then

                    If LibraryID <> 0 Then
                        Dim ObjUser As New BusinessFacade.TapeContent()
                        ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                        'ObjUser.DepartmentID = ddl_Department.SelectedValue
                        ObjUser.DepartmentID = DepartmentID
                        ObjUser.isCurrentContent = 0
                        ObjUser.ClassificationCode = txt_ClassificationCode.Text
                        ObjUser.CallNo = txt_CallNo.Text
                        ObjUser.LocationCode = txt_LocationCode.Text
                        ObjUser.IsActive = 1
                        ObjUser.Copies = ddlCopies.SelectedValue
                        TapeContentID = ObjUser.SaveRecord_TapeContent()
                        txt_TapeContentID.Text = TapeContentID

                    End If

                Else
                    If LibraryID <> 0 Then
                        Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                        ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                        ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                        'ObjUpdateUser.DepartmentID = ddl_Department.SelectedValue
                        ObjUpdateUser.DepartmentID = DepartmentID
                        ObjUpdateUser.isCurrentContent = 0
                        ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                        ObjUpdateUser.CallNo = txt_CallNo.Text
                        ObjUpdateUser.LocationCode = txt_LocationCode.Text
                        ObjUpdateUser.IsActive = 1
                        ObjUpdateUser.Copies = ddlCopies.SelectedValue
                        Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                    End If
                End If

                ''***********************************************************''
                ''**************** Tape Content Detail Save *****************''
                ''***********************************************************''

                Dim TapeContentDetailID As Integer
                Dim P As Integer
                ' For P = 0 To dg_programInfo.Rows.Count - 1
                For P = 0 To dg_TestProgram.Rows.Count - 1

                    If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then
                        ''''''''''''''' Save ''''''''''''''''''''''''''
                        Dim ObjDetail As New BusinessFacade.TapeContent()
                        'ObjDetail.TapeContentID = TapeContentID
                        ObjDetail.TapeContentID = txt_TapeContentID.Text
                        'ObjDetail.TapeLibraryID = ddl_TapeNo.SelectedValue
                        ObjDetail.TapeLibraryID = LibraryID
                        ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                        'ObjDetail.ProgramChildID = ddl_ProgramChild.SelectedValue
                        ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                        ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                        ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                        ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                        ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                        ObjDetail.ProducedBy = UserID
                        ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                        ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                        ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                        ObjDetail.NocNo = 1
                        ObjDetail.ItemID = 0
                        ObjDetail.MergeID = 0
                        ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                        'ObjDetail.ProducedByName = "Asmatullah"
                        ObjDetail.ProducedByName = UserName
                        ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                        ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                        TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail()

                        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                        Dim q As Integer
                        For q = 0 To dg_keyword_2.Rows.Count - 1
                            If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0 Then
                                If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                    objtest.TapeContentDetailID = TapeContentDetailID
                                    objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                    objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                    objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                    objtest.ProgramChildVskeyword_SaveRecord()
                                End If
                            End If
                        Next

                        ''***************************************************************''
                        ''**************** Tape Content Detail (Update) *****************''
                        ''***************************************************************''
                    Else

                        ''''''''''''''''''''UPdate '''''''''''''''''''''''''''''
                        Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                        ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                        'ObjUpdateDetail.TapeLibraryID = ddl_TapeNo.SelectedValue
                        ObjUpdateDetail.TapeLibraryID = LibraryID
                        ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                        'ObjUpdateDetail.ProgramChildID = ddl_ProgramChild.SelectedValue
                        ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                        ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                        ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                        ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                        ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                        ObjUpdateDetail.ProducedBy = UserID
                        ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                        ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                        ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                        ObjUpdateDetail.NocNo = 1
                        ObjUpdateDetail.ItemID = 1
                        ObjUpdateDetail.MergeID = 1
                        ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                        ObjUpdateDetail.ProducedByName = UserName
                        If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                            ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                        Else
                            ObjUpdateDetail.ProductionStatus = 0
                        End If
                        'ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                        ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                        ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                        ObjUpdateDetail.UpdateRecord_TapeContentDetail()

                        Dim z As Integer
                        For z = 0 To dg_keyword_2.Rows.Count - 1
                            If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0 Then
                                If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                    objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                    objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                    objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                    objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                    objtest.ProgramChildVskeyword_SaveRecord()
                                Else
                                    If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                        Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                        ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                        ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                        ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                        ObjUpdateKW.TapeContentDetailID = 1
                                        ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                        ObjUpdateKW.ProgramChildVskeyword_Update()
                                    End If
                                End If
                            End If
                        Next

                    End If

                Next


                '''''''''''''' Merge Tape Content '''''''''''''''''''''
                'Dim U As Integer
                'If ddl_MergeTapeNo.SelectedIndex <> "0" Then

                '    Dim objMerge As New BusinessFacade.TapeContent()
                '    objMerge.MasterID = TapeContentID
                '    objMerge.TapeLibraryID = ddl_MergeTapeNo.SelectedValue
                '    U = objMerge.SaveRecord_MergeTapeContent()

                '    ''''''''''''' Merge Tape Content Detail '''''''''''''''

                '    Dim Y As Integer
                '    Dim ObjMergeDetail As New BusinessFacade.TapeContent()
                '    ObjMergeDetail.DetailID = TapeContentDetailID
                '    ObjMergeDetail.TapeLibraryID = ddl_MergeTapeNo.SelectedValue
                '    Y = ObjMergeDetail.SaveRecord_MergeTapeContentDetail()
                'End If

                ''***************************************************''
                ''***************** Merge Records *******************''
                ''***************************************************''

                Dim U As Integer
                Dim Cnt As Integer
                For Cnt = 0 To dg_Merge.Rows.Count - 1

                    If ddl_MergeTapeNo.SelectedIndex <> "0" Then

                        ''*********************************''
                        ''****** Merge Tape Content *******''
                        ''*********************************''

                        Dim objMerge As New BusinessFacade.TapeContent()
                        'objMerge.MasterID = TapeContentID
                        objMerge.MasterID = txt_TapeContentID.Text
                        objMerge.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                        U = objMerge.SaveRecord_MergeTapeContent()

                        ''****************************************''
                        ''****** Merge Tape Content detail *******''
                        ''****************************************''

                        If dg_TestProgram.Rows.Count > 0 Then
                            Dim Y As Integer
                            Dim ObjMergeDetail As New BusinessFacade.TapeContent()
                            ObjMergeDetail.DetailID = TapeContentDetailID
                            ObjMergeDetail.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                            Y = ObjMergeDetail.SaveRecord_MergeTapeContentDetail()
                        Else
                            Dim Y As Integer
                            Dim ObjMergeDetail As New BusinessFacade.TapeContent()
                            ObjMergeDetail.DetailID = 0
                            ObjMergeDetail.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                            Y = ObjMergeDetail.SaveRecord_MergeTapeContentDetail()
                        End If



                        Dim MergeTapeIssuanceID As Integer
                        Dim ObjGetTapeIssuanceID As New BusinessFacade.TapeContent()
                        ObjGetTapeIssuanceID.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                        MergeTapeIssuanceID = ObjGetTapeIssuanceID.GetTapeIssuanceID()

                        ''********************************************************''
                        ''****************** Update IsActive *********************''
                        ''********************************************************''

                        If U <> 0 Then

                            'Dim ObjIsactive As New BusinessFacade.TapeContent()
                            'ObjIsactive.TapeContentID = U
                            'ObjIsactive.TapeContent_Update()

                            Dim ObjTapeContentDetail As New BusinessFacade.TapeContent()
                            ObjTapeContentDetail.TapeContentID = TapeContentID
                            ObjTapeContentDetail.OldTapeContentID = U
                            ObjTapeContentDetail.TapeContentDetail_Update()


                            ''***************************************************''
                            ''************** Make Copy of Record ****************''
                            ''***************************************************''
                            Dim TempTable As DataTable
                            Dim ObjMakeCopy As New BusinessFacade.TapeContent()
                            'ObjMakeCopy.TapeContentID = txt_TapeContentID.Text
                            ObjMakeCopy.TapeContentID = MergeTapeIssuanceID
                            TempTable = ObjMakeCopy.TapeContentDetail_CopyRecord()

                            Dim i As Integer

                            For i = 0 To TempTable.Rows.Count - 1

                                Dim ObjPrgVsKw As New BusinessFacade.TapeContent()
                                ObjPrgVsKw.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                                ObjPrgVsKw.TapeContentDetailID = TempTable.Rows(i).Item(0).ToString
                                ObjPrgVsKw.GetProgramVsKeyword()

                                Dim ObjUpdate As New BusinessFacade.TapeContent()
                                ObjUpdate.TapeContentID = txt_TapeContentID.Text
                                ObjUpdate.TapeLibraryID = LibraryID
                                ObjUpdate.TapeContentDetailID = dg_Merge.Rows(Cnt).Cells(0).Text
                                'ObjUpdate.TapeContentDetailID = TempTable.Rows(i).Item(0).ToString
                                ObjUpdate.MergeTape4nullpRogram()

                            Next
                        End If

                    End If
                Next

                ' ''********************************************************''
                ' ''****************** Update IsActive *********************''
                ' ''********************************************************''

                'If U <> 0 Then

                '    'Dim ObjIsactive As New BusinessFacade.TapeContent()
                '    'ObjIsactive.TapeContentID = U
                '    'ObjIsactive.TapeContent_Update()

                '    Dim ObjTapeContentDetail As New BusinessFacade.TapeContent()
                '    ObjTapeContentDetail.TapeContentID = TapeContentID
                '    ObjTapeContentDetail.OldTapeContentID = U
                '    ObjTapeContentDetail.TapeContentDetail_Update()

                '    ''***************************************************''
                '    ''************** Make Copy of Record ****************''
                '    ''***************************************************''
                '    Dim TempTable As DataTable
                '    Dim ObjMakeCopy As New BusinessFacade.TapeContent()
                '    ObjMakeCopy.TapeContentID = txt_TapeContentID.Text
                '    TempTable = ObjMakeCopy.TapeContentDetail_CopyRecord()

                '    Dim i As Integer
                '    For i = 0 To TempTable.Rows.Count - 1
                '        Dim ObjPrgVsKw As New BusinessFacade.TapeContent()
                '        ObjPrgVsKw.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                '        ObjPrgVsKw.TapeContentDetailID = TempTable.Rows(i).Item(0).ToString
                '        ObjPrgVsKw.GetProgramVsKeyword()
                '    Next
                'End If

                ''********************************************************''
                ''******************** Keyword ***************************''
                ''********************************************************''

                Dim K As Integer
                For K = 0 To dg_KeyWord.Rows.Count - 1

                    If dg_KeyWord.Rows(K).Cells(3).Text = "&nbsp;" Then
                        '''''''''''' Insert KeyWords '''''''''''''''''''''''
                        Dim ObjKeyword As New BusinessFacade.TapeContent()
                        ObjKeyword.SubContentTypeID = 1
                        ObjKeyword.EntKeyWord = dg_KeyWord.Rows(K).Cells(2).Text
                        ObjKeyword.KeyType = dg_KeyWord.Rows(K).Cells(1).Text
                        Dim KeyWordID As Integer
                        KeyWordID = ObjKeyword.SaveRecord_TapeContentKeyWords()


                        '''''''''' Insert in TapeContentKeywords '''''''''''
                        Dim ObjTapeKeyWord As New BusinessFacade.TapeContent()
                        ObjTapeKeyWord.MasterID = TapeContentID
                        ObjTapeKeyWord.EntertainmentKeyWordID = KeyWordID
                        ObjTapeKeyWord.ProducedBy = 3  'UserID
                        Dim J As Integer
                        J = ObjTapeKeyWord.SaveRecord_TapeContent_KeyWords()
                    Else

                        '''''''''''''''' Update KeyWords '''''''''''''''''''    
                        Dim ObjUpdate As New BusinessFacade.TapeContent()
                        ObjUpdate.EntertainmentKeyWordID = dg_KeyWord.Rows(K).Cells(3).Text
                        ObjUpdate.SubContentTypeID = 1
                        ObjUpdate.EntKeyWord = dg_KeyWord.Rows(K).Cells(2).Text
                        ObjUpdate.KeyType = dg_KeyWord.Rows(K).Cells(1).Text
                        ObjUpdate.UpdateRecord_TapeContentKeyWords()

                    End If


                Next
                lblErr.Text = "Record has been Saved Successfully!!"

                '********************************************************'
                '********** Update Tape Status in TapeLibrary ***********'
                '********************************************************'

                If LibraryID <> 0 Then
                    Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                    objUpdateTapeStatus.TapeLibraryID = LibraryID
                    objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                End If

                ''******************************************************''
                ''************* Search Engine Save Record **************''
                ''******************************************************''

                If txt_TapeContentID.Text <> "" Then

                    'Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                    'ObjSearchEngine.TapeContentID = txt_TapeContentID.Text
                    ''ObjSearchEngine.TapeContentID = txt_TapeContentID.Text
                    'ObjSearchEngine.SearchEngine_Entertainment_SaveRecord()

                    Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                    ObjSearchEngine2.TapeLibraryID = LibraryID
                    ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()
                End If

                '************************ End ***************************'
                Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                ''******************* Fill Grids ************************''


            End If

        Catch ex As Exception
            Throw
        End Try


    End Sub

    Protected Sub dg_KeyWord_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_KeyWord.SelectedIndexChanged
        Dim Index As Integer
        Index = dg_KeyWord.SelectedIndex.ToString
        ddl_KeywordType.SelectedItem.Text = dg_KeyWord.Rows(Index).Cells(1).Text
        txt_title.Text = dg_KeyWord.Rows(Index).Cells(2).Text

        txt_dgKeyword_Index.Text = Index

    End Sub

    Protected Sub dg_programInfo_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_programInfo.SelectedIndexChanged
        Dim Ind As Integer
        Ind = dg_programInfo.SelectedIndex.ToString
        txt_title.Text = dg_programInfo.Rows(Ind).Cells(1).Text
        txt_PartNo.Text = dg_programInfo.Rows(Ind).Cells(2).Text
        ddl_ProductionStatus.SelectedItem.Text = dg_programInfo.Rows(Ind).Cells(3).Text
        txt_Note.Text = dg_programInfo.Rows(Ind).Cells(4).Text
        txt_EpisodesNo.Text = dg_programInfo.Rows(Ind).Cells(5).Text
        txt_Abstract.Text = dg_programInfo.Rows(Ind).Cells(6).Text
        txt_StartTime.Text = dg_programInfo.Rows(Ind).Cells(7).Text
        txt_EndTime.Text = dg_programInfo.Rows(Ind).Cells(8).Text
        txt_TimeDuration.Text = dg_programInfo.Rows(Ind).Cells(9).Text

        txt_dgProgramInfo_Index.Text = Ind

    End Sub

    Protected Sub bttnSaveProg_2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveProg_2.Click
        txtBttnProgram.Text = "1"
        Try
            Calculate()
            Me.txt_title.Text = Me.txt_ProgramChild.Text
            lblErr.Text = String.Empty
            lblErr_ProgramInfo.Text = String.Empty

            Dim col1_ps As DataColumn = New DataColumn("Program")
            col1_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("Program")

            Dim col2_ps As DataColumn = New DataColumn("PartNo")
            col2_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("PartNo")

            Dim col3_ps As DataColumn = New DataColumn("ProductionStatus")
            col3_ps.DataType = System.Type.GetType("System.Int32")
            dt_Test_ProgramInfo.Columns.Add("ProductionStatus")

            Dim col4_ps As DataColumn = New DataColumn("Note")
            col4_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("Note")

            Dim col5_ps As DataColumn = New DataColumn("EpisodeNo")
            col5_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("EpisodeNo")

            Dim col6_ps As DataColumn = New DataColumn("Abstract")
            col6_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("Abstract")

            Dim col7_ps As DataColumn = New DataColumn("Duration")
            col7_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("Duration")

            Dim col8_ps As DataColumn = New DataColumn("StartTime")
            col8_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("StartTime")

            Dim col9_ps As DataColumn = New DataColumn("EndTime")
            col9_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("EndTime")

            Dim col10_ps As DataColumn = New DataColumn("TapeContentDetailID")
            col10_ps.DataType = System.Type.GetType("System.Int32")
            dt_Test_ProgramInfo.Columns.Add("TapeContentDetailID")

            Dim col11_ps As DataColumn = New DataColumn("ProgramChildID")
            col10_ps.DataType = System.Type.GetType("System.Int32")
            dt_Test_ProgramInfo.Columns.Add("ProgramChildID")

            Dim col12_ps As DataColumn = New DataColumn("EntryDate")
            col12_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("EntryDate")

            Dim col13_ps As DataColumn = New DataColumn("UrduScript")
            col13_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("UrduScript")

            Dim col14_ps As DataColumn = New DataColumn("onAirdate")
            col12_ps.DataType = System.Type.GetType("System.String")
            dt_Test_ProgramInfo.Columns.Add("onAirdate")

            If ViewState("ProgramInfo_Table_2") Is Nothing Then
            Else
                dt_Test_ProgramInfo = ViewState("ProgramInfo_Table_2")
            End If

            ''''''''''''     '''''''''''     '''''''''''     ''''''''''
            If txt_dgProgramInfo_Index.Text <> "" Then

                '''''''''' For Getting Program Child ID ''''''''''''''''
                Dim ProgramChildID As Integer
                Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
                ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
                ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Program") = txt_title.Text
                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProgramChildID") = ProgramChildID

                If txt_PartNo.Text <> "" Then
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("PartNo") = txt_PartNo.Text
                Else
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("PartNo") = "0"
                End If

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProductionStatus") = ddl_ProductionStatus.SelectedValue

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Note") = txt_Note.Text

                If txt_EpisodesNo.Text <> "" Then
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EpisodeNo") = txt_EpisodesNo.Text
                Else
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EpisodeNo") = "0"
                End If

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Abstract") = txt_Abstract.Text

                Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                If TDuration = ":::" Then
                    TDuration = "00:00:00:00"
                End If
                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = TDuration

                ' Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                '  Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                '  dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = ETime
                ' dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = STime

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_starttime1.Text

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_Endtime1.Text



                If txtEntryDate.Text = "" Then
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = Date.Now().ToString("dd-MMM-yyyy")
                Else
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = txtEntryDate.Text
                End If

                If txt_UrduScript.Text = "" Then
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = "N/A"
                Else
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text
                End If

                If txtonairdate.Text = "" Then
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("onAirdate") = Date.Now().ToString("dd-MMM-yyyy")
                Else
                    dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("onAirdate") = txtonairdate.Text
                End If

                dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
                txt_dgProgramInfo_Index.Text = String.Empty



            Else

                ''**********************************************************''
                ''*************** For Getting Program Child ID *************''
                ''**********************************************************''

                Dim ProgramChildID As Integer
                Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
                ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
                ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

                If ProgramChildID <> 0 Then

                    ''*********************************************************************************************''
                    ''****************************** From and To Episode Numbers **********************************''
                    ''*********************************************************************************************''

                    Dim FromEpisode As Integer
                    If txt_EpisodesNo.Text = "" Then
                        FromEpisode = 0
                    Else
                        FromEpisode = CInt(txt_EpisodesNo.Text)
                    End If

                    Dim ToEpisode As Integer
                    If txtToEpisode.Text = "" Then
                        ToEpisode = FromEpisode
                    Else
                        ToEpisode = CInt(txtToEpisode.Text)
                    End If

                    If ToEpisode < FromEpisode Then
                        RegisterClientScriptBlock("ClientScript", "<script language=javascript>" & "alert('" & "Attention: Invalid Episode Range, Please correct!" & "')" & "</script>")
                        Exit Sub
                    End If


                    For EpisodeNumber As Integer = FromEpisode To ToEpisode

                        Dim row_PS As DataRow
                        row_PS = dt_Test_ProgramInfo.NewRow()
                        row_PS.Item("Program") = txt_ProgramChild.Text

                        If txt_PartNo.Text = "" Then
                            row_PS.Item("PartNo") = "0"
                        Else
                            row_PS.Item("PartNo") = txt_PartNo.Text
                        End If

                        row_PS.Item("ProductionStatus") = ddl_ProductionStatus.SelectedValue

                        If txt_Note.Text = "" Then
                            row_PS.Item("Note") = "N/A"
                        Else
                            row_PS.Item("Note") = txt_Note.Text
                        End If



                        If txt_EpisodesNo.Text = "" Then
                            row_PS.Item("EpisodeNo") = 0
                        Else
                            If EpisodeNumber < 10 And EpisodeNumber <> 0 Then
                                row_PS.Item("EpisodeNo") = "0" + CStr(EpisodeNumber)

                            Else
                                row_PS.Item("EpisodeNo") = EpisodeNumber 'txt_EpisodesNo.Text
                            End If

                        End If

                        If txt_Abstract.Text = "" Then
                            row_PS.Item("Abstract") = "N/A"
                        Else
                            row_PS.Item("Abstract") = txt_Abstract.Text
                        End If

                        Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                        If TDuration = ":::" Then
                            TDuration = "00:00:00:00"
                        End If
                        row_PS.Item("Duration") = TDuration

                        'Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                        'Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                        row_PS.Item("StartTime") = txt_starttime1.Text
                        row_PS.Item("EndTime") = txt_Endtime1.Text




                        row_PS.Item("TapeContentDetailID") = DBNull.Value
                        If txtEntryDate.Text = "" Then
                            row_PS.Item("EntryDate") = Date.Now().ToString("dd-MMM-yyyy")
                        Else
                            row_PS.Item("EntryDate") = txtEntryDate.Text
                        End If

                        If txtonairdate.Text = "" Then
                            row_PS.Item("OnAirDate") = ""
                        Else
                            row_PS.Item("OnAirDate") = txtonairdate.Text
                        End If

                        If txt_UrduScript.Text = "" Then
                            row_PS.Item("UrduScript") = "N/A"
                        Else
                            row_PS.Item("UrduScript") = txt_UrduScript.Text
                        End If

                        row_PS.Item("ProgramChildID") = ProgramChildID
                        dt_Test_ProgramInfo.Rows.Add(row_PS)

                    Next

                    ''*********************************************************************************************''


                End If

            End If

            ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
            BindGrid_ProgramInfo_2()
            Clrscr_ProgramInfo()

            CollapsiblePanelExtender1.Collapsed = True

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindGrid_ProgramInfo_2()
        dg_TestProgram.DataSource = dt_Test_ProgramInfo
        dg_TestProgram.DataBind()
    End Sub

    Protected Sub bttnSaveKeyword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveKeyword.Click
        Try
            lblErr.Text = String.Empty
            If ViewState("KeyWordTable_2") Is Nothing Then
                Dim col1 As DataColumn = New DataColumn("ProgramChildID")
                col1.DataType = System.Type.GetType("System.Int32")
                Dt_Keyword_2.Columns.Add(col1)

                Dim col3 As DataColumn = New DataColumn("ProgramChildName")
                col1.DataType = System.Type.GetType("System.String")
                Dt_Keyword_2.Columns.Add(col3)

                Dim col2 As DataColumn = New DataColumn("EntertainmentKeywordID")
                col2.DataType = System.Type.GetType("System.Int32")
                Dt_Keyword_2.Columns.Add(col2)

                Dim col4 As DataColumn = New DataColumn("EntertainmentKeyword")
                col1.DataType = System.Type.GetType("System.String")
                Dt_Keyword_2.Columns.Add(col4)


                Dim col5 As DataColumn = New DataColumn("ProgramVsKeywordID")
                col1.DataType = System.Type.GetType("System.Int32")
                Dt_Keyword_2.Columns.Add(col5)

                Dim col6 As DataColumn = New DataColumn("KeyTypeID")
                col6.DataType = System.Type.GetType("System.Int32")
                Dt_Keyword_2.Columns.Add(col6)

                Dim col7 As DataColumn = New DataColumn("KeyType")
                col7.DataType = System.Type.GetType("System.String")
                Dt_Keyword_2.Columns.Add(col7)

                Dim col8 As DataColumn = New DataColumn("EpisodeNo")
                col8.DataType = System.Type.GetType("System.String")
                Dt_Keyword_2.Columns.Add(col8)

                Dim col9 As DataColumn = New DataColumn("PartNo")
                col9.DataType = System.Type.GetType("System.String")
                Dt_Keyword_2.Columns.Add(col9)

            Else
                Dt_Keyword_2 = ViewState("KeyWordTable_2")
            End If


            If txt_dgKeyword_Index.Text <> "" Then

                Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()

                ''*********************************************************''
                ''************* For Getting Program Child ID **************''
                ''*********************************************************''

                Dim ProgramChildID As Integer
                Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
                ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
                ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

                ''*********************************************************''
                ''************* For Getting Program Child ID **************''
                ''*********************************************************''
                Dim KWID As Integer
                Dim ObjKWID As New BusinessFacade.EntertainmentKeyword()
                ObjKWID.EntertainmentKeyword = KW1.Text
                KWID = ObjKWID.GetKeywordID_AutoComplete(ObjKWID.EntertainmentKeyword)

                ''*********************************************************''
                ''************* For Getting Program Child ID **************''
                ''*********************************************************''
                Dim KTID As Integer
                Dim ObjKTID As New BusinessFacade.KeyType()
                ObjKTID.KeyType = KT1.Text
                KTID = ObjKTID.GetKeyTypeID_AutoComplete(ObjKTID.KeyType)

                If ProgramChildID <> 0 And KWID <> 0 And KTID <> 0 Then
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("ProgramChildID") = ProgramChildID
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("ProgramChildName") = txt_ProgramChild.Text
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("KeyTypeID") = KTID
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("KeyType") = KT1.Text
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeyword") = KW1.Text
                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeywordID") = KWID
                    If txt_EpisodesNo.Text = "" Then
                        Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EpisodeNo") = 0
                    Else
                        Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EpisodeNo") = txt_EpisodesNo.Text
                    End If

                    If txt_PartNo.Text = "" Then
                        Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("PartNo") = 0
                    Else
                        Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("PartNo") = txt_PartNo.Text
                    End If

                    Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
                    txt_dgKeyword_Index.Text = String.Empty
                End If


            Else

                ''********************************************************''
                ''************* For Getting Program Child ID *************''
                ''********************************************************''

                Dim ProgramChildID As Integer
                Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
                ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
                ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

                '*********************************************************************************************''
                ''****************************** From and To Episode Numbers **********************************''
                ''*********************************************************************************************''

                'Dim FromEpisode As Integer = IIf(txt_EpisodesNo.Text = "", 0, CInt(txt_EpisodesNo.Text))
                'Dim ToEpisode As Integer
                'If txtToEpisode.Text = "" Then
                'ToEpisode = FromEpisode
                'Else
                'ToEpisode = CInt(txtToEpisode.Text)
                'End If


                Dim FromEpisode As Integer
                If txt_EpisodesNo.Text = "" Then
                    FromEpisode = 0
                Else
                    FromEpisode = CInt(txt_EpisodesNo.Text)
                End If

                Dim ToEpisode As Integer
                If txtToEpisode.Text = "" Then
                    ToEpisode = FromEpisode
                Else
                    ToEpisode = CInt(txtToEpisode.Text)
                End If

                If ToEpisode < FromEpisode Then
                    RegisterClientScriptBlock("ClientScript", "<script language=javascript>" & "alert('" & "Attention: Invalid Episode Range, Please correct!" & "')" & "</script>")
                    Exit Sub
                End If

                For EpisodeNumber As Integer = FromEpisode To ToEpisode


                    Dim x As Integer
                    For x = 1 To 3

                        Dim KW, KT As String
                        If x = 1 Then
                            KW = KW1.Text
                            KT = KT1.Text
                        ElseIf x = 2 Then
                            KW = KW2.Text
                            KT = KT2.Text
                        Else
                            KW = KW3.Text
                            KT = KT3.Text
                        End If

                        '''''''''' For Getting Program Child ID ''''''''''''''''
                        Dim KWID As Integer
                        Dim ObjKWID As New BusinessFacade.EntertainmentKeyword()
                        ObjKWID.EntertainmentKeyword = KW
                        KWID = ObjKWID.GetKeywordID_AutoComplete(ObjKWID.EntertainmentKeyword)

                        '''''''''' For Getting Program Child ID ''''''''''''''''
                        Dim KTID As Integer
                        Dim ObjKTID As New BusinessFacade.KeyType()
                        ObjKTID.KeyType = KT
                        KTID = ObjKTID.GetKeyTypeID_AutoComplete(ObjKTID.KeyType)

                        If ProgramChildID <> 0 And KWID <> 0 And KTID <> 0 Then
                            Dim Rw As DataRow
                            Rw = Dt_Keyword_2.NewRow()
                            Rw.Item("ProgramChildID") = ProgramChildID
                            Rw.Item("ProgramChildName") = txt_ProgramChild.Text
                            Rw.Item("EntertainmentKeywordID") = KWID
                            Rw.Item("EntertainmentKeyword") = KW
                            Rw.Item("ProgramVsKeywordID") = DBNull.Value
                            Rw.Item("KeyTypeID") = KTID
                            Rw.Item("KeyType") = KT
                            If txt_EpisodesNo.Text = "" Then
                                Rw.Item("EpisodeNo") = 0
                            Else

                                If EpisodeNumber < 10 And EpisodeNumber <> 0 Then
                                    Rw.Item("EpisodeNo") = "0" + CStr(EpisodeNumber) 'txt_EpisodesNo.Text
                                Else
                                    Rw.Item("EpisodeNo") = EpisodeNumber 'txt_EpisodesNo.Text
                                End If


                            End If
                            If txt_PartNo.Text = "" Then
                                Rw.Item("PartNo") = 0
                            Else
                                Rw.Item("PartNo") = txt_PartNo.Text
                            End If
                            Dt_Keyword_2.Rows.Add(Rw)

                        End If
                    Next

                Next
                ''*********************************************************************************************''


            End If

            ViewState("KeyWordTable_2") = Dt_Keyword_2

            Bind_dgKeyWord_2()

            CollapsiblePanelExtender1.Collapsed = True
            Clear()

            'If lstKeyType.SelectedIndex <> -1 And lstKeyword.SelectedIndex <> -1 Then
            '    lblErr.Text = String.Empty
            '    If ViewState("KeyWordTable_2") Is Nothing Then
            '        Dim col1 As DataColumn = New DataColumn("ProgramChildID")
            '        col1.DataType = System.Type.GetType("System.Int32")
            '        Dt_Keyword_2.Columns.Add(col1)

            '        Dim col3 As DataColumn = New DataColumn("ProgramChildName")
            '        col1.DataType = System.Type.GetType("System.String")
            '        Dt_Keyword_2.Columns.Add(col3)

            '        Dim col2 As DataColumn = New DataColumn("EntertainmentKeywordID")
            '        col2.DataType = System.Type.GetType("System.Int32")
            '        Dt_Keyword_2.Columns.Add(col2)

            '        Dim col4 As DataColumn = New DataColumn("EntertainmentKeyword")
            '        col1.DataType = System.Type.GetType("System.String")
            '        Dt_Keyword_2.Columns.Add(col4)


            '        Dim col5 As DataColumn = New DataColumn("ProgramVsKeywordID")
            '        col1.DataType = System.Type.GetType("System.Int32")
            '        Dt_Keyword_2.Columns.Add(col5)

            '        Dim col6 As DataColumn = New DataColumn("KeyTypeID")
            '        col6.DataType = System.Type.GetType("System.Int32")
            '        Dt_Keyword_2.Columns.Add(col6)

            '        Dim col7 As DataColumn = New DataColumn("KeyType")
            '        col7.DataType = System.Type.GetType("System.String")
            '        Dt_Keyword_2.Columns.Add(col7)

            '    Else
            '        Dt_Keyword_2 = ViewState("KeyWordTable_2")
            '    End If


            '    If txt_dgKeyword_Index.Text <> "" Then

            '        Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()

            '        '''''''''' For Getting Program Child ID ''''''''''''''''
            '        Dim ProgramChildID As Integer
            '        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
            '        ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
            '        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

            '        If ProgramChildID <> 0 Then
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("ProgramChildID") = ProgramChildID
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("ProgramChildName") = txt_ProgramChild.Text
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeywordID") = lstKeyword.SelectedValue
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeyword") = lstKeyword.SelectedItem.ToString
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("KeyTypeID") = lstKeyType.SelectedValue
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("KeyType") = lstKeyType.SelectedItem.ToString
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeyword") = lstKeyword.SelectedItem.ToString
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).Item("EntertainmentKeywordID") = lstKeyword.SelectedValue
            '            Dt_Keyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
            '            txt_dgKeyword_Index.Text = String.Empty
            '        End If


            '    Else

            '        '''''''''' For Getting Program Child ID ''''''''''''''''
            '        Dim ProgramChildID As Integer
            '        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
            '        ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
            '        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

            '        If ProgramChildID <> 0 Then
            '            Dim U As Integer
            '            For U = 0 To lstKeyword.Items.Count - 1
            '                If lstKeyword.Items(U).Selected = True Then
            '                    Dim Rw As DataRow
            '                    Rw = Dt_Keyword_2.NewRow()

            '                    Rw.Item("ProgramChildID") = ProgramChildID
            '                    Rw.Item("ProgramChildName") = txt_ProgramChild.Text
            '                    Rw.Item("EntertainmentKeywordID") = lstKeyword.Items(U).Value
            '                    Rw.Item("EntertainmentKeyword") = lstKeyword.Items(U).Text
            '                    Rw.Item("ProgramVsKeywordID") = DBNull.Value
            '                    Rw.Item("KeyTypeID") = lstKeyType.SelectedValue
            '                    Rw.Item("KeyType") = lstKeyType.SelectedItem.ToString
            '                    Dt_Keyword_2.Rows.Add(Rw)
            '                End If
            '            Next

            '        End If

            '    End If

            '    ViewState("KeyWordTable_2") = Dt_Keyword_2

            '    Bind_dgKeyWord_2()

            '    CollapsiblePanelExtender1.Collapsed = True
            '    Clear()
            'Else
            '    lblErr_ProgramInfo.Text = "Please Select KeyType and Keyword !"
            'End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub Bind_dgKeyWord_2()
        dg_keyword_2.DataSource = Dt_Keyword_2
        dg_keyword_2.DataBind()
    End Sub

    Protected Sub dg_TestProgram_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_TestProgram.SelectedIndexChanged

        Dim Ind_Prog As Integer
        Ind_Prog = dg_TestProgram.SelectedIndex.ToString
        txt_title.Text = dg_TestProgram.Rows(Ind_Prog).Cells(1).Text
        txt_ProgramChild.Text = dg_TestProgram.Rows(Ind_Prog).Cells(1).Text
        txt_PartNo.Text = dg_TestProgram.Rows(Ind_Prog).Cells(2).Text

        'If dg_TestProgram.Rows(Ind_Prog).Cells(3).Text <> "&nbsp;" Or dg_TestProgram.Rows(Ind_Prog).Cells(3).Text <> "0" Then
        '    ddl_ProductionStatus.SelectedValue = dg_TestProgram.Rows(Ind_Prog).Cells(3).Text
        'End If

        txt_Note.Text = Server.HtmlDecode(dg_TestProgram.Rows(Ind_Prog).Cells(4).Text)
        txt_EpisodesNo.Text = dg_TestProgram.Rows(Ind_Prog).Cells(5).Text
        txtToEpisode.Text = dg_TestProgram.Rows(Ind_Prog).Cells(5).Text
        txtToEpisode.Enabled = False

        txt_Abstract.Text = dg_TestProgram.Rows(Ind_Prog).Cells(6).Text

        txt_Endtime1.Text = dg_TestProgram.Rows(Ind_Prog).Cells(8).Text
        txt_starttime1.Text = dg_TestProgram.Rows(Ind_Prog).Cells(7).Text


        txt_EndTime.Text = dg_TestProgram.Rows(Ind_Prog).Cells(7).Text
        txt_StartTime.Text = dg_TestProgram.Rows(Ind_Prog).Cells(8).Text


        txt_TimeDuration.Text = dg_TestProgram.Rows(Ind_Prog).Cells(9).Text
        ddl_ProgramChild.SelectedValue = dg_TestProgram.Rows(Ind_Prog).Cells(11).Text
        txt_dgProgramInfo_Index.Text = Ind_Prog

        If txt_Note.Text = "&nbsp;" Then
            txt_Note.Text = "N/A"
        End If

        If txt_Abstract.Text = "&nbsp;" Then
            txt_Abstract.Text = "N/A"
        End If

        If txt_PartNo.Text = "&nbsp;" Then
            txt_PartNo.Text = "N/A"
        End If

        If txt_StartTime.Text = "&nbsp;" Then
            txt_StartTime.Text = "00:00:00:00"
        End If
        If txt_EndTime.Text = "&nbsp;" Then
            txt_EndTime.Text = "00:00:00:00"
        End If

        Dim time1 As String
        time1 = txt_StartTime.Text
        Dim Start As Array = time1.Split(":")
        ST1.Text = Start(0).ToString
        ST2.Text = Start(1).ToString
        ST3.Text = Start(2).ToString
        ST4.Text = Start(3).ToString
        'ST4.Text = "00"

        Dim time2 As String
        time2 = txt_EndTime.Text
        Dim EndT As Array = time2.Split(":")
        END1.Text = EndT(0).ToString
        END2.Text = EndT(1).ToString
        END3.Text = EndT(2).ToString
        END4.Text = EndT(3).ToString
        'END4.Text = "00"

        Dim Durat As String
        Durat = txt_EndTime.Text
        Dim Dur As Array = time2.Split(":")
        DUR_1.Text = Dur(0).ToString
        DUR_2.Text = Dur(1).ToString
        DUR_3.Text = Dur(2).ToString
        DUR_4.Text = Dur(3).ToString
        'DUR_4.Text = "00"

        If dg_TestProgram.Rows(Ind_Prog).Cells(12).Text = "&nbsp;" Then
            txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
        Else
            txtEntryDate.Text = dg_TestProgram.Rows(Ind_Prog).Cells(12).Text
        End If

        If dg_TestProgram.Rows(Ind_Prog).Cells(14).Text = "&nbsp;" Then
            txt_UrduScript.Text = "N/A"
        Else
            txt_UrduScript.Text = dg_TestProgram.Rows(Ind_Prog).Cells(14).Text
        End If

        If dg_TestProgram.Rows(Ind_Prog).Cells(15).Text = "&nbsp;" Then
            txtonairdate.Text = ""
        Else
            txtonairdate.Text = dg_TestProgram.Rows(Ind_Prog).Cells(15).Text
        End If

        If txt_dgProgramInfo_Index.Text <> "" Then
            TabContainer1.ActiveTabIndex = CInt(2)
        End If


    End Sub

    Protected Sub dg_keyword_2_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_keyword_2.SelectedIndexChanged

        Dim Index As Integer
        Index = dg_keyword_2.SelectedIndex.ToString
        txt_ProgramChild.Text = dg_keyword_2.Rows(Index).Cells(2).Text
        ddl_ProgramChild.SelectedValue = dg_keyword_2.Rows(Index).Cells(1).Text
        'lstKeyword.SelectedValue = dg_keyword_2.Rows(Index).Cells(3).Text
        'If dg_keyword_2.Rows(Index).Cells(6).Text <> "&nbsp;" Then
        '    lstKeyType.SelectedValue = dg_keyword_2.Rows(Index).Cells(6).Text
        'End If

        KW1.Text = dg_keyword_2.Rows(Index).Cells(4).Text
        If dg_keyword_2.Rows(Index).Cells(7).Text <> "&nbsp;" Then
            KT1.Text = dg_keyword_2.Rows(Index).Cells(7).Text
        Else
            KT1.Text = "N/A"
        End If

        'If dg_keyword_2.Rows(Index).Cells(7).Text <> "&nbsp;" Then
        '    txt_EpisodesNo.Text = dg_keyword_2.Rows(Index).Cells(7).Text
        'Else
        '    txt_EpisodesNo.Text = 0
        'End If

        If dg_keyword_2.Rows(Index).Cells(8).Text <> "&nbsp;" Then
            txt_EpisodesNo.Text = dg_keyword_2.Rows(Index).Cells(8).Text
            txtToEpisode.Text = dg_keyword_2.Rows(Index).Cells(8).Text

        Else
            txt_EpisodesNo.Text = 0
            txtToEpisode.Text = 0
        End If


        If dg_keyword_2.Rows(Index).Cells(9).Text <> "&nbsp;" Then
            txt_PartNo.Text = dg_keyword_2.Rows(Index).Cells(9).Text
        Else
            txt_PartNo.Text = 0
        End If

        'If txt_EpisodesNo.Text = "" Then
        '    Rw.Item("EpisodeNo") = 0
        'Else
        '    Rw.Item("EpisodeNo") = txt_EpisodesNo.Text
        'End If

        txt_dgKeyword_Index.Text = Index

    End Sub

    Protected Sub ddl_ProgramChild_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        txt_title.Text = ddl_ProgramChild.SelectedItem.ToString
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub LnkArchival_Ent_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkArchival_Ent.Click
        Response.Redirect("FrmArchiveEntry_Lister.aspx")
    End Sub

    Protected Sub bttnMerge_Click(ByVal sender As Object, ByVal e As System.EventArgs)

        lblErr_ProgramInfo.Text = String.Empty

        Dim arr1 As Array = Split(txt_TapeNumber.Text, "#")
        If arr1.Length = 2 Then
            txt_TapeNumber.Text = arr1(1)
        End If

        '''''''''' Get TapeLibraryID '''''''''''''''''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
        '''''''''''''''''''''''''''''''''''''''''''''''
        If LibraryID <> 0 Then
            Dim arr As Array = Split(txt_MergeTapes.Text, "#")
            If arr.Length = 2 Then
                txt_MergeTapes.Text = arr(1)
            End If

            '''''''''' Get TapeLibraryID '''''''''''''''''

            Dim MergeTapeLibraryID As Integer
            Dim objMergeTapeLibraryID As New BusinessFacade.TapeContent()
            objMergeTapeLibraryID.TapeNumber = txt_MergeTapes.Text
            MergeTapeLibraryID = objMergeTapeLibraryID.GerMergeTapeID_byTapeNumber_Ent(objMergeTapeLibraryID.TapeNumber)

            '''''''''''''''''''''''''''''''''''''''''''''''

            Dim col1_merge As DataColumn = New DataColumn("MergeTapeNumberID")
            col1_merge.DataType = System.Type.GetType("System.Int32")
            dt_MergeTapeNumber.Columns.Add("MergeTapeNumberID")

            Dim col2_merge As DataColumn = New DataColumn("MergeTapeNumber")
            col2_merge.DataType = System.Type.GetType("System.String")
            dt_MergeTapeNumber.Columns.Add("MergeTapeNumber")

            If ViewState("MergeTape_Table") Is Nothing Then
            Else
                dt_MergeTapeNumber = ViewState("MergeTape_Table")
            End If

            If MergeTapeLibraryID <> 0 Then
                Dim row_merge As DataRow
                row_merge = dt_MergeTapeNumber.NewRow
                'row_merge.Item("MergeTapeNumberID") = ddl_MergeTapeNo.SelectedValue
                row_merge.Item("MergeTapeNumberID") = MergeTapeLibraryID
                'row_merge.Item("MergeTapeNumber") = ddl_MergeTapeNo.SelectedItem.ToString
                row_merge.Item("MergeTapeNumber") = txt_MergeTapes.Text
                dt_MergeTapeNumber.Rows.Add(row_merge)
                ViewState("MergeTape_Table") = dt_MergeTapeNumber
                BindMergeTapeNumber()
            End If
        Else
            lblErr_ProgramInfo.Text = "Please Select New Tape First !!"
        End If


    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        Calculate()
    End Sub

    Private Sub Calculate()
        Try
            Dim newstarttime As String = txt_starttime1.Text
            Dim newendtime As String = txt_Endtime1.Text


            lblErr.Text = String.Empty

            Dim StartTime As String
            Dim EndTime As String

            If ST1.Text = "" Then
                ST1.Text = "00"
            End If
            If ST2.Text = "" Then
                ST2.Text = "00"
            End If
            If ST3.Text = "" Then
                ST3.Text = "00"
            End If
            If ST4.Text = "" Then
                ST4.Text = "00"
            End If

            If END1.Text = "" Then
                END1.Text = "00"
            End If
            If END2.Text = "" Then
                END2.Text = "00"
            End If
            If END3.Text = "" Then
                END3.Text = "00"
            End If
            If END4.Text = "" Then
                END4.Text = "00"
            End If



            'StartTime = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
            'EndTime = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text


            EndTime = txt_starttime1.Text
            StartTime = txt_Endtime1.Text

            Dim time1 As String
            time1 = StartTime
            Dim str As Array = time1.Split(":")

            Dim time2 As String
            time2 = EndTime
            Dim str2 As Array = time2.Split(":")

            Dim Min1 As Integer
            If str(0) <> "" And str(1) <> "" And str(2) <> "" Then
                Min1 = (str(0) * 60 * 60) + (str(1) * 60) + (str(2))
            End If

            Dim Min2 As Integer
            If str2(0) <> "" And str2(1) <> "" And str2(2) <> "" Then
                Min2 = (str2(0) * 60 * 60) + (str2(1) * 60) + (str2(2))
            End If

            Dim Diff As String = ""
            If Min1 >= Min2 Then
                Dim sec As Integer = Min1 - Min2
                If str(3) < str2(3) Then

                    Dim a As Integer
                    a = str(2)
                    Dim b As Integer
                    b = str2(2)
                    If a = b Then
                        sec = sec - 1
                    End If

                End If

                Diff = Format(Int(sec / 3600), "00") & ":" & Format(Int((sec - (Int(sec / 3600) * 3600)) / 60), "00") & ":" & Format(((sec Mod 60)), "00")


                Dim A1 As String = StartTime
                Dim A2 As String = EndTime
                Dim B1 As String = ""
                Dim B2 As String = ""

                B1 = Left(A1, 2) / 24 + Mid(A1, 4, 2) / (24 * 60) + Mid(A1, 7, 2) / (24 * 60 * 60) + Right(A1, 2) / (24 * 60 * 60 * 24)

                B2 = Left(A2, 2) / 24 + Mid(A2, 4, 2) / (24 * 60) + Mid(A2, 7, 2) / (24 * 60 * 60) + Right(A2, 2) / (24 * 60 * 60 * 24)

                Dim B3 As String = ""
                B3 = B1 - B2


                Dim B4 As String = ""
                B4 = Format((B3 * 24 * 60 * 60 - Int(B3 * 24 * 60 * 60)) * 24, "00")
                If B4 = "24" Then
                    B4 = "00"
                End If

                Dim Convert As String = ""
                Convert = Diff & ":" & B4

                Dim Dur As String
                Dur = Convert

                Dim arr As Array = Split(Dur, ":")

                DUR_1.Text = arr(0).ToString
                DUR_2.Text = arr(1).ToString
                DUR_3.Text = arr(2).ToString
                DUR_4.Text = arr(3).ToString

            Else
                ST1.Text = "00"
                ST2.Text = "00"
                ST3.Text = "00"
                ST4.Text = "00"
                END1.Text = "00"
                END2.Text = "00"
                END3.Text = "00"
                END4.Text = "00"
                DUR_1.Text = "00"
                DUR_2.Text = "00"
                DUR_3.Text = "00"
                DUR_4.Text = "00"

            End If

            txt_StartTime.Text = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
            txt_EndTime.Text = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text
            txt_TimeDuration.Text = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text

            ''***************************************''
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub lnkAddKW_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkAddKW.Click
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('AddEntKeyword.aspx" + "', 'mywindow', 'width=850, height=350' ); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)

    End Sub

    Protected Sub lnkCheckIsEnter_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkCheckIsEnter.Click
        ' MsgBox("Attention !! This Tape already has been Archived !!", MsgBoxStyle.Information, "Alert")
        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        Dim T As String
        Dim objCheck As New BusinessFacade.TapeContent()
        objCheck.TapeNumber = txt_TapeNumber.Text
        T = objCheck.CheckEntTapeArchived(objCheck.TapeNumber)
        If T <> "0" Then
            lblErr.Text = "This Tape already has been Archived !!"

        Else
            lblErr.Text = "This Tape is not Archived Previously !!"
        End If

    End Sub

    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click

        Err_Department.Visible = False
        Err_TapeNumber.Visible = False
        lblErr_MasterEntry.Text = String.Empty
        lblErr_ProgramInfo.Text = String.Empty

        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''****************************************''
        ''********* Get TapeLibraryID ************''
        ''****************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
        '''''''''''''''''''''''''''''''''''''''''''''''

        ''****************************************''
        ''********** Get DepartmentID ************''
        ''****************************************''

        Dim DepartmentID As Integer
        Dim objDeptID As New BusinessFacade.TapeIssuance()
        objDeptID.DepartmentName = txt_Department.Text
        DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserName As String
        Dim objUserName As New BusinessFacade.Employee()
        objUserName.EmployeeID = UserID
        UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

        Dim TapeContentID As Integer

        If LibraryID <> 0 Then
            Dim ObjUser As New BusinessFacade.TapeContent()
            ObjUser.ContentTypeID = ddl_Channel.SelectedValue
            'ObjUser.DepartmentID = ddl_Department.SelectedValue
            ObjUser.DepartmentID = DepartmentID
            ObjUser.isCurrentContent = 0
            ObjUser.ClassificationCode = txt_ClassificationCode.Text
            ObjUser.CallNo = txt_CallNo.Text
            ObjUser.LocationCode = txt_LocationCode.Text
            ObjUser.IsActive = 1
            TapeContentID = ObjUser.SaveRecord_TapeContent()
            txt_TapeContentID.Text = TapeContentID
        End If


        Dim objtest As New BusinessFacade.TapeContent()
        objtest.TapeLibraryID = LibraryID
        objtest.MergeTapeID = dg_Merge.Rows(0).Cells(0).Text
        objtest.TapeContentID = txt_TapeContentID.Text
        objtest.AddTapeContentDetail_NullProgram()

        ''**********************************************''

    End Sub

    Protected Sub dg_keyword_2_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_keyword_2.RowDeleting
        Dim RowId As Integer
        RowId = e.RowIndex

        If dg_keyword_2.Rows(RowId).Cells(5).Text <> "&nbsp;" Then
            Dim ObjDeleteKW As New BusinessFacade.TapeContent()
            ObjDeleteKW.programVsKWID = dg_keyword_2.Rows(RowId).Cells(5).Text
            ObjDeleteKW.Delete_TapeContentEntKeyword()

            Fill_KeyWord_Table_2(txt_TapeContentID.Text)

        ElseIf dg_keyword_2.Rows(RowId).Cells(5).Text = "&nbsp;" Then
            Dt_Keyword_2 = ViewState("KeyWordTable_2")
            Dt_Keyword_2.Rows(RowId).Delete()
            Dt_Keyword_2.AcceptChanges()
            ViewState("KeyWordTable_2") = Dt_Keyword_2
            dg_keyword_2.DataSource = Dt_Keyword_2
            dg_keyword_2.DataBind()
        End If
    End Sub

    Protected Sub dg_TestProgram_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_TestProgram.RowDeleting
        If dg_TestProgram.Rows.Count > 1 Then
            Dim RowId As Integer
            RowId = e.RowIndex

            If dg_TestProgram.Rows(RowId).Cells(10).Text <> "&nbsp;" Then

                Dim J As Integer
                For J = 0 To dg_keyword_2.Rows.Count - 1
                    If dg_keyword_2.Rows(J).Cells(5).Text <> "&nbsp;" And (dg_TestProgram.Rows(RowId).Cells(11).Text = dg_keyword_2.Rows(J).Cells(1).Text) And (dg_TestProgram.Rows(RowId).Cells(2).Text = dg_keyword_2.Rows(J).Cells(9).Text) And (dg_TestProgram.Rows(RowId).Cells(5).Text = dg_keyword_2.Rows(J).Cells(8).Text) Then
                        Dim ObjDeleteKW As New BusinessFacade.TapeContent()
                        ObjDeleteKW.programVsKWID = dg_keyword_2.Rows(J).Cells(5).Text
                        ObjDeleteKW.Delete_TapeContentEntKeyword()
                    End If
                Next


                Dim ObjDeleteProgram As New BusinessFacade.TapeContent()
                ObjDeleteProgram.TapeContentDetailID = dg_TestProgram.Rows(RowId).Cells(10).Text
                ObjDeleteProgram.Delete_TapeContentEntProgram()

                Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                Fill_KeyWord_Table_2(txt_TapeContentID.Text)

            ElseIf dg_TestProgram.Rows(RowId).Cells(10).Text = "&nbsp;" Then

                Dim I As Integer
                For I = 0 To dg_keyword_2.Rows.Count - 1
                    If dg_keyword_2.Rows(I).Cells(5).Text = "&nbsp;" And (dg_TestProgram.Rows(RowId).Cells(11).Text = dg_keyword_2.Rows(I).Cells(1).Text) And (dg_TestProgram.Rows(RowId).Cells(2).Text = dg_keyword_2.Rows(I).Cells(9).Text) And (dg_TestProgram.Rows(RowId).Cells(5).Text = dg_keyword_2.Rows(I).Cells(8).Text) Then
                        Dt_Keyword_2 = ViewState("KeyWordTable_2")
                        Dt_Keyword_2.Rows(I).Delete()
                        Dt_Keyword_2.AcceptChanges()
                        ViewState("KeyWordTable_2") = Dt_Keyword_2
                        dg_keyword_2.DataSource = Dt_Keyword_2
                        dg_keyword_2.DataBind()
                    End If
                Next

                Dim J As Integer
                For J = 0 To dg_keyword_2.Rows.Count - 1
                    If dg_keyword_2.Rows(J).Cells(5).Text <> "&nbsp;" And (dg_TestProgram.Rows(RowId).Cells(11).Text = dg_keyword_2.Rows(J).Cells(1).Text) And (dg_TestProgram.Rows(RowId).Cells(2).Text = dg_keyword_2.Rows(J).Cells(9).Text) And (dg_TestProgram.Rows(RowId).Cells(5).Text = dg_keyword_2.Rows(J).Cells(8).Text) Then
                        Dim ObjDeleteKW As New BusinessFacade.TapeContent()
                        ObjDeleteKW.programVsKWID = dg_keyword_2.Rows(J).Cells(5).Text
                        ObjDeleteKW.Delete_TapeContentEntKeyword()
                    End If
                Next

                dt_Test_ProgramInfo = ViewState("ProgramInfo_Table_2")
                dt_Test_ProgramInfo.Rows(RowId).Delete()
                dt_Test_ProgramInfo.AcceptChanges()
                ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
                dg_TestProgram.DataSource = dt_Test_ProgramInfo
                dg_TestProgram.DataBind()

            End If

        End If

    End Sub

    Protected Sub bttnAddNEwTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAddNEwTape.Click
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('NewTapeNumber.aspx', 'mywindow'); "
        'script = script + "var mywindow = window.open('OSRNewTapeNumber.aspx', 'mywindow'); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)
    End Sub

    Protected Sub bttnFinalSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnFinalSave.Click
        lblErr.Text = String.Empty

        Err_Department.Visible = False
        Err_TapeNumber.Visible = False
        lblErr_MasterEntry.Text = String.Empty
        lblErr_ProgramInfo.Text = String.Empty

        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''****************************************''
        ''********* Get TapeLibraryID ************''
        ''****************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
        '''''''''''''''''''''''''''''''''''''''''''''''

        ''****************************************''
        ''********** Get DepartmentID ************''
        ''****************************************''

        Dim DepartmentID As Integer
        Dim objDeptID As New BusinessFacade.TapeIssuance()
        objDeptID.DepartmentName = txt_Department.Text
        DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserName As String
        Dim objUserName As New BusinessFacade.Employee()
        objUserName.EmployeeID = UserID
        UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)


        If DepartmentID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Department !!"
            Err_Department.Visible = True
        ElseIf LibraryID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Tape Number !!"
            Err_TapeNumber.Visible = True
        Else
            Dim TapeContentID As Integer

            ''******************************************''
            ''********** Tape Content Save *************''
            ''******************************************''
            If txt_TapeContentID.Text = "" Then

                If LibraryID <> 0 Then
                    Dim ObjUser As New BusinessFacade.TapeContent()
                    ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                    ObjUser.DepartmentID = DepartmentID
                    ObjUser.isCurrentContent = 0
                    ObjUser.ClassificationCode = txt_ClassificationCode.Text
                    ObjUser.CallNo = txt_CallNo.Text
                    ObjUser.LocationCode = txt_LocationCode.Text
                    ObjUser.IsActive = 1
                    ObjUser.Copies = ddlCopies.SelectedValue
                    ObjUser.UserID = UserID
                    TapeContentID = ObjUser.SaveRecord_TapeContent()
                    txt_TapeContentID.Text = TapeContentID
                End If

            Else
                If LibraryID <> 0 Then
                    Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                    ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                    ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                    ObjUpdateUser.DepartmentID = DepartmentID
                    ObjUpdateUser.isCurrentContent = 0
                    ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                    ObjUpdateUser.CallNo = txt_CallNo.Text
                    ObjUpdateUser.LocationCode = txt_LocationCode.Text
                    ObjUpdateUser.IsActive = 1
                    ObjUpdateUser.Copies = ddlCopies.SelectedValue
                    ObjUpdateUser.UserID = UserID
                    Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                End If
            End If

            ''***********************************************************''
            ''**************** Tape Content Detail Save *****************''
            ''***********************************************************''

            Dim TapeContentDetailID As Integer
            Dim P As Integer
            For P = 0 To dg_TestProgram.Rows.Count - 1

                If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then
                    ''''''''''''''' Save ''''''''''''''''''''''''''
                    Dim ObjDetail As New BusinessFacade.TapeContent()
                    ObjDetail.TapeContentID = txt_TapeContentID.Text
                    ObjDetail.TapeLibraryID = LibraryID
                    ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                    ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                    ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                    ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                    ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                    ObjDetail.ProducedBy = UserID
                    ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                    ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                    ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                    ObjDetail.NocNo = 1
                    ObjDetail.ItemID = 0
                    ObjDetail.MergeID = 0
                    ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                    ObjDetail.ProducedByName = UserName
                    ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                    ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail()

                    ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                    Dim q As Integer
                    For q = 0 To dg_keyword_2.Rows.Count - 1
                        If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0 Then
                            If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                objtest.TapeContentDetailID = TapeContentDetailID
                                objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                objtest.ProgramChildVskeyword_SaveRecord()
                            End If
                        End If
                    Next

                    ''***************************************************************''
                    ''**************** Tape Content Detail (Update) *****************''
                    ''***************************************************************''
                Else
                    Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                    ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                    ObjUpdateDetail.TapeLibraryID = LibraryID
                    ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                    ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                    ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                    ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                    ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                    ObjUpdateDetail.ProducedBy = UserID
                    ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                    ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                    ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                    ObjUpdateDetail.NocNo = 1
                    ObjUpdateDetail.ItemID = 1
                    ObjUpdateDetail.MergeID = 1
                    ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                    ObjUpdateDetail.ProducedByName = UserName
                    If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                        ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                    Else
                        ObjUpdateDetail.ProductionStatus = 0
                    End If
                    ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                    ObjUpdateDetail.UpdateRecord_TapeContentDetail()

                    Dim z As Integer
                    For z = 0 To dg_keyword_2.Rows.Count - 1
                        If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0 Then
                            If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                objtest.ProgramChildVskeyword_SaveRecord()
                            Else
                                If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                    Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                    ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                    ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                    ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                    ObjUpdateKW.TapeContentDetailID = 1
                                    ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                    ObjUpdateKW.ProgramChildVskeyword_Update()
                                End If
                            End If
                        End If
                    Next
                End If
            Next

            ''***************************************************''
            ''***************** Merge Records *******************''
            ''***************************************************''
            Dim dt As DataTable
            Dim X As Integer
            For X = 0 To dg_Merge.Rows.Count - 1
                Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                Dim ObjTape As New BusinessFacade.TapeContent()
                ObjTape.TapeLibraryID = MergeTapeLibraryID
                dt = ObjTape.GetMergeTapeContentDetailID_Entertainment()
                Dim X2 As Integer
                For X2 = 0 To dt.Rows.Count - 1
                    Dim ObjTape2 As New BusinessFacade.TapeContent()
                    ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                    ObjTape2.TapeContentID = txt_TapeContentID.Text
                    ObjTape2.TapeLibraryID = LibraryID
                    ObjTape2.MergeArchivalEnt()
                Next
            Next

            If dg_Merge.Rows.Count > 0 Then
                Dim Y As Integer
                For Y = 0 To dg_Merge.Rows.Count - 1
                    Dim ObjInsert As New BusinessFacade.TapeContent()
                    ObjInsert.TapeLibraryID = LibraryID
                    ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                    ObjInsert.UserID = UserID
                    ObjInsert.InsertMergeArchivalEnt()
                Next
            End If

            ''***************************************************''

            lblErr.Text = "Record has been Saved Successfully!!"

            '********************************************************'
            '********** Update Tape Status in TapeLibrary ***********'
            '********************************************************'

            If LibraryID <> 0 Then
                Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                objUpdateTapeStatus.TapeLibraryID = LibraryID
                objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
            End If

            ''******************************************************''
            ''************* Search Engine Save Record **************''
            ''******************************************************''

            If txt_TapeContentID.Text <> "" Then

                Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                ObjSearchEngine2.TapeLibraryID = LibraryID
                ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()

            End If

            '************************ End ***************************'

            ''******************* Fill Grids ************************''
            Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
            Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
            Me.Fill_MergeInformation(txt_TapeContentID.Text)

            ''****************** Clear Merge Grids ******************''
            dg_Merge.DataSource = Nothing
            dg_Merge.DataBind()
            dt_MergeTapeNumber = Nothing
            ViewState("MergeTape_Table") = Nothing

        End If

    End Sub

    Protected Sub bttnClearProgram_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClearProgram.Click
        Clear()
    End Sub

    Private Sub Clear()
        Try
            Clrscr_ProgramInfo()
            '  txt_ProgramChild.Text = String.Empty
            KW1.Text = String.Empty
            KW2.Text = String.Empty
            KW3.Text = String.Empty
            KT1.Text = String.Empty
            KT2.Text = String.Empty
            KT3.Text = String.Empty
            '        lstKeyType.SelectedIndex = -1
            '       lstKeyword.SelectedIndex = -1
            lblErr.Text = String.Empty
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        ''********************************************************''
        ''*************** Check Tape Availability ****************''
        ''********************************************************''

        Dim objCheckTape As New BusinessFacade.TapeContent()
        Dim Action As String = objCheckTape.CheckTapeAvailability(txt_TapeNumber.Text)
        If Action = "Not Allow" Then
            lblErr.Text = "Attention: This tape is not returned as Blank."
            Exit Sub
        End If

        ''********************************************************''


        If dgTapeNumber.Rows.Count > 0 Then
            Dim TapesCount As Integer
            For TapesCount = 0 To dgTapeNumber.Rows.Count - 1
                txt_TapeNumber.Text = dgTapeNumber.Rows(TapesCount).Cells(0).Text
                BulkTapesArchival()
            Next
        Else

            Dim Trans As SqlTransaction
            Dim Conn As New SqlConnection

            Try

                Dim T As String
                If txt_TapeContentID.Text = "" Then
                    Dim arr_Check As Array = Split(txt_TapeNumber.Text, "#")
                    If arr_Check.Length = 2 Then
                        txt_TapeNumber.Text = arr_Check(1)
                    End If

                    Dim objCheck As New BusinessFacade.TapeContent()
                    objCheck.TapeNumber = txt_TapeNumber.Text
                    T = objCheck.CheckEntTapeArchived(objCheck.TapeNumber)
                Else
                    T = 0
                End If


                lblErr.Text = String.Empty
                Err_Department.Visible = False
                Err_TapeNumber.Visible = False
                lblErr_MasterEntry.Text = String.Empty
                lblErr_ProgramInfo.Text = String.Empty

                ''****************************************''

                Dim arr As Array = Split(txt_TapeNumber.Text, "#")
                If arr.Length = 2 Then
                    txt_TapeNumber.Text = arr(1)
                End If
                Dim dtibraryID As Data.DataTable = Nothing
                Dim objLib As New BusinessFacade.TapeIssuance()
                objLib.TapeNumber = txt_TapeNumber.Text
                dtibraryID = objLib.TapeIssuance_GetLibraryID_datatable(objLib.TapeNumber)

                ''**************************************************************************''
                ''*************************** Base Station Validation **********************''
                ''**************************************************************************''

                Dim RT As Integer
                For RT = 0 To dtibraryID.Rows.Count - 1
                    Dim objValidation As New BusinessFacade.NewTapeNumber()
                    objValidation.TapeLibraryID = CInt(dtibraryID.Rows(RT).Item(0).ToString)
                    Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID <> CokieBaseStationID Then
                        lblErr.Text = "You are not allowed to Archive this Tape!!"
                        Exit Sub
                    End If
                Next

                ''**************************************************************************''
                ''*********************************  END  **********************************''
                ''**************************************************************************''

                If txt_TapeNumber.Text <> "" Then

                End If

                ''****************************************''
                ''********** Get DepartmentID ************''
                ''****************************************''

                Dim DepartmentID As Integer
                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_Department.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


                ''****************************************''
                ''************ Get User ID ***************''
                ''****************************************''

                Dim UserID As Integer
                Dim objUserID As New BusinessFacade.Employee()
                objUserID.SM_LoginID = lbl_UserName.Text
                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                ''****************************************''
                ''************ Get User ID ***************''
                ''****************************************''

                Dim UserName As String
                Dim objUserName As New BusinessFacade.Employee()
                objUserName.EmployeeID = UserID
                UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

                If (dtibraryID.Rows(0).Item(0).ToString = 0) Then
                    lblErr.Text = "Please Select Tape Number !"
                    Exit Sub
                ElseIf DepartmentID = 0 Then
                    lblErr.Text = "Please Select Department !"
                    Exit Sub
                ElseIf T <> "0" Then
                    lblErr.Text = "This Tape already has been Archived !!"
                    Exit Sub
                Else
                    If dtibraryID.Rows.Count = 1 Then

                        ''****************************************''
                        ''********* Get TapeLibraryID ************''
                        ''****************************************''

                        Dim LibraryID As Integer
                        Dim objLibID As New BusinessFacade.TapeIssuance()
                        objLibID.TapeNumber = txt_TapeNumber.Text
                        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
                        '''''''''''''''''''''''''''''''''''''''''''''''


                        If DepartmentID = 0 Then
                            lblErr_MasterEntry.Text = "Please Select Department !!"
                            Err_Department.Visible = True
                            Exit Sub
                        ElseIf LibraryID = 0 Then
                            lblErr_MasterEntry.Text = "Please Select Tape Number !!"
                            Err_TapeNumber.Visible = True
                            Exit Sub
                        Else
                            Dim TapeContentID As Integer

                            Conn.ConnectionString = ConfigurationManager.AppSettings("ConnectionString")
                            Conn.Open()
                            Trans = Conn.BeginTransaction()
                            Try
                                ''******************************************''
                                ''********** Tape Content Save *************''
                                ''******************************************''
                                If txt_TapeContentID.Text = "" Then

                                    If LibraryID <> 0 Then
                                        Dim ObjUser As New BusinessFacade.TapeContent()
                                        ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                                        ObjUser.DepartmentID = DepartmentID
                                        ObjUser.isCurrentContent = 0
                                        ObjUser.ClassificationCode = txt_ClassificationCode.Text
                                        ObjUser.CallNo = txt_CallNo.Text
                                        ObjUser.LocationCode = txt_LocationCode.Text
                                        ObjUser.IsActive = 1
                                        ObjUser.Copies = ddlCopies.SelectedValue
                                        ObjUser.UserID = UserID
                                        ObjUser.LowResFileName = txtLowResFileName.Text
                                        ObjUser.HighResFileName = txtHighResFileName.Text
                                        ObjUser.FilePath = txtFiePath.Text
                                        TapeContentID = ObjUser.SaveRecord_TapeContent_Trans(Trans)
                                        txt_TapeContentID.Text = TapeContentID
                                    End If

                                Else
                                    If LibraryID <> 0 Then
                                        Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                                        ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                                        ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                                        ObjUpdateUser.DepartmentID = DepartmentID
                                        ObjUpdateUser.isCurrentContent = 0
                                        ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                                        ObjUpdateUser.CallNo = txt_CallNo.Text
                                        ObjUpdateUser.LocationCode = txt_LocationCode.Text
                                        ObjUpdateUser.IsActive = 1
                                        ObjUpdateUser.Copies = ddlCopies.SelectedValue
                                        ObjUpdateUser.UserID = UserID
                                        ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                                        ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                                        ObjUpdateUser.FilePath = txtFiePath.Text
                                        'Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                                        Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent_Trans(Trans)
                                    End If
                                End If

                                ''***********************************************************''
                                ''**************** Tape Content Detail Save *****************''
                                ''***********************************************************''

                                Dim TapeContentDetailID As Integer
                                Dim P As Integer
                                For P = 0 To dg_TestProgram.Rows.Count - 1

                                    If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then
                                        ''''''''''''''' Save ''''''''''''''''''''''''''
                                        Dim ObjDetail As New BusinessFacade.TapeContent()
                                        ObjDetail.TapeContentID = txt_TapeContentID.Text
                                        ObjDetail.TapeLibraryID = LibraryID
                                        ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                        ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                        ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                        ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                        ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                        ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                        ObjDetail.ProducedBy = UserID
                                        ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                        ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                        ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                        ObjDetail.NocNo = 1
                                        ObjDetail.ItemID = 0
                                        ObjDetail.MergeID = 0
                                        ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                        ObjDetail.ProducedByName = UserName
                                        ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                        ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                        ObjDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                        ObjDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                        TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_Trans(Trans)

                                        ''---------------------------------------------------------------------------------------------------------------------------------------------''
                                        ' ''***************************************************''
                                        ' ''**** update Tape Slug for Urdu Script Insertion ***''
                                        ' ''***************************************************''
                                        txt_dgProgramInfo_Index.Text = P
                                        UpdateTapeContentDetailID(TapeContentDetailID)
                                        ''---------------------------------------------------------------------------------------------------------------------------------------------''

                                        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                                        Dim q As Integer
                                        For q = 0 To dg_keyword_2.Rows.Count - 1
                                            'If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0 Then
                                            If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(q).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(q).Cells(9).Text) Then
                                                If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                    objtest.TapeContentDetailID = TapeContentDetailID
                                                    objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                                    objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                                    objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                                    'objtest.ProgramChildVskeyword_SaveRecord()
                                                    objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                End If
                                            End If
                                        Next

                                        ''***************************************************************''
                                        ''**************** Tape Content Detail (Update) *****************''
                                        ''***************************************************************''
                                    Else
                                        Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                                        ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                                        ObjUpdateDetail.TapeLibraryID = LibraryID
                                        ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                        ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                        ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                        ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                        ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                        ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                        ObjUpdateDetail.ProducedBy = UserID
                                        ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                        ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                        ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                        ObjUpdateDetail.NocNo = 1
                                        ObjUpdateDetail.ItemID = 1
                                        ObjUpdateDetail.MergeID = 1
                                        ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                        ObjUpdateDetail.ProducedByName = UserName
                                        If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                                            ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                        Else
                                            ObjUpdateDetail.ProductionStatus = 0
                                        End If
                                        ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                        ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                        ObjUpdateDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                        ObjUpdateDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                        ObjUpdateDetail.UpdateRecord_TapeContentDetail_Trans(Trans)


                                        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''

                                        Dim z As Integer
                                        For z = 0 To dg_keyword_2.Rows.Count - 1
                                            If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(z).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(z).Cells(9).Text) Then
                                                If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                    objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                                    objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                    objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                    objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                    'objtest.ProgramChildVskeyword_SaveRecord()
                                                    objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                Else
                                                    If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                                        Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                                        ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                                        ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                        ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                        ObjUpdateKW.TapeContentDetailID = 1
                                                        ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                        'ObjUpdateKW.ProgramChildVskeyword_Update()
                                                        ObjUpdateKW.ProgramChildVskeyword_Update_Trans(Trans)
                                                    End If
                                                End If
                                            End If
                                        Next
                                    End If
                                Next

                                ''***************************************************''
                                ''***************** Merge Records *******************''
                                ''***************************************************''
                                Dim dt As DataTable
                                Dim X As Integer
                                For X = 0 To dg_Merge.Rows.Count - 1
                                    Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                                    Dim ObjTape As New BusinessFacade.TapeContent()
                                    ObjTape.TapeLibraryID = MergeTapeLibraryID
                                    dt = ObjTape.GetMergeTapeContentDetailID_Entertainment()
                                    Dim X2 As Integer
                                    For X2 = 0 To dt.Rows.Count - 1
                                        Dim ObjTape2 As New BusinessFacade.TapeContent()
                                        ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                                        ObjTape2.TapeContentID = txt_TapeContentID.Text
                                        ObjTape2.TapeLibraryID = LibraryID
                                        'ObjTape2.MergeArchivalEnt()
                                        ObjTape2.MergeArchivalEnt_Trans(Trans)
                                    Next
                                Next

                                If dg_Merge.Rows.Count > 0 Then
                                    Dim Y As Integer
                                    For Y = 0 To dg_Merge.Rows.Count - 1
                                        Dim ObjInsert As New BusinessFacade.TapeContent()
                                        ObjInsert.TapeLibraryID = LibraryID
                                        ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                                        ObjInsert.UserID = UserID
                                        'ObjInsert.InsertMergeArchivalEnt()
                                        ObjInsert.InsertMergeArchivalEnt_Trans(Trans)
                                    Next
                                End If

                                ''***************************************************''

                                lblErr.Text = "Record has been Saved Successfully!!"

                                '********************************************************'
                                '********** Update Tape Status in TapeLibrary ***********'
                                '********************************************************'

                                If LibraryID <> 0 Then
                                    Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                                    objUpdateTapeStatus.TapeLibraryID = LibraryID
                                    'objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                                    objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus_Trans(Trans)
                                End If

                                ''******************************************************''
                                ''************* Search Engine Save Record **************''
                                ''******************************************************''


                                Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                                ObjSearchEngine2.TapeLibraryID = LibraryID
                                'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()
                                ' ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2_Trans(Trans) 'Orignal
                                ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord3(Trans)
                                'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord_New(Trans)



                                '************************ End ***************************'

                                '*********************Transaction Commit*****************************
                                lblErr.Text = "Record has been Saved Successfully!!"
                                Trans.Commit()
                                Conn.Close()

                            Catch ex As Exception
                                Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
                                Me.lblErr.Visible = True
                                Trans.Rollback()
                                Conn.Close()
                                Exit Sub
                            End Try
                            '*********************************************************************

                            Dim N As Integer
                            Dim IsUrduScript As Integer = 0
                            For N = 0 To dg_TestProgram.Rows.Count - 1
                                If dg_TestProgram.Rows(N).Cells(14).Text <> "N/A" Then
                                    Dim Con As System.Data.SqlClient.SqlConnection
                                    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                                    Dim cmd As New System.Data.SqlClient.SqlCommand
                                    cmd.CommandType = Data.CommandType.StoredProcedure
                                    cmd.CommandText = "Insert_UrduScript_Entertainment"
                                    cmd.Connection = Con

                                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeContentDetailID", Data.SqlDbType.Int)
                                    p1.Value = dg_TestProgram.Rows(N).Cells(10).Text

                                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                    p2.Value = dg_TestProgram.Rows(N).Cells(14).Text

                                    If Con.State = ConnectionState.Closed Then
                                        Con.Open()
                                    End If
                                    cmd.CommandTimeout = 0
                                    cmd.Connection = Con
                                    cmd.ExecuteNonQuery()
                                    cmd.Connection.Close()
                                    IsUrduScript = IsUrduScript + 1

                                End If
                            Next

                            ''******************* Fill Grids ************************''
                            Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                            Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                            Me.Fill_MergeInformation(txt_TapeContentID.Text)


                            'If IsUrduScript <> 0 Then
                            ''******************************************''
                            ''**** update Tape Slug for SearchEngine ***''
                            ''******************************************''

                            Dim O1 As Integer
                            For O1 = 0 To dg_TestProgram.Rows.Count - 1
                                If dg_TestProgram.Rows(O1).Cells(14).Text <> "N/A" Then
                                    Dim Con As System.Data.SqlClient.SqlConnection
                                    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                                    Dim cmd As New System.Data.SqlClient.SqlCommand
                                    cmd.CommandType = Data.CommandType.StoredProcedure
                                    cmd.CommandText = "Insert_UrduScript_SearchEngineEntertainment"
                                    cmd.Connection = Con

                                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeContentDetailID", Data.SqlDbType.Int)
                                    p1.Value = dg_TestProgram.Rows(O1).Cells(10).Text

                                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                    p2.Value = dg_TestProgram.Rows(O1).Cells(14).Text

                                    If Con.State = ConnectionState.Closed Then
                                        Con.Open()
                                    End If
                                    cmd.CommandTimeout = 0
                                    cmd.Connection = Con
                                    cmd.ExecuteNonQuery()
                                    cmd.Connection.Close()
                                End If
                            Next
                            ' End If

                            ''******************* Fill Grids ************************''
                            'Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                            'Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                            'Me.Fill_MergeInformation(txt_TapeContentID.Text)

                            ''****************** Clear Merge Grids ******************''
                            dg_Merge.DataSource = Nothing
                            dg_Merge.DataBind()
                            dt_MergeTapeNumber = Nothing
                            ViewState("MergeTape_Table") = Nothing

                        End If

                        ''---------------------------------------------------------------------------------------''

                    ElseIf dtibraryID.Rows.Count = 2 Then
                        Conn.ConnectionString = ConfigurationManager.AppSettings("ConnectionString")
                        Conn.Open()
                        Trans = Conn.BeginTransaction()
                        Try
                            Dim s As Integer
                            For s = 0 To dtibraryID.Rows.Count - 1
                                If DepartmentID = 0 Then
                                    lblErr_MasterEntry.Text = "Please Select Department !!"
                                    Err_Department.Visible = True
                                    Trans.Rollback()
                                    Conn.Close()
                                    Exit Sub
                                Else
                                    Dim TapeContentID As Integer

                                    ''******************************************''
                                    ''********** Tape Content Save *************''
                                    ''******************************************''

                                    If txt_TapeContentID.Text = "" Then
                                        Dim ObjUser As New BusinessFacade.TapeContent()
                                        ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                                        ObjUser.DepartmentID = DepartmentID
                                        ObjUser.isCurrentContent = 0
                                        ObjUser.ClassificationCode = txt_ClassificationCode.Text
                                        ObjUser.CallNo = txt_CallNo.Text
                                        ObjUser.LocationCode = txt_LocationCode.Text
                                        ObjUser.IsActive = 1
                                        ObjUser.Copies = ddlCopies.SelectedItem.Text
                                        ObjUser.UserID = UserID
                                        ObjUser.LowResFileName = txtLowResFileName.Text
                                        ObjUser.HighResFileName = txtHighResFileName.Text
                                        ObjUser.FilePath = txtFiePath.Text
                                        'TapeContentID = ObjUser.SaveRecord_TapeContent()
                                        TapeContentID = ObjUser.SaveRecord_TapeContent_Trans(Trans)
                                        txt_TapeContentID.Text = TapeContentID
                                    Else
                                        Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                                        ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                                        ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                                        ObjUpdateUser.DepartmentID = DepartmentID
                                        ObjUpdateUser.isCurrentContent = 0
                                        ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                                        ObjUpdateUser.CallNo = txt_CallNo.Text
                                        ObjUpdateUser.LocationCode = txt_LocationCode.Text
                                        ObjUpdateUser.IsActive = 1
                                        ObjUpdateUser.Copies = ddlCopies.SelectedItem.Text
                                        ObjUpdateUser.UserID = UserID
                                        ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                                        ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                                        ObjUpdateUser.FilePath = txtFiePath.Text
                                        'Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                                        Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent_Trans(Trans)
                                    End If

                                    ''***********************************************************''
                                    ''**************** Tape Content Detail Save *****************''
                                    ''***********************************************************''

                                    Dim TapeContentDetailID As Integer
                                    Dim P As Integer
                                    For P = 0 To dg_TestProgram.Rows.Count - 1

                                        If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then

                                            ''''''''''''''' Save ''''''''''''''''''''''''''

                                            Dim ObjDetail As New BusinessFacade.TapeContent()
                                            ObjDetail.TapeContentID = txt_TapeContentID.Text
                                            ObjDetail.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                            ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                            ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                            ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                            ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                            ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                            ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                            ObjDetail.ProducedBy = UserID
                                            ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                            ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                            ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                            ObjDetail.NocNo = 1
                                            ObjDetail.ItemID = 0
                                            ObjDetail.MergeID = 0
                                            ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                            ObjDetail.ProducedByName = UserName
                                            ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                            ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                            ObjDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                            ObjDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                            'TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail()
                                            TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_Trans(Trans)

                                            ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                                            Dim q As Integer
                                            For q = 0 To dg_keyword_2.Rows.Count - 1
                                                If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(q).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(q).Cells(9).Text) Then
                                                    If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                                        Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                        objtest.TapeContentDetailID = TapeContentDetailID
                                                        objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                                        objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                                        objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                                        'objtest.ProgramChildVskeyword_SaveRecord()
                                                        objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                    End If
                                                End If
                                            Next

                                            ''***************************************************************''
                                            ''**************** Tape Content Detail (Update) *****************''
                                            ''***************************************************************''
                                        Else
                                            Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                                            ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                                            ObjUpdateDetail.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                            ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                            ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                            ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                            ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                            ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                            ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                            ObjUpdateDetail.ProducedBy = UserID
                                            ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                            ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                            ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                            ObjUpdateDetail.NocNo = 1
                                            ObjUpdateDetail.ItemID = 1
                                            ObjUpdateDetail.MergeID = 1
                                            ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                            ObjUpdateDetail.ProducedByName = UserName
                                            If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                                                ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                            Else
                                                ObjUpdateDetail.ProductionStatus = 0
                                            End If
                                            ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                            ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                            ObjUpdateDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                            ObjUpdateDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                            'ObjUpdateDetail.UpdateRecord_TapeContentDetail()
                                            ObjUpdateDetail.UpdateRecord_TapeContentDetail_Trans(Trans)

                                            Dim z As Integer
                                            For z = 0 To dg_keyword_2.Rows.Count - 1
                                                If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(z).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(z).Cells(9).Text) Then
                                                    If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                                        Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                        objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                                        objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                        objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                        objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                        'objtest.ProgramChildVskeyword_SaveRecord()
                                                        objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                    Else
                                                        If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                                            Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                                            ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                                            ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                            ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                            ObjUpdateKW.TapeContentDetailID = 1
                                                            ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                            'ObjUpdateKW.ProgramChildVskeyword_Update()
                                                            ObjUpdateKW.ProgramChildVskeyword_Update_Trans(Trans)
                                                        End If
                                                    End If
                                                End If
                                            Next
                                        End If
                                    Next

                                    ''***************************************************''
                                    ''***************** Merge Records *******************''
                                    ''***************************************************''

                                    Dim dt As DataTable
                                    Dim X As Integer
                                    For X = 0 To dg_Merge.Rows.Count - 1
                                        Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                                        Dim ObjTape As New BusinessFacade.TapeContent()
                                        ObjTape.TapeLibraryID = MergeTapeLibraryID
                                        dt = ObjTape.GetMergeTapeContentDetailID_Entertainment()
                                        Dim X2 As Integer
                                        For X2 = 0 To dt.Rows.Count - 1
                                            Dim ObjTape2 As New BusinessFacade.TapeContent()
                                            ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                                            ObjTape2.TapeContentID = txt_TapeContentID.Text
                                            ObjTape2.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                            'ObjTape2.MergeArchivalEnt()
                                            ObjTape2.MergeArchivalEnt_Trans(Trans)
                                        Next
                                    Next

                                    If dg_Merge.Rows.Count > 0 Then
                                        Dim Y As Integer
                                        For Y = 0 To dg_Merge.Rows.Count - 1
                                            Dim ObjInsert As New BusinessFacade.TapeContent()
                                            ObjInsert.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                            ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                                            ObjInsert.UserID = UserID
                                            'ObjInsert.InsertMergeArchivalEnt()
                                            ObjInsert.InsertMergeArchivalEnt_Trans(Trans)
                                        Next
                                    End If

                                    ''***************************************************''

                                    lblErr.Text = "Record has been Saved Successfully!!"

                                    '********************************************************'
                                    '********** Update Tape Status in TapeLibrary ***********'
                                    '********************************************************'

                                    If dtibraryID.Rows(s).Item(0).ToString <> 0 Then
                                        Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                                        objUpdateTapeStatus.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        'objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                                        objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus_Trans(Trans)
                                    End If

                                    If s = 0 Then

                                        ''******************************************************''
                                        ''************* Search Engine Save Record **************''
                                        ''******************************************************''

                                        Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                                        ObjSearchEngine2.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()
                                        ' ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2_Trans(Trans) [Origanl]
                                        ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord3(Trans)

                                        '************************ End ***************************'

                                        If lblQueryString.Text = "" Then
                                            txt_TapeContentID.Text = String.Empty
                                        End If

                                    End If

                                    If lblQueryString.Text <> "" Then
                                        s = s + 1
                                    End If
                                End If
                            Next

                            '*********************Transaction Commit*****************************
                            lblErr.Text = "Record has been Saved Successfully!!"
                            Trans.Commit()
                            Conn.Close()
                            '*********************************************************************
                        Catch ex As Exception
                            Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
                            Me.lblErr.Visible = True
                            Trans.Rollback()
                            Conn.Close()
                            Exit Sub
                        End Try

                        '******************* Fill Grids ************************''
                        Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                        Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                        Me.Fill_MergeInformation(txt_TapeContentID.Text)

                        '****************** Clear Merge Grids ******************''
                        dg_Merge.DataSource = Nothing
                        dg_Merge.DataBind()
                        dt_MergeTapeNumber = Nothing
                        ViewState("MergeTape_Table") = Nothing


                    End If
                End If

            Catch ex As Exception
                Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
                Me.lblErr.Visible = True
                Exit Sub
            End Try

        End If


        ''************************************************************''
    End Sub

    Private Sub UrduScriptInsertion()
        Dim N As Integer
        For N = 0 To dg_TestProgram.Rows.Count - 1
            If dg_TestProgram.Rows(N).Cells(14).Text <> "N/A" Then
                Dim Con As System.Data.SqlClient.SqlConnection
                Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                Con = New System.Data.SqlClient.SqlConnection(connStr)
                Dim cmd As New System.Data.SqlClient.SqlCommand
                cmd.CommandType = Data.CommandType.StoredProcedure
                cmd.CommandText = "Insert_UrduScript_Entertainment"
                cmd.Connection = Con

                Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeContentDetailID", Data.SqlDbType.Int)
                p1.Value = dg_TestProgram.Rows(N).Cells(10).Text

                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                p2.Value = dg_TestProgram.Rows(N).Cells(14).Text

                If Con.State = ConnectionState.Closed Then
                    Con.Open()
                End If
                cmd.CommandTimeout = 0
                cmd.Connection = Con
                cmd.ExecuteNonQuery()
                cmd.Connection.Close()
            End If
        Next

    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        'If txtBttnProgram.Text = "0" Then
        '    Dim script As String
        '    script = "<script language='javascript' type='text/javascript'>"
        '    script = script + "window.onbeforeunload = function() {"
        '    script = script + "return ""Closing the page now may result in data loss."";"
        '    script = script + "}</script>"
        '    Page.RegisterClientScriptBlock("test", script)
        'End If
    End Sub

    Protected Sub lnkBulkTapes_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkBulkTapes.Click
        lblErr_MasterEntry.Text = String.Empty

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''**********************************************''
        ''************* Get TapeLibraryID **************''
        ''**********************************************''

        Dim dtibraryID As Data.DataTable = Nothing

        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        dtibraryID = objLibID.TapeIssuance_GetLibraryID_datatable(objLibID.TapeNumber)

        ''**********************************************''

        'If Not dtibraryID Is Nothing Then
        If dtibraryID.Rows(0)(0).ToString <> 0 Then

            Dim column1 As DataColumn = New DataColumn("TapeNumber")
            column1.DataType = System.Type.GetType("System.String")
            dt_BulkTapeNumber.Columns.Add("TapeNumber")

            If ViewState("BulkTapeNumber") Is Nothing Then
            Else
                dt_BulkTapeNumber = ViewState("BulkTapeNumber")
            End If


            Dim row As DataRow
            row = dt_BulkTapeNumber.NewRow
            row.Item("TapeNumber") = txt_TapeNumber.Text
            dt_BulkTapeNumber.Rows.Add(row)
            ViewState("BulkTapeNumber") = dt_BulkTapeNumber

            dgTapeNumber.DataSource = dt_BulkTapeNumber
            dgTapeNumber.DataBind()

        Else
            lblErr_MasterEntry.Text = "Attention : Please Enter Correct Tape Number!! "
        End If

    End Sub

    Private Sub BulkTapesArchival()
        Dim Trans As SqlTransaction
        Dim Conn As New SqlConnection

        Try

            Dim T As String
            If txt_TapeContentID.Text = "" Then
                Dim arr_Check As Array = Split(txt_TapeNumber.Text, "#")
                If arr_Check.Length = 2 Then
                    txt_TapeNumber.Text = arr_Check(1)
                End If

                Dim objCheck As New BusinessFacade.TapeContent()
                objCheck.TapeNumber = txt_TapeNumber.Text
                T = objCheck.CheckEntTapeArchived(objCheck.TapeNumber)
            Else
                T = 0
            End If


            lblErr.Text = String.Empty
            Err_Department.Visible = False
            Err_TapeNumber.Visible = False
            lblErr_MasterEntry.Text = String.Empty
            lblErr_ProgramInfo.Text = String.Empty

            ''****************************************''

            Dim arr As Array = Split(txt_TapeNumber.Text, "#")
            If arr.Length = 2 Then
                txt_TapeNumber.Text = arr(1)
            End If
            Dim dtibraryID As Data.DataTable = Nothing
            Dim objLib As New BusinessFacade.TapeIssuance()
            objLib.TapeNumber = txt_TapeNumber.Text
            dtibraryID = objLib.TapeIssuance_GetLibraryID_datatable(objLib.TapeNumber)

            ''**************************************************************************''
            ''*************************** Base Station Validation **********************''
            ''**************************************************************************''

            Dim RT As Integer
            For RT = 0 To dtibraryID.Rows.Count - 1
                Dim objValidation As New BusinessFacade.NewTapeNumber()
                objValidation.TapeLibraryID = CInt(dtibraryID.Rows(RT).Item(0).ToString)
                Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                If BaseStationID <> CokieBaseStationID Then
                    lblErr.Text = "You are not allowed to Archive this Tape!!"
                    Exit Sub
                End If
            Next

            ''**************************************************************************''
            ''*********************************  END  **********************************''
            ''**************************************************************************''

            If txt_TapeNumber.Text <> "" Then

            End If

            ''****************************************''
            ''********** Get DepartmentID ************''
            ''****************************************''

            Dim DepartmentID As Integer
            Dim objDeptID As New BusinessFacade.TapeIssuance()
            objDeptID.DepartmentName = txt_Department.Text
            DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserName As String
            Dim objUserName As New BusinessFacade.Employee()
            objUserName.EmployeeID = UserID
            UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

            If (dtibraryID.Rows(0).Item(0).ToString = 0) Then
                lblErr.Text = "Please Select Tape Number !"
                Exit Sub
            ElseIf DepartmentID = 0 Then
                lblErr.Text = "Please Select Department !"
                Exit Sub
            ElseIf T <> "0" Then
                lblErr.Text = "This Tape already has been Archived !!"
                Exit Sub
            Else
                If dtibraryID.Rows.Count = 1 Then

                    ''****************************************''
                    ''********* Get TapeLibraryID ************''
                    ''****************************************''

                    Dim LibraryID As Integer
                    Dim objLibID As New BusinessFacade.TapeIssuance()
                    objLibID.TapeNumber = txt_TapeNumber.Text
                    LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
                    '''''''''''''''''''''''''''''''''''''''''''''''


                    If DepartmentID = 0 Then
                        lblErr_MasterEntry.Text = "Please Select Department !!"
                        Err_Department.Visible = True
                        Exit Sub
                    ElseIf LibraryID = 0 Then
                        lblErr_MasterEntry.Text = "Please Select Tape Number !!"
                        Err_TapeNumber.Visible = True
                        Exit Sub
                    Else
                        Dim TapeContentID As Integer

                        Conn.ConnectionString = ConfigurationManager.AppSettings("ConnectionString")
                        Conn.Open()
                        Trans = Conn.BeginTransaction()
                        Try
                            ''******************************************''
                            ''********** Tape Content Save *************''
                            ''******************************************''
                            If txt_TapeContentID.Text = "" Then

                                If LibraryID <> 0 Then
                                    Dim ObjUser As New BusinessFacade.TapeContent()
                                    ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                                    ObjUser.DepartmentID = DepartmentID
                                    ObjUser.isCurrentContent = 0
                                    ObjUser.ClassificationCode = txt_ClassificationCode.Text
                                    ObjUser.CallNo = txt_CallNo.Text
                                    ObjUser.LocationCode = txt_LocationCode.Text
                                    ObjUser.IsActive = 1
                                    ObjUser.Copies = ddlCopies.SelectedValue
                                    ObjUser.UserID = UserID
                                    ObjUser.LowResFileName = txtLowResFileName.Text
                                    ObjUser.HighResFileName = txtHighResFileName.Text
                                    ObjUser.FilePath = txtFiePath.Text
                                    TapeContentID = ObjUser.SaveRecord_TapeContent_Trans(Trans)
                                    txt_TapeContentID.Text = TapeContentID
                                End If

                            Else
                                If LibraryID <> 0 Then
                                    Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                                    ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                                    ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                                    ObjUpdateUser.DepartmentID = DepartmentID
                                    ObjUpdateUser.isCurrentContent = 0
                                    ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                                    ObjUpdateUser.CallNo = txt_CallNo.Text
                                    ObjUpdateUser.LocationCode = txt_LocationCode.Text
                                    ObjUpdateUser.IsActive = 1
                                    ObjUpdateUser.Copies = ddlCopies.SelectedValue
                                    ObjUpdateUser.UserID = UserID
                                    ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                                    ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                                    ObjUpdateUser.FilePath = txtFiePath.Text
                                    'Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                                    Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent_Trans(Trans)
                                End If
                            End If

                            ''***********************************************************''
                            ''**************** Tape Content Detail Save *****************''
                            ''***********************************************************''

                            Dim TapeContentDetailID As Integer
                            Dim P As Integer
                            For P = 0 To dg_TestProgram.Rows.Count - 1

                                If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then
                                    ''''''''''''''' Save ''''''''''''''''''''''''''
                                    Dim ObjDetail As New BusinessFacade.TapeContent()
                                    ObjDetail.TapeContentID = txt_TapeContentID.Text
                                    ObjDetail.TapeLibraryID = LibraryID
                                    ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                    ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                    ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                    ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                    ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                    ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                    ObjDetail.ProducedBy = UserID
                                    ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                    ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                    ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                    ObjDetail.NocNo = 1
                                    ObjDetail.ItemID = 0
                                    ObjDetail.MergeID = 0
                                    ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                    ObjDetail.ProducedByName = UserName
                                    ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                    ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                    ObjDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                    ObjDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                    TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_Trans(Trans)

                                    ''---------------------------------------------------------------------------------------------------------------------------------------------''
                                    ' ''***************************************************''
                                    ' ''**** update Tape Slug for Urdu Script Insertion ***''
                                    ' ''***************************************************''
                                    txt_dgProgramInfo_Index.Text = P
                                    UpdateTapeContentDetailID(TapeContentDetailID)
                                    ''---------------------------------------------------------------------------------------------------------------------------------------------''


                                    ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                                    Dim q As Integer
                                    For q = 0 To dg_keyword_2.Rows.Count - 1
                                        'If dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0 Then
                                        If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(q).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(q).Cells(9).Text) Then
                                            If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                                Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                objtest.TapeContentDetailID = TapeContentDetailID
                                                objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                                objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                                objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                                'objtest.ProgramChildVskeyword_SaveRecord()
                                                objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                            End If
                                        End If
                                    Next

                                    ''***************************************************************''
                                    ''**************** Tape Content Detail (Update) *****************''
                                    ''***************************************************************''
                                Else
                                    Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                                    ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                                    ObjUpdateDetail.TapeLibraryID = LibraryID
                                    ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                    ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                    ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                    ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                    ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                    ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                    ObjUpdateDetail.ProducedBy = UserID
                                    ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                    ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                    ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                    ObjUpdateDetail.NocNo = 1
                                    ObjUpdateDetail.ItemID = 1
                                    ObjUpdateDetail.MergeID = 1
                                    ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                    ObjUpdateDetail.ProducedByName = UserName
                                    If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                                        ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                    Else
                                        ObjUpdateDetail.ProductionStatus = 0
                                    End If
                                    ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                    ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                    ObjUpdateDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                    ObjUpdateDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text

                                    'ObjUpdateDetail.UpdateRecord_TapeContentDetail()
                                    ObjUpdateDetail.UpdateRecord_TapeContentDetail_Trans(Trans)

                                    Dim z As Integer
                                    For z = 0 To dg_keyword_2.Rows.Count - 1
                                        If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(z).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(z).Cells(9).Text) Then
                                            If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                                Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                                objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                'objtest.ProgramChildVskeyword_SaveRecord()
                                                objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                            Else
                                                If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                                    Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                                    ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                                    ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                    ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                    ObjUpdateKW.TapeContentDetailID = 1
                                                    ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                    'ObjUpdateKW.ProgramChildVskeyword_Update()
                                                    ObjUpdateKW.ProgramChildVskeyword_Update_Trans(Trans)
                                                End If
                                            End If
                                        End If
                                    Next
                                End If
                            Next

                            ''***************************************************''
                            ''***************** Merge Records *******************''
                            ''***************************************************''
                            Dim dt As DataTable
                            Dim X As Integer
                            For X = 0 To dg_Merge.Rows.Count - 1
                                Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                                Dim ObjTape As New BusinessFacade.TapeContent()
                                ObjTape.TapeLibraryID = MergeTapeLibraryID
                                dt = ObjTape.GetMergeTapeContentDetailID_Entertainment()
                                Dim X2 As Integer
                                For X2 = 0 To dt.Rows.Count - 1
                                    Dim ObjTape2 As New BusinessFacade.TapeContent()
                                    ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                                    ObjTape2.TapeContentID = txt_TapeContentID.Text
                                    ObjTape2.TapeLibraryID = LibraryID
                                    'ObjTape2.MergeArchivalEnt()
                                    ObjTape2.MergeArchivalEnt_Trans(Trans)
                                Next
                            Next

                            If dg_Merge.Rows.Count > 0 Then
                                Dim Y As Integer
                                For Y = 0 To dg_Merge.Rows.Count - 1
                                    Dim ObjInsert As New BusinessFacade.TapeContent()
                                    ObjInsert.TapeLibraryID = LibraryID
                                    ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                                    ObjInsert.UserID = UserID
                                    'ObjInsert.InsertMergeArchivalEnt()
                                    ObjInsert.InsertMergeArchivalEnt_Trans(Trans)
                                Next
                            End If

                            ''***************************************************''

                            lblErr.Text = "Record has been Saved Successfully!!"

                            '********************************************************'
                            '********** Update Tape Status in TapeLibrary ***********'
                            '********************************************************'

                            If LibraryID <> 0 Then
                                Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                                objUpdateTapeStatus.TapeLibraryID = LibraryID
                                'objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                                objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus_Trans(Trans)
                            End If

                            ''******************************************************''
                            ''************* Search Engine Save Record **************''
                            ''******************************************************''

                            Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                            ObjSearchEngine2.TapeLibraryID = LibraryID
                            'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()
                            ' ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2_Trans(Trans) 'Orignal
                            ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord3(Trans)
                            'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord_New(Trans)



                            '************************ End ***************************'

                            '*********************Transaction Commit*****************************
                            lblErr.Text = "Record has been Saved Successfully!!"
                            Trans.Commit()
                            Conn.Close()

                        Catch ex As Exception
                            Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
                            Me.lblErr.Visible = True
                            Trans.Rollback()
                            Conn.Close()
                            Exit Sub
                        End Try
                        '*********************************************************************

                        Dim N As Integer
                        Dim IsUrduScript As Integer = 0
                        For N = 0 To dg_TestProgram.Rows.Count - 1
                            If dg_TestProgram.Rows(N).Cells(14).Text <> "N/A" Then
                                Dim Con As System.Data.SqlClient.SqlConnection
                                Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                                Con = New System.Data.SqlClient.SqlConnection(connStr)
                                Dim cmd As New System.Data.SqlClient.SqlCommand
                                cmd.CommandType = Data.CommandType.StoredProcedure
                                cmd.CommandText = "Insert_UrduScript_Entertainment"
                                cmd.Connection = Con

                                Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeContentDetailID", Data.SqlDbType.Int)
                                p1.Value = dg_TestProgram.Rows(N).Cells(10).Text

                                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                p2.Value = dg_TestProgram.Rows(N).Cells(14).Text

                                If Con.State = ConnectionState.Closed Then
                                    Con.Open()
                                End If
                                cmd.CommandTimeout = 0
                                cmd.Connection = Con
                                cmd.ExecuteNonQuery()
                                cmd.Connection.Close()
                                IsUrduScript = IsUrduScript + 1

                            End If
                        Next

                        If IsUrduScript <> 0 Then
                            ''******************************************''
                            ''**** update Tape Slug for SearchEngine ***''
                            ''******************************************''

                            Dim O1 As Integer
                            For O1 = 0 To dg_TestProgram.Rows.Count - 1
                                If dg_TestProgram.Rows(O1).Cells(14).Text <> "N/A" Then
                                    Dim Con As System.Data.SqlClient.SqlConnection
                                    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                                    Dim cmd As New System.Data.SqlClient.SqlCommand
                                    cmd.CommandType = Data.CommandType.StoredProcedure
                                    cmd.CommandText = "Insert_UrduScript_SearchEngineEntertainment"
                                    cmd.Connection = Con

                                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeContentDetailID", Data.SqlDbType.Int)
                                    p1.Value = dg_TestProgram.Rows(O1).Cells(10).Text

                                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                    p2.Value = dg_TestProgram.Rows(O1).Cells(14).Text

                                    If Con.State = ConnectionState.Closed Then
                                        Con.Open()
                                    End If
                                    cmd.CommandTimeout = 0
                                    cmd.Connection = Con
                                    cmd.ExecuteNonQuery()
                                    cmd.Connection.Close()
                                End If
                            Next
                        End If


                        ''******************* Fill Grids ************************''
                        'Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                        'Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                        'Me.Fill_MergeInformation(txt_TapeContentID.Text)

                        ''****************** Clear Merge Grids ******************''
                        dg_Merge.DataSource = Nothing
                        dg_Merge.DataBind()
                        dt_MergeTapeNumber = Nothing
                        ViewState("MergeTape_Table") = Nothing

                    End If
                    txt_TapeContentID.Text = String.Empty
                    ClearTapeContentDetailID()

                    ''---------------------------------------------------------------------------------------''

                ElseIf dtibraryID.Rows.Count = 2 Then
                    Conn.ConnectionString = ConfigurationManager.AppSettings("ConnectionString")
                    Conn.Open()
                    Trans = Conn.BeginTransaction()
                    Try
                        Dim s As Integer
                        For s = 0 To dtibraryID.Rows.Count - 1
                            If DepartmentID = 0 Then
                                lblErr_MasterEntry.Text = "Please Select Department !!"
                                Err_Department.Visible = True
                                Trans.Rollback()
                                Conn.Close()
                                Exit Sub
                            Else
                                Dim TapeContentID As Integer

                                ''******************************************''
                                ''********** Tape Content Save *************''
                                ''******************************************''

                                If txt_TapeContentID.Text = "" Then
                                    Dim ObjUser As New BusinessFacade.TapeContent()
                                    ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                                    ObjUser.DepartmentID = DepartmentID
                                    ObjUser.isCurrentContent = 0
                                    ObjUser.ClassificationCode = txt_ClassificationCode.Text
                                    ObjUser.CallNo = txt_CallNo.Text
                                    ObjUser.LocationCode = txt_LocationCode.Text
                                    ObjUser.IsActive = 1
                                    ObjUser.Copies = ddlCopies.SelectedItem.Text
                                    ObjUser.UserID = UserID
                                    ObjUser.LowResFileName = txtLowResFileName.Text
                                    ObjUser.HighResFileName = txtHighResFileName.Text
                                    ObjUser.FilePath = txtFiePath.Text
                                    'TapeContentID = ObjUser.SaveRecord_TapeContent()
                                    TapeContentID = ObjUser.SaveRecord_TapeContent_Trans(Trans)
                                    txt_TapeContentID.Text = TapeContentID
                                Else
                                    Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                                    ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                                    ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                                    ObjUpdateUser.DepartmentID = DepartmentID
                                    ObjUpdateUser.isCurrentContent = 0
                                    ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                                    ObjUpdateUser.CallNo = txt_CallNo.Text
                                    ObjUpdateUser.LocationCode = txt_LocationCode.Text
                                    ObjUpdateUser.IsActive = 1
                                    ObjUpdateUser.Copies = ddlCopies.SelectedItem.Text
                                    ObjUpdateUser.UserID = UserID
                                    ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                                    ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                                    ObjUpdateUser.FilePath = txtFiePath.Text
                                    'Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent()
                                    Dim O As Integer = ObjUpdateUser.UpdateRecord_TapeContent_Trans(Trans)
                                End If

                                ''***********************************************************''
                                ''**************** Tape Content Detail Save *****************''
                                ''***********************************************************''

                                Dim TapeContentDetailID As Integer
                                Dim P As Integer
                                For P = 0 To dg_TestProgram.Rows.Count - 1

                                    If dg_TestProgram.Rows(P).Cells(10).Text = "&nbsp;" Then

                                        ''''''''''''''' Save ''''''''''''''''''''''''''

                                        Dim ObjDetail As New BusinessFacade.TapeContent()
                                        ObjDetail.TapeContentID = txt_TapeContentID.Text
                                        ObjDetail.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                        ObjDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                        ObjDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                        ObjDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                        ObjDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                        ObjDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                        ObjDetail.ProducedBy = UserID
                                        ObjDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                        ObjDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                        ObjDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                        ObjDetail.NocNo = 1
                                        ObjDetail.ItemID = 0
                                        ObjDetail.MergeID = 0
                                        ObjDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                        ObjDetail.ProducedByName = UserName
                                        ObjDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                        ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                        ObjDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                        ObjDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                        TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_Trans(Trans)

                                        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                                        Dim q As Integer
                                        For q = 0 To dg_keyword_2.Rows.Count - 1
                                            If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(q).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(q).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(q).Cells(9).Text) Then
                                                If dg_keyword_2.Rows(q).Cells(5).Text = "&nbsp;" Then
                                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                    objtest.TapeContentDetailID = TapeContentDetailID
                                                    objtest.ProgramChildID = dg_keyword_2.Rows(q).Cells(1).Text
                                                    objtest.KeywordID = dg_keyword_2.Rows(q).Cells(3).Text
                                                    objtest.KeyTypeID = dg_keyword_2.Rows(q).Cells(6).Text
                                                    'objtest.ProgramChildVskeyword_SaveRecord()
                                                    objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                End If
                                            End If
                                        Next

                                        ''***************************************************************''
                                        ''**************** Tape Content Detail (Update) *****************''
                                        ''***************************************************************''
                                    Else
                                        Dim ObjUpdateDetail As New BusinessFacade.TapeContent()
                                        ObjUpdateDetail.TapeContentID = txt_TapeContentID.Text
                                        ObjUpdateDetail.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        ObjUpdateDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                        ObjUpdateDetail.ProgramChildID = dg_TestProgram.Rows(P).Cells(11).Text
                                        ObjUpdateDetail.EpisodeNo = dg_TestProgram.Rows(P).Cells(5).Text
                                        ObjUpdateDetail.PartNo = dg_TestProgram.Rows(P).Cells(2).Text
                                        ObjUpdateDetail.NoteArea = dg_TestProgram.Rows(P).Cells(4).Text
                                        ObjUpdateDetail.Abstract = dg_TestProgram.Rows(P).Cells(6).Text
                                        ObjUpdateDetail.ProducedBy = UserID
                                        ObjUpdateDetail.StratTime = dg_TestProgram.Rows(P).Cells(7).Text
                                        ObjUpdateDetail.EndTime = dg_TestProgram.Rows(P).Cells(8).Text
                                        ObjUpdateDetail.Duration = dg_TestProgram.Rows(P).Cells(9).Text
                                        ObjUpdateDetail.NocNo = 1
                                        ObjUpdateDetail.ItemID = 1
                                        ObjUpdateDetail.MergeID = 1
                                        ObjUpdateDetail.ProgramSubTitle = dg_TestProgram.Rows(P).Cells(1).Text
                                        ObjUpdateDetail.ProducedByName = UserName
                                        If dg_TestProgram.Rows(P).Cells(3).Text <> "&nbsp;" Then
                                            ObjUpdateDetail.ProductionStatus = dg_TestProgram.Rows(P).Cells(3).Text
                                        Else
                                            ObjUpdateDetail.ProductionStatus = 0
                                        End If
                                        ObjUpdateDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                        ObjUpdateDetail.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                        ObjUpdateDetail.EntryDate = dg_TestProgram.Rows(P).Cells(12).Text 'txtEntryDate.Text
                                        ObjUpdateDetail.OnAirDate = dg_TestProgram.Rows(P).Cells(15).Text 'txtEntryDate.Text
                                        ObjUpdateDetail.UpdateRecord_TapeContentDetail_Trans(Trans)

                                        Dim z As Integer
                                        For z = 0 To dg_keyword_2.Rows.Count - 1
                                            If (dg_TestProgram.Rows(P).Cells(11).Text - dg_keyword_2.Rows(z).Cells(1).Text = 0) And (dg_TestProgram.Rows(P).Cells(5).Text = dg_keyword_2.Rows(z).Cells(8).Text) And (dg_TestProgram.Rows(P).Cells(2).Text = dg_keyword_2.Rows(z).Cells(9).Text) Then
                                                If dg_keyword_2.Rows(z).Cells(5).Text = "&nbsp;" Then
                                                    Dim objtest As New BusinessFacade.ProgramVsKeyword()
                                                    objtest.TapeContentDetailID = dg_TestProgram.Rows(P).Cells(10).Text
                                                    objtest.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                    objtest.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                    objtest.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                    'objtest.ProgramChildVskeyword_SaveRecord()
                                                    objtest.ProgramChildVskeyword_SaveRecord_Trans(Trans)
                                                Else
                                                    If dg_keyword_2.Rows(z).Cells(6).Text <> "&nbsp;" Then
                                                        Dim ObjUpdateKW As New BusinessFacade.ProgramVsKeyword()
                                                        ObjUpdateKW.ProgramVsKeywordID = dg_keyword_2.Rows(z).Cells(5).Text
                                                        ObjUpdateKW.ProgramChildID = dg_keyword_2.Rows(z).Cells(1).Text
                                                        ObjUpdateKW.KeywordID = dg_keyword_2.Rows(z).Cells(3).Text
                                                        ObjUpdateKW.TapeContentDetailID = 1
                                                        ObjUpdateKW.KeyTypeID = dg_keyword_2.Rows(z).Cells(6).Text
                                                        'ObjUpdateKW.ProgramChildVskeyword_Update()
                                                        ObjUpdateKW.ProgramChildVskeyword_Update_Trans(Trans)
                                                    End If
                                                End If
                                            End If
                                        Next
                                    End If
                                Next

                                ''***************************************************''
                                ''***************** Merge Records *******************''
                                ''***************************************************''

                                Dim dt As DataTable
                                Dim X As Integer
                                For X = 0 To dg_Merge.Rows.Count - 1
                                    Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                                    Dim ObjTape As New BusinessFacade.TapeContent()
                                    ObjTape.TapeLibraryID = MergeTapeLibraryID
                                    dt = ObjTape.GetMergeTapeContentDetailID_Entertainment()
                                    Dim X2 As Integer
                                    For X2 = 0 To dt.Rows.Count - 1
                                        Dim ObjTape2 As New BusinessFacade.TapeContent()
                                        ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                                        ObjTape2.TapeContentID = txt_TapeContentID.Text
                                        ObjTape2.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        'ObjTape2.MergeArchivalEnt()
                                        ObjTape2.MergeArchivalEnt_Trans(Trans)
                                    Next
                                Next

                                If dg_Merge.Rows.Count > 0 Then
                                    Dim Y As Integer
                                    For Y = 0 To dg_Merge.Rows.Count - 1
                                        Dim ObjInsert As New BusinessFacade.TapeContent()
                                        ObjInsert.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                        ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                                        ObjInsert.UserID = UserID
                                        'ObjInsert.InsertMergeArchivalEnt()
                                        ObjInsert.InsertMergeArchivalEnt_Trans(Trans)
                                    Next
                                End If

                                ''***************************************************''

                                lblErr.Text = "Record has been Saved Successfully!!"

                                '********************************************************'
                                '********** Update Tape Status in TapeLibrary ***********'
                                '********************************************************'

                                If dtibraryID.Rows(s).Item(0).ToString <> 0 Then
                                    Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                                    objUpdateTapeStatus.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                    'objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                                    objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus_Trans(Trans)
                                End If

                                If s = 0 Then

                                    ''******************************************************''
                                    ''************* Search Engine Save Record **************''
                                    ''******************************************************''

                                    Dim ObjSearchEngine2 As New BusinessFacade.SearchEngine()
                                    ObjSearchEngine2.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                    'ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2()
                                    ' ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord2_Trans(Trans) [Origanl]
                                    ObjSearchEngine2.SearchEngine_Entertainment_SaveRecord3(Trans)

                                    '************************ End ***************************'

                                    If lblQueryString.Text = "" Then
                                        txt_TapeContentID.Text = String.Empty
                                    End If

                                End If

                                If lblQueryString.Text <> "" Then
                                    s = s + 1
                                End If


                            End If
                        Next

                        '*********************Transaction Commit*****************************
                        lblErr.Text = "Record has been Saved Successfully!!"
                        Trans.Commit()
                        Conn.Close()
                        '*********************************************************************
                    Catch ex As Exception
                        Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
                        Me.lblErr.Visible = True
                        Trans.Rollback()
                        Conn.Close()
                        Exit Sub
                    End Try

                    '******************* Fill Grids ************************''
                    'Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                    'Me.Fill_ProgramInformation_Table(txt_TapeContentID.Text)
                    'Me.Fill_MergeInformation(txt_TapeContentID.Text)

                    '****************** Clear Merge Grids ******************''
                    dg_Merge.DataSource = Nothing
                    dg_Merge.DataBind()
                    dt_MergeTapeNumber = Nothing
                    ViewState("MergeTape_Table") = Nothing

                    txt_TapeContentID.Text = String.Empty


                End If
            End If

        Catch ex As Exception
            Me.lblErr.Text = "Record not Saved due to error:  " & vbCrLf & ex.Message
            Me.lblErr.Visible = True
            Exit Sub
        End Try
    End Sub

    Private Sub UpdateTapeContentDetailID(ByVal TapeContentDetailID As Integer)

        If ViewState("ProgramInfo_Table_2") Is Nothing Then
        Else
            dt_Test_ProgramInfo = ViewState("ProgramInfo_Table_2")
        End If

        If txt_dgProgramInfo_Index.Text <> "" Then
            dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
            dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("TapeContentDetailID") = TapeContentDetailID
            dt_Test_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
            txt_dgProgramInfo_Index.Text = String.Empty
            ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
            BindGrid_ProgramInfo_2()
        End If

    End Sub

    Private Sub ClearTapeContentDetailID()

        If ViewState("ProgramInfo_Table_2") Is Nothing Then
        Else
            dt_Test_ProgramInfo = ViewState("ProgramInfo_Table_2")
        End If

        Dim I As Integer
        For I = 0 To dt_Test_ProgramInfo.Rows.Count - 1
            dt_Test_ProgramInfo.Rows(I).BeginEdit()
            dt_Test_ProgramInfo.Rows(I).Item("TapeContentDetailID") = DBNull.Value
            dt_Test_ProgramInfo.Rows(I).EndEdit()
            ViewState("ProgramInfo_Table_2") = dt_Test_ProgramInfo
            BindGrid_ProgramInfo_2()
        Next

    End Sub

    Private Sub UpdateUrduScript(ByVal TapeNumber As String)

        Dim Con As System.Data.SqlClient.SqlConnection
        Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
        Con = New System.Data.SqlClient.SqlConnection(connStr)
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "UpdateUrduScript"
        cmd.Connection = Con

        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeNumber", Data.SqlDbType.Text)
        p1.Value = TapeNumber

        If Con.State = ConnectionState.Closed Then
            Con.Open()
        End If
        cmd.CommandTimeout = 0
        cmd.Connection = Con
        cmd.ExecuteNonQuery()
        cmd.Connection.Close()

    End Sub

End Class
