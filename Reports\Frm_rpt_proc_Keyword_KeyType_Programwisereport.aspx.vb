Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_proc_Keyword_KeyType_Programwisereport
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                chkIgnoredate.Checked = True
                ChkProgram.Checked = True
                chk_EntKW.Checked = True
                Chk_KT.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            
            Dim Prog As String
            Dim KW As String
            Dim KT As String
            Dim FromDate As String
            Dim ToDate As String
            Dim ProgramChildID As Integer
            Dim ObjProgram As New BusinessFacade.ProgramChild()
            ObjProgram.ProgramChildName = txtProgrmaName.Text
            ProgramChildID = ObjProgram.GetProgramChildIDID_byProgramName(ObjProgram.ProgramChildName)

            ''*********************************************************''
            ''************* For Getting Program Child ID **************''
            ''*********************************************************''
            Dim KWID As Integer
            Dim ObjKWID As New BusinessFacade.EntertainmentKeyword()
            ObjKWID.EntertainmentKeyword = txtEntKeywords.Text
            KWID = ObjKWID.GetKeywordID_AutoComplete(ObjKWID.EntertainmentKeyword)

            ''*********************************************************''
            ''************* For Getting Program Child ID **************''
            ''*********************************************************''
            Dim KTID As Integer
            Dim ObjKTID As New BusinessFacade.KeyType()
            ObjKTID.KeyType = txtKT.Text
            KTID = ObjKTID.GetKeyTypeID_AutoComplete(ObjKTID.KeyType)


            If ChkProgram.Checked = True Then
                Prog = "-1"
            Else
                Prog = ProgramChildID
            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            If chk_EntKW.Checked = True Then
                KW = "-1"
            Else
                KW = KWID
            End If

            If Chk_KT.Checked = True Then
                KT = "-1"
            Else
                KT = KTID
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            If ddlComplete.SelectedValue = "Complete" Then
                If ddlPDF.SelectedValue = "PDF" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_proc_Keyword_KeyType_Programwisereport.rpt&@ProgramChildID=" + Prog + "&@FromDate=" + FromDate + "&@KeywordID=" + KW + "&@KeyTypeID=" + KT + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"
                    Page.RegisterClientScriptBlock("test", script)

                Else
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_proc_Keyword_KeyType_Programwisereport.rpt&@ProgramChildID=" + Prog + "&@FromDate=" + FromDate + "&@KeywordID=" + KW + "&@KeyTypeID=" + KT + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"
                    Page.RegisterClientScriptBlock("test", script)

                End If
            Else
                If ddlPDF.SelectedValue = "PDF" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_proc_Keyword_KeyType_Programwisereport_Distinct.rpt&@ProgramChildID=" + Prog + "&@FromDate=" + FromDate + "&@KeywordID=" + KW + "&@KeyTypeID=" + KT + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"
                    Page.RegisterClientScriptBlock("test", script)

                Else
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_proc_Keyword_KeyType_Programwisereport_Distinct.rpt&@ProgramChildID=" + Prog + "&@FromDate=" + FromDate + "&@KeywordID=" + KW + "&@KeyTypeID=" + KT + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"
                    Page.RegisterClientScriptBlock("test", script)

                End If
            End If
            

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_proc_Keyword_KeyType_Programwisereport.aspx"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub
End Class
