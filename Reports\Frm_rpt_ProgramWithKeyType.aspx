<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ProgramWithKeyType.aspx.vb" Inherits="Frm_rpt_ProgramWithKeyType" title="Other Reports > Q 5. How  Can I View Program With KeyType Report?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports > Q 5. How  Can I View Program With KeyType Report?" Width="760px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 181px; HEIGHT: 5px" vAlign=middle>&nbsp;</TD><TD style="HEIGHT: 5px" vAlign=middle></TD><TD style="HEIGHT: 5px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 5px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 181px; HEIGHT: 21px" vAlign=middle>Content Type</TD><TD style="HEIGHT: 21px" vAlign=middle>Program Name&nbsp; / Slug Name&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="ChkProgram" runat="server" Text="Ignore" AutoPostBack="True" OnCheckedChanged="ChkProgram_CheckedChanged" __designer:wfdid="w100"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>Key Type (s) &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkKeyType" runat="server" Text="Ignore" AutoPostBack="True" OnCheckedChanged="chkKeyType_CheckedChanged" __designer:wfdid="w101"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 181px; HEIGHT: 89px" vAlign=top><asp:DropDownList id="ddlContentType" runat="server" Width="175px" CssClass="mytext" AutoPostBack="True" __designer:wfdid="w102" DataValueField="ContentTypeID" DataTextField="ContentTypeName" DataSourceID="dsContentType">
                                </asp:DropDownList><asp:SqlDataSource id="dsContentType" runat="server" __designer:wfdid="w103" SelectCommand="SELECT [ContentTypeID], [ContentTypeName] FROM ApplicationSetup.ContentType order by [ContentTypeName]" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>">
                                </asp:SqlDataSource> </TD><TD style="HEIGHT: 89px" vAlign=top><asp:DropDownList id="ddlPrograms" runat="server" Width="216px" CssClass="mytext" __designer:wfdid="w104" DataValueField="ProgramChildID" DataTextField="ProgramChildName" DataSourceID="dsPrograms"></asp:DropDownList><asp:SqlDataSource id="dsPrograms" runat="server" __designer:wfdid="w105" SelectCommand="FillProgramContentTypeWise" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommandType="StoredProcedure">
                                    <SelectParameters>
                                        <asp:ControlParameter ControlID="ddlContentType" DefaultValue="23" Name="ContentTypeID"
                                            PropertyName="SelectedValue" Type="Decimal" />
                                    </SelectParameters>
                                </asp:SqlDataSource> </TD><TD style="HEIGHT: 89px" vAlign=top><asp:ListBox id="LbKeyType" runat="server" Width="175px" CssClass="mytext" Font-Size="X-Small" Font-Names="Verdana" __designer:wfdid="w106" DataValueField="KeyWordID" DataTextField="KeyWord" DataSourceID="dsKeyType" SelectionMode="Multiple">
                                    <asp:ListItem Value="ProgramChildID">ProgramChildName</asp:ListItem>
                                </asp:ListBox><asp:SqlDataSource id="dsKeyType" runat="server" __designer:wfdid="w107" SelectCommand="FillKeyWordContentTypeWise" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommandType="StoredProcedure">
                                    <SelectParameters>
                                        <asp:ControlParameter ControlID="ddlContentType" DefaultValue="23" Name="ContentTypeID"
                                            PropertyName="SelectedValue" Type="Decimal" />
                                    </SelectParameters>
                                </asp:SqlDataSource> </TD><TD style="HEIGHT: 89px" vAlign=top></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w108" TargetControlID="ddlContentType" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:ListSearchExtender id="ListSearchExtender2" runat="server" __designer:wfdid="w109" TargetControlID="ddlPrograms" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender>
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="--Show Form ( Other Reports > Q 5. How  Can I View Program With KeyType Report? )--"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="--Hide Form ( Other Reports > Q 5. How  Can I View Program With KeyType Report? )--"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

