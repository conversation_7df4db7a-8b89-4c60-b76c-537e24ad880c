
Partial Class SearchEngine_PlayVideo
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        '  Dim FileName As String = "Water Train.wmv"
        Dim FileName As String = Request.QueryString("FilePath")

        Dim strDiv As String = String.Empty

        strDiv += "<table width='100%'>"
        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://misops1\c$\Websites\DAMS\Clips\4 Inch Bona Video.wmv"">"

        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://misops1\c$\Websites\DAMS\Clips\" + FileName + """>"


        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://***********\NewsxSAN\archive\Lowresolution\" + FileName + """>"

        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://roshni\c$\Websites\Roshni3\DAMS\DAMS\Clips\" + FileName + """>"
        'strDiv += "<PARAM NAME=""URL"" VALUE=""file://ArchiveManagement\Clips\" + FileName + """>"

        'strDiv += "<PARAM NAME=""URL"" VALUE=""~/san/" + FileName + """> """

        strDiv += "<PARAM NAME=""URL"" VALUE=""file://khi-archive-apl\SAN\" + FileName + """>"

        divNews.InnerHtml = strDiv

    End Sub
End Class
