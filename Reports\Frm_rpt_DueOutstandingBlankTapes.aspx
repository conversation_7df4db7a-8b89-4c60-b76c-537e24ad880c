<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_DueOutstandingBlankTapes.aspx.vb" Inherits="Frm_rpt_DueOutstandingBlankTapes" title="Blank Tape Reports > Q 1. How Can I View Over Due Blank Tapes ?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Out-Standing Blank Tapes" Width="592px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext"><TD style="HEIGHT: 1px" vAlign=middle>&nbsp;</TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 1px" vAlign=middle></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px" vAlign=middle>Department Name</TD><TD style="HEIGHT: 21px" vAlign=middle>Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" __designer:wfdid="w12" OnCheckedChanged="chkEmployee_CheckedChanged" AutoPostBack="True"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>From Date &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" __designer:wfdid="w13"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>To Date</TD><TD style="WIDTH: 100px; HEIGHT: 21px" vAlign=middle></TD></TR><TR class="mytext"><TD vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="200px" CssClass="mytext" __designer:wfdid="w14" AutoPostBack="True" DataValueField="DepartmentID" DataTextField="DepartmentName" DataSourceID="dsDepartment">
                                </asp:DropDownList><asp:SqlDataSource id="dsDepartment" runat="server" __designer:wfdid="w15" SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>">
                                </asp:SqlDataSource>&nbsp; </TD><TD vAlign=top><asp:DropDownList id="ddlEmployee" runat="server" Width="200px" CssClass="mytext" __designer:wfdid="w16" DataValueField="EmployeeID" DataTextField="Employeename" DataSourceID="dsEmployee"></asp:DropDownList> <asp:SqlDataSource id="dsEmployee" runat="server" __designer:wfdid="w17" SelectCommand="Employee_GetRecordsDepartmentwise" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommandType="StoredProcedure">
                                    <SelectParameters>
                                        <asp:ControlParameter ControlID="ddlDepartment" Name="DepartmentID" PropertyName="SelectedValue"
                                            Type="Int32" DefaultValue="0" />
                                    </SelectParameters>
                                </asp:SqlDataSource> </TD><TD vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w18"></asp:TextBox>&nbsp; </TD><TD vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w19"></asp:TextBox>&nbsp; </TD><TD vAlign=top></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" __designer:wfdid="w20" PromptPosition="Bottom" PromptText TargetControlID="ddlDepartment"></cc1:ListSearchExtender> <cc1:ListSearchExtender id="ListSearchExtender2" runat="server" __designer:wfdid="w21" PromptPosition="Bottom" PromptText TargetControlID="ddlEmployee"></cc1:ListSearchExtender> <cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w22" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> <cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w23" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender> 
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    &nbsp;<br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (Out-Standing Blank Tapes) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (Out-Standing Blank Tapes) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

