<%@ Application Language="VB" %>

<script runat="server">

    Sub Application_Start(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs on application startup
    End Sub
    
    Sub Application_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs on application shutdown
    End Sub
        
    Sub Application_Error(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs when an unhandled error occurs
    End Sub

    Sub Session_Start(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs when a new session is started
        'Session("SessionId") = Common.GetSessionId()
        
        'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
        'obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
        'Dim arr_UserID As Array = Split(obj.UserLoginID, ",")
        'Dim LoginID As String = arr_UserID(1)
        
        'Dim ObjInsert As New BusinessFacade.LoginHistory()
        'Dim LoginID As String = "<PERSON><PERSON><PERSON><PERSON> Khan"
        'ObjInsert.LoginID = LoginID
        'ObjInsert.SaveRecord()

     
       
    End Sub

    Sub Session_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs when a session ends. 
        ' Note: The Session_End event is raised only when the sessionstate mode
        ' is set to InProc in the Web.config file. If session mode is set to StateServer 
        ' or SQLServer, the event is not raised.
        
        Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
        obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
        Dim arr_UserID As Array = Split(obj.UserLoginID, ",")
        Dim LoginID As String = arr_UserID(1)
        
        
    End Sub
       
</script>
