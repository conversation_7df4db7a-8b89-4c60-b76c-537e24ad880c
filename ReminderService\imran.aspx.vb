
Partial Class ReminderService_imran
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
        Dim objConnection As New Data.SqlClient.SqlConnection(strConnection)

        Me.TextBox1.Text = Microsoft.ApplicationBlocks.Data.SqlHelper.ExecuteScalar(objConnection, "[GetEmail]", Request("EmailID")).ToString

        Dim Body As String
        Body = Me.TextBox1.Text

        Body = Body.Replace("^", vbNewLine)
        Body = Body.Replace("!", " ")
        Body = Body.Replace("bbbccc", "")
        Body = Body.Replace("dddeee", "")
        Body = Body.Replace("~", "      ")

        Me.TextBox1.Text = Body
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click


        Dim TestBody As String = ""
        'TestBody = "<b><i><u>Friendly Reminder</b></i></u><br><br><br>" & Me.txtTestBody.Text
        'TestBody = TestBody.Replace("^", "<br>")
        'TestBody = TestBody.Replace("!", "&nbsp;")
        'TestBody = TestBody.Replace("bbbccc", "<b><u>")
        'TestBody = TestBody.Replace("dddeee", "</b></u>")
        'TestBody = TestBody.Replace("~", "&#x0009;")

        TestBody = TestBody + _
         "<HTML>" & _
                    "<HEAD>" & _
                           "" & _
                           "</HEAD>" & _
                           "<BODY MS_POSITIONING=""GridLayout"">" & _
                           "<table border=""0"" cellpadding=""0"" cellspacing=""0""" & _
                             "HEIGHT: 990px" & _
                             "font-family: Arial" & _
                            "BACKGROUND-COLOR: #4396ca"" width=""100%"">" & _
                   "<tr>" & _
                   "<td colspan=""6"" style=""font-weight: bold; font-size: 20pt; font-family: Arial; color: window; HEIGHT: 45px; BACKGROUND-COLOR: #88B3C4"" valign=""bottom"" align=""left"">  &nbsp;&nbsp;Friendly Reminder</td>" & _
                   "</tr>" & _
                   "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""6"">Dear" & "&nbsp;" & Request.QueryString("EmployeeName") & "</td></tr>" & _
                   "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                   "<tr><td colspan=""6"">The following tapes are outstanding against you. </td></tr>" & _
                    "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr style=""font-weight: bold; width: 100px; color: #990000""><b><u>" & _
                    "<td>S.No" & _
                    "</td>" & _
                    "<td>Tape No." & _
                    "</td>" & _
                    "<td>Tape Type" & _
                    "</td>" & _
                    "<td>Issue Date" & _
                    "</td>" & _
                    "<td>Due Date" & _
                    "</td>" & _
                    "<td>Remarks" & _
                    "</td></b></u>" & _
                    "</tr>" & _
                    "<tr><td colspan=""6"">&nbsp;</td></tr>"



        ''********************************************************''
        Dim TapeDetail As String
        TapeDetail = Request.QueryString("TapeDetail")
        Dim SNo, TapeType, TapeNo, IssueDate, DueDate, Remarks As String

        Dim ArrMaster As Array = Split(TapeDetail, "$")
        Dim K As Integer
        For K = 0 To ArrMaster.Length - 2
            Dim ArrChild As Array = Split(ArrMaster(K), ",")
            SNo = ArrChild(0)
            TapeType = ArrChild(1)
            TapeNo = ArrChild(2)
            IssueDate = ArrChild(3)
            DueDate = ArrChild(4)
            If ArrChild(5) = "Archive" Then
                Remarks = "Archival"
            Else
                Remarks = ArrChild(5)
            End If


            TestBody = TestBody + "<tr>" & _
                           "<td>" & SNo & "" & _
                           "</td>" & _
                           "<td>" & TapeType & "" & _
                           "</td>" & _
                           "<td>" & TapeNo & "" & _
                           "</td>" & _
                           "<td>" & IssueDate & "" & _
                           "</td>" & _
                           "<td>" & DueDate & "" & _
                           "</td>" & _
                           "<td>" & Remarks & "" & _
                           "</td>" & _
                           "</tr>" & _
             "<tr><td colspan=""6"">&nbsp;</td></tr>"
        Next



        ''********************************************************''

        TestBody = TestBody + "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""6"">Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us. </td></tr>" & _
                    "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""6"">Regards</td></tr>" & _
                    "<tr><td colspan=""6"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""6"">" & "Imran".ToUpper & "</td></tr>" & _
                    "<tr><td colspan=""6"">CirculationDesk</td></tr>" & _
                     "<tr><td colspan=""6"">Ext:6132 (News Archive) , 6568 (Central Archive)</td></tr>" & _
                   "</table>" & _
                           "</BODY>" & _
                           "</HTML>"

        Me.TextBox2.Text = TestBody

    End Sub
End Class
