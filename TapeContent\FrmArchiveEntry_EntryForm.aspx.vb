Imports System.Data

Partial Class TapeContent_FrmArchiveEntry_News
    Inherits System.Web.UI.Page
    Dim ConString As String = ConfigurationManager.AppSettings("ConnectionString")
    Dim Con As SqlClient.SqlConnection = New SqlClient.SqlConnection(ConString)
    Dim Dt_SaveKeyword As New DataTable
    Dim Dt_SaveKeyword_2 As New DataTable
    Dim Dt_SaveFootage As New DataTable
    Dim dt_ProgramInfo As New DataTable
    Dim dt_MergeTapeNumber As New DataTable
    Dim dt_BulkTapeNumber As New DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
      
        Try
            If Not Page.IsPostBack = True Then
                txt_UrduScript.Attributes.Add("onkeypress", "search()")
                txt_UrduScript.Attributes.Add("Dir", "Rtl")

                txt_StartTime1.Text = "00:00:00:00"
                txt_Endtime1.Text = "00:00:00:00"

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                BindCombo()
                If Not Request.QueryString.Get("TapeContentID") = "" Then
                    txt_TapeContentID.Text = Request.QueryString.Get("TapeContentID").ToString
                    Me.BindEditCombo(txt_TapeContentID.Text)
                    Me.Fill_KeyWord_Table(txt_TapeContentID.Text)
                    Me.Fill_Footage_Table(txt_TapeContentID.Text)
                    Me.Fill_ProgramInfo_Table(txt_TapeContentID.Text)
                    Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                    Me.Fill_MergeInformation(txt_TapeContentID.Text)
                    lblQueryString.Text = txt_TapeContentID.Text

                    lnkBulkTapes.Visible = False

                End If
                txtEntryDate.Text = Date.Now.ToString("dd-MMM-yyyy")
                'txtSlugDate.Text = Date.Now.ToString("dd-MMM-yyyy")
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub
    Private Sub Fill_ProgramInfo_Table(ByVal TapeContentID As Integer)

        'Dim Qry_progInfo As String = "SELECT a.TapeSlugID,a.TapeContentID, a.TapeContentDetail_News_ID, a.TapeLibraryID, a.SubClosetID, b.startTime_vc as 'StartTime', b.EndTime_vc as 'EndTime', b.Duration, a.MergedId, b.TapeSlug as 'ReporterSlug', b.ProposedSlug, isnull(b.ReporterID,0) [ReporterID], c.EmployeeName as 'Reporter',isnull(b.CameraManID,0) [CameraManID], b.EnglishScript, d.EmployeeName as 'CameraMan',b.Urdu_Script UrduScript, isnull(Cast(EntryDate as varchar),Cast(GetDate() as varchar)) [EntryDate], isnull(b.LowResolutionFileName,'N/A') LowResolution, isnull(b.HighResolutionFileName,'N/A') HighResolution, isnull(b.FilePath,'N/A') FilePath FROM ArchivalManagement.TapeContentDetail_News a inner join ApplicationSetup.TapeSlug b on a.TapeSlugID = b.TapeSlugID left outer join ApplicationSetup.Employee c on c.EmployeeID = b.ReporterID left outer join ApplicationSetup.Employee d on d.EmployeeID = b.CameraManID where TapeContentID = " & txt_TapeContentID.Text
        Dim Qry_progInfo As String = "SELECT a.TapeSlugID,a.TapeContentID, a.TapeContentDetail_News_ID, a.TapeLibraryID, a.SubClosetID, b.startTime_vc as 'StartTime', b.EndTime_vc as 'EndTime', b.Duration, a.MergedId, b.TapeSlug as 'ReporterSlug', b.ProposedSlug, isnull(b.ReporterID,0) [ReporterID], c.EmployeeName as 'Reporter',isnull(b.CameraManID,0) [CameraManID], b.EnglishScript, d.EmployeeName as 'CameraMan',b.Urdu_Script UrduScript, isnull(EntryDate,GetDate()) [EntryDate], isnull(b.LowResolutionFileName,'N/A') LowResolution, isnull(b.HighResolutionFileName,'N/A') HighResolution, isnull(b.FilePath,'N/A') FilePath FROM ArchivalManagement.TapeContentDetail_News a inner join ApplicationSetup.TapeSlug b on a.TapeSlugID = b.TapeSlugID left outer join ApplicationSetup.Employee c on c.EmployeeID = b.ReporterID left outer join ApplicationSetup.Employee d on d.EmployeeID = b.CameraManID where TapeContentID = " & txt_TapeContentID.Text
        Dim da_progInfo As New System.Data.SqlClient.SqlDataAdapter(Qry_progInfo, Con)
        Dim ds_prognfo As New DataSet
        da_progInfo.Fill(ds_prognfo, "Table_ProgInfo")
        dt_ProgramInfo = ds_prognfo.Tables("Table_ProgInfo")

        ViewState("ProgramInfo_Table") = dt_ProgramInfo
        'Dim FillTime As String
        'FillTime = dt_ProgramInfo.Rows(dt_ProgramInfo.Rows.Count - 1).Item(6).ToString

        'Dim Start As Array = FillTime.Split(":")
        'END1.Text = Start(0).ToString
        'END2.Text = Start(1).ToString
        'END3.Text = Start(2).ToString
        'END4.Text = Start(3).ToString

        'FillStartTime()

        BindGrid_ProgramInfo()

    End Sub
    Private Sub FillStartTime()

        'dt_ProgramInfo = ViewState("ProgramInfo_Table")

        Dim FillTime As String = dg_programInfo.Rows(dg_programInfo.Rows.Count - 1).Cells(8).Text

        ' FillTime = dt_ProgramInfo.Rows(dt_ProgramInfo.Rows.Count - 1).Item(5).ToString

        txt_StartTime1.Text = txt_Endtime1.Text

        ' txt_Endtime1.Text = "00:00:00:00"
        Dim Start As Array = FillTime.Split(":")
        END1.Text = Start(0).ToString
        END2.Text = Start(1).ToString
        END3.Text = Start(2).ToString
        END4.Text = Start(3).ToString
    End Sub
    Private Sub Fill_MergeInformation(ByVal TapeContentID As Integer)

        Dim objMergeDetail As New BusinessFacade.TapeContent_News()
        objMergeDetail.TapeContentID = txt_TapeContentID.Text
        dg_Merge2.DataSource = objMergeDetail.GetNewsMergeTapes()
        dg_Merge2.DataBind()

    End Sub
    Private Sub Fill_Footage_Table(ByVal TapeContentID As Integer)

        Dim ObjFootage As New BusinessFacade.TapeContent_News()
        ObjFootage.TapeContentID = txt_TapeContentID.Text
        Dt_SaveFootage = ObjFootage.GetFootages()

        ViewState("FootageTable") = Dt_SaveFootage

        Bind_DgFootage()


    End Sub
    Private Sub Fill_KeyWord_Table(ByVal TapeContentID As Integer)

        Dim qry_3 As String = "select a.NewsKeywordID as 'EntertainmentKeywordID', a.SubContentTypeID, a.NewsKeyword as 'KeyWord', a.TKeytype as 'KeyType' from ApplicationSetup.NewsKeyword a inner join ArchivalManagement.TapeContentNewsKeywords b on a.NewsKeywordID = b.NewsKeywordID where b.TapeContentID = " & txt_TapeContentID.Text
        Dim da_3 As New System.Data.SqlClient.SqlDataAdapter(qry_3, Con)
        Dim ds_3 As New DataSet
        da_3.Fill(ds_3, "Table_KeyWord")
        Dt_SaveKeyword = ds_3.Tables("Table_KeyWord")

        ViewState("KeyWordTable") = Dt_SaveKeyword

        Bind_dgKeyWord()

    End Sub
    Private Sub Fill_KeyWord_Table_2(ByVal TapeContentID As Integer)
        Dim ObjKW As New BusinessFacade.TapeContent_News()
        ObjKW.TapeContentID = txt_TapeContentID.Text
        Dt_SaveKeyword_2 = ObjKW.SlugVsKeyWord_GetRecords()
        ViewState("KeyWordTable_2") = Dt_SaveKeyword_2
        Bind_dgKeyWord_2()
    End Sub
    Private Sub BindEditCombo(ByVal TapeContentID As Integer)

        Dim qry As String = "SELECT TapeContentNewsID,ContentTypeID,DepartmentID,Isnull(ClassificationCode,'N/A') [ClassificationCode],Isnull(CallNo,'N/A') [CallNo],Isnull(LocationCode,'N/A') [LocationCode],isnull(Copies,0) [Copies],Isnull(LowResolutionFileName,'N/A') [LowResolutionFileName], Isnull(HighResolutionFileName,'N/A') [HighResolutionFileName],isnull(FilePath,'N/A') [FilePath] FROM [DAMS_NewDB].[ArchivalManagement].[TapeContent_News] where TapeContentNewsID = " & txt_TapeContentID.Text
        Dim da As New System.Data.SqlClient.SqlDataAdapter(qry, Con)
        Dim ds As New DataSet
        da.Fill(ds)
        If ds.Tables(0).Rows.Count > 0 Then

            Me.ddl_Channel.SelectedIndex = Me.ddl_Channel.Items.IndexOf(Me.ddl_Channel.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("ContentTypeID"))))

            'Me.ddl_Department.SelectedIndex = Me.ddl_Department.Items.IndexOf(Me.ddl_Department.Items.FindByValue(CStr(ds.Tables(0).Rows(0)("DepartmentID"))))

            txt_ClassificationCode.Text = ds.Tables(0).Rows(0)("ClassificationCode")
            txt_CallNo.Text = ds.Tables(0).Rows(0)("CallNo")
            txt_LocationCode.Text = ds.Tables(0).Rows(0)("LocationCode")

            'txtLowResFileName.Text = ds.Tables(0).Rows(0)("LowResolutionFileName")
            'txtHighResFileName.Text = ds.Tables(0).Rows(0)("HighResolutionFileName")
            'txtFiePath.Text = ds.Tables(0).Rows(0)("FilePath")

            If ds.Tables(0).Rows(0)("Copies") <> 0 Then
                ddlCopies.SelectedItem.Text = ds.Tables(0).Rows(0)("Copies")
            End If


            Dim DeptID As String = ds.Tables(0).Rows(0)("DepartmentID").ToString
            Dim ObjDepartmentName As New BusinessFacade.TapeContent()
            If DeptID = "" Then
                ObjDepartmentName.DepartmentID = 3
            Else
                ObjDepartmentName.DepartmentID = DeptID
            End If

            Me.txt_Department.Text = ObjDepartmentName.GetDepartmetnName_by_DepartmentID(ObjDepartmentName.DepartmentID)

        End If

        Dim qry_2 As String = "select a.* from ArchivalManagement.TapeContentDetail_News a inner join ArchivalManagement.TapeContent_News b on a.TapeContentID = b.TapeContentNewsID Where a.TapeContentID = " & txt_TapeContentID.Text
        Dim da_2 As New System.Data.SqlClient.SqlDataAdapter(qry_2, Con)
        Dim ds_2 As New DataSet
        da_2.Fill(ds_2)
        If ds_2.Tables(0).Rows.Count > 0 Then

        End If

        Dim TapeLib_ID As Integer = ds_2.Tables(0).Rows(0)("TapeLibraryID").ToString
        Dim objTapeNumber As New BusinessFacade.TapeContent()
        objTapeNumber.TapeLibraryID = TapeLib_ID
        Me.txt_TapeNumber.Text = objTapeNumber.GetTapeNumber_byTapeLibraryID(objTapeNumber.TapeLibraryID)

    End Sub
    Private Sub BindCombo()

        ddl_Channel.DataTextField = "ContentTypeName"
        ddl_Channel.DataValueField = "ContentTypeID"
        ddl_Channel.DataSource = New BusinessFacade.ContentType().GetRecords()
        ddl_Channel.DataBind()
        ddl_Channel.SelectedIndex = "1"

        ddl_SubCloset.DataTextField = "SubClosetName"
        ddl_SubCloset.DataValueField = "SubClostID"
        ddl_SubCloset.DataSource = New BusinessFacade.SubCloset().GetRecords()
        ddl_SubCloset.DataBind()

        ddl_KeywordType.DataTextField = "KeywordType"
        ddl_KeywordType.DataValueField = "KeywordTypeID"
        ddl_KeywordType.DataSource = New BusinessFacade.KeywordType().GetRecords()
        ddl_KeywordType.DataBind()

        ddl_ProgramChild.DataTextField = "ProgramChildName"
        ddl_ProgramChild.DataValueField = "ProgramChildID"
        ddl_ProgramChild.DataSource = New BusinessFacade.ProgramChild().GetRecords()
        ddl_ProgramChild.DataBind()

        lstKeyword.DataTextField = "NewsKeyword"
        lstKeyword.DataValueField = "NewsKeywordID"
        lstKeyword.DataSource = New BusinessFacade.NewsKeyword().GetRecords()
        lstKeyword.DataBind()

        ddl_RecycleTurn.DataTextField = "RecycleTurn"
        ddl_RecycleTurn.DataValueField = "RecycleTurnID"
        ddl_RecycleTurn.DataSource = New BusinessFacade.RecycleTurn().GetRecords()
        ddl_RecycleTurn.DataBind()

        lstFootageType.DataTextField = "FootageTypeName"
        lstFootageType.DataValueField = "FootageTypeID"
        lstFootageType.DataSource = New BusinessFacade.FootageType().GetRecords()
        lstFootageType.DataBind()

    End Sub
    Protected Sub bttnSave_Keyword_Click1(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_Keyword.Click
        Dim col1 As DataColumn = New DataColumn("KeyType")
        col1.DataType = System.Type.GetType("System.String")
        Dt_SaveKeyword.Columns.Add(col1)

        Dim col2 As DataColumn = New DataColumn("KeyWord")
        col2.DataType = System.Type.GetType("System.String")
        Dt_SaveKeyword.Columns.Add(col2)

        Dim col3 As DataColumn = New DataColumn("EntertainmentKeywordID")
        col3.DataType = System.Type.GetType("System.Int32")
        Dt_SaveKeyword.Columns.Add(col3)

        If ViewState("KeyWordTable") Is Nothing Then
        Else
            Dt_SaveKeyword = ViewState("KeyWordTable")
        End If


        If txt_dgKeyword_Index.Text <> "" Then

            Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).BeginEdit()
            Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).Item("KeyType") = ddl_KeywordType.SelectedItem.ToString
            Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).Item("KeyWord") = txt_KeyWord.Text
            Dt_SaveKeyword.Rows(txt_dgKeyword_Index.Text).EndEdit()
            txt_dgKeyword_Index.Text = String.Empty

        Else

            Dim Rw As DataRow
            Rw = Dt_SaveKeyword.NewRow()
            Rw.Item("KeyType") = ddl_KeywordType.SelectedItem.ToString
            Rw.Item("KeyWord") = txt_KeyWord.Text
            Rw.Item("EntertainmentKeywordID") = DBNull.Value
            Dt_SaveKeyword.Rows.Add(Rw)

        End If

        '''''''''''''''''''''''''''''''''''''''''

        ViewState("KeyWordTable") = Dt_SaveKeyword

        ddl_KeywordType.SelectedIndex = "0"
        txt_KeyWord.Text = String.Empty

        Bind_dgKeyWord()
    End Sub
    Private Sub Bind_dgKeyWord()

        dg_KeyWord.DataSource = Dt_SaveKeyword
        dg_KeyWord.DataBind()

    End Sub
    Protected Sub bttnSave_Footage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_Footage.Click

        ''**********************************''
        ''********* Save Footages **********''
        ''**********************************''

        Dim colFootage1 As DataColumn = New DataColumn("FootageType")
        colFootage1.DataType = System.Type.GetType("System.String")
        Dt_SaveFootage.Columns.Add(colFootage1)

        Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
        colFootage2.DataType = System.Type.GetType("System.Int32")
        Dt_SaveFootage.Columns.Add(colFootage2)

        Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
        colFootage3.DataType = System.Type.GetType("System.Int32")
        Dt_SaveFootage.Columns.Add(colFootage3)

        Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
        colFootage4.DataType = System.Type.GetType("System.Int32")
        Dt_SaveFootage.Columns.Add(colFootage4)

        Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
        colFootage5.DataType = System.Type.GetType("System.String")
        Dt_SaveFootage.Columns.Add(colFootage5)

        If ViewState("FootageTable") Is Nothing Then
        Else
            Dt_SaveFootage = ViewState("FootageTable")
        End If

        If txt_dgFootage_Index.Text <> "" Then

            ''''''''''' Get FootageTypeID '''''''''''''''''
            Dim FootageTypeID As Integer
            Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
            objFootageTypeID.FootageType = FT1.Text
            FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

            If FootageTypeID <> "0" Then
                Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
                Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = FT1.Text
                Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = FootageTypeID
                Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
                txt_dgFootage_Index.Text = String.Empty
            End If



        Else

            'Dim Y As Integer
            'For Y = 0 To lstFootageType.Items.Count - 1
            '    If lstFootageType.Items(Y).Selected Then
            '        Dim FootageRow As DataRow
            '        FootageRow = Dt_SaveFootage.NewRow
            '        FootageRow.Item("TapeContentID") = DBNull.Value
            '        FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
            '        FootageRow.Item("FootageType") = lstFootageType.Items(Y).Text
            '        FootageRow.Item("FootageTypeID") = lstFootageType.Items(Y).Value
            '        FootageRow.Item("TapeContentFootageID") = DBNull.Value
            '        Dt_SaveFootage.Rows.Add(FootageRow)
            '    End If
            'Next

            Dim x As Integer
            For x = 1 To 1

                Dim FoT As String
                If x = 1 Then
                    FoT = FT1.Text

                ElseIf x = 2 Then
                    FoT = FT1.Text
                    'KT = KT2.Text
                Else
                    FoT = FT1.Text
                    'KT = KT3.Text
                End If

                ''''''''''' Get FootageTypeID '''''''''''''''''
                Dim FootageTypeID As Integer
                Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
                objFootageTypeID.FootageType = FT1.Text
                FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)


                If FootageTypeID <> 0 Then
                    Dim FootageRow As DataRow
                    FootageRow = Dt_SaveFootage.NewRow
                    FootageRow.Item("TapeContentID") = DBNull.Value
                    FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
                    FootageRow.Item("FootageType") = FoT
                    FootageRow.Item("FootageTypeID") = FootageTypeID
                    FootageRow.Item("TapeContentFootageID") = DBNull.Value
                    Dt_SaveFootage.Rows.Add(FootageRow)
                End If
            Next
            '''''''''''''''''''''''''''''

        End If

        ViewState("FootageTable") = Dt_SaveFootage
        Bind_DgFootage()

        ''************** End ***************''
        ''**********************************''

        'Dim colFootage1 As DataColumn = New DataColumn("FootageType")
        'colFootage1.DataType = System.Type.GetType("System.String")
        'Dt_SaveFootage.Columns.Add(colFootage1)

        'Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
        'colFootage2.DataType = System.Type.GetType("System.Int32")
        'Dt_SaveFootage.Columns.Add(colFootage2)

        'Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
        'colFootage3.DataType = System.Type.GetType("System.Int32")
        'Dt_SaveFootage.Columns.Add(colFootage3)

        'Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
        'colFootage4.DataType = System.Type.GetType("System.Int32")
        'Dt_SaveFootage.Columns.Add(colFootage4)

        'Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
        'colFootage5.DataType = System.Type.GetType("System.String")
        'Dt_SaveFootage.Columns.Add(colFootage5)

        'If ViewState("FootageTable") Is Nothing Then
        'Else
        '    Dt_SaveFootage = ViewState("FootageTable")
        'End If

        'If txt_dgFootage_Index.Text <> "" Then

        '    ''''''''''' Get FootageTypeID '''''''''''''''''
        '    'Dim FootageTypeID As Integer
        '    'Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
        '    'objFootageTypeID.FootageType = txt_FootageType.Text
        '    'FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)


        '    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
        '    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = txt_FootageType.Text
        '    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = FootageTypeID
        '    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
        '    'txt_dgFootage_Index.Text = String.Empty

        'Else

        '    ''''''''''' Get FootageTypeID '''''''''''''''''
        '    'Dim FootageTypeID As Integer
        '    'Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
        '    'objFootageTypeID.FootageType = txt_FootageType.Text
        '    'FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

        '    Dim Y As Integer
        '    For Y = 0 To lstFootageType.Items.Count - 1
        '        If lstFootageType.Items(Y).Selected Then
        '            Dim FootageRow As DataRow
        '            FootageRow = Dt_SaveFootage.NewRow
        '            FootageRow.Item("TapeContentID") = DBNull.Value
        '            FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
        '            FootageRow.Item("FootageType") = lstFootageType.Items(Y).Text
        '            FootageRow.Item("FootageTypeID") = lstFootageType.Items(Y).Value
        '            FootageRow.Item("TapeContentFootageID") = DBNull.Value
        '            Dt_SaveFootage.Rows.Add(FootageRow)
        '        End If
        '    Next

        'End If

        'ViewState("FootageTable") = Dt_SaveFootage

        '' txt_FootageType.Text = String.Empty
        'Bind_DgFootage()

    End Sub
    Private Sub Bind_DgFootage()

        dg_Footage.DataSource = Dt_SaveFootage
        dg_Footage.DataBind()

    End Sub
    Protected Sub bttnMerge_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMerge.Click

        lblErr_Merge.Text = String.Empty

        Dim arr As Array = Split(txt_MergeTape.Text, "#")
        If arr.Length = 2 Then
            txt_MergeTape.Text = arr(1)
        End If

        ''**********************************************''
        ''************* Get TapeLibraryID **************''
        ''**********************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeContent_News()
        objLibID.TapeNumber = txt_MergeTape.Text
        LibraryID = objLibID.GetTapeID_News_byTapeNumber(objLibID.TapeNumber)

        ''**********************************************''

        If LibraryID <> 0 Then
            Dim col1_merge As DataColumn = New DataColumn("MergeTapeNumberID")
            col1_merge.DataType = System.Type.GetType("System.Int32")
            dt_MergeTapeNumber.Columns.Add("MergeTapeNumberID")

            Dim col2_merge As DataColumn = New DataColumn("MergeTapeNumber")
            col2_merge.DataType = System.Type.GetType("System.String")
            dt_MergeTapeNumber.Columns.Add("MergeTapeNumber")

            If ViewState("MergeTape_Table") Is Nothing Then
            Else
                dt_MergeTapeNumber = ViewState("MergeTape_Table")
            End If


            Dim row_merge As DataRow
            row_merge = dt_MergeTapeNumber.NewRow
            'row_merge.Item("MergeTapeNumberID") = ddl_MergeTapeNo.SelectedValue
            row_merge.Item("MergeTapeNumberID") = LibraryID
            'row_merge.Item("MergeTapeNumber") = ddl_MergeTapeNo.SelectedItem.ToString
            row_merge.Item("MergeTapeNumber") = txt_MergeTape.Text
            dt_MergeTapeNumber.Rows.Add(row_merge)
            ViewState("MergeTape_Table") = dt_MergeTapeNumber
            BindMergeTapeNumber()
        Else
            lblErr_Merge.Text = "Please Select TapeNumber in Master Level Entry!! "
        End If


        TabContainer1.ActiveTabIndex = CInt(1)
    End Sub
    Private Sub BindMergeTapeNumber()

        dg_Merge.DataSource = dt_MergeTapeNumber
        dg_Merge.DataBind()

    End Sub
    Protected Sub bttnCalculateDuration_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCalculateDuration.Click
        Calculate()
    End Sub
    Private Sub Calculate()
        lblErr.Text = String.Empty

        Dim StartTime As String
        Dim EndTime As String

        If ST1.Text = "" Then
            ST1.Text = "00"
        End If
        If ST2.Text = "" Then
            ST2.Text = "00"
        End If
        If ST3.Text = "" Then
            ST3.Text = "00"
        End If
        If ST4.Text = "" Then
            ST4.Text = "00"
        End If

        If END1.Text = "" Then
            END1.Text = "00"
        End If
        If END2.Text = "" Then
            END2.Text = "00"
        End If
        If END3.Text = "" Then
            END3.Text = "00"
        End If
        If END4.Text = "" Then
            END4.Text = "00"
        End If

        ' StartTime = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
        ' EndTime = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

        EndTime = txt_StartTime1.Text
        StartTime = txt_Endtime1.Text


        Dim time1 As String
        time1 = StartTime
        Dim str As Array = time1.Split(":")

        Dim time2 As String
        time2 = EndTime
        Dim str2 As Array = time2.Split(":")

        Dim Min1 As Integer
        If str(0) <> "" And str(1) <> "" And str(2) <> "" Then
            Min1 = (str(0) * 60 * 60) + (str(1) * 60) + (str(2))
        End If

        Dim Min2 As Integer
        If str2(0) <> "" And str2(1) <> "" And str2(2) <> "" Then
            Min2 = (str2(0) * 60 * 60) + (str2(1) * 60) + (str2(2))
        End If

        Dim Diff As String = ""
        If Min1 >= Min2 Then
            Dim sec As Integer = Min1 - Min2
            If str(3) < str2(3) Then

                Dim a As Integer
                a = str(2)
                Dim b As Integer
                b = str2(2)
                If a = b Then
                    sec = sec - 1
                End If

            End If

            Diff = Format(Int(sec / 3600), "00") & ":" & Format(Int((sec - (Int(sec / 3600) * 3600)) / 60), "00") & ":" & Format(((sec Mod 60)), "00")


            Dim A1 As String = StartTime
            Dim A2 As String = EndTime
            Dim B1 As String = ""
            Dim B2 As String = ""

            B1 = Left(A1, 2) / 24 + Mid(A1, 4, 2) / (24 * 60) + Mid(A1, 7, 2) / (24 * 60 * 60) + Right(A1, 2) / (24 * 60 * 60 * 24)

            B2 = Left(A2, 2) / 24 + Mid(A2, 4, 2) / (24 * 60) + Mid(A2, 7, 2) / (24 * 60 * 60) + Right(A2, 2) / (24 * 60 * 60 * 24)

            Dim B3 As String = ""
            B3 = B1 - B2


            Dim B4 As String = ""
            B4 = Format((B3 * 24 * 60 * 60 - Int(B3 * 24 * 60 * 60)) * 24, "00")
            If B4 = "24" Then
                B4 = "00"
            End If

            Dim Convert As String = ""
            Convert = Diff & ":" & B4

            Dim Dur As String
            Dur = Convert

            Dim arr As Array = Split(Dur, ":")

            DUR_1.Text = arr(0).ToString
            DUR_2.Text = arr(1).ToString
            DUR_3.Text = arr(2).ToString
            DUR_4.Text = arr(3).ToString

        Else
            ST1.Text = "00"
            ST2.Text = "00"
            ST3.Text = "00"
            ST4.Text = "00"
            END1.Text = "00"
            END2.Text = "00"
            END3.Text = "00"
            END4.Text = "00"
            DUR_1.Text = "00"
            DUR_2.Text = "00"
            DUR_3.Text = "00"
            DUR_4.Text = "00"

        End If

        txt_StartTime.Text = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
        txt_EndTime.Text = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text
        txt_TimeDuration.Text = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
    End Sub
    Protected Sub bttnSaveProgramInfo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveProgramInfo.Click

        lblErr.Text = String.Empty
        Calculate()

        If txtSlugDate.Text <> "" Then
            txt_ReporterSlug.Text = txt_ReporterSlug.Text + " , Date: " + txtSlugDate.Text
        End If
        If txtSlugDate.Text <> "" Then
            If txt_UrduScript.Text <> "N/A" Then
                txt_UrduScript.Text = txt_UrduScript.Text + " , Date: " + txtSlugDate.Text
            End If
        End If

        If txtSlugDate.Text <> "" Then
            If txt_EnglishScript.Text <> "" Then
                txt_EnglishScript.Text = txt_EnglishScript.Text + " , Date: " + txtSlugDate.Text
            End If
        End If



        ''**********************************''
        ''*********** Save Slug ************''
        ''**********************************''

        Dim col1_ps As DataColumn = New DataColumn("ReporterSlug")
        col1_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("ReporterSlug")

        Dim col2_ps As DataColumn = New DataColumn("ProposedSlug")
        col2_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("ProposedSlug")

        Dim col3_ps As DataColumn = New DataColumn("ReporterID")
        col3_ps.DataType = System.Type.GetType("System.Int32")
        dt_ProgramInfo.Columns.Add("ReporterID")

        Dim col4_ps As DataColumn = New DataColumn("CameraManID")
        col4_ps.DataType = System.Type.GetType("System.Int32")
        dt_ProgramInfo.Columns.Add("CameraManID")

        Dim col5_ps As DataColumn = New DataColumn("EnglishScript")
        col5_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("EnglishScript")

        Dim col6_ps As DataColumn = New DataColumn("UrduScript")
        col6_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("UrduScript")

        Dim col7_ps As DataColumn = New DataColumn("Duration")
        col7_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("Duration")

        Dim col8_ps As DataColumn = New DataColumn("StartTime")
        col8_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("StartTime")

        Dim col9_ps As DataColumn = New DataColumn("EndTime")
        col9_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("EndTime")

        Dim col10_ps As DataColumn = New DataColumn("TapeContentDetail_News_ID")
        col10_ps.DataType = System.Type.GetType("System.Int32")
        dt_ProgramInfo.Columns.Add("TapeContentDetail_News_ID")

        Dim col11_ps As DataColumn = New DataColumn("TapeSlugID")
        col11_ps.DataType = System.Type.GetType("System.Int32")
        dt_ProgramInfo.Columns.Add("TapeSlugID")

        Dim col12_ps As DataColumn = New DataColumn("Reporter")
        col12_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("Reporter")

        Dim col13_ps As DataColumn = New DataColumn("CameraMan")
        col13_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("CameraMan")

        Dim col14_ps As DataColumn = New DataColumn("EntryDate")
        col14_ps.DataType = System.Type.GetType("System.DateTime")
        dt_ProgramInfo.Columns.Add("EntryDate")

        Dim col15_ps As DataColumn = New DataColumn("HighResolution")
        col15_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("HighResolution")

        Dim col16_ps As DataColumn = New DataColumn("LowResolution")
        col16_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("LowResolution")

        Dim col17_ps As DataColumn = New DataColumn("FilePath")
        col17_ps.DataType = System.Type.GetType("System.String")
        dt_ProgramInfo.Columns.Add("FilePath")

        If ViewState("ProgramInfo_Table") Is Nothing Then
        Else
            dt_ProgramInfo = ViewState("ProgramInfo_Table")
        End If

        If txt_dgProgramInfo_Index.Text <> "" Then

            ''*************************************''
            ''********** Get Reporter ID **********''
            ''*************************************''

            Dim ReporterID As Integer
            Dim objReporterID As New BusinessFacade.TapeContent_News()
            objReporterID.ReporterName = txt_Reporter.Text
            ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

            ''*************************************''
            ''*********** Get Reporter ID *********''
            ''*************************************''

            Dim CameraManID As Integer
            Dim objCameraManID As New BusinessFacade.TapeContent_News()
            objCameraManID.ReporterName = txt_CameraMan.Text
            CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)

            ''*************************************''

            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterSlug") = txt_ReporterSlug.Text
            If txt_ProposedSlug.Text = " " Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = txt_ProposedSlug.Text
            End If

            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterID") = ReporterID
            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("CameraManID") = CameraManID

            If Trim(txt_EnglishScript.Text) = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = txt_EnglishScript.Text
            End If

            If Trim(txt_UrduScript.Text) = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text
            End If

            Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
            If TDuration = ":::" Then
                TDuration = "00:00:00:00"
            End If
            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = TDuration

            Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
            Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime1.Text
            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_Endtime1.Text

            If txtEntryDate.Text = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = Date.Now.ToString("dd-MMM-yyyy")
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = txtEntryDate.Text
            End If

            If txtHighResFileName.Text = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("HighResolution") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("HighResolution") = txtHighResFileName.Text
            End If

            If txtLowResFileName.Text = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("LowResolution") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("LowResolution") = txtLowResFileName.Text
            End If

            If txtFiePath.Text = "" Then
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("FilePath") = "N/A"
            Else
                dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("FilePath") = txtFiePath.Text
            End If

            dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
            txt_dgProgramInfo_Index.Text = String.Empty


        Else

            ''*******************************************''
            ''******* Check Already Entered Slug ********''
            ''*******************************************''

            Dim isExists As Integer
            isExists = CheckSlug(txt_ReporterSlug.Text)
            If isExists <> 0 Then
                lblErr.Text = "Attention : This Slug Already Exists!!"
                Exit Sub
            End If

            ''*************************************''
            ''********** Get Reporter ID **********''
            ''*************************************''

            Dim ReporterID As Integer
            Dim objReporterID As New BusinessFacade.TapeContent_News()
            objReporterID.ReporterName = txt_Reporter.Text
            ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

            ''*************************************''
            ''*********** Get Reporter ID *********''
            ''*************************************''

            Dim CameraManID As Integer
            Dim objCameraManID As New BusinessFacade.TapeContent_News()
            objCameraManID.ReporterName = txt_CameraMan.Text
            CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)


            If txt_ReporterSlug.Text = "" Then
                Err_ReporterSlug.Visible = True
            Else
                Dim row_PS As DataRow
                row_PS = dt_ProgramInfo.NewRow()
                row_PS.Item("ReporterSlug") = txt_ReporterSlug.Text
                If txt_ProposedSlug.Text = "" Then
                    row_PS.Item("ProposedSlug") = "N/A"
                Else
                    row_PS.Item("ProposedSlug") = txt_ProposedSlug.Text
                End If
                row_PS.Item("ReporterID") = ReporterID
                row_PS.Item("CameraManID") = CameraManID

                If txt_EnglishScript.Text = "" Then
                    row_PS.Item("EnglishScript") = "N/A"
                Else
                    row_PS.Item("EnglishScript") = txt_EnglishScript.Text
                End If

                If txt_UrduScript.Text = "" Then
                    row_PS.Item("UrduScript") = "N/A"
                Else
                    row_PS.Item("UrduScript") = txt_UrduScript.Text
                End If


                Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                If TDuration = ":::" Then
                    TDuration = "00:00:00:00"
                End If
                row_PS.Item("Duration") = TDuration

                Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                row_PS.Item("StartTime") = txt_StartTime1.Text
                row_PS.Item("EndTime") = txt_Endtime1.Text

                row_PS.Item("TapeContentDetail_News_ID") = DBNull.Value
                row_PS.Item("TapeSlugID") = DBNull.Value
                row_PS.Item("Reporter") = txt_Reporter.Text
                row_PS.Item("CameraMan") = txt_CameraMan.Text
                If txtEntryDate.Text = "" Then
                    row_PS.Item("EntryDate") = Date.Now.ToString("dd-MMM-yyyy")
                Else
                    row_PS.Item("EntryDate") = txtEntryDate.Text
                End If

                row_PS.Item("HighResolution") = txtHighResFileName.Text
                row_PS.Item("LowResolution") = txtLowResFileName.Text
                row_PS.Item("FilePath") = txtFiePath.Text

                dt_ProgramInfo.Rows.Add(row_PS)


            End If

        End If

        ViewState("ProgramInfo_Table") = dt_ProgramInfo
        BindGrid_ProgramInfo()
        FillStartTime()
        Clrscr_ProgramInfo()

        ''************** End ***************''
        ''**********************************''
        ClearSlug()
        FillStartTime()
        txt_Endtime1.Text = "00:00:00:00"
    End Sub
    Private Function CheckSlug(ByVal TapeSlug As String)
        Dim Count As Integer = 0
        Dim I As Integer
        For I = 0 To dt_ProgramInfo.Rows.Count - 1
            If Server.HtmlDecode(dg_programInfo.Rows(I).Cells(1).Text) = TapeSlug Then
                Count = Count + 1
            End If
        Next
        Return Count
    End Function
    Private Sub BindGrid_ProgramInfo()

        dg_programInfo.DataSource = dt_ProgramInfo
        dg_programInfo.DataBind()

    End Sub
    Private Sub Clrscr_ProgramInfo()

        'txt_ReporterSlug.Text = String.Empty
        ' txt_ProposedSlug.Text = String.Empty
        txt_EnglishScript.Text = String.Empty
        txt_UrduScript.Text = String.Empty
        txt_StartTime.Text = String.Empty
        txt_EndTime.Text = String.Empty
        txt_TimeDuration.Text = String.Empty
        txtHighResFileName.Text = "N/A"
        txtLowResFileName.Text = "N/A"
        txtFiePath.Text = "N/A"
    End Sub
    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click

        lblErr_MasterEntry.Text = String.Empty
        Err_ReporterSlug.Visible = False
        Err_Department.Visible = False
        Err_TapeNumber.Visible = False

        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''*********************************************''
        ''************ Get TapeLibraryID **************''
        ''*********************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        ''*********************************************''
        ''************* Get DepartmentID **************''
        ''*********************************************''

        Dim DepartmentID As Integer
        Dim objDeptID As New BusinessFacade.TapeIssuance()
        objDeptID.DepartmentName = txt_Department.Text
        DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

        ''*********************************************''
        ''*************** Get User ID *****************''
        ''*********************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''*********************************************''
        ''***************** Get User ID ***************''
        ''*********************************************''

        Dim UserName As String
        Dim objUserName As New BusinessFacade.Employee()
        objUserName.EmployeeID = UserID
        UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)


        Dim TapeContentID As Integer

        If DepartmentID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Department !!"
            Err_Department.Visible = True
        ElseIf LibraryID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Tape Number !!"
            Err_TapeNumber.Visible = True
        ElseIf dg_programInfo.Rows.Count = 0 Then
            Err_ReporterSlug.Visible = True
        Else

            ''****************************************************''
            ''**************** Tape Content Save *****************''
            ''****************************************************''

            If txt_TapeContentID.Text = "" Then

                Dim ObjUser As New BusinessFacade.TapeContent()
                ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                'ObjUser.DepartmentID = ddl_Department.SelectedValue
                ObjUser.DepartmentID = DepartmentID
                ObjUser.isCurrentContent = 0
                ObjUser.ClassificationCode = txt_ClassificationCode.Text
                ObjUser.CallNo = txt_CallNo.Text
                ObjUser.LocationCode = txt_LocationCode.Text
                ObjUser.IsActive = 1
                ObjUser.Copies = ddlCopies.SelectedValue
                TapeContentID = ObjUser.SaveRecord()
                txt_TapeContentID.Text = TapeContentID
            Else
                Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                'ObjUpdateUser.DepartmentID = ddl_Department.SelectedValue
                ObjUpdateUser.DepartmentID = DepartmentID
                ObjUpdateUser.isCurrentContent = 0
                ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                ObjUpdateUser.CallNo = txt_CallNo.Text
                ObjUpdateUser.LocationCode = txt_LocationCode.Text
                ObjUpdateUser.IsActive = 1
                ObjUpdateUser.Copies = ddlCopies.SelectedValue
                Dim O As Integer = ObjUpdateUser.UpdateRecord()
                TapeContentID = txt_TapeContentID.Text

            End If

            ''***************************************************************''
            ''********************* TapeContentDetail News ******************''
            ''***************************************************************''

            Dim SlugID As Integer
            Dim SlugLibraryID As Integer
            Dim TapeContentDetailID As Integer
            Dim P As Integer
            For P = 0 To dg_programInfo.Rows.Count - 1
                If dg_programInfo.Rows(P).Cells(10).Text = "&nbsp;" Then

                    ''*************************************************''
                    ''**************** Tape Slug **********************''
                    ''*************************************************''

                    Dim ObjSlug As New BusinessFacade.TapeContent_News()
                    ObjSlug.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                    ObjSlug.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                    ObjSlug.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjSlug.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjSlug.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                    ObjSlug.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                    ObjSlug.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                    ObjSlug.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                    ObjSlug.Duration = dg_programInfo.Rows(P).Cells(9).Text
                    ObjSlug.ProducedBy = UserID
                    SlugID = ObjSlug.TapeSlug_SaveRecord()

                    ''***************************************************''
                    ''*************** Insert in Slug Library ************''
                    ''***************************************************''

                    Dim ObjSlugLibrary As New BusinessFacade.TapeContent_News()
                    ObjSlugLibrary.SlugID = SlugID
                    'ObjSlugLibrary.TapeLibraryID = ddl_TapeNo.SelectedValue
                    ObjSlugLibrary.TapeLibraryID = LibraryID
                    ObjSlugLibrary.ProducedBy = 3
                    SlugLibraryID = ObjSlugLibrary.TapeSlugLibrary_SaveRecord()

                    ''***************************************************''
                    ''************* TapeContent Detail Save *************''
                    ''***************************************************''

                    Dim ObjDetail As New BusinessFacade.TapeContent_News()
                    ObjDetail.TapeContentID = TapeContentID
                    'ObjDetail.TapeLibraryID = ddl_TapeNo.SelectedValue
                    ObjDetail.TapeLibraryID = LibraryID
                    ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetail.MergeID = 1
                    ObjDetail.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjDetail.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjDetail.ProducedBy = UserID
                    ObjDetail.ProducedByName = UserName
                    ObjDetail.SlugID = SlugID
                    ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()

                    ''****************************************************''
                    ''*************** Slug Vs Keyword Save ***************''
                    ''****************************************************''

                    Dim q As Integer
                    For q = 0 To dg_KeyWord_2.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(q).Cells(4).Text Then
                            If dg_KeyWord_2.Rows(q).Cells(3).Text = "&nbsp;" Then
                                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                objtest2.NewsKeywordID = dg_KeyWord_2.Rows(q).Cells(2).Text
                                objtest2.TapeContentDetail_News_ID = TapeContentDetailID
                                objtest2.TapeSlugID = SlugID
                                objtest2.SlugVskeyword_SaveRecord()
                            End If
                        End If
                    Next

                    ''******************************************************''
                    ''******************** Footages ************************''
                    ''******************************************************''

                    Dim TapeContentFootage1 As Integer
                    Dim y As Integer
                    For y = 0 To dg_Footage.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(y).Cells(5).Text Then
                            '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                            Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                            'ObjTapeContentFootage.TapeContentID = txt_TapeContentID.Text
                            ObjTapeContentFootage.TapeContentID = TapeContentDetailID
                            ObjTapeContentFootage.FootageID = dg_Footage.Rows(y).Cells(2).Text
                            ObjTapeContentFootage.userID = 3
                            ObjTapeContentFootage.SlugID = SlugID
                            TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                        End If
                    Next
                Else

                    ''*********************************************************************''
                    ''******************* Tape Content Detail News (Update) ***************''
                    ''*********************************************************************''

                    Dim ObjDetailUpd As New BusinessFacade.TapeContent_News()
                    ObjDetailUpd.TapeContentID = txt_TapeContentID.Text
                    'ObjDetailUpd.TapeLibraryID = ddl_TapeNo.SelectedValue
                    ObjDetailUpd.TapeLibraryID = LibraryID
                    ObjDetailUpd.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetailUpd.ProducedBy = 3
                    ObjDetailUpd.MergeID = -1
                    ObjDetailUpd.ProducedBy = 3
                    ObjDetailUpd.ProducedByName = "Asmatullah"
                    ObjDetailUpd.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    ObjDetailUpd.TapeContentDetailID = dg_programInfo.Rows(P).Cells(10).Text
                    ObjDetailUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                    Dim ID As Integer
                    ID = ObjDetailUpd.UpdateRecord_TapeContentDetail_News()


                    ''*********************************************************************''
                    ''************************ Tape Slug (Update) *************************''
                    ''*********************************************************************''

                    Dim ObjSlugUpd As New BusinessFacade.TapeContent_News()
                    ObjSlugUpd.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                    ObjSlugUpd.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                    ObjSlugUpd.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjSlugUpd.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjSlugUpd.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                    ObjSlugUpd.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                    ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(8).Text
                    ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(7).Text
                    ObjSlugUpd.Duration = dg_programInfo.Rows(P).Cells(9).Text
                    ObjSlugUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                    ObjSlugUpd.ProducedBy = 3
                    Dim Slug_ID As Integer
                    Slug_ID = ObjSlugUpd.TapeSlug_UpdateRecord()

                    ''***********************************************''
                    ''**************** Slug Vs Keyword **************''
                    ''***********************************************''

                    Dim z As Integer
                    For z = 0 To dg_KeyWord_2.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(z).Cells(4).Text Then
                            If dg_KeyWord_2.Rows(z).Cells(3).Text = "&nbsp;" Then

                                ''*************************************''
                                ''******* Slug Vs Keyword (Save) ******''
                                ''*************************************''

                                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                objtest2.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                objtest2.TapeContentDetail_News_ID = ID
                                objtest2.TapeSlugID = Slug_ID
                                objtest2.SlugVskeyword_SaveRecord()
                            Else

                                ''*************************************''
                                ''***** Slug Vs Keyword (Update) ******''
                                ''*************************************''

                                Dim ObjUpdateKW As New BusinessFacade.SlugVsKeyword()
                                ObjUpdateKW.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                ObjUpdateKW.SlugVsKeywordID = dg_KeyWord_2.Rows(z).Cells(3).Text
                                ObjUpdateKW.TapeSlugID = Slug_ID
                                ObjUpdateKW.SlugVskeyword_Update()
                            End If
                        End If
                    Next

                    ''***********************************************''
                    ''**************** Slug Vs Footages *************''
                    ''***********************************************''

                    Dim l As Integer
                    For l = 0 To dg_Footage.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(l).Cells(5).Text Then
                            If dg_Footage.Rows(l).Cells(3).Text = "&nbsp;" Then

                                ''*************************************''
                                ''******* Slug Vs Keyword (Save) ******''
                                ''*************************************''

                                '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                ObjTapeContentFootage.TapeContentID = ID 'txt_TapeContentID.Text
                                ObjTapeContentFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                ObjTapeContentFootage.userID = 3
                                ObjTapeContentFootage.SlugID = Slug_ID
                                ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                            Else

                                ''*************************************''
                                ''***** Slug Vs Keyword (Update) ******''
                                ''*************************************''

                                'Dim ObjUpdateKW As New BusinessFacade.SlugVsKeyword()
                                'ObjUpdateKW.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                'ObjUpdateKW.SlugVsKeywordID = dg_KeyWord_2.Rows(z).Cells(3).Text
                                'ObjUpdateKW.TapeSlugID = Slug_ID
                                'ObjUpdateKW.SlugVskeyword_Update()
                                '''''''''''''' Update Footage '''''''''''''''''''''''''''''''''''
                                Dim ObjUpdateFootage As New BusinessFacade.TapeContent_News()
                                ObjUpdateFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                ObjUpdateFootage.TapeContentFootageID = dg_Footage.Rows(l).Cells(3).Text
                                ObjUpdateFootage.Update_Footage_TapeContent()

                            End If
                        End If
                    Next


                End If
            Next

            ''********************************************************''
            ''******************** Merge Tapes ***********************''
            ''********************************************************''

            Dim U As Integer
            Dim Cnt As Integer
            For Cnt = 0 To dg_Merge.Rows.Count - 1

                ''******************************************''
                ''********** Merge Tape Content ************''
                ''******************************************''

                Dim objMerge As New BusinessFacade.TapeContent_News()
                'objMerge.MasterID = TapeContentID
                objMerge.MasterID = txt_TapeContentID.Text
                objMerge.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                U = objMerge.SaveRecord_MergeTapeContentNews()

                ''*****************************************''
                ''********* Merge Tape Content Detail *****''
                ''*****************************************''

                Dim Y As Integer
                Dim ObjMergeDetail As New BusinessFacade.TapeContent_News()
                ObjMergeDetail.DetailID = TapeContentDetailID
                ObjMergeDetail.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                Y = ObjMergeDetail.MergeTapeContentDetail_News_SaveRecord()

                Dim MergeTapeIssuanceID As Integer
                Dim ObjGetTapeIssuanceID As New BusinessFacade.TapeContent()
                ObjGetTapeIssuanceID.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                MergeTapeIssuanceID = ObjGetTapeIssuanceID.GetTapeIssuanceID()


                If U <> 0 Then

                    'Dim ObjIsactive As New BusinessFacade.TapeContent_News()
                    'ObjIsactive.TapeContentID = U
                    'ObjIsactive.TapeContent_News_Update()

                    Dim ObjTapeContentDetail As New BusinessFacade.TapeContent_News()
                    ObjTapeContentDetail.TapeContentID = TapeContentID
                    ObjTapeContentDetail.OldTapeContentID = U
                    ObjTapeContentDetail.TapeContentDetail_News_Update()

                    ''***************************************************''
                    ''************** Make Copy of Record ****************''
                    ''***************************************************''

                    'Dim TempTable As DataTable
                    'Dim ObjMakeCopy As New BusinessFacade.TapeContent_News()
                    ' ''ObjMakeCopy.TapeContentID = txt_TapeContentID.Text
                    'ObjMakeCopy.TapeContentID = MergeTapeIssuanceID
                    ''TempTable = ObjMakeCopy.TapeContentDetail_News_CopyRecord()

                    ''Dim i As Integer
                    ''For i = 0 To TempTable.Rows.Count - 1
                    ''    Dim ObjPrgVsKw As New BusinessFacade.TapeContent_News()
                    ''    ObjPrgVsKw.TapeLibraryID = dg_Merge.Rows(Cnt).Cells(0).Text
                    ''    ObjPrgVsKw.TapeContentDetailID = TempTable.Rows(i).Item(0).ToString
                    ''    ObjPrgVsKw.GetSlugVsKeyword()
                    ''Next
                End If
            Next

            ''************************************************''
            ''************** Update IsActive *****************''
            ''************************************************''

            If U <> 0 Then

                'Dim ObjIsactive As New BusinessFacade.TapeContent_News()
                'ObjIsactive.TapeContentID = U
                'ObjIsactive.TapeContent_News_Update()

                Dim ObjTapeContentDetail As New BusinessFacade.TapeContent_News()
                ObjTapeContentDetail.TapeContentID = TapeContentID
                ObjTapeContentDetail.OldTapeContentID = U
                ObjTapeContentDetail.TapeContentDetail_News_Update()

                ' ''***************************************************''
                ' ''************** Make Copy of Record ****************''
                ' ''***************************************************''

                'Dim TempTable As DataTable
                'Dim ObjMakeCopy As New BusinessFacade.TapeContent()
                ''ObjMakeCopy.TapeContentID = txt_TapeContentID.Text
                'ObjMakeCopy.TapeContentID = MergeTapeIssuanceID
                'TempTable = ObjMakeCopy.TapeContentDetail_CopyRecord()

            End If


            '''''''''''''''''''' KeyWords'''''''''''''''''''''''''''''''
            Dim K As Integer
            For K = 0 To dg_KeyWord.Rows.Count - 1

                If dg_KeyWord.Rows(K).Cells(3).Text = "&nbsp;" Then

                    '''''''''''' Insert KeyWords '''''''''''''''''''''''

                    Dim ObjKeyword As New BusinessFacade.TapeContent_News()
                    ObjKeyword.SubContentTypeID = 1
                    ObjKeyword.NewsKeyWord = dg_KeyWord.Rows(K).Cells(2).Text
                    ObjKeyword.KeyType = dg_KeyWord.Rows(K).Cells(1).Text
                    Dim KeyWordID As Integer
                    KeyWordID = ObjKeyword.SaveRecord_TapeContentNewKeyWords()

                    '''''''''' Insert in TapeContentKeywords '''''''''''

                    Dim ObjTapeKeyWord As New BusinessFacade.TapeContent_News()
                    ObjTapeKeyWord.MasterID = TapeContentID
                    ObjTapeKeyWord.NewKeywordID = KeyWordID
                    ObjTapeKeyWord.ProducedBy = 3  'UserID
                    Dim J As Integer
                    J = ObjTapeKeyWord.TapeContentNewsKeywords_SaveRecord()
                Else

                    '''''''''''''''' Update KeyWords '''''''''''''''''''    

                    Dim ObjUpdate As New BusinessFacade.TapeContent_News()
                    ObjUpdate.NewKeywordID = dg_KeyWord.Rows(K).Cells(3).Text
                    ObjUpdate.SubContentTypeID = 1
                    ObjUpdate.NewsKeyWord = dg_KeyWord.Rows(K).Cells(2).Text
                    ObjUpdate.KeyType = dg_KeyWord.Rows(K).Cells(1).Text
                    ObjUpdate.TapeContent_NewsKeyword_Update()

                End If
            Next

            '''''''''''''''''''''''' Footage '''''''''''''''''''''''''''''''
            'Dim X As Integer
            'Dim TapeContentFootage As Integer
            'For X = 0 To dg_Footage.Rows.Count - 1
            '    If dg_Footage.Rows(X).Cells(3).Text = "&nbsp;" Then

            '        '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
            '        Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
            '        ObjTapeContentFootage.TapeContentID = txt_TapeContentID.Text
            '        ObjTapeContentFootage.FootageID = dg_Footage.Rows(X).Cells(2).Text
            '        ObjTapeContentFootage.userID = 3
            '        TapeContentFootage = ObjTapeContentFootage.SaveRecord_TapeContentFootage()

            '    Else

            '        '''''''''''''' Update Footage '''''''''''''''''''''''''''''''''''
            '        Dim ObjUpdateFootage As New BusinessFacade.TapeContent_News()
            '        ObjUpdateFootage.FootageID = dg_Footage.Rows(X).Cells(2).Text
            '        ObjUpdateFootage.TapeContentFootageID = dg_Footage.Rows(X).Cells(3).Text
            '        ObjUpdateFootage.Update_Footage_TapeContent()

            '    End If        

            'Next

            lblErr.Text = "Record has been Saved Successfully !!"

            If txt_TapeContentID.Text <> "" Then

                ''**************************************************************''
                ''***************** Search Engine Save Record ******************''
                ''**************************************************************''

                Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                ObjSearchEngine.TapeContentID = LibraryID 'txt_TapeContentID.Text
                ObjSearchEngine.SearchEngine_News_SaveRecord()
            End If

            ''***************************************''
            ''*********** Fill Grids ****************''
            ''***************************************''

            Me.Fill_ProgramInfo_Table(txt_TapeContentID.Text)
            Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
            Me.Fill_Footage_Table(txt_TapeContentID.Text)

            ''**************** End ******************''
            ''***************************************''

        End If

    End Sub
    Protected Sub dg_KeyWord_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_KeyWord.SelectedIndexChanged

        Dim Index As Integer
        Index = dg_KeyWord.SelectedIndex.ToString
        ddl_KeywordType.SelectedItem.Text = dg_KeyWord.Rows(Index).Cells(1).Text
        txt_KeyWord.Text = dg_KeyWord.Rows(Index).Cells(2).Text
        txt_dgKeyword_Index.Text = Index

    End Sub
    Protected Sub dg_Footage_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Footage.SelectedIndexChanged

        Dim Ind As Integer
        Ind = dg_Footage.SelectedIndex.ToString
        'lstFootageType.SelectedValue = dg_Footage.Rows(Ind).Cells(2).Text
        FT1.Text = Server.HtmlDecode(dg_Footage.Rows(Ind).Cells(1).Text)
        txt_ReporterSlug.Text = Server.HtmlDecode(dg_Footage.Rows(Ind).Cells(5).Text)
        txt_dgFootage_Index.Text = Ind

        If txt_dgFootage_Index.Text <> "" Then
            TabContainer1.ActiveTabIndex = CInt(2)
        End If

    End Sub
    Protected Sub dg_programInfo_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_programInfo.SelectedIndexChanged
        txtSlugDate.Text = String.Empty
        Dim IndProg As Integer
        IndProg = dg_programInfo.SelectedIndex.ToString
        txt_ReporterSlug.Text = Server.HtmlDecode(dg_programInfo.Rows(IndProg).Cells(1).Text)

        txt_ProposedSlug.Text = Server.HtmlDecode(dg_programInfo.Rows(IndProg).Cells(2).Text)
        txt_Reporter.Text = dg_programInfo.Rows(IndProg).Cells(12).Text
        txt_CameraMan.Text = dg_programInfo.Rows(IndProg).Cells(13).Text
        txt_EnglishScript.Text = Server.HtmlDecode(dg_programInfo.Rows(IndProg).Cells(5).Text)
        txt_UrduScript.Text = dg_programInfo.Rows(IndProg).Cells(6).Text

        '  txt_StartTime.Text = dg_programInfo.Rows(IndProg).Cells(7).Text
        ' txt_EndTime.Text = dg_programInfo.Rows(IndProg).Cells(8).Text
        txt_StartTime1.Text = dg_programInfo.Rows(IndProg).Cells(8).Text
        txt_Endtime1.Text = dg_programInfo.Rows(IndProg).Cells(7).Text

        txt_TimeDuration.Text = dg_programInfo.Rows(IndProg).Cells(9).Text

        txtEntryDate.Text = dg_programInfo.Rows(IndProg).Cells(14).Text

        txtHighResFileName.Text = dg_programInfo.Rows(IndProg).Cells(16).Text
        txtLowResFileName.Text = dg_programInfo.Rows(IndProg).Cells(17).Text
        txtFiePath.Text = dg_programInfo.Rows(IndProg).Cells(18).Text

        txt_dgProgramInfo_Index.Text = IndProg

        If txt_ProposedSlug.Text = "&nbsp;" Then
            txt_ProposedSlug.Text = "N/A"
        End If
        If txt_Reporter.Text = "&nbsp;" Then
            txt_Reporter.Text = "N/A"
        End If

        If txt_CameraMan.Text = "&nbsp;" Then
            txt_CameraMan.Text = "N/A"
        End If

        If txt_EnglishScript.Text = "&nbsp;" Then
            txt_EnglishScript.Text = "N/A"
        End If

        If txt_UrduScript.Text = "&nbsp;" Then
            txt_UrduScript.Text = "N/A"
        End If


        Dim time1 As String
        time1 = txt_StartTime1.Text()
        Dim Start As Array = time1.Split(":")
        If Start.Length = 4 Then
            ST1.Text = Start(0).ToString
            ST2.Text = Start(1).ToString
            ST3.Text = Start(2).ToString
            ST4.Text = Start(3).ToString
        Else
            ST1.Text = "00"
            ST2.Text = "00"
            ST3.Text = "00"
            ST4.Text = "00"
        End If

        'ST4.Text = "00"

        Dim time2 As String
        time2 = txt_Endtime1.Text()
        Dim EndT As Array = time2.Split(":")
        If EndT.Length = 4 Then
            END1.Text = EndT(0).ToString
            END2.Text = EndT(1).ToString
            END3.Text = EndT(2).ToString
            END4.Text = EndT(3).ToString
        Else
            END1.Text = "00"
            END2.Text = "00"
            END3.Text = "0"
            END4.Text = "0"
        End If

        'END4.Text = "00"

        Dim Durat As String
        Durat = txt_EndTime.Text
        Dim Dur As Array = time2.Split(":")
        If Dur.Length = 4 Then
            DUR_1.Text = Dur(0).ToString
            DUR_2.Text = Dur(1).ToString
            DUR_3.Text = Dur(2).ToString
            DUR_4.Text = Dur(3).ToString
        Else
            DUR_1.Text = "0"
            DUR_2.Text = "0"
            DUR_3.Text = "0"
            DUR_4.Text = "0"
        End If

        'DUR_4.Text = "00"

        'If lblQueryString.Text <> "" Then
        '    '    TabContainer1.ActiveTab.TabIndex = TabContainer1.OnClientActiveTabChanged.IndexOf(2)
        '    '    TabContainer1.OnClientActiveTabChanged.IndexOf(2)
        '    TabContainer1.ActiveTab.HeaderText = "Slug Information"
        'End If


        'TabContainer1.TabIndex = 2

        If txt_dgProgramInfo_Index.Text <> "" Then
            TabContainer1.ActiveTabIndex = CInt(2)
        End If


    End Sub
    Private Sub Bind_dgKeyWord_2()

        dg_KeyWord_2.DataSource = Dt_SaveKeyword_2
        dg_KeyWord_2.DataBind()

    End Sub
    Protected Sub dg_KeyWord_2_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_KeyWord_2.SelectedIndexChanged

        Dim Index As Integer
        Index = dg_KeyWord_2.SelectedIndex.ToString
        'lstKeyword.SelectedValue = dg_KeyWord_2.Rows(Index).Cells(2).Text
        KW1.Text = Server.HtmlDecode(dg_KeyWord_2.Rows(Index).Cells(5).Text)
        txt_ReporterSlug.Text = Server.HtmlDecode(dg_KeyWord_2.Rows(Index).Cells(4).Text)
        txt_dgKeyword_Index.Text = Index

        If txt_dgKeyword_Index.Text <> "" Then
            TabContainer1.ActiveTabIndex = CInt(2)
        End If

    End Sub
    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkHomePage.Click

        Response.Redirect("../Home/Home.aspx")

    End Sub

    Protected Sub LnkArchival_News_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkArchival_News.Click

        Response.Redirect("FrmArchiveEntry_News_Lister.aspx")

    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('AddNewsKeyword.aspx" + "', 'mywindow', 'width=850, height=350' ); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)
    End Sub

    Protected Sub lnkCheck_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkCheck.Click
        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        Dim T As String
        Dim objCheck As New BusinessFacade.TapeContent_News()
        objCheck.TapeNumber = txt_TapeNumber.Text
        T = objCheck.CheckNewsTapeArchived(objCheck.TapeNumber)
        If T <> "0" Then
            lblErr.Text = "This Tape already has been Archived !!"
        Else
            lblErr.Text = "This Tape is not Archived Previously !!"
        End If
    End Sub

    'Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

    '    ''****************************************''

    '    Dim arr As Array = Split(txt_TapeNumber.Text, "#")
    '    If arr.Length = 2 Then
    '        txt_TapeNumber.Text = arr(1)
    '    End If


    '    ''*************************************''
    '    ''************* Slug Table ************''
    '    ''*************************************''

    '    Dim col1_ps As DataColumn = New DataColumn("ReporterSlug")
    '    col1_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("ReporterSlug")

    '    Dim col2_ps As DataColumn = New DataColumn("ProposedSlug")
    '    col2_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("ProposedSlug")

    '    Dim col3_ps As DataColumn = New DataColumn("ReporterID")
    '    col3_ps.DataType = System.Type.GetType("System.Int32")
    '    dt_ProgramInfo.Columns.Add("ReporterID")

    '    Dim col4_ps As DataColumn = New DataColumn("CameraManID")
    '    col4_ps.DataType = System.Type.GetType("System.Int32")
    '    dt_ProgramInfo.Columns.Add("CameraManID")

    '    Dim col5_ps As DataColumn = New DataColumn("EnglishScript")
    '    col5_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("EnglishScript")

    '    Dim col6_ps As DataColumn = New DataColumn("UrduScript")
    '    col6_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("UrduScript")

    '    Dim col8_ps As DataColumn = New DataColumn("StartTime")
    '    col8_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("StartTime")

    '    Dim col9_ps As DataColumn = New DataColumn("EndTime")
    '    col9_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("EndTime")

    '    Dim col7_ps As DataColumn = New DataColumn("Duration")
    '    col7_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("Duration")

    '    Dim col10_ps As DataColumn = New DataColumn("TapeContentDetail_News_ID")
    '    col10_ps.DataType = System.Type.GetType("System.Int32")
    '    dt_ProgramInfo.Columns.Add("TapeContentDetail_News_ID")

    '    Dim col11_ps As DataColumn = New DataColumn("TapeSlugID")
    '    col11_ps.DataType = System.Type.GetType("System.Int32")
    '    dt_ProgramInfo.Columns.Add("TapeSlugID")

    '    Dim col12_ps As DataColumn = New DataColumn("Reporter")
    '    col12_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("Reporter")

    '    Dim col13_ps As DataColumn = New DataColumn("CameraMan")
    '    col13_ps.DataType = System.Type.GetType("System.String")
    '    dt_ProgramInfo.Columns.Add("CameraMan")


    '    If ViewState("ProgramInfo_Table") Is Nothing Then
    '    Else
    '        dt_ProgramInfo = ViewState("ProgramInfo_Table")
    '    End If

    '    If txt_dgProgramInfo_Index.Text <> "" Then

    '        ''*************************************''
    '        ''********** Get Reporter ID **********''
    '        ''*************************************''

    '        Dim ReporterID As Integer
    '        Dim objReporterID As New BusinessFacade.TapeContent_News()
    '        objReporterID.ReporterName = txt_Reporter.Text
    '        ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

    '        ''*************************************''
    '        ''*********** Get Reporter ID *********''
    '        ''*************************************''

    '        Dim CameraManID As Integer
    '        Dim objCameraManID As New BusinessFacade.TapeContent_News()
    '        objCameraManID.ReporterName = txt_CameraMan.Text
    '        CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)

    '        ''*************************************''

    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterSlug") = txt_ReporterSlug.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = txt_ProposedSlug.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterID") = ReporterID
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("CameraManID") = CameraManID
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = txt_EnglishScript.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = txt_TimeDuration.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_EndTime.Text
    '        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
    '        txt_dgProgramInfo_Index.Text = String.Empty

    '    Else

    '        ''*************************************''
    '        ''********** Get Reporter ID **********''
    '        ''*************************************''

    '        Dim ReporterID As Integer
    '        Dim objReporterID As New BusinessFacade.TapeContent_News()
    '        objReporterID.ReporterName = txt_Reporter.Text
    '        ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

    '        ''*************************************''
    '        ''*********** Get Reporter ID *********''
    '        ''*************************************''

    '        Dim CameraManID As Integer
    '        Dim objCameraManID As New BusinessFacade.TapeContent_News()
    '        objCameraManID.ReporterName = txt_CameraMan.Text
    '        CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)

    '        If txt_ReporterSlug.Text = "" Then
    '            Err_ReporterSlug.Visible = True
    '        Else
    '            Dim row_PS As DataRow
    '            row_PS = dt_ProgramInfo.NewRow()
    '            row_PS.Item("ReporterSlug") = txt_ReporterSlug.Text
    '            row_PS.Item("ProposedSlug") = txt_ProposedSlug.Text
    '            row_PS.Item("ReporterID") = ReporterID
    '            row_PS.Item("CameraManID") = CameraManID
    '            row_PS.Item("EnglishScript") = txt_EnglishScript.Text
    '            row_PS.Item("UrduScript") = txt_UrduScript.Text
    '            row_PS.Item("Duration") = txt_TimeDuration.Text
    '            row_PS.Item("StartTime") = txt_EndTime.Text
    '            row_PS.Item("EndTime") = txt_StartTime.Text
    '            row_PS.Item("TapeContentDetail_News_ID") = DBNull.Value
    '            row_PS.Item("TapeSlugID") = DBNull.Value
    '            row_PS.Item("Reporter") = txt_Reporter.Text
    '            row_PS.Item("CameraMan") = txt_CameraMan.Text
    '            dt_ProgramInfo.Rows.Add(row_PS)

    '        End If

    '    End If

    '    ViewState("ProgramInfo_Table") = dt_ProgramInfo
    '    'BindGrid_ProgramInfo()
    '    'Clrscr_ProgramInfo()

    '    'dt_ProgramInfo

    '    ''********************************* Save Temp Slug ********************************''

    '    ''*********************************************''
    '    ''*************** Get User ID *****************''
    '    ''*********************************************''

    '    Dim UserID As Integer
    '    Dim objUserID As New BusinessFacade.Employee()
    '    objUserID.SM_LoginID = lbl_UserName.Text
    '    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

    '    ''*********************************************''
    '    ''************ Get TapeLibraryID **************''
    '    ''*********************************************''

    '    Dim LibraryID As Integer
    '    Dim objLibID As New BusinessFacade.TapeIssuance()
    '    objLibID.TapeNumber = txt_TapeNumber.Text
    '    LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

    '    Dim SlugID As Integer

    '    If LibraryID <> 0 Then

    '        Dim ObjSlug As New BusinessFacade.TapeContent_News()
    '        ObjSlug.ReporterSlug = dt_ProgramInfo.Rows(0).Item(0).ToString
    '        ObjSlug.ProposedSlug = dt_ProgramInfo.Rows(0).Item(1).ToString
    '        ObjSlug.ReporterID = dt_ProgramInfo.Rows(0).Item(2).ToString
    '        ObjSlug.CameraManID = dt_ProgramInfo.Rows(0).Item(3).ToString
    '        ObjSlug.EnglishScript = dt_ProgramInfo.Rows(0).Item(4).ToString
    '        ObjSlug.UrduScript = dt_ProgramInfo.Rows(0).Item(5).ToString
    '        ObjSlug.StratTime = dt_ProgramInfo.Rows(0).Item(6).ToString
    '        ObjSlug.EndTime = dt_ProgramInfo.Rows(0).Item(7).ToString
    '        ObjSlug.Duration = dt_ProgramInfo.Rows(0).Item(8).ToString
    '        ObjSlug.ProducedBy = UserID
    '        ObjSlug.TapeLibraryID = LibraryID
    '        SlugID = ObjSlug.TempTapeSlug_SaveRecord()

    '    End If

    '    ''**********************************************''
    '    ''***************** Slug End *******************''
    '    ''**********************************************''


    '    If ViewState("KeyWordTable_2") Is Nothing Then

    '        Dim col3 As DataColumn = New DataColumn("SlugVsKeywordID")
    '        col3.DataType = System.Type.GetType("System.Int32")
    '        Dt_SaveKeyword_2.Columns.Add(col3)

    '        Dim col1 As DataColumn = New DataColumn("TapeSlugID")
    '        col1.DataType = System.Type.GetType("System.Int32")
    '        Dt_SaveKeyword_2.Columns.Add(col1)

    '        Dim col4 As DataColumn = New DataColumn("TapeSlug")
    '        col4.DataType = System.Type.GetType("System.String")
    '        Dt_SaveKeyword_2.Columns.Add(col4)

    '        Dim col2 As DataColumn = New DataColumn("NewsKeywordID")
    '        col2.DataType = System.Type.GetType("System.Int32")
    '        Dt_SaveKeyword_2.Columns.Add(col2)

    '        Dim col5 As DataColumn = New DataColumn("NewsKeyword")
    '        col5.DataType = System.Type.GetType("System.String")
    '        Dt_SaveKeyword_2.Columns.Add(col5)

    '    Else
    '        Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
    '    End If

    '    If txt_dgKeyword_Index.Text <> "" Then

    '        Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()
    '        'Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("TapeSlug") = txt_ReporterSlug.Text
    '        Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeywordID") = lstKeyword.SelectedValue
    '        Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeyword") = lstKeyword.SelectedItem.Text
    '        Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
    '        txt_dgKeyword_Index.Text = String.Empty

    '    Else
    '        Dim U As Integer
    '        For U = 0 To lstKeyword.Items.Count - 1
    '            If lstKeyword.Items(U).Selected Then
    '                Dim Rw As DataRow
    '                Rw = Dt_SaveKeyword_2.NewRow()
    '                Rw.Item("SlugVsKeywordID") = DBNull.Value
    '                Rw.Item("TapeSlugID") = SlugID
    '                Rw.Item("TapeSlug") = dt_ProgramInfo.Rows(0).Item(0).ToString
    '                Rw.Item("NewsKeywordID") = lstKeyword.Items(U).Value
    '                Rw.Item("NewsKeyword") = lstKeyword.Items(U).Text
    '                Dt_SaveKeyword_2.Rows.Add(Rw)
    '            End If
    '        Next
    '    End If

    '    'ViewState("KeyWordTable_2") = Dt_SaveKeyword_2
    '    'Bind_dgKeyWord_2()

    '    ''****************************************************''
    '    ''*************** Slug Vs Keyword Save ***************''
    '    ''****************************************************''

    '    Dim q As Integer
    '    For q = 0 To Dt_SaveKeyword_2.Rows.Count - 1
    '        Dim objtest2 As New BusinessFacade.SlugVsKeyword()
    '        objtest2.TapeContentDetail_News_ID = 0
    '        objtest2.TapeSlugID = Dt_SaveKeyword_2.Rows(q).Item(1).ToString
    '        objtest2.Slug = Dt_SaveKeyword_2.Rows(q).Item(2).ToString
    '        objtest2.NewsKeywordID = Dt_SaveKeyword_2.Rows(q).Item(3).ToString
    '        objtest2.NewsKeyword = Dt_SaveKeyword_2.Rows(q).Item(4).ToString
    '        objtest2.TempSlugVskeyword_SaveRecord()
    '    Next

    '    ''----------------------------------------------------------------------------------------------------------''
    '    ''--------------------------------------------- Slug Vs Footages -------------------------------------------''
    '    ''----------------------------------------------------------------------------------------------------------''

    '    Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
    '    colFootage3.DataType = System.Type.GetType("System.Int32")
    '    Dt_SaveFootage.Columns.Add(colFootage3)

    '    Dim colFootage4 As DataColumn = New DataColumn("TapeSlugID")
    '    colFootage4.DataType = System.Type.GetType("System.Int32")
    '    Dt_SaveFootage.Columns.Add(colFootage4)

    '    Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
    '    colFootage5.DataType = System.Type.GetType("System.String")
    '    Dt_SaveFootage.Columns.Add(colFootage5)

    '    Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
    '    colFootage2.DataType = System.Type.GetType("System.Int32")
    '    Dt_SaveFootage.Columns.Add(colFootage2)

    '    Dim colFootage1 As DataColumn = New DataColumn("FootageType")
    '    colFootage1.DataType = System.Type.GetType("System.String")
    '    Dt_SaveFootage.Columns.Add(colFootage1)

    '    If ViewState("FootageTable") Is Nothing Then
    '    Else
    '        Dt_SaveFootage = ViewState("FootageTable")
    '    End If


    '    Dim Y As Integer
    '    For Y = 0 To lstFootageType.Items.Count - 1
    '        If lstFootageType.Items(Y).Selected Then
    '            Dim FootageRow As DataRow
    '            FootageRow = Dt_SaveFootage.NewRow
    '            FootageRow.Item("TapeContentFootageID") = DBNull.Value
    '            FootageRow.Item("TapeSlugID") = SlugID
    '            FootageRow.Item("TapeSlug") = dt_ProgramInfo.Rows(0).Item(0).ToString
    '            FootageRow.Item("FootageTypeID") = lstFootageType.Items(Y).Value
    '            FootageRow.Item("FootageType") = lstFootageType.Items(Y).Text
    '            Dt_SaveFootage.Rows.Add(FootageRow)

    '        End If
    '    Next

    '    ViewState("FootageTable") = Dt_SaveFootage

    '    'Bind_DgFootage()

    '    ''******************************************************''
    '    ''******************** Footages ************************''
    '    ''******************************************************''

    '    Dim Z As Integer
    '    For Z = 0 To Dt_SaveFootage.Rows.Count - 1
    '        Dim ObjFootage As New BusinessFacade.TapeContent_News()
    '        ObjFootage.SlugID = SlugID
    '        ObjFootage.Slug = Dt_SaveFootage.Rows(Z).Item(2).ToString
    '        ObjFootage.FootageID = Dt_SaveFootage.Rows(Z).Item(3).ToString
    '        ObjFootage.Footage = Dt_SaveFootage.Rows(Z).Item(4).ToString
    '        ObjFootage.SlugVsFootage_SaveRecord()
    '    Next

    '    dt_ProgramInfo.Clear()
    '    Dt_SaveKeyword_2.Clear()
    '    Dt_SaveFootage.Clear()
    '    ViewState("ProgramInfo_Table") = Nothing
    '    ViewState("KeyWordTable_2") = Nothing
    '    ViewState("FootageTable") = Nothing

    'End Sub

    Protected Sub lnkDeleteFootage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim objDeleteFootage As New BusinessFacade.TapeContent()
        objDeleteFootage.DeleteFootages(2, 9)
    End Sub

    'Protected Sub bttnSaveFinal_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveFinal.Click
    '    lblErr_MasterEntry.Text = String.Empty
    '    Err_ReporterSlug.Visible = False
    '    Err_Department.Visible = False
    '    Err_TapeNumber.Visible = False

    '    ''****************************************''

    '    Dim arr As Array = Split(txt_TapeNumber.Text, "#")
    '    If arr.Length = 2 Then
    '        txt_TapeNumber.Text = arr(1)
    '    End If

    '    ''*********************************************''
    '    ''************ Get TapeLibraryID **************''
    '    ''*********************************************''

    '    Dim LibraryID As Integer
    '    Dim objLibID As New BusinessFacade.TapeIssuance()
    '    objLibID.TapeNumber = txt_TapeNumber.Text
    '    LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

    '    ''*********************************************''
    '    ''************* Get DepartmentID **************''
    '    ''*********************************************''

    '    Dim DepartmentID As Integer
    '    Dim objDeptID As New BusinessFacade.TapeIssuance()
    '    objDeptID.DepartmentName = txt_Department.Text
    '    DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

    '    ''*********************************************''
    '    ''*************** Get User ID *****************''
    '    ''*********************************************''

    '    Dim UserID As Integer
    '    Dim objUserID As New BusinessFacade.Employee()
    '    objUserID.SM_LoginID = lbl_UserName.Text
    '    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

    '    ''*********************************************''
    '    ''***************** Get User ID ***************''
    '    ''*********************************************''

    '    Dim UserName As String
    '    Dim objUserName As New BusinessFacade.Employee()
    '    objUserName.EmployeeID = UserID
    '    UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

    '    Dim TapeContentID As Integer

    '    If DepartmentID = 0 Then
    '        lblErr_MasterEntry.Text = "Please Select Department !!"
    '        Err_Department.Visible = True
    '    ElseIf LibraryID = 0 Then
    '        lblErr_MasterEntry.Text = "Please Select Tape Number !!"
    '        Err_TapeNumber.Visible = True
    '    ElseIf dg.Rows.Count = 0 Then
    '        Err_ReporterSlug.Visible = True
    '    Else

    '        ''****************************************************''
    '        ''**************** Tape Content Save *****************''
    '        ''****************************************************''

    '        If txt_TapeContentID.Text = "" Then

    '            Dim ObjUser As New BusinessFacade.TapeContent()
    '            ObjUser.ContentTypeID = ddl_Channel.SelectedValue
    '            ObjUser.DepartmentID = DepartmentID
    '            ObjUser.isCurrentContent = 0
    '            ObjUser.ClassificationCode = txt_ClassificationCode.Text
    '            ObjUser.CallNo = txt_CallNo.Text
    '            ObjUser.LocationCode = txt_LocationCode.Text
    '            ObjUser.IsActive = 1
    '            TapeContentID = ObjUser.SaveRecord()
    '            txt_TapeContentID.Text = TapeContentID
    '        Else
    '            Dim ObjUpdateUser As New BusinessFacade.TapeContent()
    '            ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
    '            ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
    '            ObjUpdateUser.DepartmentID = DepartmentID
    '            ObjUpdateUser.isCurrentContent = 0
    '            ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
    '            ObjUpdateUser.CallNo = txt_CallNo.Text
    '            ObjUpdateUser.LocationCode = txt_LocationCode.Text
    '            ObjUpdateUser.IsActive = 1
    '            Dim O As Integer = ObjUpdateUser.UpdateRecord()
    '            TapeContentID = txt_TapeContentID.Text

    '        End If

    '        ''*************************************************''
    '        ''**************** Tape Slug **********************''
    '        ''*************************************************''

    '        Dim SlugID As Integer
    '        Dim TapeContentDetailID As Integer
    '        Dim P As Integer
    '        'For P = 0 To dg.Rows.Count - 1
    '        For P = 0 To 0
    '            Dim ObjSlug As New BusinessFacade.TapeContent_News()
    '            ObjSlug.ReporterSlug = dg.Rows(P).Cells(1).Text
    '            ObjSlug.ProposedSlug = dg.Rows(P).Cells(2).Text
    '            If dg.Rows(P).Cells(3).Text = "&nbsp;" Then
    '                ObjSlug.ReporterID = 0
    '            End If
    '            If dg.Rows(P).Cells(4).Text = "&nbsp;" Then
    '                ObjSlug.CameraManID = 0
    '            End If
    '            ObjSlug.EnglishScript = dg.Rows(P).Cells(5).Text
    '            ObjSlug.UrduScript = dg.Rows(P).Cells(6).Text
    '            ObjSlug.StratTime = dg.Rows(P).Cells(7).Text
    '            ObjSlug.EndTime = dg.Rows(P).Cells(8).Text
    '            ObjSlug.Duration = dg.Rows(P).Cells(9).Text
    '            ObjSlug.ProducedBy = UserID
    '            SlugID = ObjSlug.TapeSlug_SaveRecord()

    '            ''***************************************************''
    '            ''************* TapeContent Detail Save *************''
    '            ''***************************************************''

    '            Dim ObjDetail As New BusinessFacade.TapeContent_News()
    '            ObjDetail.TapeContentID = txt_TapeContentID.Text
    '            ObjDetail.TapeLibraryID = LibraryID
    '            ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
    '            ObjDetail.MergeID = 1
    '            ObjDetail.ProducedBy = UserID
    '            ObjDetail.ProducedByName = UserName
    '            ObjDetail.SlugID = SlugID
    '            ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
    '            TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()


    '            ''****************************************************''
    '            ''*************** Slug Vs Keyword Save ***************''
    '            ''****************************************************''

    '            Dim q As Integer
    '            For q = 0 To dg.Rows.Count - 1
    '                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
    '                objtest2.NewsKeywordID = dg.Rows(q).Cells(12).Text
    '                objtest2.TapeContentDetail_News_ID = TapeContentDetailID
    '                objtest2.TapeSlugID = SlugID
    '                objtest2.SlugVskeyword_SaveRecord()
    '            Next

    '            ''******************************************************''
    '            ''******************** Footages ************************''
    '            ''******************************************************''

    '            Dim TapeContentFootage1 As Integer
    '            Dim y As Integer
    '            For y = 0 To dg.Rows.Count - 1
    '                '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
    '                Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
    '                ObjTapeContentFootage.TapeContentID = txt_TapeContentID.Text
    '                ObjTapeContentFootage.FootageID = dg.Rows(y).Cells(15).Text
    '                ObjTapeContentFootage.userID = UserID
    '                ObjTapeContentFootage.SlugID = SlugID
    '                TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
    '            Next

    '        Next

    '    End If

    'End Sub

    Protected Sub bttnSaveTable_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveTable.Click

        lblErr.Text = String.Empty
        Calculate()

        If txt_ReporterSlug.Text <> "" Then


            If txt_dgProgramInfo_Index.Text = "" And txt_dgKeyword_Index.Text = "" And txt_dgFootage_Index.Text = "" Then
                ''**********************************''
                ''*********** Save Slug ************''
                ''**********************************''

                Dim col1_ps As DataColumn = New DataColumn("ReporterSlug")
                col1_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("ReporterSlug")

                Dim col2_ps As DataColumn = New DataColumn("ProposedSlug")
                col2_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("ProposedSlug")

                Dim col3_ps As DataColumn = New DataColumn("ReporterID")
                col3_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("ReporterID")

                Dim col4_ps As DataColumn = New DataColumn("CameraManID")
                col4_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("CameraManID")

                Dim col5_ps As DataColumn = New DataColumn("EnglishScript")
                col5_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("EnglishScript")

                Dim col6_ps As DataColumn = New DataColumn("UrduScript")
                col6_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("UrduScript")

                Dim col7_ps As DataColumn = New DataColumn("Duration")
                col7_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("Duration")

                Dim col8_ps As DataColumn = New DataColumn("StartTime")
                col8_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("StartTime")

                Dim col9_ps As DataColumn = New DataColumn("EndTime")
                col9_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("EndTime")

                Dim col10_ps As DataColumn = New DataColumn("TapeContentDetail_News_ID")
                col10_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("TapeContentDetail_News_ID")

                Dim col11_ps As DataColumn = New DataColumn("TapeSlugID")
                col11_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("TapeSlugID")

                Dim col12_ps As DataColumn = New DataColumn("Reporter")
                col12_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("Reporter")

                Dim col13_ps As DataColumn = New DataColumn("CameraMan")
                col13_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("CameraMan")

                Dim col14_ps As DataColumn = New DataColumn("EntryDate")
                col14_ps.DataType = System.Type.GetType("System.DateTime")
                dt_ProgramInfo.Columns.Add("EntryDate")


                If ViewState("ProgramInfo_Table") Is Nothing Then
                Else
                    dt_ProgramInfo = ViewState("ProgramInfo_Table")
                End If

                If txt_dgProgramInfo_Index.Text <> "" Then

                    ''*************************************''
                    ''********** Get Reporter ID **********''
                    ''*************************************''

                    Dim ReporterID As Integer
                    Dim objReporterID As New BusinessFacade.TapeContent_News()
                    objReporterID.ReporterName = txt_Reporter.Text
                    ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

                    ''*************************************''
                    ''*********** Get Reporter ID *********''
                    ''*************************************''

                    Dim CameraManID As Integer
                    Dim objCameraManID As New BusinessFacade.TapeContent_News()
                    objCameraManID.ReporterName = txt_CameraMan.Text
                    CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)

                    ''*************************************''

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterSlug") = txt_ReporterSlug.Text
                    If txt_ProposedSlug.Text = " " Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = txt_ProposedSlug.Text
                    End If

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterID") = ReporterID
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("CameraManID") = CameraManID
                    If Trim(txt_EnglishScript.Text) = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = txt_EnglishScript.Text
                    End If

                    If Trim(txt_UrduScript.Text) = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text
                    End If


                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = txt_TimeDuration.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_EndTime.Text

                    Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                    If TDuration = ":::" Then
                        TDuration = "00:00:00:00"
                    End If
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = TDuration

                    Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                    Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime1.Text

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_Endtime1.Text
                    If txtEntryDate.Text = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = Date.Now.ToString("dd-MMM-yyyy")
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = txtEntryDate.Text
                    End If


                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
                    txt_dgProgramInfo_Index.Text = String.Empty

                Else

                    ''*************************************''
                    ''********** Get Reporter ID **********''
                    ''*************************************''

                    Dim ReporterID As Integer
                    Dim objReporterID As New BusinessFacade.TapeContent_News()
                    objReporterID.ReporterName = txt_Reporter.Text
                    ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

                    ''*************************************''
                    ''*********** Get Reporter ID *********''
                    ''*************************************''

                    Dim CameraManID As Integer
                    Dim objCameraManID As New BusinessFacade.TapeContent_News()
                    objCameraManID.ReporterName = txt_CameraMan.Text
                    CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)


                    If txt_ReporterSlug.Text = "" Then
                        Err_ReporterSlug.Visible = True
                    Else
                        Dim row_PS As DataRow
                        row_PS = dt_ProgramInfo.NewRow()

                        row_PS.Item("ReporterSlug") = txt_ReporterSlug.Text

                        If txt_ProposedSlug.Text = "" Then
                            row_PS.Item("ProposedSlug") = "N/A"
                        Else
                            row_PS.Item("ProposedSlug") = txt_ProposedSlug.Text
                        End If
                        row_PS.Item("ReporterID") = ReporterID
                        row_PS.Item("CameraManID") = CameraManID

                        If txt_EnglishScript.Text = "" Then
                            row_PS.Item("EnglishScript") = "N/A"
                        Else
                            row_PS.Item("EnglishScript") = txt_EnglishScript.Text
                        End If

                        If txt_UrduScript.Text = "" Then
                            row_PS.Item("UrduScript") = "N/A"
                        Else
                            row_PS.Item("UrduScript") = txt_UrduScript.Text
                        End If


                        Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                        If TDuration = ":::" Then
                            TDuration = "00:00:00:00"
                        End If
                        row_PS.Item("Duration") = TDuration

                        Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                        Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                        row_PS.Item("StartTime") = txt_StartTime1.Text
                        row_PS.Item("EndTime") = txt_Endtime1.Text

                        'row_PS.Item("Duration") = txt_TimeDuration1.Text
                        'row_PS.Item("StartTime") = txt_EndTime1.Text
                        'row_PS.Item("EndTime") = txt_StartTime1.Text
                        row_PS.Item("TapeContentDetail_News_ID") = DBNull.Value
                        row_PS.Item("TapeSlugID") = DBNull.Value
                        row_PS.Item("Reporter") = txt_Reporter.Text
                        row_PS.Item("CameraMan") = txt_CameraMan.Text
                        If txtEntryDate.Text = "" Then
                            row_PS.Item("EntryDate") = Date.Now.ToString("dd-MMM-yyyy")
                        Else
                            row_PS.Item("EntryDate") = txtEntryDate.Text
                        End If
                        dt_ProgramInfo.Rows.Add(row_PS)

                    End If

                End If

                ViewState("ProgramInfo_Table") = dt_ProgramInfo
                BindGrid_ProgramInfo()
                FillStartTime()
                Clrscr_ProgramInfo()

                ''************** End ***************''
                ''**********************************''

                ''**********************************''
                ''********* Save Keywords **********''
                ''**********************************''

                If ViewState("KeyWordTable_2") Is Nothing Then
                    Dim col1 As DataColumn = New DataColumn("TapeSlugID")
                    col1.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col1)

                    Dim col2 As DataColumn = New DataColumn("NewsKeywordID")
                    col2.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col2)

                    Dim col3 As DataColumn = New DataColumn("SlugVsKeywordID")
                    col3.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col3)

                    Dim col4 As DataColumn = New DataColumn("TapeSlug")
                    col4.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col4)

                    Dim col5 As DataColumn = New DataColumn("NewsKeyword")
                    col5.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col5)

                Else
                    Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
                End If

                If txt_dgKeyword_Index.Text <> "" Then

                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()
                    'Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("TapeSlug") = txt_ReporterSlug.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeywordID") = lstKeyword.SelectedValue
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeyword") = lstKeyword.SelectedItem.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
                    txt_dgKeyword_Index.Text = String.Empty

                Else
                    Dim U As Integer
                    For U = 0 To lstKeyword.Items.Count - 1
                        If lstKeyword.Items(U).Selected Then
                            Dim Rw As DataRow
                            Rw = Dt_SaveKeyword_2.NewRow()
                            Rw.Item("TapeSlugID") = DBNull.Value
                            Rw.Item("NewsKeywordID") = lstKeyword.Items(U).Value
                            Rw.Item("SlugVsKeywordID") = DBNull.Value
                            Rw.Item("TapeSlug") = txt_ReporterSlug.Text
                            Rw.Item("NewsKeyword") = lstKeyword.Items(U).Text

                            Dt_SaveKeyword_2.Rows.Add(Rw)
                        End If
                    Next
                End If



                ViewState("KeyWordTable_2") = Dt_SaveKeyword_2

                Bind_dgKeyWord_2()

                ''************** End ***************''
                ''**********************************''

                ''**********************************''
                ''********* Save Footages **********''
                ''**********************************''

                Dim colFootage1 As DataColumn = New DataColumn("FootageType")
                colFootage1.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage1)

                Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
                colFootage2.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage2)

                Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
                colFootage3.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage3)

                Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
                colFootage4.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage4)

                Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
                colFootage5.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage5)

                If ViewState("FootageTable") Is Nothing Then
                Else
                    Dt_SaveFootage = ViewState("FootageTable")
                End If

                If txt_dgFootage_Index.Text <> "" Then

                    ''''''''''' Get FootageTypeID '''''''''''''''''
                    'Dim FootageTypeID As Integer
                    'Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
                    'objFootageTypeID.FootageType = txt_FootageType.Text
                    'FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = txt_FootageType.Text
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = FootageTypeID
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
                    'txt_dgFootage_Index.Text = String.Empty

                Else

                    Dim Y As Integer
                    For Y = 0 To lstFootageType.Items.Count - 1
                        If lstFootageType.Items(Y).Selected Then
                            Dim FootageRow As DataRow
                            FootageRow = Dt_SaveFootage.NewRow
                            FootageRow.Item("TapeContentID") = DBNull.Value
                            FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
                            FootageRow.Item("FootageType") = lstFootageType.Items(Y).Text
                            FootageRow.Item("FootageTypeID") = lstFootageType.Items(Y).Value
                            FootageRow.Item("TapeContentFootageID") = DBNull.Value
                            Dt_SaveFootage.Rows.Add(FootageRow)
                        End If
                    Next

                End If

                ViewState("FootageTable") = Dt_SaveFootage
                Bind_DgFootage()

                ''************** End ***************''
                ''**********************************''

                txt_dgProgramInfo_Index.Text = String.Empty
                txt_dgKeyword_Index.Text = String.Empty
                txt_dgFootage_Index.Text = String.Empty
                lstKeyword.SelectedIndex = -1
                lstFootageType.SelectedIndex = -1

            End If

            ''************************************************************************************************************''
            ''************************************************* Edit Slug ************************************************''
            ''************************************************************************************************************''

            If txt_dgProgramInfo_Index.Text <> "" And txt_dgKeyword_Index.Text = "" And txt_dgFootage_Index.Text = "" Then

                Dim col1_ps As DataColumn = New DataColumn("ReporterSlug")
                col1_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("ReporterSlug")

                Dim col2_ps As DataColumn = New DataColumn("ProposedSlug")
                col2_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("ProposedSlug")

                Dim col3_ps As DataColumn = New DataColumn("ReporterID")
                col3_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("ReporterID")

                Dim col4_ps As DataColumn = New DataColumn("CameraManID")
                col4_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("CameraManID")

                Dim col5_ps As DataColumn = New DataColumn("EnglishScript")
                col5_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("EnglishScript")

                Dim col6_ps As DataColumn = New DataColumn("UrduScript")
                col6_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("UrduScript")

                Dim col7_ps As DataColumn = New DataColumn("Duration")
                col7_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("Duration")

                Dim col8_ps As DataColumn = New DataColumn("StartTime")
                col8_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("StartTime")

                Dim col9_ps As DataColumn = New DataColumn("EndTime")
                col9_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("EndTime")

                Dim col10_ps As DataColumn = New DataColumn("TapeContentDetail_News_ID")
                col10_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("TapeContentDetail_News_ID")

                Dim col11_ps As DataColumn = New DataColumn("TapeSlugID")
                col11_ps.DataType = System.Type.GetType("System.Int32")
                dt_ProgramInfo.Columns.Add("TapeSlugID")

                Dim col12_ps As DataColumn = New DataColumn("Reporter")
                col12_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("Reporter")

                Dim col13_ps As DataColumn = New DataColumn("CameraMan")
                col13_ps.DataType = System.Type.GetType("System.String")
                dt_ProgramInfo.Columns.Add("CameraMan")

                Dim col14_ps As DataColumn = New DataColumn("EntryDate")
                col14_ps.DataType = System.Type.GetType("System.DateTime")
                dt_ProgramInfo.Columns.Add("EntryDate")


                If ViewState("ProgramInfo_Table") Is Nothing Then
                Else
                    dt_ProgramInfo = ViewState("ProgramInfo_Table")
                End If

                If txt_dgProgramInfo_Index.Text <> "" Then

                    ''*************************************''
                    ''********** Get Reporter ID **********''
                    ''*************************************''

                    Dim ReporterID As Integer
                    Dim objReporterID As New BusinessFacade.TapeContent_News()
                    objReporterID.ReporterName = txt_Reporter.Text
                    ReporterID = objReporterID.GetReporterID_byReporterName(objReporterID.ReporterName)

                    ''*************************************''
                    ''*********** Get Reporter ID *********''
                    ''*************************************''

                    Dim CameraManID As Integer
                    Dim objCameraManID As New BusinessFacade.TapeContent_News()
                    objCameraManID.ReporterName = txt_CameraMan.Text
                    CameraManID = objCameraManID.GetReporterID_byReporterName(objReporterID.ReporterName)

                    ''*************************************''

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).BeginEdit()
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterSlug") = txt_ReporterSlug.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = txt_ProposedSlug.Text
                    If txt_ProposedSlug.Text = " " Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ProposedSlug") = txt_ProposedSlug.Text
                    End If

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("ReporterID") = ReporterID
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("CameraManID") = CameraManID

                    If Trim(txt_EnglishScript.Text) = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = txt_EnglishScript.Text
                    End If

                    If Trim(txt_UrduScript.Text) = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = "N/A"
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text
                    End If

                    ' dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EnglishScript") = txt_EnglishScript.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("UrduScript") = txt_UrduScript.Text

                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = txt_TimeDuration.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = txt_StartTime.Text
                    'dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = txt_EndTime.Text

                    Dim TDuration As String = DUR_1.Text & ":" & DUR_2.Text & ":" & DUR_3.Text & ":" & DUR_4.Text
                    If TDuration = ":::" Then
                        TDuration = "00:00:00:00"
                    End If
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("Duration") = TDuration

                    Dim STime As String = ST1.Text & ":" & ST2.Text & ":" & ST3.Text & ":" & ST4.Text
                    Dim ETime As String = END1.Text & ":" & END2.Text & ":" & END3.Text & ":" & END4.Text

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("StartTime") = ETime
                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EndTime") = STime
                    If txtEntryDate.Text = "" Then
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = Date.Now.ToString("dd-MMM-yyyy")
                    Else
                        dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).Item("EntryDate") = txtEntryDate.Text
                    End If

                    dt_ProgramInfo.Rows(txt_dgProgramInfo_Index.Text).EndEdit()
                    txt_dgProgramInfo_Index.Text = String.Empty

                End If

                ViewState("ProgramInfo_Table") = dt_ProgramInfo
                BindGrid_ProgramInfo()
                Clrscr_ProgramInfo()

                ''**********************************''
                ''********* Save Keywords **********''
                ''**********************************''

                If ViewState("KeyWordTable_2") Is Nothing Then
                    Dim col1 As DataColumn = New DataColumn("TapeSlugID")
                    col1.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col1)

                    Dim col2 As DataColumn = New DataColumn("NewsKeywordID")
                    col2.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col2)

                    Dim col3 As DataColumn = New DataColumn("SlugVsKeywordID")
                    col3.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col3)

                    Dim col4 As DataColumn = New DataColumn("TapeSlug")
                    col4.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col4)

                    Dim col5 As DataColumn = New DataColumn("NewsKeyword")
                    col5.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col5)

                Else
                    Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
                End If

                If txt_dgKeyword_Index.Text <> "" Then

                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()
                    'Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("TapeSlug") = txt_ReporterSlug.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeywordID") = lstKeyword.SelectedValue
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeyword") = lstKeyword.SelectedItem.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
                    txt_dgKeyword_Index.Text = String.Empty

                Else
                    Dim U As Integer
                    For U = 0 To lstKeyword.Items.Count - 1
                        If lstKeyword.Items(U).Selected Then
                            Dim Rw As DataRow
                            Rw = Dt_SaveKeyword_2.NewRow()
                            Rw.Item("TapeSlugID") = DBNull.Value
                            Rw.Item("NewsKeywordID") = lstKeyword.Items(U).Value
                            Rw.Item("SlugVsKeywordID") = DBNull.Value
                            Rw.Item("TapeSlug") = txt_ReporterSlug.Text
                            Rw.Item("NewsKeyword") = lstKeyword.Items(U).Text

                            Dt_SaveKeyword_2.Rows.Add(Rw)
                        End If
                    Next
                End If



                ViewState("KeyWordTable_2") = Dt_SaveKeyword_2

                Bind_dgKeyWord_2()

                ''************** End ***************''
                ''**********************************''

                ''**********************************''
                ''********* Save Footages **********''
                ''**********************************''

                Dim colFootage1 As DataColumn = New DataColumn("FootageType")
                colFootage1.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage1)

                Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
                colFootage2.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage2)

                Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
                colFootage3.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage3)

                Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
                colFootage4.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage4)

                Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
                colFootage5.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage5)

                If ViewState("FootageTable") Is Nothing Then
                Else
                    Dt_SaveFootage = ViewState("FootageTable")
                End If

                If txt_dgFootage_Index.Text <> "" Then

                    ''''''''''' Get FootageTypeID '''''''''''''''''
                    'Dim FootageTypeID As Integer
                    'Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
                    'objFootageTypeID.FootageType = txt_FootageType.Text
                    'FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = txt_FootageType.Text
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = FootageTypeID
                    'Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
                    'txt_dgFootage_Index.Text = String.Empty

                Else

                    Dim Y As Integer
                    For Y = 0 To lstFootageType.Items.Count - 1
                        If lstFootageType.Items(Y).Selected Then
                            Dim FootageRow As DataRow
                            FootageRow = Dt_SaveFootage.NewRow
                            FootageRow.Item("TapeContentID") = DBNull.Value
                            FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
                            FootageRow.Item("FootageType") = lstFootageType.Items(Y).Text
                            FootageRow.Item("FootageTypeID") = lstFootageType.Items(Y).Value
                            FootageRow.Item("TapeContentFootageID") = DBNull.Value
                            Dt_SaveFootage.Rows.Add(FootageRow)
                        End If
                    Next

                End If

                ViewState("FootageTable") = Dt_SaveFootage
                Bind_DgFootage()

                ''************** End ***************''
                ''**********************************''

                txt_dgProgramInfo_Index.Text = String.Empty
                txt_dgKeyword_Index.Text = String.Empty
                txt_dgFootage_Index.Text = String.Empty
                lstKeyword.SelectedIndex = -1
                lstFootageType.SelectedIndex = -1

            End If


            ''*************************************************** End ****************************************************''
            ''************************************************************************************************************''

            ''************************************************************************************************************''
            ''************************************************* Edit Keyword *********************************************''
            ''************************************************************************************************************''
            If txt_dgProgramInfo_Index.Text = "" And txt_dgKeyword_Index.Text <> "" And txt_dgFootage_Index.Text = "" Then

                If ViewState("KeyWordTable_2") Is Nothing Then
                    Dim col1 As DataColumn = New DataColumn("TapeSlugID")
                    col1.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col1)

                    Dim col2 As DataColumn = New DataColumn("NewsKeywordID")
                    col2.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col2)

                    Dim col3 As DataColumn = New DataColumn("SlugVsKeywordID")
                    col3.DataType = System.Type.GetType("System.Int32")
                    Dt_SaveKeyword_2.Columns.Add(col3)

                    Dim col4 As DataColumn = New DataColumn("TapeSlug")
                    col4.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col4)

                    Dim col5 As DataColumn = New DataColumn("NewsKeyword")
                    col5.DataType = System.Type.GetType("System.String")
                    Dt_SaveKeyword_2.Columns.Add(col5)

                Else
                    Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
                End If

                If txt_dgKeyword_Index.Text <> "" Then

                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()
                    'Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("TapeSlug") = txt_ReporterSlug.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeywordID") = lstKeyword.SelectedValue
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeyword") = lstKeyword.SelectedItem.Text
                    Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
                    txt_dgKeyword_Index.Text = String.Empty
                End If

                ViewState("KeyWordTable_2") = Dt_SaveKeyword_2
                Bind_dgKeyWord_2()
                lstKeyword.SelectedIndex = -1
            End If

            ''*************************************************** End ****************************************************''
            ''************************************************************************************************************''


            ''************************************************************************************************************''
            ''************************************************* Edit Footage *********************************************''
            ''************************************************************************************************************''
            If txt_dgProgramInfo_Index.Text = "" And txt_dgKeyword_Index.Text = "" And txt_dgFootage_Index.Text <> "" Then

                Dim colFootage1 As DataColumn = New DataColumn("FootageType")
                colFootage1.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage1)

                Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
                colFootage2.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage2)

                Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
                colFootage3.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage3)

                Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
                colFootage4.DataType = System.Type.GetType("System.Int32")
                Dt_SaveFootage.Columns.Add(colFootage4)

                Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
                colFootage5.DataType = System.Type.GetType("System.String")
                Dt_SaveFootage.Columns.Add(colFootage5)

                If ViewState("FootageTable") Is Nothing Then
                Else
                    Dt_SaveFootage = ViewState("FootageTable")
                End If

                If txt_dgFootage_Index.Text <> "" Then

                    Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
                    Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = lstFootageType.SelectedItem.Text
                    Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = lstFootageType.SelectedValue
                    Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
                    txt_dgFootage_Index.Text = String.Empty

                End If

                ViewState("FootageTable") = Dt_SaveFootage
                Bind_DgFootage()
                lstFootageType.SelectedIndex = -1
            End If

            CollapsiblePanelExtender1.Collapsed = True
        Else
            lblErr.Text = "Please Enter Slug !!"
        End If

        ClearSlug()
        FillStartTime()
    End Sub

    Protected Sub dg_Footage_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_Footage.RowDeleting
        Dim RowId As Integer
        RowId = e.RowIndex

        If dg_Footage.Rows(RowId).Cells(3).Text <> "&nbsp;" Then
            Dim ObjDeleteFootages As New BusinessFacade.TapeContent_News()
            ObjDeleteFootages.TapeContentFootageID = dg_Footage.Rows(RowId).Cells(3).Text
            ObjDeleteFootages.Delete_TapeContentNewsFootages()

            Dim ObjFootage As New BusinessFacade.TapeContent_News()
            ObjFootage.TapeContentID = txt_TapeContentID.Text
            Dt_SaveFootage = ObjFootage.GetFootages()
            ViewState("FootageTable") = Dt_SaveFootage
            Bind_DgFootage()

        ElseIf dg_Footage.Rows(RowId).Cells(3).Text = "&nbsp;" Then
            Dt_SaveFootage = ViewState("FootageTable")
            Dt_SaveFootage.Rows(RowId).Delete()
            Dt_SaveFootage.AcceptChanges()

            ViewState("FootageTable") = Dt_SaveFootage

            dg_Footage.DataSource = Dt_SaveFootage
            dg_Footage.DataBind()
        End If


    End Sub

    Protected Sub dg_KeyWord_2_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_KeyWord_2.RowDeleting
        Dim RowId As Integer
        RowId = e.RowIndex

        If dg_KeyWord_2.Rows(RowId).Cells(3).Text <> "&nbsp;" Then
            Dim ObjDeleteKW As New BusinessFacade.TapeContent_News()
            ObjDeleteKW.SlugVsKeywordID = dg_KeyWord_2.Rows(RowId).Cells(3).Text
            ObjDeleteKW.Delete_TapeContentNewsKeyword()

            Fill_KeyWord_Table_2(txt_TapeContentID.Text)

        ElseIf dg_KeyWord_2.Rows(RowId).Cells(3).Text = "&nbsp;" Then
            Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
            Dt_SaveKeyword_2.Rows(RowId).Delete()
            Dt_SaveKeyword_2.AcceptChanges()

            ViewState("KeyWordTable_2") = Dt_SaveKeyword_2

            dg_KeyWord_2.DataSource = Dt_SaveKeyword_2
            dg_KeyWord_2.DataBind()
        End If
    End Sub

    Protected Sub dg_programInfo_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_programInfo.RowDeleting
        If dg_programInfo.Rows.Count > 1 Then
            Dim RowId As Integer
            RowId = e.RowIndex

            If dg_programInfo.Rows(RowId).Cells(10).Text <> "&nbsp;" Then
                Dim ObjDeletePrg As New BusinessFacade.TapeContent_News()
                ObjDeletePrg.TapeContentDetailID = dg_programInfo.Rows(RowId).Cells(10).Text
                ObjDeletePrg.Delete_TapeContentNewsSlug()

                Fill_ProgramInfo_Table(txt_TapeContentID.Text)
                Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                Dim ObjFootage As New BusinessFacade.TapeContent_News()
                ObjFootage.TapeContentID = txt_TapeContentID.Text
                Dt_SaveFootage = ObjFootage.GetFootages()
                ViewState("FootageTable") = Dt_SaveFootage
                Bind_DgFootage()


            ElseIf dg_programInfo.Rows(RowId).Cells(10).Text = "&nbsp;" Then

                ''************* Slug ******************''

                dt_ProgramInfo = ViewState("ProgramInfo_Table")
                dt_ProgramInfo.Rows(RowId).Delete()
                dt_ProgramInfo.AcceptChanges()

                ViewState("ProgramInfo_Table") = dt_ProgramInfo

                dg_programInfo.DataSource = dt_ProgramInfo
                dg_programInfo.DataBind()

                ''************* Keywords ***************''

                'Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
                'Dt_SaveKeyword_2.Rows(RowId).Delete()
                'Dt_SaveKeyword_2.AcceptChanges()

                'ViewState("KeyWordTable_2") = Dt_SaveKeyword_2

                'dg_KeyWord_2.DataSource = Dt_SaveKeyword_2
                'dg_KeyWord_2.DataBind()

                ' ''************** Footages ***************''

                'Dt_SaveFootage = ViewState("FootageTable")
                'Dt_SaveFootage.Rows(RowId).Delete()
                'Dt_SaveFootage.AcceptChanges()

                'ViewState("FootageTable") = Dt_SaveFootage

                'dg_Footage.DataSource = Dt_SaveFootage
                'dg_Footage.DataBind()

                ''****************** End ***************''

            End If
        End If

    End Sub

    Protected Sub bttnAddNewTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAddNewTape.Click
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onload=function OpenReport() {"
        script = script + "var mywindow = window.open('NewTapeNumber_News.aspx', 'mywindow'); "
        'script = script + "var mywindow = window.open('OSRNewTapeNumber_News.aspx', 'mywindow'); "
        script = script + "}</script>"

        Page.RegisterClientScriptBlock("test", script)
    End Sub

    Protected Sub bttnSaveFinal_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSaveFinal.Click

        lblErr_MasterEntry.Text = String.Empty
        Err_ReporterSlug.Visible = False
        Err_Department.Visible = False
        Err_TapeNumber.Visible = False

        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''*********************************************''
        ''************ Get TapeLibraryID **************''
        ''*********************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        ''*********************************************''
        ''************* Get DepartmentID **************''
        ''*********************************************''

        Dim DepartmentID As Integer
        Dim objDeptID As New BusinessFacade.TapeIssuance()
        objDeptID.DepartmentName = txt_Department.Text
        DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

        ''*********************************************''
        ''*************** Get User ID *****************''
        ''*********************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''*********************************************''
        ''***************** Get User ID ***************''
        ''*********************************************''

        Dim UserName As String
        Dim objUserName As New BusinessFacade.Employee()
        objUserName.EmployeeID = UserID
        UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)


        Dim TapeContentID As Integer

        If DepartmentID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Department !!"
            Err_Department.Visible = True
        ElseIf LibraryID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Tape Number !!"
            Err_TapeNumber.Visible = True
            'ElseIf dg_programInfo.Rows.Count = 0 Then
            '    Err_ReporterSlug.Visible = True
        Else

            ''****************************************************''
            ''**************** Tape Content Save *****************''
            ''****************************************************''

            If txt_TapeContentID.Text = "" Then

                Dim ObjUser As New BusinessFacade.TapeContent()
                ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                ObjUser.DepartmentID = DepartmentID
                ObjUser.isCurrentContent = 0
                ObjUser.ClassificationCode = txt_ClassificationCode.Text
                ObjUser.CallNo = txt_CallNo.Text
                ObjUser.LocationCode = txt_LocationCode.Text
                ObjUser.IsActive = 1
                ObjUser.Copies = ddlCopies.SelectedValue
                ObjUser.UserID = UserID
                TapeContentID = ObjUser.SaveRecord()
                txt_TapeContentID.Text = TapeContentID
            Else
                Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                ObjUpdateUser.DepartmentID = DepartmentID
                ObjUpdateUser.isCurrentContent = 0
                ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                ObjUpdateUser.CallNo = txt_CallNo.Text
                ObjUpdateUser.LocationCode = txt_LocationCode.Text
                ObjUpdateUser.IsActive = 1
                ObjUpdateUser.Copies = ddlCopies.SelectedValue
                ObjUpdateUser.UserID = UserID
                Dim O As Integer = ObjUpdateUser.UpdateRecord()
                TapeContentID = txt_TapeContentID.Text

            End If

            ''***************************************************************''
            ''********************* TapeContentDetail News ******************''
            ''***************************************************************''

            Dim SlugID As Integer
            Dim SlugLibraryID As Integer
            Dim TapeContentDetailID As Integer
            Dim P As Integer
            For P = 0 To dg_programInfo.Rows.Count - 1
                If dg_programInfo.Rows(P).Cells(10).Text = "&nbsp;" Then

                    ''*************************************************''
                    ''**************** Tape Slug **********************''
                    ''*************************************************''

                    Dim ObjSlug As New BusinessFacade.TapeContent_News()
                    ObjSlug.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                    ObjSlug.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                    ObjSlug.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjSlug.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjSlug.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                    ObjSlug.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                    ObjSlug.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                    ObjSlug.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                    ObjSlug.Duration = dg_programInfo.Rows(P).Cells(9).Text
                    ObjSlug.ProducedBy = UserID
                    SlugID = ObjSlug.TapeSlug_SaveRecord()

                    ''***************************************************''
                    ''*************** Insert in Slug Library ************''
                    ''***************************************************''

                    Dim ObjSlugLibrary As New BusinessFacade.TapeContent_News()
                    ObjSlugLibrary.SlugID = SlugID
                    ObjSlugLibrary.TapeLibraryID = LibraryID
                    ObjSlugLibrary.ProducedBy = 3
                    SlugLibraryID = ObjSlugLibrary.TapeSlugLibrary_SaveRecord()

                    ''***************************************************''
                    ''************* TapeContent Detail Save *************''
                    ''***************************************************''

                    Dim ObjDetail As New BusinessFacade.TapeContent_News()
                    ObjDetail.TapeContentID = TapeContentID
                    ObjDetail.TapeLibraryID = LibraryID
                    ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetail.MergeID = 1
                    ObjDetail.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjDetail.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjDetail.ProducedBy = UserID
                    ObjDetail.ProducedByName = UserName
                    ObjDetail.SlugID = SlugID
                    ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()

                    ''****************************************************''
                    ''*************** Slug Vs Keyword Save ***************''
                    ''****************************************************''

                    Dim q As Integer
                    For q = 0 To dg_KeyWord_2.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(q).Cells(4).Text Then
                            If dg_KeyWord_2.Rows(q).Cells(3).Text = "&nbsp;" Then
                                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                objtest2.NewsKeywordID = dg_KeyWord_2.Rows(q).Cells(2).Text
                                objtest2.TapeContentDetail_News_ID = TapeContentDetailID
                                objtest2.TapeSlugID = SlugID
                                objtest2.SlugVskeyword_SaveRecord()
                            End If
                        End If
                    Next

                    ''******************************************************''
                    ''******************** Footages ************************''
                    ''******************************************************''

                    Dim TapeContentFootage1 As Integer
                    Dim y As Integer
                    For y = 0 To dg_Footage.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(y).Cells(5).Text Then
                            '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                            Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                            ObjTapeContentFootage.TapeContentID = TapeContentDetailID
                            ObjTapeContentFootage.FootageID = dg_Footage.Rows(y).Cells(2).Text
                            ObjTapeContentFootage.userID = 3
                            ObjTapeContentFootage.SlugID = SlugID
                            TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                        End If
                    Next
                Else

                    ''*********************************************************************''
                    ''******************* Tape Content Detail News (Update) ***************''
                    ''*********************************************************************''

                    Dim ObjDetailUpd As New BusinessFacade.TapeContent_News()
                    ObjDetailUpd.TapeContentID = txt_TapeContentID.Text
                    ObjDetailUpd.TapeLibraryID = LibraryID
                    ObjDetailUpd.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetailUpd.ProducedBy = 3
                    ObjDetailUpd.MergeID = -1
                    ObjDetailUpd.ProducedBy = 3
                    ObjDetailUpd.ProducedByName = "Asmatullah"
                    ObjDetailUpd.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    ObjDetailUpd.TapeContentDetailID = dg_programInfo.Rows(P).Cells(10).Text
                    ObjDetailUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                    Dim ID As Integer
                    ID = ObjDetailUpd.UpdateRecord_TapeContentDetail_News()


                    ''*********************************************************************''
                    ''************************ Tape Slug (Update) *************************''
                    ''*********************************************************************''

                    Dim ObjSlugUpd As New BusinessFacade.TapeContent_News()
                    ObjSlugUpd.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                    ObjSlugUpd.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                    ObjSlugUpd.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjSlugUpd.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjSlugUpd.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                    ObjSlugUpd.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                    ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(8).Text
                    ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(7).Text
                    ObjSlugUpd.Duration = dg_programInfo.Rows(P).Cells(9).Text
                    ObjSlugUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                    ObjSlugUpd.ProducedBy = 3
                    Dim Slug_ID As Integer
                    Slug_ID = ObjSlugUpd.TapeSlug_UpdateRecord()

                    ''***********************************************''
                    ''**************** Slug Vs Keyword **************''
                    ''***********************************************''

                    Dim z As Integer
                    For z = 0 To dg_KeyWord_2.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(z).Cells(4).Text Then
                            If dg_KeyWord_2.Rows(z).Cells(3).Text = "&nbsp;" Then

                                ''*************************************''
                                ''******* Slug Vs Keyword (Save) ******''
                                ''*************************************''

                                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                objtest2.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                objtest2.TapeContentDetail_News_ID = ID
                                objtest2.TapeSlugID = Slug_ID
                                objtest2.SlugVskeyword_SaveRecord()
                            Else

                                ''*************************************''
                                ''***** Slug Vs Keyword (Update) ******''
                                ''*************************************''

                                Dim ObjUpdateKW As New BusinessFacade.SlugVsKeyword()
                                ObjUpdateKW.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                ObjUpdateKW.SlugVsKeywordID = dg_KeyWord_2.Rows(z).Cells(3).Text
                                ObjUpdateKW.TapeSlugID = Slug_ID
                                ObjUpdateKW.SlugVskeyword_Update()
                            End If
                        End If
                    Next

                    ''***********************************************''
                    ''**************** Slug Vs Footages *************''
                    ''***********************************************''

                    Dim l As Integer
                    For l = 0 To dg_Footage.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(l).Cells(5).Text Then
                            If dg_Footage.Rows(l).Cells(3).Text = "&nbsp;" Then

                                ''*************************************''
                                ''******* Slug Vs Keyword (Save) ******''
                                ''*************************************''

                                '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                ObjTapeContentFootage.TapeContentID = ID 'txt_TapeContentID.Text
                                ObjTapeContentFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                ObjTapeContentFootage.userID = 3
                                ObjTapeContentFootage.SlugID = Slug_ID
                                ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                            Else

                                '''''''''''''' Update Footage '''''''''''''''''''''''''''''''''''
                                Dim ObjUpdateFootage As New BusinessFacade.TapeContent_News()
                                ObjUpdateFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                ObjUpdateFootage.TapeContentFootageID = dg_Footage.Rows(l).Cells(3).Text
                                ObjUpdateFootage.Update_Footage_TapeContent()

                            End If
                        End If
                    Next


                End If
            Next

            ''*********************************************''
            ''************** merge Tapes ******************''
            ''*********************************************''
            Dim dt As DataTable
            Dim X As Integer
            For X = 0 To dg_Merge.Rows.Count - 1
                Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                Dim ObjTape As New BusinessFacade.TapeContent_News()
                ObjTape.TapeLibraryID = MergeTapeLibraryID
                dt = ObjTape.GetMergeTapeContentDetailID_News()
                Dim X2 As Integer
                For X2 = 0 To dt.Rows.Count - 1
                    Dim ObjTape2 As New BusinessFacade.TapeContent_News()
                    ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                    ObjTape2.TapeContentID = txt_TapeContentID.Text
                    ObjTape2.TapeLibraryID = LibraryID
                    ObjTape2.MergeArchivalNews()
                Next
            Next

            If dg_Merge.Rows.Count > 0 Then
                Dim Y As Integer
                For Y = 0 To dg_Merge.Rows.Count - 1
                    Dim ObjInsert As New BusinessFacade.TapeContent_News()
                    ObjInsert.TapeLibraryID = LibraryID
                    ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                    ObjInsert.userID = UserID
                    ObjInsert.InsertMergeArchivalNews()
                Next
            End If

            ''*********************************************''

            lblErr.Text = "Record has been Saved Successfully !!"

            If txt_TapeContentID.Text <> "" Then

                ''**************************************************************''
                ''***************** Search Engine Save Record ******************''
                ''**************************************************************''

                Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                ObjSearchEngine.TapeLibraryID = LibraryID 'txt_TapeContentID.Text
                ObjSearchEngine.SearchEngine_News_SaveRecord()
            End If

            ''***************************************''
            ''*********** Fill Grids ****************''
            ''***************************************''

            Me.Fill_ProgramInfo_Table(txt_TapeContentID.Text)
            Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
            Me.Fill_Footage_Table(txt_TapeContentID.Text)
            dg_Merge.DataSource = Nothing
            dg_Merge.DataBind()
            Me.Fill_MergeInformation(txt_TapeContentID.Text)

            ''**************** End ******************''
            ''***************************************''

        End If
    End Sub

    Protected Sub bttnClearSlug_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClearSlug.Click
        ClearSlug()
        ClearKeywords()
        ClearFootages()
        txt_ReporterSlug.Text = String.Empty
        txtSlugDate.Text = String.Empty
        'dg_programInfo.DataSource = Nothing
        'dg_programInfo.DataBind()
        'dg_KeyWord_2.DataSource = Nothing
        'dg_KeyWord_2.DataBind()
        'dg_Footage.DataSource = Nothing
        'dg_Footage.DataBind()

        ' ''**** Merge Section ****''
        'txt_MergeTape.Text = String.Empty
        'lblErr_Merge.Text = String.Empty
        'dg_Merge2.DataSource = Nothing
        'dg_Merge2.DataBind()
        'dg_Merge.DataSource = Nothing
        'dg_Merge.DataBind()
        'dt_MergeTapeNumber = Nothing
        'ViewState("MergeTape_Table") = Nothing

        'Dt_SaveKeyword = Nothing
        'Dt_SaveKeyword_2 = Nothing
        'Dt_SaveFootage = Nothing
        'dt_ProgramInfo = Nothing

        'ViewState("KeyWordTable_2") = Nothing
        'ViewState("ProgramInfo_Table") = Nothing
        'ViewState("FootageTable") = Nothing
        'ViewState("KeyWordTable") = Nothing

        ' ''**** Master Info ****''
        'ddl_Channel.SelectedIndex = 0
        'txt_Department.Text = String.Empty
        'ddl_SubCloset.SelectedIndex = 0
        'txt_ClassificationCode.Text = String.Empty
        'txt_CallNo.Text = String.Empty
        'txt_LocationCode.Text = String.Empty
        'ddl_RecycleTurn.SelectedIndex = 0
        'ddlCopies.SelectedIndex = 0
        'lblErr_MasterEntry.Text = String.Empty
        'txt_TapeContent_ID.Text = String.Empty
        'txt_dgProgramInfo_Index.Text = String.Empty
        'txt_dgKeyword_Index.Text = String.Empty
        'txt_TapeTypeID.Text = String.Empty
        'lblQueryString.Text = String.Empty
        'txt_TapeContentID.Text = String.Empty
        'txt_TapeNumber.Text = String.Empty

    End Sub

    Private Sub ClearSlug()
        'txt_ReporterSlug.Text = String.Empty
        txt_ProposedSlug.Text = "N/A"
        txt_Reporter.Text = String.Empty
        txt_EnglishScript.Text = "N/A"
        'txt_UrduScript.Text = String.Empty
        txt_UrduScript.Text = "N/A"
        ST1.Text = "00"
        ST2.Text = "00"
        ST3.Text = "00"
        ST4.Text = "00"
        END1.Text = "00"
        END2.Text = "00"
        END3.Text = "00"
        END4.Text = "00"
        DUR_1.Text = "00"
        DUR_2.Text = "00"
        DUR_3.Text = "00"
        DUR_4.Text = "00"
        txt_EndTime.Text = "00"
        txt_StartTime.Text = "00"
        txt_TimeDuration.Text = "00"
        lstFootageType.SelectedIndex = -1
        lstKeyword.SelectedIndex = -1
        lblErr.Text = String.Empty
        txt_CameraMan.Text = String.Empty

        txt_dgFootage_Index.Text = ""
        txt_dgProgramInfo_Index.Text = ""
        txt_dgKeyword_Index.Text = ""

    End Sub


    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        ''********************************************************''
        ''*************** Check Tape Availability ****************''
        ''********************************************************''

        Dim objCheckTape As New BusinessFacade.TapeContent()
        Dim Action As String = objCheckTape.CheckTapeAvailability(txt_TapeNumber.Text)
        If Action = "Not Allow" Then
            lblErr.Text = "Attention: This tape is not returned as Blank."
            Exit Sub
        End If

        ''********************************************************''

        If dgTapeNumber.Rows.Count > 0 Then

            Dim TapesCount As Integer
            For TapesCount = 0 To dgTapeNumber.Rows.Count - 1
                txt_TapeNumber.Text = dgTapeNumber.Rows(TapesCount).Cells(0).Text

                ''*********************************************''
                ''************* Get DepartmentID **************''
                ''*********************************************''

                Dim DepartmentID As Integer
                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_Department.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

                ''*********************************************''
                ''*************** Get User ID *****************''
                ''*********************************************''

                Dim UserID As Integer
                Dim objUserID As New BusinessFacade.Employee()
                objUserID.SM_LoginID = lbl_UserName.Text
                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                ''*********************************************''
                ''***************** Get User ID ***************''
                ''*********************************************''

                Dim UserName As String
                Dim objUserName As New BusinessFacade.Employee()
                objUserName.EmployeeID = UserID
                UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

                BulkTapesArchival(DepartmentID, UserID, UserName)
            Next

        Else

            lblErr_MasterEntry.Text = String.Empty
            Err_ReporterSlug.Visible = False
            Err_Department.Visible = False
            Err_TapeNumber.Visible = False

            ''****************************************''

            Dim arr As Array = Split(txt_TapeNumber.Text, "#")
            If arr.Length = 2 Then
                txt_TapeNumber.Text = arr(1)
            End If

            Dim dtibraryID As Data.DataTable = Nothing
            If txt_TapeNumber.Text <> "" Then
                Dim objLib As New BusinessFacade.TapeIssuance()
                objLib.TapeNumber = txt_TapeNumber.Text
                dtibraryID = objLib.TapeIssuance_GetLibraryID_datatable(objLib.TapeNumber)
            End If

            ''**************************************************************************''
            ''*************************** Base Station Validation **********************''
            ''**************************************************************************''

            Dim RT As Integer
            For RT = 0 To dtibraryID.Rows.Count - 1
                Dim objValidation As New BusinessFacade.NewTapeNumber()
                objValidation.TapeLibraryID = CInt(dtibraryID.Rows(RT).Item(0).ToString)
                Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                If BaseStationID <> CokieBaseStationID Then
                    lblErr.Text = "You are not allowed to Archive this Tape!!"
                    Exit Sub
                End If
            Next

            ''**************************************************************************''
            ''*********************************  END  **********************************''
            ''**************************************************************************''

            ''*********************************************''
            ''************* Get DepartmentID **************''
            ''*********************************************''

            Dim DepartmentID As Integer
            Dim objDeptID As New BusinessFacade.TapeIssuance()
            objDeptID.DepartmentName = txt_Department.Text
            DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

            ''*********************************************''
            ''*************** Get User ID *****************''
            ''*********************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''*********************************************''
            ''***************** Get User ID ***************''
            ''*********************************************''

            Dim UserName As String
            Dim objUserName As New BusinessFacade.Employee()
            objUserName.EmployeeID = UserID
            UserName = objUserName.GetEmployeeName_byEmployeeID(objUserID.EmployeeID)

            If dtibraryID.Rows.Count = 1 Then

                ''*********************************************''
                ''************ Get TapeLibraryID **************''
                ''*********************************************''

                Dim LibraryID As Integer
                Dim objLibID As New BusinessFacade.TapeIssuance()
                objLibID.TapeNumber = txt_TapeNumber.Text
                LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)


                Dim TapeContentID As Integer

                If DepartmentID = 0 Then
                    lblErr_MasterEntry.Text = "Please Select Department !!"
                    Err_Department.Visible = True
                ElseIf LibraryID = 0 Then
                    lblErr_MasterEntry.Text = "Please Select Tape Number !!"
                    Err_TapeNumber.Visible = True
                Else

                    ''****************************************************''
                    ''**************** Tape Content Save *****************''
                    ''****************************************************''

                    If txt_TapeContentID.Text = "" Then

                        Dim ObjUser As New BusinessFacade.TapeContent()
                        ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                        ObjUser.DepartmentID = DepartmentID
                        ObjUser.isCurrentContent = 0
                        ObjUser.ClassificationCode = txt_ClassificationCode.Text
                        ObjUser.CallNo = txt_CallNo.Text
                        ObjUser.LocationCode = txt_LocationCode.Text
                        ObjUser.IsActive = 1
                        ObjUser.Copies = ddlCopies.SelectedValue
                        ObjUser.UserID = UserID
                        ObjUser.LowResFileName = txtLowResFileName.Text
                        ObjUser.HighResFileName = txtHighResFileName.Text
                        ObjUser.FilePath = txtFiePath.Text
                        TapeContentID = ObjUser.SaveRecord()
                        txt_TapeContentID.Text = TapeContentID
                    Else
                        Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                        ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                        ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                        ObjUpdateUser.DepartmentID = DepartmentID
                        ObjUpdateUser.isCurrentContent = 0
                        ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                        ObjUpdateUser.CallNo = txt_CallNo.Text
                        ObjUpdateUser.LocationCode = txt_LocationCode.Text
                        ObjUpdateUser.IsActive = 1
                        ObjUpdateUser.Copies = ddlCopies.SelectedValue
                        ObjUpdateUser.UserID = UserID
                        ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                        ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                        ObjUpdateUser.FilePath = txtFiePath.Text
                        Dim O As Integer = ObjUpdateUser.UpdateRecord()
                        TapeContentID = txt_TapeContentID.Text

                    End If

                    ''***************************************************************''
                    ''********************* TapeContentDetail News ******************''
                    ''***************************************************************''

                    Dim SlugID As Integer
                    Dim SlugLibraryID As Integer
                    Dim TapeContentDetailID As Integer
                    Dim P As Integer
                    For P = 0 To dg_programInfo.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(10).Text = "&nbsp;" Then

                            ''*************************************************''
                            ''**************** Tape Slug **********************''
                            ''*************************************************''

                            Dim ObjSlug As New BusinessFacade.TapeContent_News()
                            ObjSlug.ReporterSlug = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(1).Text)
                            ObjSlug.ProposedSlug = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(2).Text)
                            ObjSlug.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                            ObjSlug.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                            ObjSlug.EnglishScript = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(5).Text)
                            ObjSlug.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                            ObjSlug.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                            ObjSlug.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                            ObjSlug.Duration = dg_programInfo.Rows(P).Cells(9).Text
                            ObjSlug.ProducedBy = UserID
                            ObjSlug.LowResFileName = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(16).Text) 'txtLowResFileName.Text
                            ObjSlug.HighResFileName = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(17).Text) 'txtHighResFileName.Text
                            ObjSlug.FilePath = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(18).Text) 'txtFiePath.Text
                            Try
                                SlugID = ObjSlug.TapeSlug_SaveRecord()
                            Catch ex As Exception
                                lblErr.Text = ex.Message
                                If Not (ex.InnerException Is Nothing) Then
                                    lblErr.Text = lblErr.Text + "<br/>" + ex.InnerException.Message
                                End If
                                Return
                            End Try

                            ''***************************************************''
                            ''**** update Tape Slug for Urdu Script Insertion ***''
                            ''***************************************************''

                            If dg_programInfo.Rows(P).Cells(6).Text <> "N/A" Then
                                Dim Con As System.Data.SqlClient.SqlConnection
                                Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                                Con = New System.Data.SqlClient.SqlConnection(connStr)
                                Dim cmd As New System.Data.SqlClient.SqlCommand
                                cmd.CommandType = Data.CommandType.StoredProcedure
                                cmd.CommandText = "Insert_UrduScript"
                                cmd.Connection = Con

                                Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                                p1.Value = SlugID

                                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                p2.Value = dg_programInfo.Rows(P).Cells(6).Text

                                If Con.State = ConnectionState.Closed Then
                                    Con.Open()
                                End If
                                cmd.CommandTimeout = 0
                                cmd.Connection = Con
                                cmd.ExecuteNonQuery()
                                cmd.Connection.Close()
                            End If


                            ''***************************************************''
                            ''*************** Insert in Slug Library ************''
                            ''***************************************************''

                            Dim ObjSlugLibrary As New BusinessFacade.TapeContent_News()
                            ObjSlugLibrary.SlugID = SlugID
                            ObjSlugLibrary.TapeLibraryID = LibraryID
                            ObjSlugLibrary.ProducedBy = UserID
                            SlugLibraryID = ObjSlugLibrary.TapeSlugLibrary_SaveRecord()

                            ''***************************************************''
                            ''************* TapeContent Detail Save *************''
                            ''***************************************************''

                            Dim ObjDetail As New BusinessFacade.TapeContent_News()
                            ObjDetail.TapeContentID = TapeContentID
                            ObjDetail.TapeLibraryID = LibraryID
                            ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                            ObjDetail.MergeID = 1
                            ObjDetail.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                            ObjDetail.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                            ObjDetail.ProducedBy = UserID
                            ObjDetail.ProducedByName = UserName
                            ObjDetail.SlugID = SlugID
                            ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                            'TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()
                            If txtEntryDate.Text = "" Then
                                ObjDetail.EntryDate = Date.Now.ToString("dd-MMM-yyyy")
                            Else
                                ObjDetail.EntryDate = dg_programInfo.Rows(P).Cells(14).Text 'txtEntryDate.Text
                            End If

                            TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News2()

                            ''****************************************************''
                            ''*************** Slug Vs Keyword Save ***************''
                            ''****************************************************''

                            Dim q As Integer
                            For q = 0 To dg_KeyWord_2.Rows.Count - 1
                                If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(q).Cells(4).Text Then
                                    If dg_KeyWord_2.Rows(q).Cells(3).Text = "&nbsp;" Then
                                        Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                        objtest2.NewsKeywordID = dg_KeyWord_2.Rows(q).Cells(2).Text
                                        objtest2.TapeContentDetail_News_ID = TapeContentDetailID
                                        objtest2.TapeSlugID = SlugID
                                        objtest2.SlugVskeyword_SaveRecord()
                                    End If
                                End If
                            Next

                            ''******************************************************''
                            ''******************** Footages ************************''
                            ''******************************************************''

                            Dim TapeContentFootage1 As Integer
                            Dim y As Integer
                            For y = 0 To dg_Footage.Rows.Count - 1
                                If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(y).Cells(5).Text Then
                                    '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                    Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                    ObjTapeContentFootage.TapeContentID = TapeContentDetailID
                                    ObjTapeContentFootage.FootageID = dg_Footage.Rows(y).Cells(2).Text
                                    ObjTapeContentFootage.userID = UserID
                                    ObjTapeContentFootage.SlugID = SlugID
                                    TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                                End If
                            Next
                        Else

                            ''*********************************************************************''
                            ''******************* Tape Content Detail News (Update) ***************''
                            ''*********************************************************************''

                            Dim ObjDetailUpd As New BusinessFacade.TapeContent_News()
                            ObjDetailUpd.TapeContentID = txt_TapeContentID.Text
                            ObjDetailUpd.TapeLibraryID = LibraryID
                            ObjDetailUpd.SubClosetID = ddl_SubCloset.SelectedValue
                            ObjDetailUpd.ProducedBy = UserID
                            ObjDetailUpd.MergeID = -1
                            ObjDetailUpd.ProducedBy = UserID
                            ObjDetailUpd.ProducedByName = "Asmatullah"
                            ObjDetailUpd.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                            ObjDetailUpd.TapeContentDetailID = dg_programInfo.Rows(P).Cells(10).Text
                            ObjDetailUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                            'TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()
                            If txtEntryDate.Text = "" Then
                                ObjDetailUpd.EntryDate = Date.Now.ToString("dd-MMM-yyyy")
                            Else
                                ObjDetailUpd.EntryDate = dg_programInfo.Rows(P).Cells(14).Text 'txtEntryDate.Text
                            End If

                            Dim ID As Integer
                            ID = ObjDetailUpd.UpdateRecord_TapeContentDetail_News2()


                            ''*********************************************************************''
                            ''************************ Tape Slug (Update) *************************''
                            ''*********************************************************************''

                            Dim ObjSlugUpd As New BusinessFacade.TapeContent_News()
                            ObjSlugUpd.ReporterSlug = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(1).Text)
                            ObjSlugUpd.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                            ObjSlugUpd.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                            ObjSlugUpd.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                            ObjSlugUpd.EnglishScript = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(5).Text)
                            ObjSlugUpd.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                            'ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(8).Text
                            'ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(7).Text
                            ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                            ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                            ObjSlugUpd.Duration = dg_programInfo.Rows(P).Cells(9).Text
                            ObjSlugUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                            ObjSlugUpd.ProducedBy = UserID
                            Dim Slug_ID As Integer
                            ObjSlugUpd.LowResFileName = dg_programInfo.Rows(P).Cells(16).Text 'txtLowResFileName.Text
                            ObjSlugUpd.HighResFileName = dg_programInfo.Rows(P).Cells(17).Text  'txtHighResFileName.Text
                            ObjSlugUpd.FilePath = dg_programInfo.Rows(P).Cells(18).Text  'txtFiePath.Text
                            Slug_ID = ObjSlugUpd.TapeSlug_UpdateRecord()

                            ''***************************************************''
                            ''**** update Tape Slug for Urdu Script Insertion ***''
                            ''***************************************************''

                            If dg_programInfo.Rows(P).Cells(6).Text <> "N/A" Then
                                Dim Con As System.Data.SqlClient.SqlConnection
                                Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                                Con = New System.Data.SqlClient.SqlConnection(connStr)
                                Dim cmd As New System.Data.SqlClient.SqlCommand
                                cmd.CommandType = Data.CommandType.StoredProcedure
                                cmd.CommandText = "Insert_UrduScript"
                                cmd.Connection = Con

                                Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                                p1.Value = Slug_ID

                                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                p2.Value = dg_programInfo.Rows(P).Cells(6).Text

                                If Con.State = ConnectionState.Closed Then
                                    Con.Open()
                                End If
                                cmd.CommandTimeout = 0
                                cmd.Connection = Con
                                cmd.ExecuteNonQuery()
                                cmd.Connection.Close()
                            End If


                            ''***********************************************''
                            ''**************** Slug Vs Keyword **************''
                            ''***********************************************''

                            Dim z As Integer
                            For z = 0 To dg_KeyWord_2.Rows.Count - 1
                                If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(z).Cells(4).Text Then
                                    If dg_KeyWord_2.Rows(z).Cells(3).Text = "&nbsp;" Then

                                        ''*************************************''
                                        ''******* Slug Vs Keyword (Save) ******''
                                        ''*************************************''

                                        Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                        objtest2.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                        objtest2.TapeContentDetail_News_ID = ID
                                        objtest2.TapeSlugID = Slug_ID
                                        objtest2.SlugVskeyword_SaveRecord()
                                    Else

                                        ''*************************************''
                                        ''***** Slug Vs Keyword (Update) ******''
                                        ''*************************************''

                                        Dim ObjUpdateKW As New BusinessFacade.SlugVsKeyword()
                                        ObjUpdateKW.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                        ObjUpdateKW.SlugVsKeywordID = dg_KeyWord_2.Rows(z).Cells(3).Text
                                        ObjUpdateKW.TapeSlugID = Slug_ID
                                        ObjUpdateKW.SlugVskeyword_Update()
                                    End If
                                End If
                            Next

                            ''***********************************************''
                            ''**************** Slug Vs Footages *************''
                            ''***********************************************''

                            Dim l As Integer
                            For l = 0 To dg_Footage.Rows.Count - 1
                                If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(l).Cells(5).Text Then
                                    If dg_Footage.Rows(l).Cells(3).Text = "&nbsp;" Then

                                        ''*************************************''
                                        ''******* Slug Vs Keyword (Save) ******''
                                        ''*************************************''

                                        '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                        Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                        ObjTapeContentFootage.TapeContentID = ID 'txt_TapeContentID.Text
                                        ObjTapeContentFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                        ObjTapeContentFootage.userID = UserID
                                        ObjTapeContentFootage.SlugID = Slug_ID
                                        ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                                    Else

                                        '''''''''''''' Update Footage '''''''''''''''''''''''''''''''''''
                                        Dim ObjUpdateFootage As New BusinessFacade.TapeContent_News()
                                        ObjUpdateFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                        ObjUpdateFootage.TapeContentFootageID = dg_Footage.Rows(l).Cells(3).Text
                                        ObjUpdateFootage.Update_Footage_TapeContent()

                                    End If
                                End If
                            Next


                        End If
                    Next

                    ''*********************************************''
                    ''************** merge Tapes ******************''
                    ''*********************************************''
                    Dim dt As DataTable
                    Dim X As Integer
                    For X = 0 To dg_Merge.Rows.Count - 1
                        Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                        Dim ObjTape As New BusinessFacade.TapeContent_News()
                        ObjTape.TapeLibraryID = MergeTapeLibraryID
                        dt = ObjTape.GetMergeTapeContentDetailID_News()
                        Dim X2 As Integer
                        For X2 = 0 To dt.Rows.Count - 1
                            Dim ObjTape2 As New BusinessFacade.TapeContent_News()
                            ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                            ObjTape2.TapeContentID = txt_TapeContentID.Text
                            ObjTape2.TapeLibraryID = LibraryID
                            ObjTape2.MergeArchivalNews()
                        Next
                    Next

                    If dg_Merge.Rows.Count > 0 Then
                        Dim Y As Integer
                        For Y = 0 To dg_Merge.Rows.Count - 1
                            Dim ObjInsert As New BusinessFacade.TapeContent_News()
                            ObjInsert.TapeLibraryID = LibraryID
                            ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                            ObjInsert.userID = UserID
                            ObjInsert.InsertMergeArchivalNews()
                        Next
                    End If

                    ''*********************************************''

                    lblErr.Text = "Record has been Saved Successfully !!"

                    '********************************************************'
                    '********** Update Tape Status in TapeLibrary ***********'
                    '********************************************************'

                    If LibraryID <> 0 Then
                        Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                        objUpdateTapeStatus.TapeLibraryID = LibraryID
                        objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                    End If


                    If txt_TapeContentID.Text <> "" Then

                        ''**************************************************************''
                        ''***************** Search Engine Save Record ******************''
                        ''**************************************************************''

                        Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                        ObjSearchEngine.TapeLibraryID = LibraryID 'txt_TapeContentID.Text
                        'ObjSearchEngine.SearchEngine_News_SaveRecord()
                        ObjSearchEngine.SearchEngine_News_SaveRecord3()
                    End If

                    ''***************************************''
                    ''*********** Fill Grids ****************''
                    ''***************************************''

                    Me.Fill_ProgramInfo_Table(txt_TapeContentID.Text)
                    Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                    Me.Fill_Footage_Table(txt_TapeContentID.Text)
                    dg_Merge.DataSource = Nothing
                    dg_Merge.DataBind()
                    Me.Fill_MergeInformation(txt_TapeContentID.Text)

                    ''**************** End ******************''
                    ''***************************************''

                    ''******************************************''
                    ''**** update Tape Slug for SearchEngine ***''
                    ''******************************************''

                    Dim O1 As Integer
                    For O1 = 0 To dg_programInfo.Rows.Count - 1
                        If dg_programInfo.Rows(O1).Cells(6).Text <> "N/A" Then
                            Dim Con As System.Data.SqlClient.SqlConnection
                            Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                            Con = New System.Data.SqlClient.SqlConnection(connStr)
                            Dim cmd As New System.Data.SqlClient.SqlCommand
                            cmd.CommandType = Data.CommandType.StoredProcedure
                            cmd.CommandText = "Insert_UrduScript_SearchEngine"
                            cmd.Connection = Con

                            Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                            p1.Value = dg_programInfo.Rows(O1).Cells(11).Text

                            Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                            p2.Value = dg_programInfo.Rows(O1).Cells(6).Text

                            If Con.State = ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd.CommandTimeout = 0
                            cmd.Connection = Con
                            cmd.ExecuteNonQuery()
                            cmd.Connection.Close()
                        End If
                    Next
                End If

                ''--------------------------------------------------------------------''

            ElseIf dtibraryID.Rows.Count = 2 Then
                Dim s As Integer
                For s = 0 To dtibraryID.Rows.Count - 1

                    Dim TapeContentID As Integer

                    If DepartmentID = 0 Then
                        lblErr_MasterEntry.Text = "Please Select Department !!"
                        Err_Department.Visible = True
                    Else

                        ''****************************************************''
                        ''**************** Tape Content Save *****************''
                        ''****************************************************''

                        If txt_TapeContentID.Text = "" Then

                            Dim ObjUser As New BusinessFacade.TapeContent()
                            ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                            ObjUser.DepartmentID = DepartmentID
                            ObjUser.isCurrentContent = 0
                            ObjUser.ClassificationCode = txt_ClassificationCode.Text
                            ObjUser.CallNo = txt_CallNo.Text
                            ObjUser.LocationCode = txt_LocationCode.Text
                            ObjUser.IsActive = 1
                            ObjUser.Copies = ddlCopies.SelectedValue
                            ObjUser.UserID = UserID
                            ObjUser.LowResFileName = txtLowResFileName.Text
                            ObjUser.HighResFileName = txtHighResFileName.Text
                            ObjUser.FilePath = txtFiePath.Text
                            TapeContentID = ObjUser.SaveRecord()
                            txt_TapeContentID.Text = TapeContentID
                        Else
                            Dim ObjUpdateUser As New BusinessFacade.TapeContent()
                            ObjUpdateUser.TapeContentID = txt_TapeContentID.Text
                            ObjUpdateUser.ContentTypeID = ddl_Channel.SelectedValue
                            ObjUpdateUser.DepartmentID = DepartmentID
                            ObjUpdateUser.isCurrentContent = 0
                            ObjUpdateUser.ClassificationCode = txt_ClassificationCode.Text
                            ObjUpdateUser.CallNo = txt_CallNo.Text
                            ObjUpdateUser.LocationCode = txt_LocationCode.Text
                            ObjUpdateUser.IsActive = 1
                            ObjUpdateUser.Copies = ddlCopies.SelectedValue
                            ObjUpdateUser.UserID = UserID
                            ObjUpdateUser.LowResFileName = txtLowResFileName.Text
                            ObjUpdateUser.HighResFileName = txtHighResFileName.Text
                            ObjUpdateUser.FilePath = txtFiePath.Text
                            Dim O As Integer = ObjUpdateUser.UpdateRecord()
                            TapeContentID = txt_TapeContentID.Text

                        End If

                        ''***************************************************************''
                        ''********************* TapeContentDetail News ******************''
                        ''***************************************************************''

                        Dim SlugID As Integer
                        Dim SlugLibraryID As Integer
                        Dim TapeContentDetailID As Integer
                        Dim P As Integer
                        For P = 0 To dg_programInfo.Rows.Count - 1
                            If dg_programInfo.Rows(P).Cells(10).Text = "&nbsp;" Then

                                ''*************************************************''
                                ''**************** Tape Slug **********************''
                                ''*************************************************''

                                Dim ObjSlug As New BusinessFacade.TapeContent_News()
                                ObjSlug.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                                ObjSlug.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                                ObjSlug.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                                ObjSlug.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                                ObjSlug.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                                ObjSlug.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                                ObjSlug.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                                ObjSlug.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                                ObjSlug.Duration = dg_programInfo.Rows(P).Cells(9).Text
                                ObjSlug.ProducedBy = UserID
                                SlugID = ObjSlug.TapeSlug_SaveRecord()

                                ''***************************************************''
                                ''**** update Tape Slug for Urdu Script Insertion ***''
                                ''***************************************************''
                                If dg_programInfo.Rows(P).Cells(6).Text <> "N/A" Then
                                    Dim Con As System.Data.SqlClient.SqlConnection
                                    Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                                    Dim cmd As New System.Data.SqlClient.SqlCommand
                                    cmd.CommandType = Data.CommandType.StoredProcedure
                                    cmd.CommandText = "Insert_UrduScript"
                                    cmd.Connection = Con

                                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                                    p1.Value = SlugID

                                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                    p2.Value = dg_programInfo.Rows(P).Cells(6).Text

                                    If Con.State = ConnectionState.Closed Then
                                        Con.Open()
                                    End If
                                    cmd.CommandTimeout = 0
                                    cmd.Connection = Con
                                    cmd.ExecuteNonQuery()
                                    cmd.Connection.Close()
                                End If


                                ''***************************************************''
                                ''*************** Insert in Slug Library ************''
                                ''***************************************************''

                                Dim ObjSlugLibrary As New BusinessFacade.TapeContent_News()
                                ObjSlugLibrary.SlugID = SlugID
                                ObjSlugLibrary.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                ObjSlugLibrary.ProducedBy = UserID
                                SlugLibraryID = ObjSlugLibrary.TapeSlugLibrary_SaveRecord()

                                ''***************************************************''
                                ''************* TapeContent Detail Save *************''
                                ''***************************************************''

                                Dim ObjDetail As New BusinessFacade.TapeContent_News()
                                ObjDetail.TapeContentID = TapeContentID
                                ObjDetail.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                                ObjDetail.MergeID = 1
                                ObjDetail.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                                ObjDetail.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                                ObjDetail.ProducedBy = UserID
                                ObjDetail.ProducedByName = UserName
                                ObjDetail.SlugID = SlugID
                                ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                'TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()
                                'TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News()
                                If txtEntryDate.Text = "" Then
                                    ObjDetail.EntryDate = Date.Now.ToString("dd-MMM-yyyy")
                                Else
                                    ObjDetail.EntryDate = dg_programInfo.Rows(P).Cells(14).Text 'txtEntryDate.Text
                                End If

                                TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News2()


                                ''****************************************************''
                                ''*************** Slug Vs Keyword Save ***************''
                                ''****************************************************''

                                Dim q As Integer
                                For q = 0 To dg_KeyWord_2.Rows.Count - 1
                                    If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(q).Cells(4).Text Then
                                        If dg_KeyWord_2.Rows(q).Cells(3).Text = "&nbsp;" Then
                                            Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                            objtest2.NewsKeywordID = dg_KeyWord_2.Rows(q).Cells(2).Text
                                            objtest2.TapeContentDetail_News_ID = TapeContentDetailID
                                            objtest2.TapeSlugID = SlugID
                                            objtest2.SlugVskeyword_SaveRecord()
                                        End If
                                    End If
                                Next

                                ''******************************************************''
                                ''******************** Footages ************************''
                                ''******************************************************''

                                Dim TapeContentFootage1 As Integer
                                Dim y As Integer
                                For y = 0 To dg_Footage.Rows.Count - 1
                                    If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(y).Cells(5).Text Then
                                        '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                        Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                        ObjTapeContentFootage.TapeContentID = TapeContentDetailID
                                        ObjTapeContentFootage.FootageID = dg_Footage.Rows(y).Cells(2).Text
                                        ObjTapeContentFootage.userID = UserID
                                        ObjTapeContentFootage.SlugID = SlugID
                                        TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                                    End If
                                Next
                            Else

                                ''*********************************************************************''
                                ''******************* Tape Content Detail News (Update) ***************''
                                ''*********************************************************************''

                                Dim ObjDetailUpd As New BusinessFacade.TapeContent_News()
                                ObjDetailUpd.TapeContentID = txt_TapeContentID.Text
                                ObjDetailUpd.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                ObjDetailUpd.SubClosetID = ddl_SubCloset.SelectedValue
                                ObjDetailUpd.ProducedBy = UserID
                                ObjDetailUpd.MergeID = -1
                                ObjDetailUpd.ProducedByName = UserName
                                ObjDetailUpd.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                                ObjDetailUpd.TapeContentDetailID = dg_programInfo.Rows(P).Cells(10).Text
                                ObjDetailUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                                'ID = ObjDetailUpd.UpdateRecord_TapeContentDetail_News()
                                If txtEntryDate.Text = "" Then
                                    ObjDetailUpd.EntryDate = Date.Now.ToString("dd-MMM-yyyy")
                                Else
                                    ObjDetailUpd.EntryDate = dg_programInfo.Rows(P).Cells(14).Text 'txtEntryDate.Text
                                End If

                                Dim ID As Integer
                                ID = ObjDetailUpd.UpdateRecord_TapeContentDetail_News2()



                                ''*********************************************************************''
                                ''************************ Tape Slug (Update) *************************''
                                ''*********************************************************************''

                                Dim ObjSlugUpd As New BusinessFacade.TapeContent_News()
                                ObjSlugUpd.ReporterSlug = dg_programInfo.Rows(P).Cells(1).Text
                                ObjSlugUpd.ProposedSlug = dg_programInfo.Rows(P).Cells(2).Text
                                ObjSlugUpd.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                                ObjSlugUpd.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                                ObjSlugUpd.EnglishScript = dg_programInfo.Rows(P).Cells(5).Text
                                ObjSlugUpd.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                                'ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(8).Text
                                'ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(7).Text
                                ObjSlugUpd.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                                ObjSlugUpd.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                                ObjSlugUpd.Duration = dg_programInfo.Rows(P).Cells(9).Text
                                ObjSlugUpd.SlugID = dg_programInfo.Rows(P).Cells(11).Text
                                ObjSlugUpd.ProducedBy = UserID
                                Dim Slug_ID As Integer
                                Slug_ID = ObjSlugUpd.TapeSlug_UpdateRecord()

                                ''***************************************************''
                                ''**** update Tape Slug for Urdu Script Insertion ***''
                                ''***************************************************''
                                If dg_programInfo.Rows(P).Cells(6).Text <> "N/A" Then
                                    Dim Con As System.Data.SqlClient.SqlConnection
                                    Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                                    Dim cmd As New System.Data.SqlClient.SqlCommand
                                    cmd.CommandType = Data.CommandType.StoredProcedure
                                    cmd.CommandText = "Insert_UrduScript"
                                    cmd.Connection = Con

                                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                                    p1.Value = Slug_ID

                                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                                    p2.Value = dg_programInfo.Rows(P).Cells(6).Text

                                    If Con.State = ConnectionState.Closed Then
                                        Con.Open()
                                    End If
                                    cmd.CommandTimeout = 0
                                    cmd.Connection = Con
                                    cmd.ExecuteNonQuery()
                                    cmd.Connection.Close()
                                End If

                                ''***********************************************''
                                ''**************** Slug Vs Keyword **************''
                                ''***********************************************''

                                Dim z As Integer
                                For z = 0 To dg_KeyWord_2.Rows.Count - 1
                                    If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(z).Cells(4).Text Then
                                        If dg_KeyWord_2.Rows(z).Cells(3).Text = "&nbsp;" Then

                                            ''*************************************''
                                            ''******* Slug Vs Keyword (Save) ******''
                                            ''*************************************''

                                            Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                            objtest2.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                            objtest2.TapeContentDetail_News_ID = ID
                                            objtest2.TapeSlugID = Slug_ID
                                            objtest2.SlugVskeyword_SaveRecord()
                                        Else

                                            ''*************************************''
                                            ''***** Slug Vs Keyword (Update) ******''
                                            ''*************************************''

                                            Dim ObjUpdateKW As New BusinessFacade.SlugVsKeyword()
                                            ObjUpdateKW.NewsKeywordID = dg_KeyWord_2.Rows(z).Cells(2).Text
                                            ObjUpdateKW.SlugVsKeywordID = dg_KeyWord_2.Rows(z).Cells(3).Text
                                            ObjUpdateKW.TapeSlugID = Slug_ID
                                            ObjUpdateKW.SlugVskeyword_Update()
                                        End If
                                    End If
                                Next

                                ''***********************************************''
                                ''**************** Slug Vs Footages *************''
                                ''***********************************************''

                                Dim l As Integer
                                For l = 0 To dg_Footage.Rows.Count - 1
                                    If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(l).Cells(5).Text Then
                                        If dg_Footage.Rows(l).Cells(3).Text = "&nbsp;" Then

                                            ''*************************************''
                                            ''******* Slug Vs Keyword (Save) ******''
                                            ''*************************************''

                                            '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                                            Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                                            ObjTapeContentFootage.TapeContentID = ID 'txt_TapeContentID.Text
                                            ObjTapeContentFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                            ObjTapeContentFootage.userID = UserID
                                            ObjTapeContentFootage.SlugID = Slug_ID
                                            ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                                        Else

                                            '''''''''''''' Update Footage '''''''''''''''''''''''''''''''''''
                                            Dim ObjUpdateFootage As New BusinessFacade.TapeContent_News()
                                            ObjUpdateFootage.FootageID = dg_Footage.Rows(l).Cells(2).Text
                                            ObjUpdateFootage.TapeContentFootageID = dg_Footage.Rows(l).Cells(3).Text
                                            ObjUpdateFootage.Update_Footage_TapeContent()

                                        End If
                                    End If
                                Next


                            End If
                        Next

                        ''*********************************************''
                        ''************** merge Tapes ******************''
                        ''*********************************************''
                        Dim dt As DataTable
                        Dim X As Integer
                        For X = 0 To dg_Merge.Rows.Count - 1
                            Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                            Dim ObjTape As New BusinessFacade.TapeContent_News()
                            ObjTape.TapeLibraryID = MergeTapeLibraryID
                            dt = ObjTape.GetMergeTapeContentDetailID_News()
                            Dim X2 As Integer
                            For X2 = 0 To dt.Rows.Count - 1
                                Dim ObjTape2 As New BusinessFacade.TapeContent_News()
                                ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                                ObjTape2.TapeContentID = txt_TapeContentID.Text
                                ObjTape2.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                ObjTape2.MergeArchivalNews()
                            Next
                        Next

                        If dg_Merge.Rows.Count > 0 Then
                            Dim Y As Integer
                            For Y = 0 To dg_Merge.Rows.Count - 1
                                Dim ObjInsert As New BusinessFacade.TapeContent_News()
                                ObjInsert.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                                ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                                ObjInsert.userID = UserID
                                ObjInsert.InsertMergeArchivalNews()
                            Next
                        End If

                        ''*********************************************''

                        lblErr.Text = "Record has been Saved Successfully !!"

                        '********************************************************'
                        '********** Update Tape Status in TapeLibrary ***********'
                        '********************************************************'

                        If dtibraryID.Rows(s).Item(0).ToString <> 0 Then
                            Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                            objUpdateTapeStatus.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString
                            objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
                        End If


                        If s = 0 Then

                            ''**************************************************************''
                            ''***************** Search Engine Save Record ******************''
                            ''**************************************************************''

                            Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                            ObjSearchEngine.TapeLibraryID = dtibraryID.Rows(s).Item(0).ToString 'txt_TapeContentID.Text
                            'ObjSearchEngine.SearchEngine_News_SaveRecord()
                            ObjSearchEngine.SearchEngine_News_SaveRecord3()


                            '************************ End ***************************'
                            If lblQueryString.Text = "" Then
                                txt_TapeContentID.Text = String.Empty
                            End If

                        End If

                        If lblQueryString.Text <> "" Then
                            s = s + 1
                        End If

                        ''***************************************''
                    End If
                Next

                ''***************************************''
                ''*********** Fill Grids ****************''
                ''***************************************''

                Me.Fill_ProgramInfo_Table(txt_TapeContentID.Text)
                Me.Fill_KeyWord_Table_2(txt_TapeContentID.Text)
                Me.Fill_Footage_Table(txt_TapeContentID.Text)
                dg_Merge.DataSource = Nothing
                dg_Merge.DataBind()
                Me.Fill_MergeInformation(txt_TapeContentID.Text)

                ''**************** End ******************''

                ''******************************************''
                ''**** update Tape Slug for SearchEngine ***''
                ''******************************************''

                Dim O2 As Integer
                For O2 = 0 To dg_programInfo.Rows.Count - 1
                    If dg_programInfo.Rows(O2).Cells(6).Text <> "N/A" Then
                        Dim Con As System.Data.SqlClient.SqlConnection
                        Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                        Con = New System.Data.SqlClient.SqlConnection(connStr)
                        Dim cmd As New System.Data.SqlClient.SqlCommand
                        cmd.CommandType = Data.CommandType.StoredProcedure
                        cmd.CommandText = "Insert_UrduScript_SearchEngine"
                        cmd.Connection = Con

                        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                        p1.Value = dg_programInfo.Rows(O2).Cells(11).Text

                        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                        p2.Value = dg_programInfo.Rows(O2).Cells(6).Text

                        If Con.State = ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd.CommandTimeout = 0
                        cmd.Connection = Con
                        cmd.ExecuteNonQuery()
                        cmd.Connection.Close()
                    End If
                Next
            End If
            'FillStartTime()
        End If


    End Sub

    Private Sub BulkTapesArchival(ByVal DepartmentID As Integer, ByVal UserID As Integer, ByVal UserName As String)

        lblErr_MasterEntry.Text = String.Empty
        Err_ReporterSlug.Visible = False
        Err_Department.Visible = False
        Err_TapeNumber.Visible = False

        ''****************************************''

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If



        ''*********************************************''
        ''************ Get TapeLibraryID **************''
        ''*********************************************''

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)


        ''*******************************''
        ''*** Base Station Validation ***''
        ''*******************************''

        Dim objValidation As New BusinessFacade.NewTapeNumber()
        objValidation.TapeLibraryID = LibraryID
        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        If BaseStationID <> CokieBaseStationID Then
            lblErr.Text = "You are not allowed to Archive this Tape!!"
            Exit Sub
        End If



        Dim TapeContentID As Integer

        If DepartmentID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Department !!"
            Err_Department.Visible = True
        ElseIf LibraryID = 0 Then
            lblErr_MasterEntry.Text = "Please Select Tape Number !!"
            Err_TapeNumber.Visible = True
        Else

            ''****************************************************''
            ''**************** Tape Content Save *****************''
            ''****************************************************''

            If txt_TapeContentID.Text = "" Then
                Dim ObjUser As New BusinessFacade.TapeContent()
                ObjUser.ContentTypeID = ddl_Channel.SelectedValue
                ObjUser.DepartmentID = DepartmentID
                ObjUser.isCurrentContent = 0
                ObjUser.ClassificationCode = txt_ClassificationCode.Text
                ObjUser.CallNo = txt_CallNo.Text
                ObjUser.LocationCode = txt_LocationCode.Text
                ObjUser.IsActive = 1
                ObjUser.Copies = ddlCopies.SelectedValue
                ObjUser.UserID = UserID
                ObjUser.LowResFileName = txtLowResFileName.Text
                ObjUser.HighResFileName = txtHighResFileName.Text
                ObjUser.FilePath = txtFiePath.Text
                TapeContentID = ObjUser.SaveRecord()
                txt_TapeContentID.Text = TapeContentID
            End If

            ''***************************************************************''
            ''********************* TapeContentDetail News ******************''
            ''***************************************************************''

            Dim SlugID As Integer
            Dim SlugLibraryID As Integer
            Dim TapeContentDetailID As Integer
            Dim P As Integer
            For P = 0 To dg_programInfo.Rows.Count - 1
                If dg_programInfo.Rows(P).Cells(10).Text = "&nbsp;" Then

                    ''*************************************************''
                    ''**************** Tape Slug **********************''
                    ''*************************************************''

                    Dim ObjSlug As New BusinessFacade.TapeContent_News()
                    ObjSlug.ReporterSlug = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(1).Text)
                    ObjSlug.ProposedSlug = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(2).Text)
                    ObjSlug.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjSlug.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjSlug.EnglishScript = Server.HtmlDecode(dg_programInfo.Rows(P).Cells(5).Text)
                    ObjSlug.UrduScript = dg_programInfo.Rows(P).Cells(6).Text
                    ObjSlug.StratTime = dg_programInfo.Rows(P).Cells(7).Text
                    ObjSlug.EndTime = dg_programInfo.Rows(P).Cells(8).Text
                    ObjSlug.Duration = dg_programInfo.Rows(P).Cells(9).Text
                    ObjSlug.ProducedBy = UserID
                    SlugID = ObjSlug.TapeSlug_SaveRecord()

                    ''***************************************************''
                    ''**** update Tape Slug for Urdu Script Insertion ***''
                    ''***************************************************''

                    If dg_programInfo.Rows(P).Cells(6).Text <> "N/A" Then
                        Dim Con As System.Data.SqlClient.SqlConnection
                        Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                        Con = New System.Data.SqlClient.SqlConnection(connStr)
                        Dim cmd As New System.Data.SqlClient.SqlCommand
                        cmd.CommandType = Data.CommandType.StoredProcedure
                        cmd.CommandText = "Insert_UrduScript"
                        cmd.Connection = Con

                        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                        p1.Value = SlugID

                        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                        p2.Value = dg_programInfo.Rows(P).Cells(6).Text

                        If Con.State = ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd.CommandTimeout = 0
                        cmd.Connection = Con
                        cmd.ExecuteNonQuery()
                        cmd.Connection.Close()
                    End If


                    ''***************************************************''
                    ''*************** Insert in Slug Library ************''
                    ''***************************************************''

                    Dim ObjSlugLibrary As New BusinessFacade.TapeContent_News()
                    ObjSlugLibrary.SlugID = SlugID
                    ObjSlugLibrary.TapeLibraryID = LibraryID
                    ObjSlugLibrary.ProducedBy = 3
                    SlugLibraryID = ObjSlugLibrary.TapeSlugLibrary_SaveRecord()

                    ''***************************************************''
                    ''************* TapeContent Detail Save *************''
                    ''***************************************************''

                    Dim ObjDetail As New BusinessFacade.TapeContent_News()
                    ObjDetail.TapeContentID = TapeContentID
                    ObjDetail.TapeLibraryID = LibraryID
                    ObjDetail.SubClosetID = ddl_SubCloset.SelectedValue
                    ObjDetail.MergeID = 1
                    ObjDetail.ReporterID = dg_programInfo.Rows(P).Cells(3).Text
                    ObjDetail.CameraManID = dg_programInfo.Rows(P).Cells(4).Text
                    ObjDetail.ProducedBy = UserID
                    ObjDetail.ProducedByName = UserName
                    ObjDetail.SlugID = SlugID
                    ObjDetail.RecycleTurnID = ddl_RecycleTurn.SelectedValue
                    If txtEntryDate.Text = "" Then
                        ObjDetail.EntryDate = Date.Now.ToString("dd-MMM-yyyy")
                    Else
                        ObjDetail.EntryDate = txtEntryDate.Text
                    End If

                    TapeContentDetailID = ObjDetail.SaveRecord_TapeContentDetail_News2()

                    ''****************************************************''
                    ''*************** Slug Vs Keyword Save ***************''
                    ''****************************************************''

                    Dim q As Integer
                    For q = 0 To dg_KeyWord_2.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_KeyWord_2.Rows(q).Cells(4).Text Then
                            If dg_KeyWord_2.Rows(q).Cells(3).Text = "&nbsp;" Then
                                Dim objtest2 As New BusinessFacade.SlugVsKeyword()
                                objtest2.NewsKeywordID = dg_KeyWord_2.Rows(q).Cells(2).Text
                                objtest2.TapeContentDetail_News_ID = TapeContentDetailID
                                objtest2.TapeSlugID = SlugID
                                objtest2.SlugVskeyword_SaveRecord()
                            End If
                        End If
                    Next

                    ''******************************************************''
                    ''******************** Footages ************************''
                    ''******************************************************''

                    Dim TapeContentFootage1 As Integer
                    Dim y As Integer
                    For y = 0 To dg_Footage.Rows.Count - 1
                        If dg_programInfo.Rows(P).Cells(1).Text = dg_Footage.Rows(y).Cells(5).Text Then
                            '''''''''''''''''' Insert TapeContent Footage '''''''''''''''''''
                            Dim ObjTapeContentFootage As New BusinessFacade.TapeContent_News()
                            ObjTapeContentFootage.TapeContentID = TapeContentDetailID
                            ObjTapeContentFootage.FootageID = dg_Footage.Rows(y).Cells(2).Text
                            ObjTapeContentFootage.userID = 3
                            ObjTapeContentFootage.SlugID = SlugID
                            TapeContentFootage1 = ObjTapeContentFootage.SaveRecord_TapeContentFootage()
                        End If
                    Next

                End If
            Next

            ''*********************************************''
            ''************** merge Tapes ******************''
            ''*********************************************''

            Dim dt As DataTable
            Dim X As Integer
            For X = 0 To dg_Merge.Rows.Count - 1
                Dim MergeTapeLibraryID = dg_Merge.Rows(X).Cells(0).Text
                Dim ObjTape As New BusinessFacade.TapeContent_News()
                ObjTape.TapeLibraryID = MergeTapeLibraryID
                dt = ObjTape.GetMergeTapeContentDetailID_News()
                Dim X2 As Integer
                For X2 = 0 To dt.Rows.Count - 1
                    Dim ObjTape2 As New BusinessFacade.TapeContent_News()
                    ObjTape2.TapeContentDetailID = dt.Rows(X2).Item(0).ToString
                    ObjTape2.TapeContentID = txt_TapeContentID.Text
                    ObjTape2.TapeLibraryID = LibraryID
                    ObjTape2.MergeArchivalNews()
                Next
            Next

            If dg_Merge.Rows.Count > 0 Then
                Dim Y As Integer
                For Y = 0 To dg_Merge.Rows.Count - 1
                    Dim ObjInsert As New BusinessFacade.TapeContent_News()
                    ObjInsert.TapeLibraryID = LibraryID
                    ObjInsert.MergeTapeLibraryID = dg_Merge.Rows(Y).Cells(0).Text
                    ObjInsert.userID = UserID
                    ObjInsert.InsertMergeArchivalNews()
                Next
            End If

            ''*********************************************''

            lblErr.Text = "Record has been Saved Successfully !!"

            '********************************************************'
            '********** Update Tape Status in TapeLibrary ***********'
            '********************************************************'

            If LibraryID <> 0 Then
                Dim objUpdateTapeStatus As New BusinessFacade.TapeLibrary()
                objUpdateTapeStatus.TapeLibraryID = LibraryID
                objUpdateTapeStatus.Update_TapeLibrary_CurrentTapeStatus()
            End If


            If txt_TapeContentID.Text <> "" Then

                ''**************************************************************''
                ''***************** Search Engine Save Record ******************''
                ''**************************************************************''

                Dim ObjSearchEngine As New BusinessFacade.SearchEngine()
                ObjSearchEngine.TapeLibraryID = LibraryID
                ObjSearchEngine.SearchEngine_News_SaveRecord3()
            End If

            ''**************** End ******************''
            ''***************************************''

            ''******************************************''
            ''**** update Tape Slug for SearchEngine ***''
            ''******************************************''

            Dim O1 As Integer
            For O1 = 0 To dg_programInfo.Rows.Count - 1
                If dg_programInfo.Rows(O1).Cells(6).Text <> "N/A" Then
                    Dim Con As System.Data.SqlClient.SqlConnection
                    Dim connStr As String = ConfigurationManager.AppSettings("ConnectionString")
                    Con = New System.Data.SqlClient.SqlConnection(connStr)
                    Dim cmd As New System.Data.SqlClient.SqlCommand
                    cmd.CommandType = Data.CommandType.StoredProcedure
                    cmd.CommandText = "Insert_UrduScript_SearchEngine"
                    cmd.Connection = Con

                    Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@slugid", Data.SqlDbType.Int)
                    p1.Value = dg_programInfo.Rows(O1).Cells(11).Text

                    Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@urdu", Data.SqlDbType.NText)
                    p2.Value = dg_programInfo.Rows(O1).Cells(6).Text

                    If Con.State = ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd.CommandTimeout = 0
                    cmd.Connection = Con
                    cmd.ExecuteNonQuery()
                    cmd.Connection.Close()
                End If
            Next
        End If

        txt_TapeContentID.Text = String.Empty

        ''--------------------------------------------------------------------''

        'FillStartTime()
    End Sub

    Protected Sub bttnClrFootageType_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClrFootageType.Click
        lstFootageType.SelectedIndex = -1
        lstKeyword.SelectedIndex = -1
    End Sub

    Protected Sub dg_programInfo_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_programInfo.RowDataBound
        For Each tc As TableCell In e.Row.Cells
            tc.Attributes("style") = "border-color:#99BAE9"
        Next

    End Sub

    Protected Sub TabContainer1_ActiveTabChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles TabContainer1.ActiveTabChanged
        TabContainer1.ActiveTab.TabIndex = 2
    End Sub

    Protected Sub AddKeyword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles AddKeyword.Click

        SaveKeyword()

        SaveFootage()

        ClearKeywords()

        ClearFootages()

        txt_ReporterSlug.Text = String.Empty

    End Sub

    Private Sub ClearKeywords()
        KW1.Text = String.Empty
        KW2.Text = String.Empty
        KW3.Text = String.Empty
        KW4.Text = String.Empty
        KW5.Text = String.Empty
        KW6.Text = String.Empty
        KW7.Text = String.Empty
    End Sub

    Private Sub ClearFootages()
        FT1.Text = String.Empty
        FT2.Text = String.Empty
        FT3.Text = String.Empty
        FT4.Text = String.Empty
        FT5.Text = String.Empty
        FT6.Text = String.Empty
        FT7.Text = String.Empty
    End Sub

    Private Sub SaveFootage()

        ''****************************************************************************************''
        ''********************************** Save Footages ***************************************''
        ''****************************************************************************************''

        If ViewState("FootageTable") Is Nothing Then
            Dim colFootage1 As DataColumn = New DataColumn("FootageType")
            colFootage1.DataType = System.Type.GetType("System.String")
            Dt_SaveFootage.Columns.Add(colFootage1)

            Dim colFootage2 As DataColumn = New DataColumn("FootageTypeID")
            colFootage2.DataType = System.Type.GetType("System.Int32")
            Dt_SaveFootage.Columns.Add(colFootage2)

            Dim colFootage3 As DataColumn = New DataColumn("TapeContentFootageID")
            colFootage3.DataType = System.Type.GetType("System.Int32")
            Dt_SaveFootage.Columns.Add(colFootage3)

            Dim colFootage4 As DataColumn = New DataColumn("TapeContentID")
            colFootage4.DataType = System.Type.GetType("System.Int32")
            Dt_SaveFootage.Columns.Add(colFootage4)

            Dim colFootage5 As DataColumn = New DataColumn("TapeSlug")
            colFootage5.DataType = System.Type.GetType("System.String")
            Dt_SaveFootage.Columns.Add(colFootage5)
        Else
            Dt_SaveFootage = ViewState("FootageTable")
        End If

        If txt_dgFootage_Index.Text <> "" Then

            ''*******************************************''
            ''*********** Get FootageTypeID *************''
            ''*******************************************''

            Dim FootageTypeID As Integer
            Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
            objFootageTypeID.FootageType = FT1.Text
            FootageTypeID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

            ''*******************************************''

            Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).BeginEdit()
            Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageType") = FT1.Text
            Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).Item("FootageTypeID") = FootageTypeID
            Dt_SaveFootage.Rows(txt_dgFootage_Index.Text).EndEdit()
            txt_dgFootage_Index.Text = String.Empty

        Else

            Dim x As Integer
            For x = 1 To 7

                Dim FT As String = ""
                If x = 1 Then
                    FT = FT1.Text
                ElseIf x = 2 Then
                    FT = FT2.Text
                ElseIf x = 3 Then
                    FT = FT3.Text
                ElseIf x = 4 Then
                    FT = FT4.Text
                ElseIf x = 5 Then
                    FT = FT5.Text
                ElseIf x = 6 Then
                    FT = FT6.Text
                Else
                    FT = FT7.Text
                End If

                Dim isExistsFootage As Integer = CheckFootages(FT)

                If isExistsFootage = 0 Then
                    ''*******************************************''
                    ''*********** Get FootageTypeID *************''
                    ''*******************************************''

                    Dim FTID As Integer
                    Dim objFootageTypeID As New BusinessFacade.TapeContent_News()
                    objFootageTypeID.FootageType = FT
                    FTID = objFootageTypeID.GetFootageTypeID_byFootageTypeName(objFootageTypeID.FootageType)

                    ''*******************************************''
                    ''*********************************************''

                    If FTID <> 0 Then
                        Dim FootageRow As DataRow
                        FootageRow = Dt_SaveFootage.NewRow
                        FootageRow.Item("TapeContentID") = DBNull.Value
                        FootageRow.Item("TapeSlug") = txt_ReporterSlug.Text
                        FootageRow.Item("FootageType") = FT
                        FootageRow.Item("FootageTypeID") = FTID
                        FootageRow.Item("TapeContentFootageID") = DBNull.Value
                        Dt_SaveFootage.Rows.Add(FootageRow)
                    End If
                End If
            Next
        End If

        ViewState("FootageTable") = Dt_SaveFootage
        Bind_DgFootage()

        ''************** End ***************''
        ''**********************************''

    End Sub

    Private Sub SaveKeyword()

        ''****************************************************************************************''
        ''********************************** Save Keywords ***************************************''
        ''****************************************************************************************''

        If ViewState("KeyWordTable_2") Is Nothing Then
            Dim col1 As DataColumn = New DataColumn("TapeSlugID")
            col1.DataType = System.Type.GetType("System.Int32")
            Dt_SaveKeyword_2.Columns.Add(col1)

            Dim col2 As DataColumn = New DataColumn("NewsKeywordID")
            col2.DataType = System.Type.GetType("System.Int32")
            Dt_SaveKeyword_2.Columns.Add(col2)

            Dim col3 As DataColumn = New DataColumn("SlugVsKeywordID")
            col3.DataType = System.Type.GetType("System.Int32")
            Dt_SaveKeyword_2.Columns.Add(col3)

            Dim col4 As DataColumn = New DataColumn("TapeSlug")
            col4.DataType = System.Type.GetType("System.String")
            Dt_SaveKeyword_2.Columns.Add(col4)

            Dim col5 As DataColumn = New DataColumn("NewsKeyword")
            col5.DataType = System.Type.GetType("System.String")
            Dt_SaveKeyword_2.Columns.Add(col5)

        Else
            Dt_SaveKeyword_2 = ViewState("KeyWordTable_2")
        End If

        If txt_dgKeyword_Index.Text <> "" Then

            ''*********************************************''
            ''********** For Getting KeywordID ************''
            ''*********************************************''

            Dim KWID As Integer
            Dim ObjKWID As New BusinessFacade.NewsKeyword()
            ObjKWID.NewsKeyword = KW1.Text
            KWID = ObjKWID.GetKeywordID_News_AutoComplete(ObjKWID.NewsKeyword)

            ''*********************************************''

            If KWID <> "0" Then
                Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).BeginEdit()
                Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeywordID") = KWID
                Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).Item("NewsKeyword") = KW1.Text
                Dt_SaveKeyword_2.Rows(txt_dgKeyword_Index.Text).EndEdit()
                txt_dgKeyword_Index.Text = String.Empty
            End If

        Else

            Dim x As Integer
            For x = 1 To 7

                Dim KW As String = ""
                If x = 1 Then
                    KW = KW1.Text
                ElseIf x = 2 Then
                    KW = KW2.Text
                ElseIf x = 3 Then
                    KW = KW3.Text
                ElseIf x = 4 Then
                    KW = KW4.Text
                ElseIf x = 5 Then
                    KW = KW5.Text
                ElseIf x = 6 Then
                    KW = KW6.Text
                Else
                    KW = KW7.Text
                End If

                Dim IsKeywordExists As Integer = CheckKeywords(KW)

                If IsKeywordExists = 0 Then
                    ''*********************************************''
                    ''********** For Getting KeywordID ************''
                    ''*********************************************''

                    Dim KWID As Integer
                    Dim ObjKWID As New BusinessFacade.NewsKeyword()
                    ObjKWID.NewsKeyword = KW
                    KWID = ObjKWID.GetKeywordID_News_AutoComplete(ObjKWID.NewsKeyword)

                    ''*********************************************''

                    If KWID <> 0 Then
                        Dim Rw As DataRow
                        Rw = Dt_SaveKeyword_2.NewRow()
                        Rw.Item("TapeSlugID") = DBNull.Value
                        Rw.Item("TapeSlug") = txt_ReporterSlug.Text
                        Rw.Item("NewsKeywordID") = KWID
                        Rw.Item("NewsKeyword") = KW
                        Rw.Item("SlugVsKeywordID") = DBNull.Value
                        Dt_SaveKeyword_2.Rows.Add(Rw)

                    End If
                End If
            Next

        End If

        ViewState("KeyWordTable_2") = Dt_SaveKeyword_2
        Bind_dgKeyWord_2()

        ''****************************************************************************************''
        ''*********************************** End Keywords ***************************************''
        ''****************************************************************************************''

    End Sub

    Protected Sub LnkShowKeyBoard_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkShowKeyBoard.Click
        If LnkShowKeyBoard.Text = "Show Keyboard" Then
            ImgKeyBoard.Visible = True
            LnkShowKeyBoard.Text = "Hide Keyboard"
        Else
            ImgKeyBoard.Visible = False
            LnkShowKeyBoard.Text = "Show Keyboard"
        End If

        TabContainer1.ActiveTabIndex = CInt(2)

    End Sub

    Private Function CheckKeywords(ByVal Keyword As String)
        Dim Count As Integer = 0
        Dim I As Integer
        For I = 0 To dg_KeyWord_2.Rows.Count - 1
            If Server.HtmlDecode(dg_KeyWord_2.Rows(I).Cells(5).Text) = Keyword And Server.HtmlDecode(dg_KeyWord_2.Rows(I).Cells(4).Text) = txt_ReporterSlug.Text Then
                Count = Count + 1
            End If
        Next
        Return Count
    End Function

    Private Function CheckFootages(ByVal Footage As String)
        Dim Count As Integer = 0
        Dim I As Integer
        For I = 0 To dg_Footage.Rows.Count - 1
            If Server.HtmlDecode(dg_Footage.Rows(I).Cells(1).Text) = Footage And Server.HtmlDecode(dg_Footage.Rows(I).Cells(5).Text) = txt_ReporterSlug.Text Then
                Count = Count + 1
            End If
        Next
        Return Count
    End Function

    Protected Sub lnkBulkTapes_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkBulkTapes.Click
        lblErr_MasterEntry.Text = String.Empty

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''**********************************************''
        ''************* Get TapeLibraryID **************''
        ''**********************************************''

        Dim dtibraryID As Data.DataTable = Nothing

        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txt_TapeNumber.Text
        dtibraryID = objLibID.TapeIssuance_GetLibraryID_datatable(objLibID.TapeNumber)

        ''**********************************************''

        If Not dtibraryID Is Nothing Then

            Dim column1 As DataColumn = New DataColumn("TapeNumber")
            column1.DataType = System.Type.GetType("System.String")
            dt_BulkTapeNumber.Columns.Add("TapeNumber")

            If ViewState("BulkTapeNumber") Is Nothing Then
            Else
                dt_BulkTapeNumber = ViewState("BulkTapeNumber")
            End If


            Dim row As DataRow
            row = dt_BulkTapeNumber.NewRow
            row.Item("TapeNumber") = txt_TapeNumber.Text
            dt_BulkTapeNumber.Rows.Add(row)
            ViewState("BulkTapeNumber") = dt_BulkTapeNumber

            dgTapeNumber.DataSource = dt_BulkTapeNumber
            dgTapeNumber.DataBind()

        Else
            lblErr_MasterEntry.Text = "Attention : Please Enter Correct Tape Number!! "
        End If

    End Sub

End Class


