Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Reports_ReportViewer_excel
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim reportName As String = Request("reportname")

        Dim qryParam As Integer = Request.QueryString.Count
        Dim keys() As String = Request.QueryString.AllKeys
        'Dim paramValues As New ArrayList()
        Dim paramValues As New Hashtable()
        Dim i As Integer = 0
        For i = 1 To keys.Length - 1
            paramValues.Add(keys(i), Request.QueryString.Get(keys(i)))
        Next
        LoadPDFReport(reportName, paramValues)
        If Not Page.IsPostBack Then

        End If

    End Sub

    Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)
        '--- Exporting Crystal Report to PDF process...
        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports")
        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/reports") & "\" & rpt
        crReportDocument.Load(rpt)
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions


        ' destinationOption.DiskFileName = ExportPath & "\" & Replace(RptName, ".rpt", ".pdf")
        destinationOption.DiskFileName = ExportPath & "\" & Replace(RptName, ".rpt", ".xls")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.Excel
        'expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        crReportDocument.Export()
        crReportDocument.Close()


        'Dim scriptString As String = "<script type='text/javascript'>" & _
        '   "window.onload = function(){" & _
        '   "var url = '" + "../reports/" + Replace(RptName, ".rpt", ".pdf") + "';" & _
        '   "var winPop = window.open(url,'winPop');" & _
        '   "}" & _
        '   "</script>"
        Dim scriptString As String = "<script type='text/javascript'>" & _
         "window.onload = function(){" & _
         "var url = '" + "../reports/" + Replace(RptName, ".rpt", ".xls") + "';" & _
         "var winPop = window.open(url,'winPop');" & _
         "}" & _
         "</script>"
        'Register this script to avoid font size increase automatically when viewing the report
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)

        'This was causing to enlarge the font size automatically
        'Response.Write(scriptString)   

    End Sub

    Public Function convertAndOpeninPDF(ByVal Rptname As String, ByVal paramValues As Hashtable) As String
        Dim pathandname As String
        Dim ReportName As String
        Dim DiskOpts As CrystalDecisions.Shared.DiskFileDestinationOptions = New CrystalDecisions.Shared.DiskFileDestinationOptions

        ' ReportName = Replace(Rptname, ".rpt", ".pdf")
        ReportName = Replace(Rptname, ".rpt", ".xls")
        pathandname = Server.MapPath(Rptname)

        'Try
        Dim RptDocument As New CrystalDecisions.CrystalReports.Engine.ReportDocument

        RptDocument.Load(pathandname)
        ApplyConnectionInfo(RptDocument)

        'Dim paramValues As New Hashtable
        'paramValues.Add("@Datefrom", "05/04/2008")
        'paramValues.Add("@dateto", "05/04/2008")

        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator
        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)
            RptDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        Dim exportpath As String = Server.MapPath(ReportName)

        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions
        destinationOption.DiskFileName = exportpath
        expOptions = RptDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption
        expOptions.ExportDestinationType = ExportDestinationType.DiskFile
        expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        RptDocument.Export()
        RptDocument.Close()

        Return ReportName

        'Catch ex As Exception
        '    Return Nothing
        'End Try
    End Function

    Private Sub ApplyConnectionInfo(ByRef pRptDocument As ReportDocument)

        Dim logOnInfo As New CrystalDecisions.Shared.TableLogOnInfo
        logOnInfo = pRptDocument.Database.Tables(0).LogOnInfo

        'Dim strConnection As String = "Data Source=intranet-db\sapm;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********"
        'Dim strConnection As String = "Data Source=MISOPS1\Dams;Initial Catalog=DAMS_NewDB;User ID=dams_user;Password=**********"
        Dim strConnection As String = "Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********"

        Dim connection As New SqlClient.SqlConnection(strConnection)

        Dim connectionInfo As New CrystalDecisions.Shared.ConnectionInfo
        connectionInfo = pRptDocument.Database.Tables.Item(0).LogOnInfo.ConnectionInfo
        ' Set the Connection parameters.

        'connectionInfo.DatabaseName = con.DatabaseName
        'connectionInfo.ServerName = con.ServerName
        connectionInfo.Password = "********"
        'connectionInfo.UserID = "sa"
        connectionInfo.UserID = "sa"
        logOnInfo.ConnectionInfo = connectionInfo
        pRptDocument.Database.Tables(0).ApplyLogOnInfo(logOnInfo)

        Dim sec As Section
        Dim rptObj As ReportObject
        Dim subRpt As CrystalDecisions.CrystalReports.Engine.SubreportObject

        For Each sec In pRptDocument.ReportDefinition.Sections
            For Each rptObj In sec.ReportObjects
                If rptObj.Kind = CrystalDecisions.[Shared].ReportObjectKind.SubreportObject Then
                    subRpt = CType(rptObj, CrystalDecisions.CrystalReports.Engine.SubreportObject)
                    subRpt.OpenSubreport(subRpt.SubreportName).Database.Tables(0).ApplyLogOnInfo(logOnInfo)
                End If
            Next
        Next
    End Sub

End Class
