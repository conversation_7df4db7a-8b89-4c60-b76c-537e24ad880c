<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmDepartment.aspx.vb" Inherits="ApplicationSetup_frmDepartment" title="Home > Department > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Department" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 19px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w3">Home</asp:LinkButton> &gt; Department &gt; Add New</TD></TR><TR><TD><TABLE style="WIDTH: 592px"><TBODY><TR class="mytext"><TD style="WIDTH: 129px">Department Name</TD><TD style="WIDTH: 177px">City Name</TD><TD style="WIDTH: 92px">Tape Due Days</TD><TD></TD><TD></TD></TR><TR class="mytext"><TD style="WIDTH: 129px"><asp:TextBox id="txt_DepartmentName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD><TD style="WIDTH: 177px"><asp:DropDownList id="ddl_City" runat="server" CssClass="mytext" Width="160px">
                            </asp:DropDownList></TD><TD style="WIDTH: 92px"><asp:TextBox id="txt_TapeDueDays" runat="server" CssClass="mytext" Width="72px"></asp:TextBox></TD><TD></TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="432px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 29px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w7" Font-Bold="True"></asp:Label></TD></TR><TR><TD><asp:GridView id="dg_Department" runat="server" CssClass="gridContent" Width="736px" AllowPaging="True" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="DepartmentID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="DepartmentName" HeaderText="DepartmentName" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="TapeDueDays" HeaderText="Tape Due Days"></asp:BoundField>
<asp:BoundField DataField="CityID" Visible="False">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="CityName" HeaderText="City Name"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD style="HEIGHT: 21px"><asp:Label id="lblAuditHistory" runat="server" CssClass="labelheading" __designer:wfdid="w3" Visible="False">Audit History - Employee</asp:Label></TD></TR><TR><TD style="HEIGHT: 21px"><asp:GridView id="dgAuditHistory" runat="server" CssClass="gridContent" __designer:wfdid="w5" Width="100%" PageSize="25" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD style="HEIGHT: 21px"><asp:TextBox id="txt_DepartmentID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:TextBox id="txt_DepartmentID_SM" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:DropDownList id="ddl_Department_SM" runat="server" CssClass="mytext" Width="160px" Visible="False">
                            </asp:DropDownList> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w7" Visible="False"></asp:Label></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w12" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

