<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmFootageType.aspx.vb" Inherits="ApplicationSetup_frmFootageType" title="Home > Footage Type > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="WIDTH: 100%; HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="lnkHomePage" onclick="lnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w12">Home</asp:LinkButton>&nbsp; &gt;&nbsp;Footage Type&nbsp;&gt; Add New</TD></TR><TR><TD style="WIDTH: 357px"><TABLE><TBODY><TR class="mytext"><TD>Footage Type Name</TD><TD><asp:TextBox id="txt_FootageTypeName" runat="server" CssClass="mytext"></asp:TextBox></TD><TD></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="288px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 357px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD style="WIDTH: 357px" align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w9" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 357px"><asp:GridView id="dg_FootageType" runat="server" CssClass="gridContent" Width="100%" OnPageIndexChanging="dg_FootageType_PageIndexChanging" OnSelectedIndexChanged="dg_FootageType_SelectedIndexChanged1" AllowPaging="True" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="FootageTypeID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="FootageTypeName" HeaderText="Footage Type" ApplyFormatInEditMode="True">
<HeaderStyle Width="350px"></HeaderStyle>
</asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w17" ConfirmText="Do you want to Delete !" TargetControlID="BttnDelete"></cc1:ConfirmButtonExtender></TD></TR><TR><TD style="WIDTH: 357px"><asp:Label id="lblAuditHistory" runat="server" CssClass="labelheading" __designer:wfdid="w3" Visible="False">Audit History - Footage Type</asp:Label></TD></TR><TR><TD style="WIDTH: 357px"><asp:GridView id="dgAuditHistory" runat="server" CssClass="gridContent" __designer:wfdid="w11" Width="100%" PageSize="25" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD style="WIDTH: 357px"><asp:TextBox id="txt_FootageTypeID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w13" Visible="False"></asp:Label></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

