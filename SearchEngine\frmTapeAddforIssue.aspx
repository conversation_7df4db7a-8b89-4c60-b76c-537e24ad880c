<%@ Page Language="VB" AutoEventWireup="false" CodeFile="frmTapeAddforIssue.aspx.vb" Inherits="SearchEngine_frmTapeAddforIssue" %>
<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Namespace="System.Web.UI" TagPrefix="asp" %>

<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >

    
<head id="Head1" runat="server">
    <title>Home &gt; Search Engine &gt; Entertainment</title>
    <link href="main.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
    <!-- 
        SPAN.searchword { background-color:yellow; }
    // -->
    </style>
        </head>
<body>
    <form id="form1" runat="server">
    <asp:ScriptManager ID="dm" runat ="Server"></asp:ScriptManager>
    <div>
    
    </div>
        <table style="width: 100%">
            <tr>
                <td align="left" style="width: 100%; height: 20px; text-decoration: underline;" class="labelheading">
                   List of Tapes  </td>
            </tr>
             <tr>
                <td >
                    <asp:Label ID="lblErr" runat="Server" Font-Bold="True" ForeColor="Red"
                                    Width="100%" Font-Names="Arial" Font-Size="10pt" ></asp:Label>
                </td>
            </tr>
            <tr>
                <td style="width: 100%;" valign="top" align="left">
                    <asp:GridView ID="dg_Search" runat="server" AllowPaging="True"
                     AutoGenerateColumns="False" PageSize="2000" Width="70%"
                      BackColor="Transparent" CssClass="gridContent" AllowSorting="True" DataKeyNames="TapeNumber">
                        <Columns>
                         <asp:TemplateField ShowHeader="False" HeaderText="Delete">
                            <ItemTemplate>
                                <asp:LinkButton ID="lkDelete" runat="server" CausesValidation="False" 
                                    CommandName="Delete"  Text="Delete" ></asp:LinkButton>                             
                                <cc1:ConfirmButtonExtender ID="lkDelete_ConfirmButtonExtender" runat="server" ConfirmText="Are you sure, you want to delete the record ?" Enabled="True" TargetControlID="lkDelete">
                                </cc1:ConfirmButtonExtender>
                            </ItemTemplate>
                        </asp:TemplateField>
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                            <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" />
                            <asp:BoundField DataField="TapeLibraryID" HeaderText="RecordID" Visible="False" />
                            <asp:BoundField DataField="StationName" HeaderText="Station" />
                            <asp:BoundField DataField="TapeMode" HeaderText="Section" />
                       </Columns>
                        <HeaderStyle BackColor="Gainsboro" CssClass="gridheader" />
                        <AlternatingRowStyle BackColor="Lavender" />
                    </asp:GridView>
                    &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;</td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                    <asp:Label ID="lblTotal" runat="server" Visible="False"></asp:Label>
                    <asp:Button ID="bttnPrevious" runat="server" Font-Bold="True" Text=" << " Visible="False" /><asp:Button ID="bttnNext" runat="server" Font-Bold="True" Text=" >> " Visible="False" /><asp:TextBox ID="Tape" runat="server" Width="32px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="ENDATE" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="PRG" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP1" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP2" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP3" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP4" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="EP5" runat="server" Width="24px" BackColor="#F2F5FE" BorderStyle="None" ForeColor="#F2F5FE"></asp:TextBox><asp:TextBox ID="T_type" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                        ForeColor="#F2F5FE" Width="24px"></asp:TextBox><asp:Label ID="lblPages" runat="server" Visible="False"></asp:Label><asp:Label ID="lblPageIndex" runat="server" Visible="False">0</asp:Label></td>
            </tr>
            <tr>
                <td align="right" style="width: 100%; height: 21px">
                     
                 </td>
            </tr>
        </table>
    </form>
</body>
</html>
