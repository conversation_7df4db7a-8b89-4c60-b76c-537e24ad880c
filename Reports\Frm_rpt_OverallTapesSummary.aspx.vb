Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_OverallTapesSummary
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try

            Dim Dept As String
            Dim DeptName As String
            Dim Emp As String
            Dim EmpName As String
            Dim FromDate As String
            Dim ToDate As String
            Dim Report As String



            Dept = ddlDepartment.SelectedValue.ToString
            DeptName = ddlDepartment.SelectedItem.Text


            'If chkEmployee.Checked = True Then
            '    Emp = "-1"
            'Else
            '    Emp = ddlEmployee.SelectedValue.ToString
            'End If

            If chkEmployee.Checked = True Then
                Emp = "-1"
                EmpName = "All"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    ' Emp = ddlEmployee.SelectedValue.ToString
                    Emp = EmployeeID.ToString
                    EmpName = TxtEmployee.Text
                Else
                    Emp = "-1"
                    EmpName = "All"
                End If

            End If


            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            Dim Pending As String = ""
            If ddlPendings.SelectedValue = "Ignore" Then
                Pending = 1
            ElseIf ddlPendings.SelectedValue = "No" Then
                Pending = 2
            ElseIf ddlPendings.SelectedValue = "Yes" Then
                Pending = 3
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_OverallIssueReturnSummary.rpt&@Employee=" + Emp + "&@DepartmentId=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Emp=" + EmpName + "&@Dept=" + DeptName + "&@Report=" + Pending + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)

                'Dim qryString As String
                'qryString = "viewer.aspx?reportname=" & "rpt_OverallIssueReturnSummary.rpt&@Employee=" + Emp + "&@DepartmentId=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Emp=" + EmpName + "&@Dept=" + DeptName + "&@Report=" + Pending
                'test.Attributes.Add("src", qryString)


                Dim dt As New Hashtable
               
                dt.Add("@Employee", Emp)
                dt.Add("@DepartmentId", Dept)
                dt.Add("@fromdate", FromDate)
                dt.Add("@todate", ToDate)
                dt.Add("@BaseStationID", BaseStationID)
                dt.Add("@Emp", EmpName)
                dt.Add("@Dept", DeptName)
                dt.Add("@Report", Pending)

                'dt.Add("@Month", 4)
                'dt.Add("@Year", 2009)
                LoadPDFReport("rpt_OverallIssueReturnSummary", dt)



                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_OverallIssueReturnSummary_New.rpt&@Employee=" + Emp + "&@DepartmentID=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Emp=" + EmpName + "&@Dept=" + DeptName + "&@Report=" + Pending + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)
            Else
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_OverallIssueReturnSummary_New.rpt&@Employee=" + Emp + "&@DepartmentId=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@Emp=" + EmpName + "&@Dept=" + DeptName + "&@Report=" + Pending + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)
            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_OverallTapesSummary.aspx"
            ObjSave.ReportName = "Blank --> Q 14. How Can I View Overall Tapes Issue And Return?"
            ObjSave.SaveRecord()

            ''******************************************************''



        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub

    Protected Sub chkEmployee_CheckedChanged1(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub

    Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        '--- Exporting Crystal Report to PDF process...


        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports/")

        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("appuser", "newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".pdf" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        crReportDocument.Export()
        crReportDocument.Close()

        Dim scriptString As String = "<script type='text/javascript'>" & _
           "window.onload = function(){" & _
           "var url = '" + "" + RptName + ".pdf" + "';" & _
           "var winPop = window.open(url,'winPop');" & _
           "}" & _
           "</script>"
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)
    End Sub

End Class
