Imports Microsoft.VisualBasic
Imports DAAB = Microsoft.ApplicationBlocks.Data.SqlHelper
Imports System

Public Class TapeType

    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    'Private strConnection As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

    Private m_TapeTypeID As Integer
    Private m_TapeType As String
    Private m_Description As String
    Private m_EntryDate As DateTime
    Private m_UOM As String
    Private m_OpeningBal As Integer
    Private m_CurrentBal As Integer
    Private m_ReOrderLevel As Integer
    Private m_Cost As Single
    Private m_IsActive As Integer
    Private m_TapeTypeCode_Oracle As String


#Region " Object Properties "

    Public Property TapeTypeID() As Integer
        Get
            Return m_TapeTypeID
        End Get
        Set(ByVal value As Integer)
            m_TapeTypeID = value
        End Set
    End Property
    Public Property TapeType() As String
        Get
            Return m_TapeType
        End Get
        Set(ByVal value As String)
            m_TapeType = value
        End Set
    End Property
    Public Property Description() As String
        Get
            Return m_Description
        End Get
        Set(ByVal value As String)
            m_Description = value
        End Set
    End Property
    Public Property EntryDate() As DateTime
        Get
            Return m_EntryDate
        End Get
        Set(ByVal value As DateTime)
            m_EntryDate = value
        End Set
    End Property
    Public Property UOM() As String
        Get
            Return m_UOM
        End Get
        Set(ByVal value As String)
            m_UOM = value
        End Set
    End Property
    Public Property OpeningBal() As Integer
        Get
            Return m_OpeningBal
        End Get
        Set(ByVal value As Integer)
            m_OpeningBal = value
        End Set
    End Property
    Public Property CurrentBal() As Integer
        Get
            Return m_CurrentBal
        End Get
        Set(ByVal value As Integer)
            m_CurrentBal = value
        End Set
    End Property
    Public Property ReOrderLevel() As Integer
        Get
            Return m_ReOrderLevel
        End Get
        Set(ByVal value As Integer)
            m_ReOrderLevel = value
        End Set
    End Property
    Public Property Cost() As Single
        Get
            Return m_Cost
        End Get
        Set(ByVal value As Single)
            m_Cost = value
        End Set
    End Property
    Public Property IsActive() As Integer
        Get
            Return m_IsActive
        End Get
        Set(ByVal value As Integer)
            m_IsActive = value
        End Set
    End Property
    Public Property TapeTypeCode_Oracle() As String
        Get
            Return m_TapeTypeCode_Oracle
        End Get
        Set(ByVal value As String)
            m_TapeTypeCode_Oracle = value
        End Set
    End Property


#End Region


#Region " Data Manipulation Methods "

    Public Sub SaveRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "TapeType_SaveRecord", DBNull.Value, m_TapeType, m_Description, m_EntryDate, m_UOM, m_OpeningBal, m_CurrentBal, m_ReOrderLevel, m_Cost, m_IsActive, m_TapeTypeCode_Oracle)
        Catch ex As Exception
            Throw
        End Try
    End Sub


    Public Sub UpdateRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "TapeType_SaveRecord", m_TapeTypeID, m_TapeType, m_Description, m_EntryDate, m_UOM, m_OpeningBal, m_CurrentBal, m_ReOrderLevel, m_Cost, m_IsActive, m_TapeTypeCode_Oracle)
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub DeleteRecord(ByVal id As Integer)
        Try
            DAAB.ExecuteNonQuery(objConnection, "TapeType_DeleteRecord", id)
        Catch ex As Exception
            Throw
        End Try
    End Sub


#End Region


#Region " Data Retrieval Methods "

    Public Sub GetRecord(ByVal id As Integer)
        Try
            Dim dtbl As Data.DataTable = DAAB.ExecuteDataset(objConnection, "TapeType_GetRecords", id).Tables(0)
            If (Not dtbl Is Nothing) And (dtbl.Rows.Count > 0) Then
                m_TapeTypeID = dtbl.Rows(0)("TapeTypeID")
                m_TapeType = dtbl.Rows(0)("TapeType")
                m_Description = dtbl.Rows(0)("Description")
                m_EntryDate = dtbl.Rows(0)("EntryDate")
                m_UOM = dtbl.Rows(0)("UOM")
                m_OpeningBal = dtbl.Rows(0)("OpeningBal")
                m_CurrentBal = dtbl.Rows(0)("CurrentBal")
                m_ReOrderLevel = dtbl.Rows(0)("ReOrderLevel")
                m_Cost = dtbl.Rows(0)("Cost")
                m_TapeTypeCode_Oracle = dtbl.Rows(0)("TapeTypeCode_Oracle")
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    'You may overload this method for retrieving records by using parameters and filters, 
    'e.g., GetRecords(filter1) or GetRecords(filter1, filter2) etc.
    Public Function GetRecords() As Data.DataTable
        Try
            Return DAAB.ExecuteDataset(objConnection, "TapeType_GetRecords", DBNull.Value).Tables(0)
        Catch ex As Exception
            Throw
        End Try
    End Function


    

#End Region

End Class