<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="FrmEmployeeMapping.aspx.vb" Inherits="ApplicationSetup_FrmEmployeeMapping" title="Untitled Page" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table width="100%">
        <tr>
            <td class="labelheading" style="height: 21px; text-decoration: underline">
                <asp:ScriptManager id="ScriptManager1" runat="server">
                </asp:ScriptManager>
                &nbsp;<asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading" >Home</asp:LinkButton>
                &gt; Employee Mapping</td>
        </tr>
        <tr>
            <td style="height: 71px">
                <table style="width: 592px">
                    <tr class="mytext">
                        <td style="width: 106px">
                            Old Employee Name</td>
                        <td style="width: 148px">
                            <asp:TextBox ID="txtOldEmployee" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                        <td style="width: 22px">
                        </td>
                        <td style="width: 107px">
                            New Employee Name</td>
                        <td style="width: 22px">
                            <asp:TextBox ID="txtNewEmployee" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain" style="height: 29px">
                &nbsp;<asp:Button ID="bttnMappEmployee" runat="server" CssClass="buttonA" Text="~ Map Employee ~" Width="136px" Font-Bold="True" />&nbsp;
                <asp:Button ID="bttnClear" runat="server" CssClass="buttonA" Text="~ Clear ~" Width="72px" Font-Bold="True" />
                </td>
        </tr>
        <tr>
            <td>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender1" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetEmployee"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtOldEmployee">
                </cc1:AutoCompleteExtender>
                <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender2" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetEmployee"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtNewEmployee">
                </cc1:AutoCompleteExtender>
                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Map Employee !"
                    TargetControlID="bttnMappEmployee">
                </cc1:ConfirmButtonExtender>
            </td>
        </tr>
        <tr>
            <td style="height: 11px">
                <asp:TextBox ID="txt_ClosetMasterID" runat="server" CssClass="mytext" Visible="False"
                    Width="48px"></asp:TextBox>
                <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label></td>
        </tr>
    </table>
</asp:Content>

