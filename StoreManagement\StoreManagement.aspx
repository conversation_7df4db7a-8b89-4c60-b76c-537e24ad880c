<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="StoreManagement.aspx.vb" Inherits="StoreManagement_StoreManagement" title="Home > Store Management" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>

<%@ Register Assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebGrid" TagPrefix="igtbl" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <table width="100%">
     <tr>
         <td style="text-decoration: underline" class="labelheading">
             <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
             &gt; Store Management</td>
     </tr>
     <tr>
         <td>
                    <TABLE width=100%>
                    <TBODY>
                    <TR class="mytext">
                    <TD style="width: 79px; height: 10px;" >Pr.No</TD>
                    <TD style="width: 123px; height: 10px;" >SIR No.</TD>
                    <TD style="width: 118px; height: 10px;" >
                        Source</TD>
                        <td style="width: 47px; height: 10px;">
                            Country</td>
                        <td style="width: 3px; height: 10px;">
    <asp:CheckBox CssClass="mytext" ID="IgnoreTapeType" runat="server" Checked="True" Text="Ignore Tape Type"
        Width="136px" /></td>
                        <td style="width: 3px; height: 10px">
                        <asp:CheckBox CssClass="mytext" ID="IgnorerCity" runat="server" Text="Ignore City" Checked="True" Width="96px" /></td>
                    <TD style="width: 131px; height: 10px;" >
                        From Date
                        <asp:CheckBox ID="chkDate" runat="server" Text="Ignore" Checked="True" /></TD>
                    <td style="width: 50px; height: 10px;" >
                        To Date</td>
                    </TR>
                    <TR class="mytext">
                    <TD style="width: 79px; height: 22px;" >
                    <asp:TextBox CssClass="mytext" id="txt_PrNo" runat="server"></asp:TextBox></TD>
                    <TD style="width: 123px; height: 22px;" ><asp:TextBox CssClass="mytext" id="txt_SIRNo" runat="server"></asp:TextBox></TD>
                    <TD style="width: 118px; height: 22px;" >
                            <asp:TextBox CssClass="mytext" id="txt_Source" runat="server"></asp:TextBox></TD>
                        <td style="width: 47px; height: 22px;">
                            <asp:DropDownList CssClass="mytext" id="ddl_Country" runat="server" Width="160px">
                                </asp:DropDownList></td>
                        <td style="width: 3px; height: 22px;">
                            <asp:DropDownList CssClass="mytext" id="ddl_TapeType" runat="server" Width="152px">
                                </asp:DropDownList></td>
                        <td style="width: 3px; height: 22px">
                        <asp:TextBox ID="txt_CityName" runat="server" CssClass="mytext"></asp:TextBox></td>
                    <TD style="width: 131px; height: 22px;" >
                        <asp:TextBox ID="txtFromDate" runat="server" CssClass="mytext"></asp:TextBox></TD>
    <td style="width: 45%; height: 22px;" >
        <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext"></asp:TextBox></td>
</TR>
                        <tr class="mytext">
                            <td style="width: 79px; height: 8px">
                            </td>
                            <td style="width: 123px; height: 8px">
                            </td>
                            <td style="width: 118px; height: 8px">
                            </td>
                            <td style="width: 47px; height: 8px">
                            </td>
                            <td style="width: 3px; height: 8px">
                            </td>
                            <td style="width: 3px; height: 8px">
                            </td>
                            <td style="width: 131px; height: 8px">
                            </td>
                            <td style="width: 3px; height: 8px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="height: 29px" class="bottomMain" colspan="8">
                                &nbsp;<asp:Button CssClass="buttonA" id="bttnSearch" runat="server" Text="Search" Font-Bold="False"></asp:Button>&nbsp;
                                    <asp:Button CssClass="buttonA" ID="btnAddNew" runat="server" Text="Add New" /></td>
                        </tr>
    <tr class="mytext">
        <td colspan="4" >
            <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="528px"></asp:Label></td>
        <td style="width: 3px">
        </td>
        <td style="width: 3px">
        </td>
        <td style="width: 131px" >
        </td>
        <td style="width: 3px" >
        </td>
    </tr>
    <tr class="mytext">
        <td colspan="8" style="height: 15px">
            <igtbl:UltraWebGrid ID="grd_Result" runat="server" Width="824px" DataKeyField="StockRegisterID">
                        <Bands>
                            <igtbl:UltraGridBand SelectTypeRow="Single">
                                <RowEditTemplate>
                                    <p align="right">
                                        &nbsp;<input id="igtbl_TextBox_0_0" columnkey="" style="width: 150px" type="text" /><br />
                                        Date
                                        <input id="igtbl_TextBox_0_2" columnkey="" style="width: 150px" type="text" /><br />
                                        Source
                                        <input id="igtbl_TextBox_0_3" columnkey="" style="width: 150px" type="text" /><br />
                                        Pr No.
                                        <input id="igtbl_TextBox_0_4" columnkey="" style="width: 150px" type="text" /><br />
                                        SIR No.
                                        <input id="igtbl_TextBox_0_5" columnkey="" style="width: 150px" type="text" /><br />
                                        City
                                        <input id="igtbl_TextBox_0_6" columnkey="" style="width: 150px" type="text" /><br />
                                    </p>
                                    <br />
                                    <p align="center">
                                        <input id="igtbl_reOkBtn" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="OK" />&nbsp;
                                        <input id="igtbl_reCancelBtn" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="Cancel" /></p>
                                </RowEditTemplate>
                                <AddNewRow View="NotSet" Visible="NotSet">
                                </AddNewRow>
                                <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                    <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                        CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                        Font-Size="11px" Width="200px">
                                        <Padding Left="2px" />
                                    </FilterDropDownStyle>
                                    <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                    </FilterHighlightRowStyle>
                                </FilterOptions>
                                <Columns>
                                    <igtbl:TemplatedColumn BaseColumnName="StockRegisterID" Key="Delete">
                                        <CellTemplate>
                                            &nbsp;<asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/btn_delete.gif" CommandName="Delete" OnClientClick="return confirm('Are you Sure you want to delete this?')"/>
                                        </CellTemplate>
                                    </igtbl:TemplatedColumn>
                                    <igtbl:TemplatedColumn BaseColumnName="StockRegisterID" Key="Edit">
                                        <CellTemplate>
                                            <asp:ImageButton ID="ImageButton2" runat="server" ImageUrl="~/Images/btn_edit.gif" CommandName="Edit"/>
                                        </CellTemplate>
                                        <Header>
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Header>
                                        <Footer>
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Footer>
                                    </igtbl:TemplatedColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="StockRegisterID" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Stock Register ID" Hidden="True" Key="StockRegisterID">
                                        <Header Caption="Stock Register ID">
                                            <RowLayoutColumnInfo OriginX="2" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="2" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="RequisitionDate" EditorControlID="" FooterText=""
                                        Format="dd-MMM-yyyy" HeaderText="Date">
                                        <Header Caption="Date">
                                            <RowLayoutColumnInfo OriginX="3" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="3" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="RequisitionSource" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Source">
                                        <Header Caption="Source">
                                            <RowLayoutColumnInfo OriginX="4" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="4" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="PrNo" EditorControlID="" FooterText="" Format=""
                                        HeaderText="Pr No.">
                                        <Header Caption="Pr No.">
                                            <RowLayoutColumnInfo OriginX="5" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="5" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="SIRNo" EditorControlID="" FooterText="" Format=""
                                        HeaderText="SIR No.">
                                        <Header Caption="SIR No.">
                                            <RowLayoutColumnInfo OriginX="6" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="6" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="CityName" EditorControlID="" FooterText=""
                                        Format="" HeaderText="City">
                                        <Header Caption="City">
                                            <RowLayoutColumnInfo OriginX="7" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="7" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                </Columns>
                                <RowTemplateStyle BackColor="Window" BorderColor="Window" BorderStyle="Ridge">
                                    <BorderDetails WidthBottom="3px" WidthLeft="3px" WidthRight="3px" WidthTop="3px" />
                                </RowTemplateStyle>
                            </igtbl:UltraGridBand>
                            <igtbl:UltraGridBand RowSelectors="No" SelectTypeCell="None" SelectTypeCol="None">
                                <RowEditTemplate>
                                    <br />
                                    <p align="center">
                                        <input id="Button1" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="OK" />&nbsp;
                                        <input id="Button2" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="Cancel" /></p>
                                </RowEditTemplate>
                                <AddNewRow View="NotSet" Visible="NotSet">
                                </AddNewRow>
                                <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                    <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                        CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                        Font-Size="11px" Width="200px">
                                        <Padding Left="2px" />
                                    </FilterDropDownStyle>
                                    <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                    </FilterHighlightRowStyle>
                                </FilterOptions>
                                <Columns>
                                    <igtbl:UltraGridColumn BaseColumnName="TapeType" EditorControlID="" FooterText=""
                                        Format="" FormulaErrorValue="" HeaderText="Tape Type" Key="TapeType">
                                        <Header Caption="Tape Type" Title="">
                                        </Header>
                                        <Footer Caption="" Title="">
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="Qty" EditorControlID="" FooterText="" Format=""
                                        FormulaErrorValue="" HeaderText="Quantity" Key="Qty">
                                        <Header Caption="Quantity" Title="">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Header>
                                        <Footer Caption="" Title="">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                </Columns>
                                <RowTemplateStyle BackColor="Window" BorderColor="Window" BorderStyle="Ridge">
                                    <BorderDetails WidthBottom="3px" WidthLeft="3px" WidthRight="3px" WidthTop="3px" />
                                </RowTemplateStyle>
                            </igtbl:UltraGridBand>
                        </Bands>
                        <DisplayLayout AllowColSizingDefault="Free" AllowColumnMovingDefault="OnServer" AllowDeleteDefault="Yes"
                            AllowSortingDefault="OnClient" AllowUpdateDefault="Yes" BorderCollapseDefault="Separate"
                            HeaderClickActionDefault="SortMulti" Name="grdxResult" RowHeightDefault="20px" SelectTypeRowDefault="Single" Version="4.00" ViewType="OutlookGroupBy" JavaScriptFileName="" JavaScriptFileNameCommon="" AutoGenerateColumns="False" GroupByColumnsHiddenDefault="NotSet">
                            <GroupByBox Hidden="True">
                                <Style BackColor="ActiveBorder" BorderColor="Window"></Style>
                            </GroupByBox>
                            <GroupByRowStyleDefault BackColor="Control" BorderColor="Window">
                            </GroupByRowStyleDefault>
                            <FooterStyleDefault BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                                <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                            </FooterStyleDefault>
                            <RowStyleDefault BackColor="Window" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px">
                                <BorderDetails ColorLeft="Window" ColorTop="Window" />
                                <Padding Left="3px" />
                            </RowStyleDefault>
                            <FilterOptionsDefault AllString="(All)" EmptyString="(Empty)" NonEmptyString="(NonEmpty)">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptionsDefault>
                            <HeaderStyleDefault BackColor="#5774C2" BorderStyle="Solid" HorizontalAlign="Center" ForeColor="White">
                                <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                            </HeaderStyleDefault>
                            <EditCellStyleDefault BorderStyle="None" BorderWidth="0px">
                            </EditCellStyleDefault>
                            <FrameStyle BackColor="Window" BorderColor="ControlLightLight"
                                BorderWidth="1px" Font-Names="Microsoft Sans Serif" Font-Size="8.25pt"
                                Width="824px">
                            </FrameStyle>
                            <Pager AllowPaging="True" PageSize="25">
                                <Style BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                            </Pager>
                            <AddNewBox Hidden="False">
                                <Style BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                            </AddNewBox>
                            <ImageUrls BlankImage="" CollapseImage="" CurrentEditRowImage="" CurrentRowImage=""
                                ExpandImage="" FilterAppliedImage="" FilterImage="" FixedHeaderOffImage="" FixedHeaderOnImage=""
                                GridCornerImage="" GroupByImage="" GroupDownArrow="" GroupUpArrow="" ImageDirectory=""
                                NewRowImage="" RowLabelBlankImage="" SortAscending="" SortDescending="" UnGroupByImage="" />
                            <RowAlternateStyleDefault BackColor="White">
                            </RowAlternateStyleDefault>
                            <SelectedRowStyleDefault BackColor="#F09D21" ForeColor="White">
                            </SelectedRowStyleDefault>
                        </DisplayLayout>
                    </igtbl:UltraWebGrid></td>
    </tr>
</tbody>
</table>
         </td>
     </tr>
            <tr>
                <td style="height: 14px">
                    &nbsp;<asp:Label ID="lblMsg" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></td>
            </tr>
        </table>
    <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
    </cc1:ToolkitScriptManager>
    <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_Country">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_TapeType">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_City">
    </cc1:ListSearchExtender>
    <cc1:CalendarExtender ID="CalendarExtender_ToDate" runat="server" TargetControlID="txtToDate" CssClass="MyCalendar" Format="dd-MMM-yyyy">
    </cc1:CalendarExtender>
    <cc1:CalendarExtender ID="CalendarExtender_FromDate" runat="server" Format="dd-MMM-yyyy"
        TargetControlID="txtFromDate" CssClass="MyCalendar">
    </cc1:CalendarExtender>
                   <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender_City" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="GetCity"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txt_CityName">
                </cc1:AutoCompleteExtender>
<asp:DropDownList CssClass="mytext" id="ddl_City" runat="server" Width="144px" AppendDataBoundItems="True" Visible="False">
                                </asp:DropDownList>
</asp:Content>

