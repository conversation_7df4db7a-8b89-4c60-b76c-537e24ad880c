<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_RecycleTapeDetail.aspx.vb" Inherits="Frm_rpt_RecycleTapeDetail" title="Other Reports > Q6. How Can I View List of Recycled Tapes? " %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports >  How Can I View List of Recycled Tapes? " Width="696px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <table cellspacing="3">
                        <tr class="mytext">
                            <td style="width: 80px">
                            </td>
                            <td style="width: 95px">
                            </td>
                            <td style="height: 5px; width: 135px;">
                                &nbsp;</td>
                            <td style="height: 5px; width: 135px;">
                            </td>
                            <td style="width: 100px; height: 5px">
                            </td>
                            <td style="height: 5px">
                            </td>
                            <td style="height: 5px">
                            </td>
                            <td style="height: 5px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="width: 80px" valign="bottom">
                                Content Type</td>
                            <td style="width: 95px; font-size: 8pt; font-family: Arial;" valign="bottom">
                                Station
                                <asp:CheckBox ID="chkStation" runat="server" AutoPostBack="True" Checked="True" Text="Ignore" /></td>
                            <td style="font-size: 8pt; font-family: Arial; width: 135px;" valign="bottom">
                                From Date &nbsp; &nbsp;&nbsp;
                                <asp:CheckBox ID="chkIgnoredate" runat="server" Font-Bold="False" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="Ignore" /></td>
                            <td style="font-size: 8pt; font-family: Arial; width: 135px;" valign="bottom">
                                To Date</td>
                            <td style="width: 100px;" valign="bottom">
                                Recycle
                            </td>
                            <td valign="bottom">
                                &nbsp;<asp:CheckBox ID="ChkProgram" runat="server" Font-Bold="False" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="Ignore" Visible="False" /></td>
                            <td valign="bottom">
                                &nbsp; &nbsp; &nbsp;<asp:CheckBox ID="ChkSlug" runat="server" Font-Bold="False"
                                    Font-Names="Verdana" Font-Size="X-Small" Text="Ignore" Visible="False" /></td>
                            <td valign="bottom">
                                <asp:Label ID="Label2" runat="server" Text="Export to" Width="48px"></asp:Label></td>
                        </tr>
                        <tr class="mytext">
                            <td valign="top" style="width: 80px">
                                <asp:DropDownList ID="ddlContentType" runat="server" CssClass="mytext" Width="72px">
                                    <asp:ListItem>Ent</asp:ListItem>
                                    <asp:ListItem>News</asp:ListItem>
                                </asp:DropDownList></td>
                            <td valign="top" style="width: 95px">
                                <asp:DropDownList ID="ddlBaseStation" runat="server" CssClass="mytext" Width="88px">
                                </asp:DropDownList></td>
                            <td style="height: 33px; width: 135px;" valign="top">
                                <asp:TextBox ID="txtFromdate" runat="server" CssClass="mytext" Width="128px"></asp:TextBox></td>
                            <td style="height: 33px; width: 135px;" valign="top">
                                <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext" Width="128px"></asp:TextBox></td>
                            <td style="width: 100px; height: 33px" valign="top">
                                <asp:DropDownList ID="ddlRecycle" runat="server" CssClass="mytext" Width="96px">
                                    <asp:ListItem Value="2">Ist Recycle</asp:ListItem>
                                    <asp:ListItem Value="3">2nd Recycle</asp:ListItem>
                                </asp:DropDownList></td>
                            <td style="height: 33px" valign="top">
                                <asp:TextBox ID="txtProgram" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                            <td style="height: 33px" valign="top">
                                <asp:TextBox ID="txtSlug" runat="server" CssClass="mytext" Width="128px" Visible="False"></asp:TextBox></td>
                            <td style="height: 33px" valign="top">
                                <asp:DropDownList ID="ddlPDF" runat="server" CssClass="mytext">
                                    <asp:ListItem>PDF</asp:ListItem>
                                    <asp:ListItem>EXCEL</asp:ListItem>
                                </asp:DropDownList></td>
                        </tr>
                    </table>
                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" Format="dd-MMM-yyyy"
                        TargetControlID="txtToDate" CssClass="MyCalendar">
                    </cc1:CalendarExtender>
                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" Format="dd-MMM-yyyy"
                        TargetControlID="txtFromdate" CssClass="MyCalendar">
                    </cc1:CalendarExtender>
                      <cc1:AutoCompleteExtender 
                    ID="AutoCompleteExtender6" 
                    runat="server"
                    CompletionInterval="1"
                    CompletionSetCount="12" 
                    EnableCaching="true" 
                    MinimumPrefixLength="3" 
                    ServiceMethod="Program"
                    ServicePath="AutoComplete.asmx" 
                    TargetControlID="txtProgram">
                </cc1:AutoCompleteExtender>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%; height: 29px;">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    &nbsp;</asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Form ( Other Reports > How Can I View List of Recycled Tapes? ) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Form ( Other Reports > How Can I View List of Recycled Tapes? ) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
    &nbsp;
</asp:Content>

