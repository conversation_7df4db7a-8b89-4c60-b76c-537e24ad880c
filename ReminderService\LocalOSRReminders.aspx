<%@ Page Language="VB" AutoEventWireup="false" CodeFile="LocalOSRReminders.aspx.vb" Inherits="ReminderService_LocalOSRReminders" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        
            <asp:Label ID="lbMessage" runat="server" BackColor="Yellow" BorderColor="Black" BorderStyle="Solid"
                Font-Bold="True" Font-Names="Verdana" Font-Size="X-Large" ForeColor="Green" Height="32px"
                Style="z-index: 105; left: 8px; position: absolute; top: 664px" Visible="False"
                Width="944px">Your Email has been sent to the Desired Reciepants</asp:Label>
            <table id="Table1" border="0" cellpadding="1" cellspacing="1" style="z-index: 120;
                left: 32px; position: absolute; top: 8px" width="300">
                <tr>
                    <td style="width: 146px">
                        <asp:Label ID="Label1" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon">From :</asp:Label></td>
                    <td>
                        <asp:TextBox ID="txtFrom" runat="server" BackColor="LemonChiffon" Font-Names="Verdana"
                            Font-Size="X-Small" Height="24px" Width="664px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                    </td>
                    <td>
                        &nbsp;</td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon">To :</asp:Label></td>
                    <td>
                        <asp:TextBox ID="txtTo" runat="server" BackColor="LemonChiffon" Font-Names="Verdana"
                            Font-Size="X-Small" Height="24px" Width="664px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        &nbsp;</td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon">BCC :</asp:Label></td>
                    <td>
                        <asp:TextBox ID="txtBCC" runat="server" BackColor="LemonChiffon" Font-Names="Verdana"
                            Font-Size="X-Small" Height="24px" Width="664px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        &nbsp;</td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        <asp:Label ID="Label4" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon">CC :</asp:Label></td>
                    <td>
                        <asp:TextBox ID="txtCC" runat="server" BackColor="LemonChiffon" Font-Names="Verdana"
                            Font-Size="X-Small" Height="24px" Width="664px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        &nbsp;</td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        <asp:Label ID="Label5" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon">Subject :</asp:Label></td>
                    <td>
                        <asp:TextBox ID="txtSubject" runat="server" BackColor="LemonChiffon" Font-Names="Verdana"
                            Font-Size="X-Small" Height="24px" Width="664px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                        &nbsp;</td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td style="width: 146px; height: 20px">
                        <asp:Label ID="Label6" runat="server" Font-Bold="True" Font-Names="Arial" ForeColor="Maroon"
                            Width="112px">Attachment :</asp:Label></td>
                    <td style="height: 20px">
                        <a class="Link" href="../Reports/OSR_Reminders.pdf" >View Attachments</a></td>
                </tr>
                <tr>
                    <td style="width: 146px">
                    </td>
                    <td>
                        &nbsp;</td>
                </tr>
                <tr>
                    <td colspan="1" style="width: 146px">
                    </td>
                    <td colspan="1">
                        <asp:TextBox ID="txtBody" runat="server" BackColor="LemonChiffon" Height="240px"
                            TextMode="MultiLine" Width="672px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 146px; height: 20px">
                    </td>
                    <td style="height: 20px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 146px">
                    </td>
                    <td>
                        <asp:Button ID="bttnSend" runat="server" BackColor="#FFFFC0" Font-Bold="True" ForeColor="Maroon"
                            Text="Send Email" Width="160px" /><asp:TextBox ID="txtAttachments" runat="server"
                                BackColor="#FFE0C0" Font-Names="Verdana" Font-Size="X-Small" Height="24px" TextMode="MultiLine"
                                Visible="False" Width="32px"></asp:TextBox><asp:TextBox ID="txtSender" runat="server"
                                    BackColor="#FFE0C0" Font-Names="Verdana" Font-Size="X-Small" Height="24px" TextMode="MultiLine"
                                    Visible="False" Width="32px"></asp:TextBox><asp:TextBox ID="txtExtension" runat="server"
                                        Visible="False" Width="32px"></asp:TextBox><asp:TextBox ID="txtUserName" runat="server"
                                            BackColor="#FFE0C0" Font-Names="Verdana" Font-Size="X-Small" Height="24px" TextMode="MultiLine"
                                            Visible="False" Width="32px"></asp:TextBox><asp:TextBox ID="txtconcernArchive" runat="server"
                                                Visible="False" Width="24px"></asp:TextBox></td>
                </tr>
            </table>
       
    </div>
    </form>
</body>
</html>
