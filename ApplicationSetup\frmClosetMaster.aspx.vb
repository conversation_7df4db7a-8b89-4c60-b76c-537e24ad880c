
Partial Class ApplicationSetup_frmClosetMaster
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            FillGrid()
            BindCombo()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_City.DataSource = New BusinessFacade.City().GetRecords()
        ddl_City.DataTextField = "CityName"
        ddl_City.DataValueField = "CityID"
        ddl_City.DataBind()
        ddl_City.Items.Insert(0, "--Select--")
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.ClosetMaster().IsExists_ClosetMaster(txt_ClosetMasterName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : ClosetMaster already Exists !"
        Else
            If txt_ClosetMasterID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If

        
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()

    End Sub

    Private Sub SaveRecord()
        If ddl_City.SelectedIndex = "0" Then
            lblErr.Text = "Please Select City!!"
        Else
            Dim objClosetMaster As New BusinessFacade.ClosetMaster()
            If txt_ClosetMasterName.Text = "" Then
                txt_ClosetMasterName.Text = " "
            Else
                objClosetMaster.ClosetMasterName = txt_ClosetMasterName.Text
            End If
            objClosetMaster.CityID = ddl_City.SelectedValue
            objClosetMaster.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
        
    End Sub

    Private Sub UpdateRecord()
        Dim objClosetMaster As New BusinessFacade.ClosetMaster()
        objClosetMaster.ClosetMasterID = txt_ClosetMasterID.Text
        objClosetMaster.ClosetMasterName = txt_ClosetMasterName.Text
        objClosetMaster.CityID = ddl_City.SelectedValue
        objClosetMaster.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_ContentType.DataSource() = New BusinessFacade.ClosetMaster().GetRecords()
        dg_ContentType.DataBind()
        'dg_ContentType.Columns(0).Visible = False
        'dg_ContentType.Columns(2).Visible = False

    End Sub

    Protected Sub dg_ContentType_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_ContentType.RowCreated
        e.Row.Cells(1).Visible = False
        e.Row.Cells(3).Visible = False

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_ContentType.SelectedIndexChanged
        I = dg_ContentType.SelectedIndex.ToString
        txt_ClosetMasterID.Text = Convert.ToInt32(dg_ContentType.Rows(I).Cells(1).Text)
        txt_ClosetMasterName.Text = dg_ContentType.Rows(I).Cells(2).Text
        ddl_City.SelectedValue = Convert.ToInt32(dg_ContentType.Rows(I).Cells(3).Text)
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
        lblErr.Text = String.Empty
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_ClosetMasterID.Text = "" Then
                lblErr.Text = "Please Select Record!!"
            Else
                Dim objClosetMaster As New BusinessFacade.ClosetMaster()
                objClosetMaster.ClosetMasterID = txt_ClosetMasterID.Text
                objClosetMaster.DeleteRecord(objClosetMaster.ClosetMasterID)
                FillGrid()
                Clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Closet Master is already in Used !"
            dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            Clrscr()
        End Try
    End Sub

    Private Sub Clrscr()
        txt_ClosetMasterName.Text = String.Empty
        txt_ClosetMasterID.Text = String.Empty
        ddl_City.SelectedIndex = 0
    End Sub

    'Protected Sub dg_ContentType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_ContentType.PageIndexChanging
    '    dg_ContentType.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
