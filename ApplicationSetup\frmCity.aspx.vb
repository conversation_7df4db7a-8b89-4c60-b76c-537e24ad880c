Partial Class ApplicationSetup_frmCity
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If


            FillGrid()
            BindCombo()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_Country.DataSource = New BusinessFacade.Country().GetRecords()
        ddl_Country.DataTextField = "CountryName"
        ddl_Country.DataValueField = "CountryID"
        ddl_Country.DataBind()
        '  ddl_Country.Items.Insert(0, "--Select--")

       End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_CityID.Text = "" Then
            SaveRecord()
        Else

            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.City()
            objValidation.CityID = txt_CityID.Text
            Dim BaseStationID As Integer = objValidation.City_BaseStationValidation()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then
                UpdateRecord()
            Else
                lblErr.Text = "You are not allowed to Edit this Record!!"
            End If
            ''************ End *************''
            ''******************************''

        End If

        dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        ClearAuditHistory()
    End Sub

    Private Sub SaveRecord()
        Dim IsExists As String
        IsExists = New BusinessFacade.City().IsExists_City(txt_CityName.Text, ddl_IsOSR.SelectedValue)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : City already Exists !"
        Else
            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''
            ''************ Save Record ***************''
            ''****************************************''


            If Trim(txt_CityName.Text) = "" Then
                lblErr.Text = "Please Enter City!!"
            Else
                Dim ObjCity As New BusinessFacade.City()
                ObjCity.CityName = txt_CityName.Text
                If txt_CityShortName.Text = "" Then
                    txt_CityShortName.Text = " "
                Else
                    ObjCity.CityShortName = txt_CityShortName.Text
                End If
                ObjCity.CountryID = Convert.ToInt32(ddl_Country.SelectedValue)

                ObjCity.IsOSR = ddl_IsOSR.SelectedValue
                ObjCity.UserID = UserID
                ObjCity.SaveRecord()
                FillGrid()
                lblErr.Text = "Record has been Saved!!"
            End If
        End If

    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        lblErr.Text = String.Empty
        Dim ObjCity As New BusinessFacade.City()
        ObjCity.CityID = txt_CityID.Text
        ObjCity.CityName = txt_CityName.Text
        ObjCity.CityShortName = txt_CityShortName.Text
        ObjCity.CountryID = Convert.ToInt32(ddl_Country.SelectedValue)
        ObjCity.IsOSR = Convert.ToInt32(ddl_IsOSR.SelectedValue)
        ObjCity.UserID = UserID
        ObjCity.UpdateRecord()
        FillGrid()

        'If ddl_Country.SelectedIndex = 0 Then
        '    lblErr.Text = "Please Select City !!"
        'Else
        'End If

    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.City().GetRecords()
        dg_City.DataSource() = dt
        dg_City.Columns(0).Visible = True
        dg_City.Columns(1).Visible = True
        dg_City.DataBind()
        dg_City.Columns(0).Visible = False
        dg_City.Columns(1).Visible = False

        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_City.SelectedIndexChanged
        I = dg_City.SelectedIndex.ToString
        txt_CityID.Text = Convert.ToInt32(dg_City.Rows(I).Cells(1).Text)
        txt_CityName.Text = dg_City.Rows(I).Cells(4).Text
        txt_CityShortName.Text = dg_City.Rows(I).Cells(5).Text
        If dg_City.Rows(I).Cells(6).Text = "False" Then
            ddl_IsOSR.SelectedIndex = 1
        Else
            ddl_IsOSR.SelectedIndex = 0
        End If
        'ddl_IsOSR.SelectedItem.Text = dg_City.Rows(I).Cells(6).Text
        dg_City.SelectedRowStyle.BackColor = Drawing.Color.Wheat
        ddl_Country.SelectedItem.Text = dg_City.Rows(I).Cells(3).Text

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.City()
        ObjAudit.CityID = txt_CityID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_City()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_CityID.Text = "" Then
                lblErr.Text = "Please Select Record !!"
            Else

                ''******************************''
                ''*** BaseStation Validation ***''
                ''******************************''

                Dim objValidation As New BusinessFacade.City()
                objValidation.CityID = txt_CityID.Text
                Dim BaseStationID As Integer = objValidation.City_BaseStationValidation()
                Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                If BaseStationID = CokieBaseStationID Then
                    Dim ObjCity As New BusinessFacade.City()
                    ObjCity.CityID = txt_CityID.Text
                    ObjCity.DeleteRecord(ObjCity.CityID)
                    FillGrid()
                    dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                    Clrscr()
                    lblErr.Text = "Record has been Deleted !!"
                    dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                    ClearAuditHistory()
                Else
                    lblErr.Text = "You are not allowed to Delete this Record!!"
                End If
                ''************ End *************''
                ''******************************''

            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This City is Already in Used !"
            dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            Clrscr()
        End Try
    End Sub

    Private Sub Clrscr()
        txt_CityID.Text = String.Empty
        txt_CityName.Text = String.Empty
        ddl_Country.SelectedIndex = 0
        txt_CityShortName.Text = String.Empty

    End Sub

    Protected Sub dg_City_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_City.PageIndexChanging
        dg_City.PageIndex = e.NewPageIndex()
        dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        FillGrid()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click

        dg_City.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
        ClearAuditHistory()
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub


    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
