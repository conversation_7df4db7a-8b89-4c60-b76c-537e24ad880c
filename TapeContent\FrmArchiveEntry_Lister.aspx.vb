Imports System.Data
Imports System.Data.SqlClient

Partial Class TapeContent_FrmArchiveEntry_Lister
    Inherits System.Web.UI.Page
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    ' Private strConnection As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then

                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                '    lbl_UserName.Text = Master.FooterText

                '    Dim Arr_UserID As Array = Split(lbl_UserName.Text, ",")
                '    lbl_UserName.Text = Arr_UserID(1)
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If


                Chk_Abstract.Checked = True
                Chk_Note.Checked = True
                Chk_ProdStatus.Checked = True
                Chk_Program.Checked = True
                Chk_SubCloset.Checked = True
                Chk_TapeNumber.Checked = True
                Chk_Title.Checked = True
                Chk_Loc.Checked = True

                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        ddl_SubCloset.DataTextField = "SubClosetName"
        ddl_SubCloset.DataValueField = "SubClostID"
        ddl_SubCloset.DataSource = New BusinessFacade.SubCloset().GetRecords()
        ddl_SubCloset.DataBind()

        ddl_ProdStatus.DataTextField = "ProductionStatus"
        ddl_ProdStatus.DataValueField = "ProductionStatusID"
        ddl_ProdStatus.DataSource = New BusinessFacade.ProductionStatus().GetRecords()
        ddl_ProdStatus.DataBind()

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnGetRecord.Click
        FillGrid()
    End Sub

    Private Sub FillGrid()

        Dim arr As Array = Split(txt_TapeNumber.Text, "#")
        If arr.Length = 2 Then
            txt_TapeNumber.Text = arr(1)
        End If

        ''********************************************''
        ''************ Get TapeLibraryID *************''
        ''********************************************''

        Dim TapeLibraryID As Integer
        Dim objTapeLibraryID As New BusinessFacade.TapeContent()
        objTapeLibraryID.TapeNumber = txt_TapeNumber.Text
        TapeLibraryID = objTapeLibraryID.GerMergeTapeID_byTapeNumber_Ent(objTapeLibraryID.TapeNumber)

        ''********************************************''
        ''****** For Getting Program Child ID ********''
        ''********************************************''

        Dim ProgramChildID As Integer
        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
        ObjProgramChildID.ProgramChildName = txt_ProgramChild.Text
        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

        Try

            Dim Qry As String
            Dim cmd As New SqlCommand()
            cmd.Connection = objConnection
            'cmd.CommandText = "TapeContent._GetRecords_New"
            cmd.CommandText = "TapeContent_GetRecords_New2"
            cmd.CommandType = Data.CommandType.StoredProcedure

            Dim ad As New SqlDataAdapter(cmd)
            Dim ds As New Data.DataSet()

            ''*************************************************''

            Dim A1 As Integer
            If Chk_TapeNumber.Checked = True Then
                A1 = -1
            Else
                A1 = TapeLibraryID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@TapeLibraryID", A1)

            ''*************************************************''

            Dim B1 As Integer
            If Chk_Program.Checked = True Then
                B1 = -1
            Else
                B1 = ProgramChildID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ProgramChildID", B1)

            ''*************************************************''

            Dim Var11 As String
            If Chk_Abstract.Checked = True Then
                Var11 = "-1"
            Else
                Var11 = txt_Abstract.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@Abstract", Var11)

            ''*************************************************''

            Dim Var21 As String
            If Chk_Note.Checked = True Then
                Var21 = "-1"
            Else
                Var21 = txt_Note.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@NoteArea", Var21)

            ''*************************************************''

            Dim Var31 As String
            If Chk_Loc.Checked = True Then
                Var31 = "-1"
            Else
                Var31 = txt_Loc.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@LocationCode", Var31)

            ''*************************************************''

            ad.Fill(ds, "TapeContent")
            Qry = ad.SelectCommand.CommandText
            'ad.SelectCommand.CommandText = "TapeContentDetail_GetRecords_New"
            ad.SelectCommand.CommandText = "TapeContentDetail_GetRecords_New2"
            ad.SelectCommand.Parameters.Clear()

            Dim A As Integer
            If Chk_TapeNumber.Checked = True Then
                A = -1
            Else
                A = TapeLibraryID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@TapeLibraryID", A)

            Dim B As Integer
            If Chk_Program.Checked = True Then
                B = -1
            Else
                B = ProgramChildID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@ProgramChildID", B)

            Dim Var1 As String
            If Chk_Abstract.Checked = True Then
                Var1 = "-1"
            Else
                Var1 = txt_Abstract.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@Abstract", Var1)

            Dim Var2 As String
            If Chk_Note.Checked = True Then
                Var2 = "-1"
            Else
                Var2 = txt_Note.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@NoteArea", Var2)

            ''*************************************************''

            Dim Var3 As String
            If Chk_Loc.Checked = True Then
                Var3 = "-1"
            Else
                Var3 = txt_Loc.Text
            End If
            ad.SelectCommand.Parameters.AddWithValue("@LocationCode", Var3)

            ''*************************************************''
            ad.Fill(ds, "TapeContentDetail")
            ds.Relations.Add(ds.Tables("TapeContent").Columns("TapeContentID"), ds.Tables("TapeContentDetail").Columns("TapeContentID"))
            UltraWebGrid1.DataSource = ds.Tables("TapeContent").DefaultView
            UltraWebGrid1.DataBind()
            If ds.Tables(0).Rows.Count > 0 Then
                UltraWebGrid1.DataSource = ds.Tables(0)
                UltraWebGrid1.DataBind()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnAdd_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAdd.Click
        Response.Redirect("FrmArchiveEntry_Ent.aspx")
    End Sub

    Protected Sub UltraWebGrid1_SelectedRowsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedRowsEventArgs) Handles UltraWebGrid1.SelectedRowsChange
        txt_TapeContentID.Text = UltraWebGrid1.DisplayLayout.SelectedRows.Item(0).Cells(0).ToString
    End Sub

    Protected Sub bttnEdit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEdit.Click
        If txt_TapeContentID.Text <> "" Then

            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.TapeContent()
            objValidation.TapeContentID = txt_TapeContentID.Text
            Dim BaseStationID As Integer = objValidation.BaseStationValidation_ArchivalEnt()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then
                Response.Redirect("FrmArchiveEntry_Ent.aspx?TapeContentID=" & txt_TapeContentID.Text)
            Else
                lblErr.Text = "You are not allowed to Edit this Record!!"
            End If

            ''************ End *************''
            ''******************************''

        End If
    End Sub

    Protected Sub bttnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel.Click
        txt_TapeContentID.Text = String.Empty
        txt_Abstract.Text = String.Empty
        txt_Note.Text = String.Empty
        txt_Title.Text = String.Empty
    End Sub

   
    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

   
    Protected Sub bttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDelete.Click

        If txt_TapeContentID.Text <> "" Then

            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.TapeContent()
            objValidation.TapeContentID = txt_TapeContentID.Text
            Dim BaseStationID As Integer = objValidation.BaseStationValidation_ArchivalEnt()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then

                ''******************************************************''
                ''******************************************************''
                ''******************************************************''

                Dim ObjIsIssued As New BusinessFacade.TapeContent()
                ObjIsIssued.TapeContentID = txt_TapeContentID.Text
                Dim IsAvailable As String
                IsAvailable = ObjIsIssued.Proc_IsAvailableEntertainmentArchivalTapes()

                If IsAvailable = 0 Then
                    lblErr.Text = "This Tape Is Not Available, You are not allowed to Delete this Record!!"
                Else
                    ''******************************************************''
                    ''******************** Get User ID *********************''
                    ''******************************************************''

                    Dim UserID As Integer
                    Dim objUserID As New BusinessFacade.Employee()
                    objUserID.SM_LoginID = lbl_UserName.Text
                    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                    Dim objDelete As New BusinessFacade.TapeContent()
                    objDelete.UserID = UserID
                    objDelete.TapeContentID = txt_TapeContentID.Text
                    objDelete.DeleteRecord()

                    txt_TapeContentID.Text = String.Empty
                    FillGrid()
                End If
            Else
                lblErr.Text = "You are not allowed to Delete this Record!!"
            End If
            ''************ End *************''
            ''******************************''


           
        End If
    End Sub

    Protected Sub UltraWebGrid1_PageIndexChanged(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.PageEventArgs) Handles UltraWebGrid1.PageIndexChanged
        UltraWebGrid1.DisplayLayout.Pager.CurrentPageIndex = e.NewPageIndex()
        FillGrid()
    End Sub
End Class
