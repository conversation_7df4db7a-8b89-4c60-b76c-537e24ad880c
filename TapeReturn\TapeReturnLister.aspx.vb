Imports System.Data
Imports System.Data.SqlClient

Partial Class TapeReturn_TapeReturnLister
    Inherits System.Web.UI.Page
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If
                ''**************************''
                ''****** Check Boxes *******''
                ''**************************''

                chkEmp_Return.Checked = True
                chkDept_Return.Checked = True
                chkDate_Return.Checked = True
                txt_Employee_Return.Enabled = False
                txt_Department_Return.Enabled = False
                txtEntryDate_Return.Enabled = False

                chkEmp_issue.Checked = True
                chkDept_Issue.Checked = True
                chkDate_Issue.Checked = True
                txt_Employee_Issue.Enabled = False
                txt_Department_Issue.Enabled = False
                txtEntryDate_Issue.Enabled = False

                ''**************************''
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnReturnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReturnSearch.Click
        FillGrid()
        lblErr_Return.Text = String.Empty
    End Sub

    Private Sub FillGrid()
        Try
            ''******************************************''
            ''************ Get EmployeeID **************''
            ''******************************************''
            Dim EmployeeID As Integer
            If chkEmp_Return.Checked = True Then
                EmployeeID = "-1"
            Else
                If txt_Employee_Return.Text = "" Then
                    EmployeeID = "-1"
                Else
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = txt_Employee_Return.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                End If
            End If


            ''********************************************''
            ''************ Get DepartmentID **************''
            ''********************************************''

            Dim DepartmentID As Integer
            If chkDept_Return.Checked = True Then
                DepartmentID = "-1"
            Else
                If txt_Department_Return.Text = "" Then
                    DepartmentID = "-1"
                Else
                    Dim objDepartmentID As New BusinessFacade.TapeIssuance()
                    objDepartmentID.DepartmentName = txt_Department_Return.Text
                    DepartmentID = objDepartmentID.GetDepartmentID_byDepartmentName(objDepartmentID.DepartmentName)
                End If
            End If


            ''******************************************''
            ''************ Get Entry Date **************''
            ''******************************************''
            Dim EntryDate_Return As String
            If chkDate_Return.Checked = True Then
                EntryDate_Return = "-1"
            Else
                EntryDate_Return = txtEntryDate_Return.Text
            End If

            ''******************************************''
            ''************** Fill Grid *****************''
            ''******************************************''

            Dim Obj As New BusinessFacade.TapeReturn()
            Obj.EmployeeID = EmployeeID
            Obj.DepartmentID = DepartmentID
            Obj.EntryDate = EntryDate_Return
            dg_Return.DataSource = Obj.GetBulkReturnDetails()
            dg_Return.Columns(0).Visible = True
            dg_Return.Columns(6).Visible = True
            dg_Return.Columns(7).Visible = True
            dg_Return.DataBind()
            dg_Return.Columns(0).Visible = False
            dg_Return.Columns(6).Visible = False
            dg_Return.Columns(7).Visible = False
          
            ''******************************************''

        Catch ex As Exception
            Throw
        End Try
    End Sub


    Protected Sub chkEmp_Return_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkEmp_Return.CheckedChanged
        If chkEmp_Return.Checked = True Then
            txt_Employee_Return.Enabled = False
        Else
            txt_Employee_Return.Enabled = True
        End If
    End Sub

    Protected Sub chkDept_Return_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkDept_Return.CheckedChanged
        If chkDept_Return.Checked = True Then
            txt_Department_Return.Enabled = False
        Else
            txt_Department_Return.Enabled = True
        End If
    End Sub

    Protected Sub chkDate_Return_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkDate_Return.CheckedChanged
        If chkDate_Return.Checked = True Then
            txtEntryDate_Return.Enabled = False
        Else
            txtEntryDate_Return.Enabled = True
        End If
    End Sub


    Protected Sub dg_Return_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_Return.RowDeleting

        lblErr_Return.Text = String.Empty
       
        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''


        Dim RowId As Integer
        RowId = e.RowIndex

        Dim Objdelete As New BusinessFacade.TapeReturn()
        Objdelete.BulkReturnID = dg_Return.Rows(RowId).Cells(7).Text
        Objdelete.UserID = UserID
        Objdelete.TapeTypeID = dg_Return.Rows(RowId).Cells(6).Text
        Objdelete.Delete_BulkTapeReturn()
        lblErr_Return.Text = "Record has been Deleted !"

        FillGrid()

    End Sub

    Protected Sub dg_Return_RowEditing(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewEditEventArgs) Handles dg_Return.RowEditing
        dg_Return.EditIndex = e.NewEditIndex
        FillGrid()
    End Sub

    Protected Sub dg_Return_RowCancelingEdit(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCancelEditEventArgs) Handles dg_Return.RowCancelingEdit
        dg_Return.EditIndex = -1
        FillGrid()
    End Sub

    Protected Sub dg_Return_RowUpdating(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewUpdateEventArgs) Handles dg_Return.RowUpdating
        Try

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''

            Dim txtQuantity As TextBox
            Dim txt_Date As TextBox
            txtQuantity = CType(dg_Return.Rows(e.RowIndex).Cells(5).FindControl("txt_Quantity"), TextBox)
            txt_Date = CType(dg_Return.Rows(e.RowIndex).Cells(4).FindControl("txtDate"), TextBox)
            Dim BulkReturnID As Integer = Convert.ToInt32(dg_Return.DataKeys(e.RowIndex).Value.ToString)

            Dim ObjUpdate As New BusinessFacade.TapeReturn()
            ObjUpdate.BulkReturnID = BulkReturnID
            ObjUpdate.UserID = UserID
            ObjUpdate.Qty = Convert.ToInt32(txtQuantity.Text)
            ObjUpdate.EntryDate = txt_Date.Text
            ObjUpdate.Update_BulkTapeReturn()
            lblErr_Return.Text = "Record Updated Successfully."
            dg_Return.EditIndex = -1
            FillGrid()
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub Button3_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button3.Click
        lblErr_Return.Text = String.Empty
        txt_Employee_Return.Text = String.Empty
        txt_Department_Return.Text = String.Empty
        txtEntryDate_Return.Text = String.Empty
        txt_Employee_Return.Enabled = False
        txt_Department_Return.Enabled = False
        txtEntryDate_Return.Enabled = False
        chkEmp_Return.Checked = True
        chkDept_Return.Checked = True
        chkDate_Return.Checked = True
        dg_Return.DataSource = Nothing
        dg_Return.DataBind()
    End Sub

    Protected Sub bttnSearch_Issuance_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch_Issuance.Click
        grd_Result.Visible = True
        FillGrid_Issuance()
    End Sub

    Private Sub FillGrid_Issuance()
        Try
            ''******************************************''
            ''************ Get EmployeeID **************''
            ''******************************************''
            Dim EmployeeID As Integer
            If chkEmp_issue.Checked = True Then
                EmployeeID = "-1"
            Else
                If txt_Employee_Issue.Text = "" Then
                    EmployeeID = "-1"
                Else
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = txt_Employee_Issue.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                End If
            End If


            ''********************************************''
            ''************ Get DepartmentID **************''
            ''********************************************''

            Dim DepartmentID As Integer
            If chkDept_Issue.Checked = True Then
                DepartmentID = "-1"
            Else
                If txt_Department_Issue.Text = "" Then
                    DepartmentID = "-1"
                Else
                    Dim objDepartmentID As New BusinessFacade.TapeIssuance()
                    objDepartmentID.DepartmentName = txt_Department_Issue.Text
                    DepartmentID = objDepartmentID.GetDepartmentID_byDepartmentName(objDepartmentID.DepartmentName)
                End If
            End If

            ''******************************************''
            ''************ Get Entry Date **************''
            ''******************************************''
            Dim EntryDate_Issue As String
            If chkDate_Issue.Checked = True Then
                EntryDate_Issue = "-1"
            Else
                EntryDate_Issue = txtEntryDate_Issue.Text
            End If

            Dim Qry As String
            Dim cmd As New SqlCommand()
            cmd.Connection = objConnection
            cmd.CommandText = "GetBulkIssuance"
            cmd.CommandType = Data.CommandType.StoredProcedure

            Dim ad As New SqlDataAdapter(cmd)
            Dim ds As New Data.DataSet()

            ''*************************************************''

            Dim A1 As Integer
            If chkEmp_issue.Checked = True Then
                A1 = -1
            Else
                A1 = EmployeeID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@EmployeeID", A1)


            Dim A2 As Integer
            If chkDept_Issue.Checked = True Then
                A2 = -1
            Else
                A2 = DepartmentID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@DepartmentID", A2)

            Dim A3 As String
            If chkDate_Issue.Checked = True Then
                A3 = "-1"
            Else
                A3 = EntryDate_Issue
            End If
            ad.SelectCommand.Parameters.AddWithValue("@EntryDate", A3)


            ad.Fill(ds, "TapeContent")
            Qry = ad.SelectCommand.CommandText
            ad.SelectCommand.CommandText = "GetBulkIssuanceDetails"
            ad.SelectCommand.Parameters.Clear()

            Dim B1 As Integer
            If chkEmp_issue.Checked = True Then
                B1 = -1
            Else
                B1 = EmployeeID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@EmployeeID", B1)

            Dim B2 As Integer
            If chkDept_Issue.Checked = True Then
                B2 = -1
            Else
                B2 = DepartmentID
            End If
            ad.SelectCommand.Parameters.AddWithValue("@DepartmentID", B2)

            Dim B3 As String
            If chkDate_Issue.Checked = True Then
                B3 = "-1"
            Else
                B3 = EntryDate_Issue
            End If
            ad.SelectCommand.Parameters.AddWithValue("@EntryDate", B3)

            ''*************************************************''
            ad.Fill(ds, "TapeContentDetail")
            ds.Relations.Add(ds.Tables("TapeContent").Columns("BulkTapeIssuanceID"), ds.Tables("TapeContentDetail").Columns("BulkTapeIssuanceID"))
            grd_Result.DataSource = ds.Tables("TapeContent").DefaultView
            grd_Result.DataBind()
            If ds.Tables(0).Rows.Count > 0 Then
                grd_Result.DataSource = ds.Tables(0)
                grd_Result.DataBind()
            End If

            'Dim Qry As String
            'Dim cmd As New SqlCommand()
            'cmd.Connection = objConnection
            'cmd.CommandText = "GetBulkIssuanceDetails"
            'cmd.CommandType = Data.CommandType.StoredProcedure
            'cmd.Parameters.AddWithValue("@EmployeeID", EmployeeID)
            'cmd.Parameters.AddWithValue("@DepartmentID", DepartmentID)
            'cmd.Parameters.AddWithValue("@EntryDate", EntryDate_Issue)
            'Dim ad As New SqlDataAdapter(cmd)
            'Dim ds As New Data.DataSet()
            'ad.Fill(ds, "BulkTapeIssuance")
            ''ad.Fill(ds)s
            'Qry = ad.SelectCommand.CommandText
            'ad.SelectCommand.CommandText = "GetBulkIssuanceDetails"
            'ad.SelectCommand.Parameters.Clear()
            'ad.SelectCommand.Parameters.AddWithValue("@EmployeeID", EmployeeID)
            'ad.SelectCommand.Parameters.AddWithValue("@DepartmentID", DepartmentID)
            'ad.SelectCommand.Parameters.AddWithValue("@EntryDate", EntryDate_Issue)
            'ad.Fill(ds, "BulkTapeIssuance_Detail")
            'ds.Relations.Add(ds.Tables("BulkTapeIssuance").Columns("BulkTapeIssuanceID"), ds.Tables("BulkTapeIssuance_Detail").Columns("BulkTapeIssuanceID"))
            'grd_Result.DataSource = ds.Tables("BulkTapeIssuance").DefaultView
            'grd_Result.DataBind()
            'If ds.Tables(0).Rows.Count > 0 Then
            '    grd_Result.DataSource = ds.Tables(0)
            '    grd_Result.DataBind()
            'End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub chkEmp_issue_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmp_issue.Checked = True Then
            txt_Employee_Issue.Enabled = False
        Else
            txt_Employee_Issue.Enabled = True
        End If
    End Sub

    Protected Sub chkDept_Issue_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkDept_Issue.Checked = True Then
            txt_Department_Issue.Enabled = False
        Else
            txt_Department_Issue.Enabled = True
        End If
    End Sub

    Protected Sub chkDate_Issue_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkDate_Issue.Checked = True Then
            txtEntryDate_Issue.Enabled = False
        Else
            txtEntryDate_Issue.Enabled = True
        End If
    End Sub

    Protected Sub grd_Result_SelectedRowsChange(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.SelectedRowsEventArgs) Handles grd_Result.SelectedRowsChange
        txtBulkTapeIssuanceID.Text = grd_Result.DisplayLayout.SelectedRows.Item(0).Cells(0).ToString
    End Sub

    Protected Sub bttnEdit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnEdit.Click
        If txtBulkTapeIssuanceID.Text <> "" Then
            Response.Redirect("../TapeManagement/BulkTapeManagement.aspx?BulkTapeIssuanceID=" & txtBulkTapeIssuanceID.Text)
        End If
    End Sub

    Protected Sub grd_Result_PageIndexChanged(ByVal sender As Object, ByVal e As Infragistics.WebUI.UltraWebGrid.PageEventArgs) Handles grd_Result.PageIndexChanged
        grd_Result.DisplayLayout.Pager.CurrentPageIndex = e.NewPageIndex()
        FillGrid()
    End Sub

    Protected Sub bttnCancel_Issue_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_Issue.Click
        txt_Employee_Issue.Text = String.Empty
        txt_Department_Issue.Text = String.Empty
        txtEntryDate_Issue.Text = String.Empty
        txt_Employee_Issue.Enabled = False
        txt_Department_Issue.Enabled = False
        txtEntryDate_Issue.Enabled = False
        chkEmp_issue.Checked = True
        chkDept_Issue.Checked = True
        chkDate_Issue.Checked = True
        lblErrIssue.Text = String.Empty
        grd_Result.Visible = False
        grd_Result.DataSource = Nothing
        grd_Result.DataBind()
    End Sub

    Protected Sub bttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnDelete.Click
        If txtBulkTapeIssuanceID.Text <> "" Then
            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''

            Dim ObjDelete As New BusinessFacade.TapeReturn()
            ObjDelete.BulkTapeIssuanceID = txtBulkTapeIssuanceID.Text
            ObjDelete.UserID = UserID
            ObjDelete.Delete_BulkTapeRIssuance()

            lblErrIssue.Text = String.Empty

            FillGrid_Issuance()


        End If
    End Sub
End Class
