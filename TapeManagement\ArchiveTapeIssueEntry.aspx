<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" 
CodeFile="ArchiveTapeIssueEntry.aspx.vb" Inherits="TapeManagement_ArchiveTapeIssueEntry" 
title="Home > Archive Tape Setup > Archive Tape Issue & Return" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <table width="100%">
                <tr>
                    <td style="width: 100%; height: 21px; text-decoration: underline;" class="labelheading">
                        <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                        &gt; Archive Tape Setup &gt; Archive Tape Issue &amp; Return</td>
                </tr>
     <tr>
         <td style="width: 100px; height: 1px">
         </td>
     </tr>
                <tr>
                    <td style="width: 100%; height: 20px">
                        <asp:Panel ID="TitlePanel" runat="server" Width="99%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                            <asp:Image ID="Image1" runat="server" />
                            <asp:Label ID="Label2" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Medium"
                                Text="Archival Tape Issuance" Width="528px" CssClass="heading1"></asp:Label></asp:Panel>
                        <asp:Panel ID="ContentPanel" runat="server" Width="99.5%">
                            <table width="100%" border="0">
                                <tr class="mytext">
                                    <td style="width: 138px;">
                                    </td>
                                    <td style="width: 225px;">
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px; height: 13px;" >
                                        </td>
                                    <td style="width: 225px; height: 13px" >
                                        Program Name</td>
                                    <td style="height: 13px" >
                                        Tape Number</td>
                                    <td style="height: 13px" >
                                    </td>
                                    <td style="height: 13px" >
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px" >
                                        <asp:Label ID="Label4" runat="server" Font-Bold="True" Text="Entertainemnt :-" Width="96px"></asp:Label></td>
                                    <td style="width: 225px" >
                                        <asp:TextBox ID="txt_ProgramName" runat="server" CssClass="mytext" EnableViewState="False" Width="216px"></asp:TextBox></td>
                                    <td >
                                        <asp:TextBox ID="txt_TapeNumber_Ent" runat="server" CssClass="mytext" EnableViewState="False"
                                            Width="216px"></asp:TextBox></td>
                                    <td >
                                        &nbsp;<asp:LinkButton ID="lnkSearch_Ent" runat="server" Width="44px">Search</asp:LinkButton></td>
                                    <td >
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td colspan="5" style="height: 13px">
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px; height: 13px;">
                                    </td>
                                    <td style="width: 225px; height: 13px;">
                                        Tape Slug</td>
                                    <td style="height: 13px">
                                        Tape Number</td>
                                    <td style="height: 13px">
                                    </td>
                                    <td style="height: 13px">
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px">
                                        <asp:Label ID="Label5" runat="server" Font-Bold="True" Text="News :-" Width="96px"></asp:Label></td>
                                    <td style="width: 225px">
                                        <asp:TextBox ID="txt_SlugName" runat="server" CssClass="mytext" EnableViewState="False"
                                            Width="216px"></asp:TextBox></td>
                                    <td>
                                        <asp:TextBox ID="txt_TapeNumber_News" runat="server" CssClass="mytext" EnableViewState="False"
                                            Width="216px"></asp:TextBox></td>
                                    <td>
                                        <asp:LinkButton ID="lnkAudit_News" runat="server" Width="48px">Search</asp:LinkButton></td>
                                    <td>
                                        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 
                                            <asp:CheckBox ID="Chk_Program" runat="server" Text="Ignore" Visible="False" />
                                        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                        <asp:CheckBox ID="Chk_TapeNo" runat="server" Text="Ignore" Visible="False"  />
                                        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;
                                        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td colspan="5">
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px; height: 4px">
                                    </td>
                                    <td style="width: 225px; height: 4px">
                                        Issue To Employee</td>
                                    <td style="height: 4px">
                                        Entry Date&nbsp;</td>
                                    <td style="height: 4px">
                                    </td>
                                    <td style="height: 4px; width: 55%;">
                                        <asp:DropDownList CssClass="mytext" ID="ddl_IssueToEmp" runat="server" Width="144px" Visible="False">
                                        </asp:DropDownList></td>
                                </tr>
                                <tr class="mytext">
                                    <td style="width: 138px; height: 13px">
                                        </td>
                                    <td style="width: 225px; height: 13px">
                                        <asp:TextBox ID="txt_IssueToEmp" runat="server" CssClass="mytext" EnableViewState="False"
                                            Width="192px"></asp:TextBox>
                                        <asp:Image ID="Err_Employee_Issue" runat="server" ImageUrl="~/Images/error.gif" Visible="False" /></td>
                                    <td style="height: 13px">
                                        <asp:TextBox ID="txtEntryDate_Issue" runat="server" CssClass="mytext" EnableViewState="False"
                                            Width="128px"></asp:TextBox></td>
                                    <td style="height: 13px">
                                        <asp:DropDownList CssClass="mytext" ID="ddl_ContentType" runat="server" Width="136px" AutoPostBack="True" Visible="False">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="height: 13px">
                                        <asp:TextBox CssClass="mytext" ID="txt_TapeNo" runat="server" EnableViewState="False" Width="72px" Visible="False"></asp:TextBox>
                                    </td>
                                </tr>
                                            &nbsp;
                                <tr class="mytext">
                                    <td colspan="4" style="height: 21px">
                                <asp:Label ID="lblErr_Issue" runat="server" Font-Bold="True" ForeColor="Red" Width="632px" Font-Size="Small" Font-Names="Arial"></asp:Label></td>
                                    <td style="height: 21px">
                                    </td>
                                </tr>
                                <tr class="mytext">
                                    <td colspan="4" style="height: 13px">
                                        &nbsp;</td>
                                    <td style="height: 13px">
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="5">
                                        <table style="width: 673px">
                                            <tr>
                                                <td style="width: 50%" align="center" valign="middle">
                                                    <asp:Label ID="lblEntTapes" runat="server" BackColor="LightSkyBlue" Font-Bold="True" Font-Names="Arial"
                                                        Font-Size="10pt" Text="Entertainment Tapes" Width="100%" Visible="False" BorderColor="DodgerBlue" BorderStyle="Solid" BorderWidth="1px"></asp:Label></td>
                                                <td align="center" valign="middle" style="width: 346px">
                                                    <asp:Label ID="lblNewsTapes" runat="server" BackColor="LightSkyBlue" Font-Bold="True" Font-Names="Arial"
                                                        Font-Size="10pt" Text="News Tapes" Width="100%" Visible="False" BorderColor="DodgerBlue" BorderStyle="Solid" BorderWidth="1px"></asp:Label></td>
                                            </tr>
                                            <tr>
                                                <td style="width: 50%" valign="top">
                                        <asp:GridView ID="dg_TapeIssue" runat="server" AutoGenerateColumns="False"
                                            PageSize="20" Width="336px" CssClass="gridContent" BorderColor="Transparent">
                                            <Columns>
                                                   <asp:TemplateField HeaderText="S.No">
                                  <ItemTemplate>
                                    <%#Container.DataItemIndex + 1%>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            
                                                <asp:TemplateField HeaderText="Issue">
                                                    <ItemStyle HorizontalAlign="Center" />
                                                    <ItemTemplate>
                                                        <asp:CheckBox ID="Chk_Issue" onclick="Check_Click(this)"  runat="server" />
                                                    </ItemTemplate>
                                                   <HeaderTemplate> All
                                                      <asp:CheckBox ID="checkAll" runat="server" onclick = "checkAll(this);" /> 
                                                </HeaderTemplate> 
                                                </asp:TemplateField>
                                                <asp:BoundField DataField="TapeNumber" HeaderText="Tape No">
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="TapeLibraryID" Visible="False" >
                                                    <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                    <HeaderStyle Width="0px" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="StationName" HeaderText="Station Name" />
                                                <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                                            </Columns>
                                            <HeaderStyle CssClass="gridheader" />
                                            <AlternatingRowStyle CssClass="AlternateRows" BackColor="#E0E0E0" />
                                        </asp:GridView>
                                                </td>
                                                <td style="width: 346px" valign="top">
                                        <asp:GridView ID="dg_TapeIssuance_SlugWise" runat="server" AutoGenerateColumns="False"
                                            PageSize="20" Width="328px" CssClass="gridContent" BorderColor="Transparent">
                                            <Columns>
                                                                   <asp:TemplateField HeaderText="S.No">
                                  <ItemTemplate>
                                    <%#Container.DataItemIndex + 1%>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                           
                                                 <asp:TemplateField HeaderText="Issue">
                                                    <ItemStyle HorizontalAlign="Center" />
                                                    <ItemTemplate>
                                                        <asp:CheckBox ID="Chk_Issue" onclick="Check_Click(this)"  runat="server" />
                                                    </ItemTemplate>
                                                   <HeaderTemplate> All
                                                      <asp:CheckBox ID="checkAll" runat="server" onclick = "checkAll(this);" /> 
                                                </HeaderTemplate> 
                                                </asp:TemplateField>
                                                <asp:BoundField DataField="TapeNumber" HeaderText="Tape No">
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="TapeLibraryID" Visible="False" >
                                                    <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="StationName" HeaderText="Station Name" />
                                                <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />

                                            </Columns>
                                            <HeaderStyle CssClass="gridheader" />
                                            <AlternatingRowStyle CssClass="AlternateRows" BackColor="#E0E0E0" />
                                        </asp:GridView>
                                                </td>
                                            </tr>
                                        </table>
                                        <asp:Image ID="imgWait" runat="server" BorderColor="Navy" BorderStyle="Solid" BorderWidth="3px"
                                            ImageUrl="~/Images/wait.gif" Visible="False" /></td>
                                </tr>
                                <tr class="mytext">
                                    <td class="bottomMain" colspan="5" style="height: 29px">
                                        &nbsp;&nbsp;
                                        <asp:Button CssClass="buttonA" ID="bttnIssue" runat="server" Text="Issue" Width="97px" Font-Bold="True" />
                                        &nbsp;
                                        <asp:Button
                            CssClass="buttonA" ID="bttnClearSel" runat="server" Text="Clear Selection" Width="149px" Font-Bold="True" />&nbsp;
                                        &nbsp;<asp:Button
                            CssClass="buttonA" ID="Button2" runat="server" Text="Email" Width="104px" Visible="False" />
                            </td>
                                </tr>
                            </table>
                        </asp:Panel>
                        
                                                
                    </td>
                </tr>
                <tr>
                    <td style="width: 100%; height: 20px">
                        <asp:Panel ID="TitlePanel_2" runat="server" Width="99%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                            <asp:Image ID="Image2" runat="server" />
                            <asp:Label ID="Label3" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Medium"
                                Text="Archival Tape Return" Width="592px" CssClass="heading1"></asp:Label></asp:Panel>
                        <asp:Panel ID="ContentPanel_2" runat="server" Width="99.5%">
                                <table width="100%">
                                    <tr class="mytext">
                                        <td style="width: 332px; height: 14px">
                                        </td>
                                        <td style="height: 14px; width: 4px;">
                                        </td>
                                        <td style="height: 14px">
                                        </td>
                                        <td style="height: 14px">
                                        </td>
                                        <td style="width: 60%; height: 14px">
                                        </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="width: 332px" >
                                            Employee &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                            <asp:CheckBox  ID="Chk_Employee" runat="server" AutoPostBack="True" Text="Ignore" Visible="False" /></td>
                                        <td style="width: 216px" >
                                            TapeNumber &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                                            &nbsp;&nbsp; &nbsp; &nbsp;
                                            &nbsp; &nbsp; &nbsp;
                                            <asp:CheckBox  ID="chkTapeNumber" runat="server" AutoPostBack="True" Text="Ignore" Visible="False" /></td>
                                        <td>
                                            </td>
                                        <td >
                                            Entry Date</td>
                                        <td style="width: 60%" >
                                            <asp:Label CssClass="mytext" ID="lblDepartment" runat="server" Font-Bold="True" Visible="False"></asp:Label></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td valign="top" style="width: 332px" >
                                            <asp:TextBox ID="txt_EmpReturn" runat="server" CssClass="mytext" EnableViewState="False"
                                                Width="216px"></asp:TextBox>
                                            </td>
                                        <td valign="top" >
                                            <asp:TextBox ID="txtTapeNumber" runat="server" CssClass="mytext" EnableViewState="False"
                                                Width="216px"></asp:TextBox></td>
                                        <td style="height: 38px" >
                                            <asp:LinkButton ID="bttnSearchReturn" runat="server" Height="16px" Width="40px">Search</asp:LinkButton>&nbsp;</td>
                                        <td style="height: 38px" valign="top" >
                                            <asp:TextBox ID="txtEntryDate_Return" runat="server" CssClass="mytext" EnableViewState="False"
                                                Width="128px"></asp:TextBox></td>
                                        <td style="height: 38px" >
                                            &nbsp;<asp:Label CssClass="mytext" ID="lblCity" runat="server" Font-Bold="True" Visible="False"></asp:Label>
                                            <asp:DropDownList CssClass="mytext" ID="ddl_Employee" runat="server" AutoPostBack="True" Width="224px" Visible="False">
                                            </asp:DropDownList></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td align="center" colspan="5">
                                            <asp:Label ID="lblRecordCount" runat="server" BackColor="#FFE0C0" Font-Bold="True"
                                                Font-Size="10pt" ForeColor="Maroon" Height="17px" Width="100%" Visible="False"></asp:Label></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="5" style="height: 100%">
                                            <asp:GridView ID="dg_OneByOne" runat="server" AutoGenerateColumns="False"
                                                Width="862px" CssClass="gridContent" PageSize="50">
                                                <Columns>
                                                  <asp:TemplateField HeaderText="S.No">
                                  <ItemTemplate>
                                    <%#Container.DataItemIndex + 1%>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                                                    <asp:BoundField DataField="TapeIssuanceID" Visible="False" >
                                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                        <HeaderStyle Width="0px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="TapeLibraryID" Visible="False" >
                                                        <ItemStyle ForeColor="#F2F5FE" Width="0px" />
                                                        <HeaderStyle Width="0px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="TapeNumber" HeaderText="Tape Number" >
                                                        <ItemStyle Width="100px" />
                                                        <HeaderStyle Width="100px" />
                                                    </asp:BoundField>
                                                    <asp:TemplateField HeaderText="Return">
                                                        <ItemTemplate>
                                                            <asp:CheckBox ID="Chk"  onclick="Check_Click(this)"  runat="server" />
                                                        </ItemTemplate>
                                                        <HeaderTemplate>All
                                                        <asp:CheckBox ID="checkAll_Return" runat="server" onclick = "checkAll(this);" /> 
                                                        </HeaderTemplate>
                                                        <ItemStyle HorizontalAlign="Center" />
                                                        <HeaderStyle HorizontalAlign="Center"  Width="100px" />
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" >
                                                        <HeaderStyle Width="250px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="StationName" HeaderText="Station Name">
                                                        <ItemStyle HorizontalAlign="Center" />
                                                        <HeaderStyle HorizontalAlign="Center" Width="125px" />
                                                    </asp:BoundField>
                                                    <asp:BoundField DataField="TapeIssuanceDate" DataFormatString="{0:dd-MMM-yyyy}" HeaderText="Tape Issuance Date"
                                                        HtmlEncode="False">
                                                        <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                    </asp:BoundField>
                                                     <asp:BoundField DataField="TapeType" HeaderText="Tape Type" >
                                                       <HeaderStyle Width="250px" />
                                                        </asp:BoundField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Delete"
                                                                Text="Delete" OnClientClick="return confirm('Are you Sure you want to Delete ?')"></asp:LinkButton>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                </Columns>
                                                <HeaderStyle CssClass="gridheader" />
                                                <AlternatingRowStyle BackColor="#E0E0E0" />
                                            </asp:GridView>
                                </td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="5" style="height: 18px">
                                <asp:Label ID="lblErr_Return" runat="server" Font-Bold="True" ForeColor="Red" Width="456px"></asp:Label></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td colspan="5" style="height: 29px" class="bottomMain">
                                            &nbsp;<asp:Button CssClass="buttonA" ID="bttnReturn" runat="server" Text="Return" Width="99px" Font-Bold="True" />
                                            &nbsp;
                                            <asp:Button
                                               CssClass="buttonA" ID="bttnClearScreen" runat="server" Text="Clear Screen" Width="114px" Font-Bold="True" />&nbsp;
                                            &nbsp;<asp:Button CssClass="buttonA" ID="Button1" runat="server" Text="Send Email" BackColor="Yellow" Visible="False"  />&nbsp; &nbsp;<asp:Button CssClass="buttonA" ID="bttnRemove" runat="server" Text="Remove" Visible="False" /></td>
                                    </tr>
                                </table>
                        </asp:Panel>
                        &nbsp;
                        <asp:Label ID="Label1" runat="server" Text="Label" Visible="False"></asp:Label>&nbsp;
                        <asp:TextBox ID="txtIsEmployeeSearch" runat="server" BackColor="#FF8080" BorderStyle="None" Width="46px" Visible="False"></asp:TextBox></td>
                </tr>
                <tr>
                    <td style="width: 100%; height: 21px">
                        
                    </td>
                </tr>
            </table>
            <asp:ScriptManager ID="ScriptManager1" runat="server" >
                    <Services>
                                <asp:ServiceReference Path="AutoComplete.asmx" />
                            </Services>
                        </asp:ScriptManager>
                                        <asp:GridView ID="Dg_TapeReturn" runat="server" AutoGenerateColumns="False" Width="816px" PageSize="15" CssClass="gridContent">
                                            <Columns>
                                                <asp:TemplateField HeaderText="Remove">
                                                    <ItemTemplate>
                                                        <asp:CheckBox ID="chk_Remove" runat="server" />&nbsp;
                                                    </ItemTemplate>
                                                    <ItemStyle HorizontalAlign="Center" />
                                                </asp:TemplateField>
                                                <asp:BoundField DataField="ProgramChildName" HeaderText="Program" >
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="TapeSlug" HeaderText="Report Slug" >
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="TapeNumber" HeaderText="Tape No" >
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="TapeIssuanceDate" HeaderText="Issue Date" >
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="DueDate" HeaderText="Due Date" >
                                                    <HeaderStyle HorizontalAlign="Left" />
                                                </asp:BoundField>
                                                <asp:TemplateField HeaderText="Return">
                                                    <ItemTemplate>
                                                        <asp:CheckBox ID="Chk_Return" runat="server" />&nbsp;
                                                    </ItemTemplate>
                                                    <ItemStyle HorizontalAlign="Center" />
                                                </asp:TemplateField>
                                                <asp:BoundField DataField="TapeIssuanceID" HeaderText="TapeIssuanceID" />
                                                <asp:BoundField DataField="ProgramChildID" HeaderText="ProgramChildID" />
                                                <asp:BoundField DataField="TapeLibraryID" HeaderText="TapeLibraryID" />
                                            </Columns>
                                            <HeaderStyle CssClass="gridheader" />
                                            <AlternatingRowStyle CssClass="AlternateRows" />
                                        </asp:GridView>
                        <cc1:AutoCompleteExtender 
                            runat="server"
                            ID="AutoCompleteExtender1" 
                            TargetControlID="txt_TapeNo" 
                            ServicePath="AutoComplete.asmx"
                            ServiceMethod="Guest"
                            MinimumPrefixLength="1" 
                            CompletionInterval="25"
                            EnableCaching="true"
                            CompletionSetCount="12">
                        </cc1:AutoCompleteExtender>
                        <cc1:CollapsiblePanelExtender 
                                ID="CollapsiblePanelExtender2" 
                                runat="server" 
                                collapsecontrolid="TitlePanel_2"
                                collapsed="true" 
                                collapsedimage="~/Images/Collapse.gif" 
                                collapsedtext="-- Show Parameter Form (Archival Tape Return) --"
                                expandcontrolid="TitlePanel_2" 
                                expandedimage="~/Images/expand.gif" 
                                expandedtext="-- Hide Parameter Form (Archival Tape Return) --"
                                imagecontrolid="Image2" 
                                suppresspostback="true"
                                textlabelid="Label3"
                                TargetControlID="ContentPanel_2">
                        </cc1:CollapsiblePanelExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender2" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_ContentType">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender3" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_ProgramName">
    </cc1:ListSearchExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender5" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_IssueToEmp">
    </cc1:ListSearchExtender>
                        <cc1:CollapsiblePanelExtender 
                                ID="CollapsiblePanelExtender1" 
                                runat="server"
                                collapsecontrolid="TitlePanel"
                                collapsed="true" 
                                collapsedimage="~/Images/Collapse.gif" 
                                collapsedtext="-- Show Parameter Form (Archival Tape Issuance) --"
                                expandcontrolid="TitlePanel" 
                                expandedimage="~/Images/expand.gif" 
                                expandedtext="-- Hide Parameter Form (Archival Tape Issuance) --"
                                imagecontrolid="Image1" 
                                suppresspostback="true"
                                textlabelid="Label2"
                                TargetControlID="ContentPanel">
                        </cc1:CollapsiblePanelExtender>
    <cc1:ListSearchExtender ID="ListSearchExtender6" runat="server" PromptPosition="Bottom"
        PromptText="" TargetControlID="ddl_Employee">
    </cc1:ListSearchExtender>
                                        <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_SlugName" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetSlugName_News"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_SlugName">
                                        </cc1:AutoCompleteExtender>
                                         <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_TapeNumber_Ent" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetTapeNumber_ArchivalIssue_Ent"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_TapeNumber_Ent">
                                        </cc1:AutoCompleteExtender>
                                        <cc1:ListSearchExtender ID="ListSearchExtender1" runat="server" PromptPosition="Bottom"
                                            PromptText="" TargetControlID="ddl_TapeNumber">
                                        </cc1:ListSearchExtender>
                                        <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_ProgramName_Ent" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetProgramName_Ent"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_ProgramName">
                                        </cc1:AutoCompleteExtender>
                                         <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_TapeNumber_News" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetTapeNumber_ArchivalIssue_News"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_TapeNumber_News">
                                        </cc1:AutoCompleteExtender>
                                        <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_Department" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="1" 
                                            ServiceMethod="GetTapeNo_ArchivalReturn"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txtTapeNumber">
                                        </cc1:AutoCompleteExtender>
                                         <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_Employee" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetEmployee"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_IssueToEmp">
                                        </cc1:AutoCompleteExtender>
                                        <cc1:AutoCompleteExtender 
                                            ID="AutoCompleteExtender_ReturnEmployee" 
                                            runat="server"
                                            CompletionInterval="1"
                                            CompletionSetCount="12" 
                                            EnableCaching="true" 
                                            MinimumPrefixLength="3" 
                                            ServiceMethod="GetEmployee"
                                            ServicePath="AutoComplete.asmx" 
                                            TargetControlID="txt_EmpReturn">
                                        </cc1:AutoCompleteExtender>
                                         <cc1:CalendarExtender 
                                            ID="CalendarExtenderIssue" 
                                            runat="server" TargetControlID="txtEntryDate_Issue" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                                        </cc1:CalendarExtender>
                                        <cc1:CalendarExtender 
                                            ID="CalendarExtenderReturn" 
                                            runat="server" TargetControlID="txtEntryDate_Return" Format="dd-MMM-yyyy" CssClass="MyCalendar">
                                        </cc1:CalendarExtender>
                                        <asp:DropDownList CssClass="mytext" ID="ddl_TapeNumber" runat="server" Width="208px" Visible="False">
                                        </asp:DropDownList>
                                        <asp:DropDownList CssClass="mytext" ID="ddl_ProgramName" runat="server" Width="192px" Visible="False">
                                        </asp:DropDownList>
    <asp:Button CssClass="buttonA" ID="bttnSearch" runat="server" Font-Bold="False" Text="Search" Width="64px" Visible="False" />
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>

