Imports System
Imports System.Data
Imports System.Data.SqlClient

Partial Class FrmKeyTypeMappings
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")
            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)
        End If


        'If (lbl_UserName.Text.ToLower <> "turab.ali") Or (lbl_UserName.Text.ToLower <> "sobia.aziz") Then
        '    bttnMappKeyType.Enabled = False
        '    lblErr.Text = "You are not Authorize for Key Type Mapping !"
        'End If

        If Not Page.IsPostBack = True Then
            bttnMappKeyType.Enabled = False
            lblErr.Text = "You are not Authorize for Key Type Mapping !"

            If (lbl_UserName.Text.ToLower = "turab.ali") Or (lbl_UserName.Text.ToLower = "sobia.aziz") Then
                bttnMappKeyType.Enabled = False
                lblErr.Text = ""
            End If

        End If


        Dim connStr As String
        connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString") '"server=KHI-SOFT-016\ASMAT;database=DAMS_NewDB;uid=sa;password=**********"

    End Sub

    Protected Sub bttnMappEmployee_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappKeyType.Click
        Try

            ''********************************************************''
            ''******************Get Old DepartmentID  ****************''
            ''********************************************************''

            Dim OldKeyTypeID As Integer
            Dim ObjOldKWID As New BusinessFacade.KeyType()
            ObjOldKWID.KeyType = txtOldKeyType.Text
            OldKeyTypeID = ObjOldKWID.GetKeyTypeID_AutoComplete(ObjOldKWID.KeyType)


            ''*******************************************************''
            ''******************Get New DepartmentID ****************''
            ''*******************************************************''

            Dim NewKeyTypeID As Integer
            Dim ObjNewKWID As New BusinessFacade.KeyType()
            ObjNewKWID.KeyType = txtNewKeyType.Text
            NewKeyTypeID = ObjNewKWID.GetKeyTypeID_AutoComplete(ObjNewKWID.KeyType)


            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If (OldKeyTypeID <> "0") And (NewKeyTypeID <> "0") And (OldKeyTypeID <> NewKeyTypeID) Then

                ''****************************************************''
                ''*********** Insert in Footage Type Mapping *********''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString") '"server=KHI-SOFT-016\ASMAT;database=DAMS_NewDB;uid=sa;password=**********"
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "Proc_KeyTypesMapping " & OldKeyTypeID & "," & NewKeyTypeID & "," & UserID
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr.Text = "Key Types has been Mapped Successfully."

                If Not Page.IsPostBack Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmKeyTypeMapping.aspx" + ";"""
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                End If
                txtNewKeyType.Text = String.Empty
                txtOldKeyType.Text = String.Empty
            ElseIf (OldKeyTypeID = "0") Or (NewKeyTypeID = "0") Then
                lblErr.Text = "Please Select a Valid Key Types!"
            ElseIf OldKeyTypeID = NewKeyTypeID Then
                lblErr.Text = "Old and New Key Types must be different from each other!"
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        txtNewKeyType.Text = String.Empty
        txtOldKeyType.Text = String.Empty
        lblErr.Text = String.Empty
    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onbeforeunload = function() {"
        script = script + "return ""Closing the page now may result in data loss."";"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)
    End Sub

End Class
