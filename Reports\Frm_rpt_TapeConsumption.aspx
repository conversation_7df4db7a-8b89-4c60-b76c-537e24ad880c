<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_TapeConsumption.aspx.vb" Inherits="Frm_rpt_TapeConsumption" title="How Can I View Tape Consumption Detail?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Other Reports --> How Can I view Tape Consumption Detail ?" Width="880px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE cellSpacing=3><TBODY><TR class="mytext">
    <td style="height: 7px" valign="middle">
        &nbsp;</td>
    <TD style="HEIGHT: 7px" vAlign=middle>&nbsp;</TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="HEIGHT: 7px" vAlign=middle></TD><TD style="WIDTH: 100px; HEIGHT: 7px" vAlign=middle></TD></TR><TR class="mytext">
    <td style="height: 23px" valign="middle">
        Month</td>
    <TD style="HEIGHT: 23px" vAlign=middle>Department Name</TD><TD style="HEIGHT: 23px" vAlign=middle>Employee Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" Checked="True" AutoPostBack="True" OnCheckedChanged="chkEmployee_CheckedChanged"></asp:CheckBox></TD><TD vAlign=middle style="height: 23px">
    Program Name &nbsp; &nbsp; &nbsp;&nbsp;<asp:CheckBox ID="chkProgram" runat="server"
        Checked="True" Text="Ignore" /></TD><TD style="WIDTH: 100px; HEIGHT: 23px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px"></asp:Label></TD></TR><TR class="mytext">
    <td valign="top">
        <asp:DropDownList id="ddlMonth" runat="server" Width="106px" CssClass="mytext"></asp:DropDownList></td>
    <TD vAlign=top><asp:DropDownList id="ddlDepartment" runat="server" Width="200px" CssClass="mytext" Font-Size="X-Small" Font-Names="Verdana" AutoPostBack="True" DataSourceID="dsDepartment" DataTextField="DepartmentName" DataValueField="DepartmentID">
                                </asp:DropDownList><asp:SqlDataSource id="dsDepartment" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString %>" SelectCommand="SELECT [DepartmentID], [DepartmentName] FROM ApplicationSetup.Department&#13;&#10;union&#13;&#10;select -1 as DepartmentID, '-----All Departments-----' as DepartmentName from ApplicationSetup.Department&#13;&#10;order by [DepartmentName]&#13;&#10;&#13;&#10;">
                                </asp:SqlDataSource> </TD><TD vAlign=top><asp:TextBox id="TxtEmployee" runat="server" Width="192px" CssClass="mytext" EnableViewState="False"></asp:TextBox>&nbsp; </TD><TD vAlign=top>
                                    <asp:TextBox ID="txtProgram" runat="server" CssClass="mytext" Font-Names="Verdana"
                                        Font-Size="X-Small" Width="175px"></asp:TextBox></TD><TD vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:ListSearchExtender id="ListSearchExtender1" runat="server" TargetControlID="ddlDepartment" PromptText PromptPosition="Bottom"></cc1:ListSearchExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_Employee" runat="server" TargetControlID="TxtEmployee" ServicePath="AutoComplete.asmx" ServiceMethod="GetEmployee" MinimumPrefixLength="3" CompletionInterval="1" EnableCaching="true" CompletionSetCount="12">
</cc1:AutoCompleteExtender> 
                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender6" runat="server" CompletionInterval="1"
                                CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="Program"
                                ServicePath="AutoComplete.asmx" TargetControlID="txtProgram">
                            </cc1:AutoCompleteExtender>
                            <asp:Label ID="lblErr" runat="server" Font-Bold="True" Font-Names="Arial Narrow"
                                ForeColor="Red"></asp:Label>
</contenttemplate>
                    </asp:UpdatePanel>
                    <table width="100%">
                        <tr>
                            <td class="bottomMain" style="width: 100px">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                    &nbsp;</asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="--Show Form (Other Reports > How Can I View Tape Consumption Detail?)--"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="--Hide Form (Other Reports > How Can I View Tape Consumption Detail?)--"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

