/*
    Project: CrystalX
    URL: http://www.nuvio.cz
    
    Output device: screen, projection
    
    Author: Vit <PERSON> (<EMAIL>); Nuvio (www.nuvio.cz)
    Last revision: 2006-12-05, 12:00 GMT+1

    Structure:
        display | position | float | overflow | width | height | border | margin | padding | background | align | font
*/

* {min-height:10px;}
body
{
	border: 0;
	margin: 0;
	padding: 0;
	background: #F2F5FE url( "bg.gif" ) 0 0 repeat-x;
}


.TitlePanel
{
	background:LightSteelBlue;
	border-color:#E0E0E0;
	border-style:ridge;
	width:99%;
}

.Test
{
	background-color:Blue;
}

	
.mytext
{
		
	/*width:534px;
	height:74px;*/
	font:normal 11px/11px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	float:left;
}	
	
.heading1
{
width:597px;
	font:bold 16px/16px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	padding:0 0 0 10px;	
}	
	
.bottomMain{
	width:100%;
	height:29px;
	background-color:#5774C2;
	color:#000;
	border-top:#DAD9C1 solid 2px;
	border-bottom:#FFFDE7 solid 1px;
	padding:0px 0 0px 0;
}

.design{
	display:block;
	position:absolute;
	font:bold 12px/20px Arial, Helvetica, sans-serif;
	color:#7DC012;
	background-color:inherit;
}

.buttonA
{
background-color:#EBEBD1;
}

.gridheader
{
	background:url(Images/ig_cal_blue1.gif) ;
	padding:0 0 0 12px;
	color:#3F3D1F;
	font:normal 12px/21px Arial, Helvetica, sans-serif;
	text-decoration:underline;
}

.gridContent
{
		
	/*width:534px;
	height:74px;*/
	font:normal 12px/12px Arial, Helvetica, sans-serif;
	background-color:inherit;
	float:left;
}	
	
.popup
{
	background: #F2F5FE url( "bg.gif" ) 0 0 repeat-x;
}

.myddl
{
		
	/*width:534px;
	height:74px;*/
	font:normal 11px/11px Arial, Helvetica, sans-serif;
	color:#233B9B;
	background-color:inherit;
	}	
		