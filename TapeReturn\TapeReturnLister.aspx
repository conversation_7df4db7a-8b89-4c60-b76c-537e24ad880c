<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="TapeReturnLister.aspx.vb" Inherits="TapeReturn_TapeReturnLister" Title="Home > Tape Management >Blank Tape Return" %>

<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ Register Assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.Misc" TagPrefix="igmisc" %>

<%@ Register Assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebGrid" TagPrefix="igtbl" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <table width="100%">
        <tr>
            <td style="width: 100px">
                <asp:ScriptManager ID="ScriptManager1" runat="server">
                </asp:ScriptManager>
            </td>
        </tr>
        <tr>
            <td style="width: 100%; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Blank Tape Return Lister</td>
        </tr>
        <tr>
            <td style="height: 9px; text-decoration: underline;" class="labelheading"></td>
        </tr>
        <tr>
            <td style="width: 100%; height: 0px;" valign="top">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue">
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Font-Bold="True" Text="(Show Form) - Bulk Tape Issuance Search"
                        Width="448px" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label><br />
                </asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                        <ContentTemplate>
                            <table style="width: 712px">
                                <tbody>
                                    <tr class="mytext">
                                        <td style="height: 13px"></td>
                                        <td style="width: 135px; height: 13px"></td>
                                        <td style="width: 68px; height: 13px"></td>
                                        <td style="width: 103px; height: 13px"></td>
                                    </tr>
                                    <tr class="mytext">
                                        <td style="height: 12px">Employee Name &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                            <asp:CheckBox ID="chkEmp_issue" runat="server" Text="Ignore" OnCheckedChanged="chkEmp_issue_CheckedChanged" __designer:wfdid="w1" AutoPostBack="True"></asp:CheckBox></td>
                                        <td style="height: 12px">Department Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;&nbsp;
                                            <asp:CheckBox ID="chkDept_Issue" runat="server" Text="Ignore" OnCheckedChanged="chkDept_Issue_CheckedChanged" __designer:wfdid="w2" AutoPostBack="True"></asp:CheckBox></td>
                                        <td style="height: 12px">Entry Date &nbsp; &nbsp;
                                            <asp:CheckBox ID="chkDate_Issue" runat="server" Text="Ignore" OnCheckedChanged="chkDate_Issue_CheckedChanged" __designer:wfdid="w3" AutoPostBack="True"></asp:CheckBox>
                                        </td>
                                        <td style="width: 103px; height: 12px"></td>
                                    </tr>
                                    <tr>
                                        <td style="height: 22px" class="mytext">
                                            <asp:TextBox ID="txt_Employee_Issue" runat="server" CssClass="mytext" Width="200px" __designer:wfdid="w4"></asp:TextBox></td>
                                        <td style="width: 135px; height: 22px" class="mytext">
                                            <asp:TextBox ID="txt_Department_Issue" runat="server" CssClass="mytext" Width="208px" __designer:wfdid="w5"></asp:TextBox></td>
                                        <td style="width: 68px; height: 22px" class="mytext">
                                            <asp:TextBox ID="txtEntryDate_Issue" runat="server" CssClass="mytext" Width="112px" __designer:wfdid="w6"></asp:TextBox></td>
                                        <td style="width: 103px; height: 22px" class="mytext">
                                            <asp:TextBox ID="txtBulkTapeIssuanceID" runat="server" Width="80px" Visible="False" __designer:wfdid="w7"></asp:TextBox></td>
                                    </tr>
                                </tbody>
                            </table>
                            <cc1:CalendarExtender ID="CalendarExtender1" runat="server" __designer:dtid="281474976710826" CssClass="MyCalendar" TargetControlID="txtEntryDate_Issue" __designer:wfdid="w8" PopupPosition="TopRight" Format="dd-MMM-yyyy"></cc1:CalendarExtender>
                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Employee_BulkIssue" runat="server" __designer:dtid="281474976710726" TargetControlID="txt_Employee_Issue" __designer:wfdid="w9" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetBulkTapeIssuedEmployee" ServicePath="AutoComplete.asmx"></cc1:AutoCompleteExtender>
                            <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Dept_BulkIssue" runat="server" __designer:dtid="281474976710725" TargetControlID="txt_Department_Issue" __designer:wfdid="w1" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetDepartment" ServicePath="AutoComplete.asmx"></cc1:AutoCompleteExtender>
                            <asp:Label ID="lblErrIssue" runat="server" ForeColor="Red" Width="528px" Font-Bold="True" __designer:wfdid="w2"></asp:Label>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%; height: 29px">&nbsp;<asp:Button ID="bttnSearch_Issuance" runat="server" CssClass="buttonA" Font-Bold="True"
                                Text="Search" Width="88px" />&nbsp;&nbsp;<asp:Button ID="bttnEdit" runat="server" CssClass="buttonA"
                                    Font-Bold="True" Text="Edit" Width="96px" />&nbsp;<asp:Button ID="bttnDelete" runat="server"
                                        CssClass="buttonA" Font-Bold="True" Text="Delete" Width="96px" />
                                &nbsp;<asp:Button ID="bttnCancel_Issue" runat="server" CssClass="buttonA" Font-Bold="True"
                                    Text="Cancel" Width="96px" /></td>
                        </tr>
                    </table>
                    <igtbl:UltraWebGrid ID="grd_Result" runat="server" DataKeyField="StockRegisterID"
                        Width="800px" Visible="False">
                        <Bands>
                            <igtbl:UltraGridBand SelectTypeRow="Single">
                                <Columns>
                                    <igtbl:UltraGridColumn BaseColumnName="BulkTapeIssuanceID" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Bulk TapeIssuance ID" Hidden="True" Key="BulkTapeIssuanceID">
                                        <Header Caption="Bulk TapeIssuance ID">
                                        </Header>
                                        <Footer Caption="">
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="BulkTapeIssuanceDate" EditorControlID="" FooterText=""
                                        Format="dd-MMM-yyyy" HeaderText="Date" Width="150px">
                                        <Header Caption="Date">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="EmployeeName" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Employee Name" Width="300px">
                                        <Header Caption="Employee Name">
                                            <RowLayoutColumnInfo OriginX="2" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="2" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="DepartmentName" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Department Name" Width="250px">
                                        <Header Caption="Department Name">
                                            <RowLayoutColumnInfo OriginX="3" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="3" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                </Columns>
                                <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                    <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                        CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                        Font-Size="11px" Width="200px">
                                        <Padding Left="2px" />
                                    </FilterDropDownStyle>
                                    <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                    </FilterHighlightRowStyle>
                                </FilterOptions>
                                <RowEditTemplate>
                                    <p align="right">
                                        &nbsp;<input id="igtbl_TextBox_0_0" columnkey="" style="width: 150px" type="text" /><br />
                                        Date
                                            <input id="igtbl_TextBox_0_2" columnkey="" style="width: 150px" type="text" /><br />
                                        Source
                                            <input id="igtbl_TextBox_0_3" columnkey="" style="width: 150px" type="text" /><br />
                                        Pr No.
                                            <input id="igtbl_TextBox_0_4" columnkey="" style="width: 150px" type="text" /><br />
                                        SIR No.
                                            <input id="igtbl_TextBox_0_5" columnkey="" style="width: 150px" type="text" /><br />
                                        City
                                            <input id="igtbl_TextBox_0_6" columnkey="" style="width: 150px" type="text" /><br />
                                    </p>
                                    <br />
                                    <p align="center">
                                        <input id="igtbl_reOkBtn" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="OK" />&nbsp;
                                            <input id="igtbl_reCancelBtn" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                                type="button" value="Cancel" />
                                    </p>
                                </RowEditTemplate>
                                <RowTemplateStyle BackColor="Window" BorderColor="Window" BorderStyle="Ridge">
                                    <BorderDetails WidthBottom="3px" WidthLeft="3px" WidthRight="3px" WidthTop="3px" />
                                </RowTemplateStyle>
                                <AddNewRow View="NotSet" Visible="NotSet">
                                </AddNewRow>
                            </igtbl:UltraGridBand>
                            <igtbl:UltraGridBand RowSelectors="No" SelectTypeCell="None" SelectTypeCol="None">
                                <Columns>
                                    <igtbl:UltraGridColumn BaseColumnName="TapeType" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Tape Type" Key="TapeType">
                                        <Header Caption="Tape Type">
                                        </Header>
                                        <Footer Caption="">
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                    <igtbl:UltraGridColumn BaseColumnName="Quantity" EditorControlID="" FooterText=""
                                        Format="" HeaderText="Quantity" Key="Quantity">
                                        <Header Caption="Quantity">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Header>
                                        <Footer Caption="">
                                            <RowLayoutColumnInfo OriginX="1" />
                                        </Footer>
                                    </igtbl:UltraGridColumn>
                                </Columns>
                                <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                    <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                        CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                        Font-Size="11px" Width="200px">
                                        <Padding Left="2px" />
                                    </FilterDropDownStyle>
                                    <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                    </FilterHighlightRowStyle>
                                </FilterOptions>
                                <RowEditTemplate>
                                    <br />
                                    <p align="center">
                                        <input id="Button1" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                            type="button" value="OK" />&nbsp;
                                            <input id="Button2" onclick="igtbl_gRowEditButtonClick(event);" style="width: 50px"
                                                type="button" value="Cancel" />
                                    </p>
                                </RowEditTemplate>
                                <RowTemplateStyle BackColor="Window" BorderColor="Window" BorderStyle="Ridge">
                                    <BorderDetails WidthBottom="3px" WidthLeft="3px" WidthRight="3px" WidthTop="3px" />
                                </RowTemplateStyle>
                                <AddNewRow View="NotSet" Visible="NotSet">
                                </AddNewRow>
                            </igtbl:UltraGridBand>
                        </Bands>
                        <DisplayLayout AllowColSizingDefault="Free" AllowColumnMovingDefault="OnServer" AllowDeleteDefault="Yes"
                            AllowSortingDefault="OnClient" AllowUpdateDefault="Yes" AutoGenerateColumns="False"
                            BorderCollapseDefault="Separate" GroupByColumnsHiddenDefault="NotSet" HeaderClickActionDefault="SortMulti"
                            JavaScriptFileName="" JavaScriptFileNameCommon="" Name="grdxResult" RowHeightDefault="20px"
                            SelectTypeRowDefault="Single" Version="4.00" ViewType="OutlookGroupBy">
                            <FrameStyle BackColor="Window" BorderColor="ControlLightLight" BorderWidth="1px"
                                Font-Names="Microsoft Sans Serif" Font-Size="8.25pt" Width="800px">
                            </FrameStyle>
                            <ImageUrls BlankImage="" CollapseImage="" CurrentEditRowImage="" CurrentRowImage=""
                                ExpandImage="" FilterAppliedImage="" FilterImage="" FixedHeaderOffImage="" FixedHeaderOnImage=""
                                GridCornerImage="" GroupByImage="" GroupDownArrow="" GroupUpArrow="" ImageDirectory=""
                                NewRowImage="" RowLabelBlankImage="" SortAscending="" SortDescending="" UnGroupByImage="" />
                            <RowAlternateStyleDefault BackColor="White">
                            </RowAlternateStyleDefault>
                            <Pager AllowPaging="True" PageSize="15">
                                <Style BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                                    <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" > </BorderDetails>
                                </Style>
                            </Pager>
                            <EditCellStyleDefault BorderStyle="None" BorderWidth="0px">
                            </EditCellStyleDefault>
                            <FooterStyleDefault BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                                <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                            </FooterStyleDefault>
                            <HeaderStyleDefault BackColor="#5774C2" BorderStyle="Solid" ForeColor="White" HorizontalAlign="Center">
                                <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                            </HeaderStyleDefault>
                            <RowStyleDefault BackColor="Window" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px">
                                <Padding Left="3px" />
                                <BorderDetails ColorLeft="Window" ColorTop="Window" />
                            </RowStyleDefault>
                            <GroupByRowStyleDefault BackColor="Control" BorderColor="Window">
                            </GroupByRowStyleDefault>
                            <SelectedRowStyleDefault BackColor="#F09D21" ForeColor="White">
                            </SelectedRowStyleDefault>
                            <GroupByBox Hidden="True">
                                <Style BackColor="ActiveBorder" BorderColor="Window"></Style>
                            </GroupByBox>
                            <AddNewBox Hidden="False">
                                <Style BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid" BorderWidth="1px">
                                    <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" > </BorderDetails>
                                </Style>
                            </AddNewBox>
                            <FilterOptionsDefault AllString="(All)" EmptyString="(Empty)" NonEmptyString="(NonEmpty)">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptionsDefault>
                        </DisplayLayout>
                    </igtbl:UltraWebGrid>
                </asp:Panel>
            </td>
        </tr>
        <tr>
            <td style="width: 100%; height: 0px" valign="top">
                <asp:Panel ID="Panel1" runat="server" Width="100%" BackColor="LightSteelBlue">
                    <asp:Image ID="Image2" runat="server" />
                    <asp:Label ID="Label2" runat="server" CssClass="heading1" Font-Bold="True" Font-Names="Arial"
                        Font-Size="Medium" Text="(Show Form) - Bulk Tape Return Search" Width="448px"></asp:Label><br />
                </asp:Panel>
                <asp:Panel ID="Panel2" runat="server" Width="100%">
                    <table width="100%">
                        <tr>
                            <td valign="top" style="width: 718px; height: 98px;">
                                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                    <ContentTemplate>
                                        <table style="width: 616px">
                                            <tbody>
                                                <tr class="mytext">
                                                    <td style="height: 13px"></td>
                                                    <td style="width: 135px; height: 13px"></td>
                                                    <td style="width: 68px; height: 13px"></td>
                                                </tr>
                                                <tr class="mytext">
                                                    <td style="height: 12px">Employee Name &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                                                        <asp:CheckBox ID="chkEmp_Return" runat="server" Text="Ignore" AutoPostBack="True"></asp:CheckBox></td>
                                                    <td style="height: 12px">Department Name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;&nbsp;
                                                        <asp:CheckBox ID="chkDept_Return" runat="server" Text="Ignore" AutoPostBack="True"></asp:CheckBox></td>
                                                    <td style="height: 12px">Entry Date &nbsp; &nbsp;
                                                        <asp:CheckBox ID="chkDate_Return" runat="server" Text="Ignore" AutoPostBack="True"></asp:CheckBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="height: 15px" class="mytext">
                                                        <asp:TextBox ID="txt_Employee_Return" runat="server" CssClass="mytext" Width="200px"></asp:TextBox></td>
                                                    <td style="width: 135px; height: 15px" class="mytext">
                                                        <asp:TextBox ID="txt_Department_Return" runat="server" CssClass="mytext" Width="208px"></asp:TextBox></td>
                                                    <td style="width: 68px; height: 15px" class="mytext">
                                                        <asp:TextBox ID="txtEntryDate_Return" runat="server" CssClass="mytext" Width="112px"></asp:TextBox></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <cc1:CalendarExtender ID="CalendarExtender2" runat="server" CssClass="MyCalendar" TargetControlID="txtEntryDate_Return" Format="dd-MMM-yyyy"></cc1:CalendarExtender>
                                        <cc1:AutoCompleteExtender ID="AutoCompleteExtender1" runat="server" __designer:dtid="281474976710725" TargetControlID="txt_Department_Return" __designer:wfdid="w11" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetDepartment" ServicePath="AutoComplete.asmx">
                                        </cc1:AutoCompleteExtender>
                                        <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Employee_BulkReturn" runat="server" __designer:dtid="281474976710726" TargetControlID="txt_Employee_Return" __designer:wfdid="w12" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetBulkTapeIssuedEmployee" ServicePath="AutoComplete.asmx">
                                        </cc1:AutoCompleteExtender>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                                <asp:Label ID="lblErr_Return" runat="server" Font-Bold="True" Font-Size="Medium" ForeColor="Red"></asp:Label></td>
                        </tr>
                        <tr class="mytext">
                            <td style="width: 718px; height: 29px" class="bottomMain" valign="middle">&nbsp;&nbsp;
                         <asp:Button CssClass="buttonA" ID="bttnReturnSearch" runat="server" Text="Search" Width="80px" Font-Bold="True" />&nbsp;
                         <asp:Button CssClass="buttonA" ID="Button3" runat="server" Text="Cancel" Width="80px" Font-Bold="True" />
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td valign="middle">&nbsp;</td>
                        </tr>
                    </table>
                    <asp:GridView ID="dg_Return" runat="server" AutoGenerateColumns="False" CssClass="gridContent"
                        Width="680px" DataKeyNames="BulkReturnID">
                        <Columns>
                            <asp:TemplateField HeaderText="BulkReturnID" Visible="False">
                                <ItemTemplate>
                                    <asp:Label ID="Label1" runat="server" Text='<%# Bind("BulkReturnID") %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="EmployeeName">
                                <ItemTemplate>
                                    <asp:Label ID="Label2" runat="server" Text='<%# Bind("EmployeeName") %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Department Name">
                                <ItemTemplate>
                                    <asp:Label ID="Label3" runat="server" Text='<%# Bind("DepartmentName") %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="TapeType">
                                <ItemTemplate>
                                    <asp:Label ID="Label4" runat="server" Text='<%# Bind("TapeType") %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Entry Date">
                                <ItemTemplate>
                                    <asp:Label ID="Label5" runat="server" Text='<%# Bind("TapeReturnedOn", "{0:dd-MMM-yyyy}") %>'></asp:Label>
                                </ItemTemplate>
                                <EditItemTemplate>
                                    <asp:TextBox ID="txtDate" runat="server" Text='<%# Bind("TapeReturnedOn", "{0:dd-MMM-yyyy}") %>' CssClass="mytext" Width="88px"></asp:TextBox>
                                    <cc1:CalendarExtender ID="CalendarExtender3" runat="server" Format="dd-MMM-yyyy"
                                        TargetControlID="txtDate">
                                    </cc1:CalendarExtender>
                                </EditItemTemplate>
                                <ItemStyle CssClass="MyCalendar" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="Quantity">
                                <EditItemTemplate>
                                    <asp:TextBox ID="txt_Quantity" runat="server" Text='<%# Bind("Quantity") %>' Width="72px"></asp:TextBox>
                                </EditItemTemplate>
                                <ItemTemplate>
                                    <asp:Label ID="lblQuantity" runat="server" Text='<%# Bind("Quantity") %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" Visible="False" />
                            <asp:BoundField DataField="BulkReturnID" HeaderText="BulkReturnID" Visible="False" />
                            <asp:CommandField ShowEditButton="True" />
                            <asp:TemplateField ShowHeader="False">
                                <ItemTemplate>
                                    <asp:LinkButton ID="LinkButton1" runat="server" CausesValidation="False" CommandName="Delete"
                                        OnClientClick="return confirm('Are you Sure you want to Delete ?')" Text="Delete"></asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" />
                    </asp:GridView>
                </asp:Panel>
            </td>
        </tr>
    </table>

    <cc1:CollapsiblePanelExtender
        ID="CollapsiblePanelExtender1"
        runat="server"
        CollapseControlID="TitlePanel"
        Collapsed="true"
        CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Parameter Form (Bulk Tape Issuance Search) --"
        ExpandControlID="TitlePanel"
        ExpandedImage="~/Images/expand.gif"
        ExpandedText="-- Hide Parameter Form (Bulk Tape Issuance Search) --"
        ImageControlID="Image1"
        SuppressPostBack="true"
        TextLabelID="Label1"
        TargetControlID="ContentPanel">
    </cc1:CollapsiblePanelExtender>
    <cc1:CollapsiblePanelExtender
        ID="Collapsiblepanelextender2"
        runat="server"
        CollapseControlID="Panel1"
        Collapsed="true"
        CollapsedImage="~/Images/Collapse.gif"
        CollapsedText="-- Show Parameter Form (Bulk Tape Return Search) --"
        ExpandControlID="Panel1"
        ExpandedImage="~/Images/expand.gif"
        ExpandedText="-- Hide Parameter Form (Bulk Tape Return Search) --"
        ImageControlID="Image2"
        SuppressPostBack="true"
        TextLabelID="Label2"
        TargetControlID="Panel2">
    </cc1:CollapsiblePanelExtender>
    <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText="Do you want to Delete !"
        TargetControlID="bttnDelete">
    </cc1:ConfirmButtonExtender>
    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
</asp:Content>