<%@ Page Language="VB" AutoEventWireup="false" CodeFile="OSRNewTapeNumber_News.aspx.vb" Inherits="BulkTapeManagement_OSRNewTapeNumber_News" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<script language="javascript" type="text/javascript">
        
    function RefreshParent_2(){
        window.opener.location.href="FrmArchiveEntry_News.aspx";
    }
     
    function closewin_2()
    {
        RefreshParent_2();
        self.close();
    }
   
    </script>

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
   <title>Untitled Page</title>  
   <link href="main.css" rel="stylesheet" type="text/css" />     
</head>
    
<body style="background-repeat: repeat-x">
    <form id="form1" runat="server">
    <div>
        </div>
        <asp:ScriptManager ID="ScriptManager1" runat="server">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
        <table >
            <tr>
                <td style="height: 23px;">
                    <asp:Label CssClass="heading1" ID="Label2" runat="server" Text="New Tape Number Entry - Archival News" Width="472px"></asp:Label></td>
            </tr>
            <tr>
                <td >
                    <table >
                        <tr class="mytext">
                            <td style="width: 100px; height: 16px">
                                Single Tape No.</td>
                            <td style="width: 100px; height: 16px">
                            </td>
                            <td style="height: 16px" colspan="2">
                                Multiple Tape No.</td>
                            <td style="width: 52px; height: 16px">
                            </td>
                            <td style="width: 97px; height: 16px">
                            </td>
                            <td style="width: 165px; height: 16px">
                            </td>
                            <td style="width: 165px; height: 16px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="width: 100px; height: 16px">
                                Tape No.
                            </td>
                            <td style="width: 100px; height: 16px">
                                Station</td>
                            <td style="width: 53px; height: 16px">
                                From</td>
                            <td style="width: 99px; height: 16px">
                                </td>
                            <td style="width: 52px; height: 16px">
                                To</td>
                            <td style="width: 97px; height: 16px">
                            </td>
                            <td style="width: 165px; height: 16px">
                            </td>
                            <td style="width: 165px; height: 16px">
                            </td>
                        </tr>
                        <tr class="mytext">
                            <td style="width: 100px; height: 16px">
                                <asp:TextBox CssClass="mytext" ID="txt_TapeNumber" runat="server"></asp:TextBox></td>
                            <td style="width: 100px; height: 16px">
                                <asp:DropDownList ID="ddlStation" runat="server" CssClass="myddl" Width="48px">
                                    <asp:ListItem Value="3">OSR</asp:ListItem>
                                </asp:DropDownList></td>
                            <td style="height: 16px" colspan="2">
                                <asp:TextBox ID="txt_FromTape_Head" runat="server" Width="136px"></asp:TextBox></td>
                            <td colspan="2" style="height: 16px">
                                <asp:TextBox ID="txt_ToDate_Head" runat="server" Width="120px"></asp:TextBox></td>
                            <td style="width: 165px; height: 16px">
                                <asp:Button CssClass="buttonA" ID="bttnOK" runat="server" Text="OK" Width="56px" /></td>
                            <td style="width: 165px; height: 16px">
                                <asp:TextBox CssClass="mytext" ID="txt_FromTapeNumber" runat="server" Width="48px" Visible="False"></asp:TextBox>
                                <asp:TextBox CssClass="mytext" ID="txt_ToTapeNumer" runat="server" Height="16px" Width="48px" Visible="False"></asp:TextBox></td>
                        </tr>
                        <tr>
                            <td style="height: 16px" colspan="7">
                    <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="624px"></asp:Label></td>
                            <td style="width: 165px; height: 16px">
                            </td>
                        </tr>
                    </table>
                    <table>
                        <tr>
                            <td valign="top">
                    <asp:GridView ID="dgMain" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True"
                        CssClass="gridContent" OnSelectedIndexChanging="dgMain_SelectedIndexChanging"
                        PageSize="20" Width="424px" >
                        <Columns>
                            <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" />
                            <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                            <asp:TemplateField>
                                <ItemStyle HorizontalAlign="Center" />
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemTemplate>
                                    &nbsp;
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="gridheader" BackColor="LightBlue" />
                        <AlternatingRowStyle CssClass="AlternateRows" />
                        <SelectedRowStyle BackColor="#FFE0C0" />
                    </asp:GridView>
                            </td>
                            <td style="height: 328px;" valign="top">
                    <asp:GridView ID="dg_tapeNumber" runat="server" AutoGenerateColumns="False" Width="408px" CssClass="gridContent" PageSize="18" OnRowDeleting="dg_tapeNumber_RowDeleting">
                        <Columns>
                            <asp:BoundField DataField="StockRegisterDetailID" >
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeTypeID" >
                                <ItemStyle ForeColor="#F2F5FE" />
                                <HeaderStyle Width="0px" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeNumber" HeaderText="TapeNumber" />
                            <asp:BoundField DataField="TapeType" HeaderText="TapeType" />
                            <asp:BoundField DataField="StationID">
                                <ItemStyle ForeColor="#F2F5FE" />
                            </asp:BoundField>
                            <asp:BoundField DataField="StationName" HeaderText="Station Name" />
                            <asp:CommandField ShowDeleteButton="True" />
                        </Columns>
                        <HeaderStyle BackColor="LightBlue" />
                    </asp:GridView>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
            <tr>
                <td class="bottomMain" style="width: 1194px; height: 28px">
                    &nbsp;<asp:Button CssClass="buttonA" ID="bttnTableSave" runat="server" Text="Save" Width="64px" />
                    <asp:LinkButton ID="LinkButton1" runat="server" Font-Size="Large" OnClientClick="javascript:closewin_2();" Width="48px" CssClass="buttonA" ForeColor="Black">Close</asp:LinkButton>
                    <asp:Button ID="Button2" runat="server" OnClick="Button2_Click" Text="Button" Visible="False" /></td>
            </tr>
            <tr>
                <td style="height: 28px;">
                    <asp:Button ID="bttnSave" runat="server" Text="OK" Width="72px" Visible="False" />
                    <asp:TextBox ID="txt_SRDID" runat="server" Width="88px" BorderStyle="None" ForeColor="#F2F5FE" BackColor="#F2F5FE"></asp:TextBox>
                    <asp:TextBox ID="txt_TapeTypeID" runat="server" Width="64px" BorderStyle="None" ForeColor="#F2F5FE" BackColor="#F2F5FE"></asp:TextBox>
                    <asp:TextBox ID="txt_Count" runat="server" Width="64px" BorderStyle="None" ForeColor="#F2F5FE" BackColor="#F2F5FE"></asp:TextBox>&nbsp;
                    <asp:Label ID="Label1" runat="server" Text="Label" Width="64px" Visible="False"></asp:Label>
                                <asp:Button ID="Button1" runat="server" Text="Button" Visible="False" />
                    <cc1:filteredtextboxextender id="FilteredTextBoxExtender1" runat="server" filtertype="Numbers"
                        invalidchars="1234567890" targetcontrolid="txt_FromTapeNumber"></cc1:filteredtextboxextender>
                    <cc1:filteredtextboxextender id="FilteredTextBoxExtender2" runat="server" filtertype="Numbers"
                        invalidchars="1234567890" targetcontrolid="txt_ToTapeNumer"></cc1:filteredtextboxextender>
                    &nbsp;
                </td>
            </tr>
            <tr>
                <td align="center" style="height: 27px">
                    &nbsp;<asp:GridView ID="dg" runat="server" AutoGenerateColumns="False" AutoGenerateSelectButton="True" AllowPaging="True" CssClass="gridContent" PageSize="18" Width="648px" >
                        <Columns>
                            <asp:BoundField DataField="StockRegisterDetailID" HeaderText="StockRegisterDetailID" />
                            <asp:BoundField DataField="TapeType" HeaderText="TapeType" />
                            <asp:BoundField DataField="PRNo" HeaderText="PRNo" />
                            <asp:BoundField DataField="SIRNo" HeaderText="SIRNo" />
                            <asp:BoundField DataField="Issued" HeaderText="Count" >
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TotalQty" HeaderText="TotalQty" >
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:BoundField>
                            <asp:BoundField DataField="Remaining" HeaderText="Remaining" >
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:BoundField>
                            <asp:BoundField DataField="TapeTypeID" HeaderText="TapeTypeID" />
                            <asp:BoundField DataField="Date" HeaderText="Date" />
                        </Columns>
                        <SelectedRowStyle BackColor="#FFFFC0" />
                        <HeaderStyle BackColor="LightBlue" />
                    </asp:GridView>
                    </td>
            </tr>
        </table>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
</body>
</html>
