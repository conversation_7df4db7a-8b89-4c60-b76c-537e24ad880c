<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmStockStatus.aspx.vb" Inherits="ApplicationSetup_frmStockStatus" title="Home > Stock Status > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="WIDTH: 446px; HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w14" CssClass="labelheading">Home</asp:LinkButton> &gt; Stock Status &gt; Add New</TD></TR><TR><TD style="WIDTH: 446px"><TABLE><TBODY><TR class="mytext"><TD style="HEIGHT: 22px">Stock Status</TD><TD style="HEIGHT: 22px"><asp:TextBox id="txt_StockStatus" runat="server" CssClass="mytext"></asp:TextBox></TD><TD style="HEIGHT: 22px"></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True" Width="416px"></asp:Label></TD></TR><TR><TD style="WIDTH: 446px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD style="WIDTH: 446px"><asp:GridView id="dg_StockStatus" runat="server" CssClass="gridContent" Width="376px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="StockStatusID" HeaderText="StockStatusID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="StockStatus" HeaderText="Stock Status" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD style="WIDTH: 446px"><asp:TextBox id="txt_StockStatusID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w5" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

