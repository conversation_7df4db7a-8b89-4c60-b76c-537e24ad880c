<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false"
    CodeFile="ReminderService.aspx.vb" Inherits="ReminderService_ReminderService"
    Title="Home > Reminder Service" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table style="width: 100%">
        <tr>
            <td class="labelheading" style="width: 100%; text-decoration: underline">
                <asp:LinkButton ID="LnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Reminder Service</td>
        </tr>
        <tr>
            <td>
                <table style="width: 1027px">
                    <tr class="mytext">
                        <td style="height: 22px">
                            Employee &nbsp; &nbsp; &nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            <asp:CheckBox ID="Chk_Employee" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            Department &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;
                            <asp:CheckBox ID="chkDepartment" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            From Date&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; <asp:CheckBox ID="Chk_Date" runat="server" Text="Ignore" /></td>
                        <td style="height: 22px">
                            To Date
                        </td>
                        <td style="height: 22px">
                            Blank/Archive
                        </td>
                        <td style="height: 22px">
                            Station</td>
                        <td style="height: 22px">
                            Sort By</td>
                        <td style="height: 22px">
                            Sort Order</td>
                    </tr>
                    <tr class="mytext">
                        <td>
                            <asp:TextBox ID="txtEmployee" runat="server" CssClass="mytext" Width="208px"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txt_DepartmentName" runat="server" CssClass="mytext" Width="175px"></asp:TextBox></td>
                        <td>
                            <asp:TextBox CssClass="mytext" ID="txt_DueDate" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtToDate" runat="server" CssClass="mytext"></asp:TextBox></td>
                        <td>
                            <asp:DropDownList ID="ddlIsBlank" runat="server" CssClass="myddl" Width="88px">
                                <asp:ListItem Value="-1">Ignore</asp:ListItem>
                                <asp:ListItem Value="1">Blank</asp:ListItem>
                                <asp:ListItem Value="0">Archive</asp:ListItem>
                            </asp:DropDownList></td>
                        <td>
                            <asp:DropDownList ID="ddlBaseStation" runat="server" CssClass="mytext" Width="88px">
                            </asp:DropDownList></td>
                        <td>
                            <asp:DropDownList ID="ddlSortby" runat="server" CssClass="mytext" Width="112px">
                                <asp:ListItem Value="1">Employee</asp:ListItem>
                                <asp:ListItem Value="2">Issue Date</asp:ListItem>
                                <asp:ListItem Value="3">Department</asp:ListItem>
                                <asp:ListItem Value="4">Tape Type</asp:ListItem>
                            </asp:DropDownList></td>
                        <td>
                            <asp:DropDownList ID="ddlSOrder" runat="server" CssClass="mytext" Width="79px">
                                <asp:ListItem Value="0">Acending</asp:ListItem>
                                <asp:ListItem Value="1">Descending</asp:ListItem>
                            </asp:DropDownList></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr class="mytext">
            <td class="bottomMain">
                &nbsp;
                <asp:Button CssClass="buttonA" ID="bttnSearch" runat="server" Text="Search" Font-Bold="True" Width="80px" />&nbsp;
                <asp:Button CssClass="buttonA" ID="bttnSendMail" runat="server" Text="Send Email" Width="104px" Font-Bold="True" />&nbsp;
                <asp:Button CssClass="buttonA" ID="bttnClear" runat="server" Text="Clear" Width="88px" Font-Bold="True" />
                <asp:Button CssClass="buttonA" ID="bttnTestEmail" runat="server" Text="Test Email" Width="104px" Font-Bold="True" Visible="False" /></td>
        </tr>
        <tr class="mytext">
            <td style="height: 68px">
                <table style="width: 588px">
                    <tr class="mytext">
                        <td>
                            CC</td>
                        <td>
                            BCC</td>
                        <td>
                            Employee Email Address</td>
                        <td>
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 178px">
                            <asp:TextBox CssClass="mytext" ID="txt_CC" runat="server" Width="150px"></asp:TextBox></td>
                        <td style="width: 163px">
                            <asp:TextBox CssClass="mytext" ID="txt_BCC" runat="server" Width="150px"></asp:TextBox></td>
                        <td style="width: 160px" align="center" valign="middle">
                            <asp:TextBox CssClass="mytext" ID="txt_EmailAddress" runat="server" Width="200px"></asp:TextBox></td>
                        <td style="width: 77px; color: #ff0033;">
                            <asp:LinkButton ID="LinkButton1" runat="server" Width="93px">Get Email Address</asp:LinkButton></td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 178px">
                        </td>
                        <td style="width: 163px">
                        </td>
                        <td align="left" style="width: 160px" valign="top">
                            eg:&nbsp; <EMAIL></td>
                        <td style="width: 150px; color: #ff0033">
                        </td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" CssClass="mytext" Font-Size="Small" ForeColor="Red"
                    Width="704px" Font-Bold="True"></asp:Label></td>
        </tr>
        <tr class="mytext">
            <td>
                <asp:GridView ID="dg_search" runat="server" AutoGenerateColumns="False" Width="100%"
                    CssClass="gridContent" AllowPaging="True" PageSize="250" >
                    <Columns>
                        <asp:TemplateField HeaderText="S.#">
                            <ItemTemplate>
                                <asp:Label ID="lbRowID" runat="server" Text='<%# Bind("NumberItems") %>'></asp:Label>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="TapeNumber" HeaderText="Tape No." />
                        <asp:BoundField DataField="EmployeeName" HeaderText="Employee Name" />
                        <asp:BoundField DataField="TapeIssuanceDate" HeaderText="Issue Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False" />
                        <asp:BoundField DataField="TapeReturnDueDate" HeaderText="Due Date" DataFormatString="{0:dd-MMM-yyyy}" HtmlEncode="False" />
                        <asp:TemplateField HeaderText="Check">
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle HorizontalAlign="Center" />
                             <HeaderTemplate> All
                                 <asp:CheckBox ID="checkAll" runat="server" onclick = "checkAll(this);" /> 
                            </HeaderTemplate> 

                            <ItemTemplate>
                                <asp:CheckBox ID="Chk_Tape" onclick="Check_Click(this)"  runat="server" />
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="DepartmentName" HeaderText="Department Name" />
                        <asp:BoundField DataField="Blank/Archive" HeaderText="Blank/Archive" />
                        <asp:BoundField DataField="TotalReminders" HeaderText="Total Reminders" />
                        <asp:BoundField DataField="TapeType" HeaderText="Tape Type" />
                       <asp:BoundField DataField="Program / Slug" HeaderText="Program / Slug" >
                            <ItemStyle Width="200px" />
                        </asp:BoundField>
                    </Columns>
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView>
            </td>
        </tr>
    </table>
    <cc1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server">
    </cc1:ToolkitScriptManager>
    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txt_DueDate" CssClass="MyCalendar" Format="dd-MMM-yyyy">
    </cc1:CalendarExtender>
    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" Format="dd-MMM-yyyy"
        TargetControlID="txtToDate" CssClass="MyCalendar">
    </cc1:CalendarExtender>
    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Employee" runat="server" CompletionInterval="1"
        CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="1" ServiceMethod="GetActiveEmployee"
        ServicePath="AutoComplete.asmx" TargetControlID="txtEmployee">
    </cc1:AutoCompleteExtender>
    <cc1:AutoCompleteExtender ID="AutoCompleteExtender_Department" runat="server" CompletionInterval="1"
        CompletionSetCount="12" EnableCaching="true" MinimumPrefixLength="3" ServiceMethod="GetDepartment"
        ServicePath="AutoComplete.asmx" TargetControlID="txt_DepartmentName">
    </cc1:AutoCompleteExtender>
                            <asp:DropDownList CssClass="mytext" ID="ddl_Employee" runat="server" Width="220px" Visible="False">
                            </asp:DropDownList>
    <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lblEmployeeName" runat="server" Visible="False"></asp:Label>
</asp:Content>
