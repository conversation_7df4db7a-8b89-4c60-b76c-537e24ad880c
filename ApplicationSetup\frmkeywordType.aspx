<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmkeywordType.aspx.vb" Inherits="ApplicationSetup_frmkeywordType" title="Home > Keyword Type > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="lnkHomePage" onclick="lnkHomePage_Click" runat="server" __designer:wfdid="w8" CssClass="labelheading">Home</asp:LinkButton> &gt; Keyword Type &gt; Add New</TD></TR><TR><TD style="HEIGHT: 39px"><TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 76px">KeywordType</TD><TD><asp:TextBox id="txt_KeywordType" runat="server" CssClass="mytext"></asp:TextBox></TD><TD>Content Type</TD><TD><asp:DropDownList id="ddl_ContentType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="320px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 27px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_KeywordType" runat="server" CssClass="gridContent" Width="632px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="KeywordTypeID" HeaderText="KeywordTypeID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="KeywordType" HeaderText="Keyword Type" />
                        <asp:BoundField DataField="ContentTypeID" HeaderText="ContentTypeID" />
                        <asp:BoundField DataField="ContentTypeName" HeaderText="ContentType Name" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w19" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender></TD></TR><TR><TD style="HEIGHT: 22px"><asp:TextBox id="txt_KeywordTypeID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

