<%@ Page Language="VB" AutoEventWireup="false" CodeFile="EntTapeDetail.aspx.vb" Inherits="EntTapeDetail" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<script language="Javascript1.2">
<!--
// please keep these lines on when you copy the source
// made by: Nicolas - http://www.javascript-page.com

//var message = "<< Print >>";

//function printpage() {
//window.print();  
//}

//document.write("<form><input type=button "
//+"value=\""+message+"\" onClick=\"printpage()\"></form>");

//-->
</script>

<head runat="server">
    <title>Search Engine > Entertainment Tape Details</title>
    <link href="main.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <table width="100%">
                <tr>
                    <td style="width: 100%">
                        &nbsp;
                        <asp:FormView ID="FormView1" runat="server" DataKeyNames="RecordID" DataSourceID="SqlDataSource1" CellPadding="4" ForeColor="#333333" HeaderText="-- Entertainment Tape Details --" HorizontalAlign="Left" Width="100%" Font-Names="Arial">
                            <ItemTemplate>
                                <asp:Label ID="Label4" runat="server" Font-Bold="true" Text="Tape Number:"></asp:Label>
                                <asp:Label ID="TapeNumberLabel" Font-Size="Small" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:Label><br />
                                <hr />
                                <asp:Label ID="Label1" runat="server" Font-Bold="true" Text="Sub Closet:"></asp:Label>
                                <asp:Label ID="SubClosetLabel" runat="server" Font-Size="Small" Text='<%# Bind("SubCloset") %>'></asp:Label><br /><hr />
                                <asp:Label ID="Label2" runat="server" Font-Bold="true" Text="Available:"></asp:Label>
                                <asp:Label ID="isAvailableLabel" runat="server" Font-Size="Small" Text='<%# Bind("isAvailable") %>'>
                                </asp:Label><br />
                                 <hr />
                                <asp:Label ID="Label3" runat="server" Font-Bold="true" Text="Entry Date:"></asp:Label>
                                <asp:Label ID="EntryDateLabel" runat="server" Font-Size="Small" Text='<%# Bind("EntryDate") %>'></asp:Label><br /> <hr />
                                   <asp:Label ID="lblOnAirDate" runat="server" Font-Bold="true" Text="On-Air Date:"></asp:Label>
                                 <asp:Label ID="OnAirDateLabel" runat="server" Font-Size="Small" Text='<%# Bind("OnAirDate") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label5" runat="server" Font-Bold="true" Text="Tape Type:"></asp:Label>
                                <asp:Label ID="TapeTypeLabel" runat="server" Font-Size="Small" Text='<%# Bind("TapeType") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label6" runat="server" Font-Bold="true" Text="Tape Status:"></asp:Label>
                                <asp:Label ID="TapeStatusLabel" runat="server" Font-Size="Small" Text='<%# Bind("TapeStatus") %>'>
                                </asp:Label><br /> <hr />
                                <asp:Label ID="Label7" runat="server" Font-Bold="true" Text="Start Time:"></asp:Label>
                                <asp:Label ID="StartTimeLabel" runat="server" Font-Size="Small" Text='<%# Bind("StartTime") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label8" runat="server" Font-Bold="true" Text="End Time:"></asp:Label>
                                <asp:Label ID="EndTimeLabel" runat="server" Font-Size="Small" Text='<%# Bind("EndTime") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label9" runat="server" Font-Bold="true" Text="Program Name:"></asp:Label>
                                <asp:Label ID="ProgramChildNameLabel" runat="server" Font-Size="Small" Text='<%# Bind("ProgramChildName") %>'>
                                </asp:Label><br /> <hr />
                                <asp:Label ID="Label10" runat="server" Font-Bold="true" Text="Note Area:"></asp:Label>
                                <asp:Label ID="NoteAreaLabel" runat="server" Font-Size="Small" Text='<%# Bind("NoteArea") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label11" runat="server" Font-Bold="true" Text="Abstract:"></asp:Label>
                                <asp:Label ID="AbstractLabel" runat="server" Font-Size="Small" Text='<%# Bind("Abstract") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label12" runat="server" Font-Bold="true" Text="Episode No:"></asp:Label>
                                <asp:Label ID="EpisodeNoLabel" runat="server" Font-Size="Small" Text='<%# Bind("EpisodeNo") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label13" runat="server" Font-Bold="true" Text="Part No:"></asp:Label>
                                <asp:Label ID="PartNoLabel" runat="server" Font-Size="Small" Text='<%# Bind("PartNo") %>'></asp:Label><br /> <hr />
                                <asp:Label ID="Label14" runat="server" Font-Bold="true" Text="Duration:"></asp:Label>
                                <asp:Label ID="DurationLabel" runat="server" Font-Size="Small" Text='<%# Bind("Duration") %>'></asp:Label><br /> <hr />
                              
                            </ItemTemplate>
                            <EditItemTemplate>
                               TapeNumber:
                                <asp:TextBox ID="TapeNumberTextBox" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:TextBox><br />
                                SubCloset:
                                <asp:TextBox ID="SubClosetTextBox" runat="server" Text='<%# Bind("SubCloset") %>'>
                                </asp:TextBox><br />
                                isAvailable:
                                <asp:TextBox ID="isAvailableTextBox" runat="server" Text='<%# Bind("isAvailable") %>'>
                                </asp:TextBox><br />
                                EntryDate:
                                <asp:TextBox ID="EntryDateTextBox" runat="server" Text='<%# Bind("EntryDate") %>'>
                                </asp:TextBox><br />
                                TapeType:
                                <asp:TextBox ID="TapeTypeTextBox" runat="server" Text='<%# Bind("TapeType") %>'>
                                </asp:TextBox><br />
                                TapeStatus:
                                <asp:TextBox ID="TapeStatusTextBox" runat="server" Text='<%# Bind("TapeStatus") %>'>
                                </asp:TextBox><br />
                                StartTime:
                                <asp:TextBox ID="StartTimeTextBox" runat="server" Text='<%# Bind("StartTime") %>'>
                                </asp:TextBox><br />
                                EndTime:
                                <asp:TextBox ID="EndTimeTextBox" runat="server" Text='<%# Bind("EndTime") %>'>
                                </asp:TextBox><br />
                                ProgramChildName:
                                <asp:TextBox ID="ProgramChildNameTextBox" runat="server" Text='<%# Bind("ProgramChildName") %>'>
                                </asp:TextBox><br />
                                NoteArea:
                                <asp:TextBox ID="NoteAreaTextBox" runat="server" Text='<%# Bind("NoteArea") %>'>
                                </asp:TextBox><br />
                                Abstract:
                                <asp:TextBox ID="AbstractTextBox" runat="server" Text='<%# Bind("Abstract") %>'>
                                </asp:TextBox><br />
                                EpisodeNo:
                                <asp:TextBox ID="EpisodeNoTextBox" runat="server" Text='<%# Bind("EpisodeNo") %>'>
                                </asp:TextBox><br />
                                PartNo:
                                <asp:TextBox ID="PartNoTextBox" runat="server" Text='<%# Bind("PartNo") %>'>
                                </asp:TextBox><br />
                                Duration:
                                <asp:TextBox ID="DurationTextBox" runat="server" Text='<%# Bind("Duration") %>'>
                                </asp:TextBox><br />
                                On Air Date:
                                <asp:TextBox ID="OnAirTextBox" runat="server" Text='<%# Bind("OnAirDate") %>'>
                                </asp:TextBox><br />
                                <asp:LinkButton ID="UpdateButton" runat="server" CausesValidation="True" CommandName="Update"
                                    Text="Update">
                                </asp:LinkButton>
                                <asp:LinkButton ID="UpdateCancelButton" runat="server" CausesValidation="False" CommandName="Cancel"
                                    Text="Cancel">
                                </asp:LinkButton>
                            </EditItemTemplate>
                            <InsertItemTemplate>
                                TapeNumber:
                                <asp:TextBox ID="TapeNumberTextBox" runat="server" Text='<%# Bind("TapeNumber") %>'>
                                </asp:TextBox><br />
                                SubCloset:
                                <asp:TextBox ID="SubClosetTextBox" runat="server" Text='<%# Bind("SubCloset") %>'>
                                </asp:TextBox><br />
                                isAvailable:
                                <asp:TextBox ID="isAvailableTextBox" runat="server" Text='<%# Bind("isAvailable") %>'>
                                </asp:TextBox><br />
                                EntryDate:
                                <asp:TextBox ID="EntryDateTextBox" runat="server" Text='<%# Bind("EntryDate") %>'>
                                </asp:TextBox><br />
                                TapeType:
                                <asp:TextBox ID="TapeTypeTextBox" runat="server" Text='<%# Bind("TapeType") %>'>
                                </asp:TextBox><br />
                                TapeStatus:
                                <asp:TextBox ID="TapeStatusTextBox" runat="server" Text='<%# Bind("TapeStatus") %>'>
                                </asp:TextBox><br />
                                StartTime:
                                <asp:TextBox ID="StartTimeTextBox" runat="server" Text='<%# Bind("StartTime") %>'>
                                </asp:TextBox><br />
                                EndTime:
                                <asp:TextBox ID="EndTimeTextBox" runat="server" Text='<%# Bind("EndTime") %>'>
                                </asp:TextBox><br />
                                ProgramChildName:
                                <asp:TextBox ID="ProgramChildNameTextBox" runat="server" Text='<%# Bind("ProgramChildName") %>'>
                                </asp:TextBox><br />
                                NoteArea:
                                <asp:TextBox ID="NoteAreaTextBox" runat="server" Text='<%# Bind("NoteArea") %>'>
                                </asp:TextBox><br />
                                Abstract:
                                <asp:TextBox ID="AbstractTextBox" runat="server" Text='<%# Bind("Abstract") %>'>
                                </asp:TextBox><br />
                                EpisodeNo:
                                <asp:TextBox ID="EpisodeNoTextBox" runat="server" Text='<%# Bind("EpisodeNo") %>'>
                                </asp:TextBox><br />
                                PartNo:
                                <asp:TextBox ID="PartNoTextBox" runat="server" Text='<%# Bind("PartNo") %>'>
                                </asp:TextBox><br />
                                Duration:
                                <asp:TextBox ID="DurationTextBox" runat="server" Text='<%# Bind("Duration") %>'>
                                </asp:TextBox><br />
                                OnAirDate:
                                <asp:TextBox ID="OnAirDateTextBox" runat="server" Text='<%# Bind("OnAirDate") %>'>
                                </asp:TextBox><br />
                                <asp:LinkButton ID="InsertButton" runat="server" CausesValidation="True" CommandName="Insert"
                                    Text="Insert">
                                </asp:LinkButton>
                                <asp:LinkButton ID="InsertCancelButton" runat="server" CausesValidation="False" CommandName="Cancel"
                                    Text="Cancel">
                                </asp:LinkButton>
                            </InsertItemTemplate>
                            <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                            <EditRowStyle BackColor="#2461BF" />
                            <RowStyle BackColor="#EFF3FB" />
                            <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
                            <HeaderStyle BackColor="#507CD1" Font-Bold="True" Font-Names="Arial" Font-Size="Large"
                                ForeColor="White" HorizontalAlign="Center" />
                        </asp:FormView>
                        <asp:SqlDataSource ID="SqlDataSource1" runat="server" ConnectionString="<%$ ConnectionStrings:DAMS_NewDBConnectionString5 %>"
                            SelectCommand="GetEntSearchEngine_TapeDetails2" SelectCommandType="StoredProcedure">
                            <SelectParameters>
                                <asp:QueryStringParameter Name="TapeNumber" QueryStringField="TapeNumber" Type="String" />
                                <asp:QueryStringParameter Name="Program" QueryStringField="ProgramChildName" Type="String" />
                                <asp:QueryStringParameter Name="EpisodeNo" QueryStringField="EpisodeNo" Type="String" />
                                <asp:QueryStringParameter Name="PartNo" QueryStringField="PartNo" Type="String" />
                            </SelectParameters>
                        </asp:SqlDataSource>
                    </td>
                </tr>
                <tr>
                    <td style="font-weight: bold; width: 100%; font-family: Arial; height: 21px">
                        Added &amp; Modified By :
                        <hr />
                        <table>
                            <tr>
                                <td style="width: 419px; height: 18px;">
                                    <asp:Label ID="Label17" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Small"
                                        Width="98px">Added By :</asp:Label></td>
                                <td style="width: 97%; height: 18px;">
                                    <asp:Label ID="lblAddedBy" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Small"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 419px">
                                    <asp:Label ID="Label19" runat="server" Font-Bold="True" Font-Names="Arial" Font-Size="Small"
                                        Width="100px">Modified By :</asp:Label></td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblModifiedBy" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Small"></asp:Label></td>
                            </tr>
                        </table>
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td style="width: 100%; height: 21px; font-weight: bold; font-family: Arial;"><hr />
                        &nbsp;KeyTypes &amp; 
                        Keywords :- &nbsp;&nbsp;
        <hr />
                        <table width="100%">
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
        <asp:Label ID="lblKeyword" runat="server" Font-Names="Arial" Font-Bold="False" Font-Size="Small"></asp:Label></td>
                            </tr>
                        </table><hr />
                        Availability:</td>
                                         </tr>
                <tr>
                    <td style="font-weight: bold; width: 100%; font-family: Arial; height: 21px">
                        <table width="100%">
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblIsAvailable" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Small"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblIsAvailable2" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Small"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblIsAvailable3" runat="server" Font-Bold="False" Font-Names="Arial"
                                        Font-Size="Small"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblIsAvailable4" runat="server" Font-Bold="False" Font-Names="Arial"
                                        Font-Size="Small"></asp:Label></td>
                            </tr>
                            <tr>
                                <td style="width: 181px">
                                </td>
                                <td style="width: 97%">
                                    <asp:Label ID="lblIsAvailable5" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Small"></asp:Label></td>
                            </tr>
                        </table>
                        <hr />
                        <asp:Label ID="Label18" runat="server" Font-Bold="True" Font-Names="Arial" Text="Damage (Yes/No):"></asp:Label></td>
                </tr>
                <tr>
                    <td style="font-weight: bold; width: 100%; font-family: Arial; height: 21px">
                        <table width="100%">
                            <tr>
                                <td style="width: 100px; height: 21px">
                                </td>
                                <td style="width: 96%; height: 21px">
                                    <asp:Label ID="lblIsDamage" runat="server" Font-Bold="False" Font-Names="Arial" Font-Size="Smaller"></asp:Label></td>
                            </tr>
                        </table><hr />
                        <asp:Label ID="Label15" runat="server" Font-Bold="True" Font-Names="Arial" Text="Urdu Script :"></asp:Label></td>
                </tr>
                <tr>
                    <td style="font-weight: bold; width: 100%; font-family: Arial; height: 21px">
                        <table width="100%">
                            <tr>
                                <td style="height: 21px" colspan="2">
                                    <asp:Label ID="lblUrduScript" runat="server" Font-Bold="False" Font-Names="Arial Narrow"
                                        Font-Size="Large" Visible="False"></asp:Label>
                                    <asp:TextBox ID="txtUrduScript" runat="server" BackColor="#F2F5FE" BorderStyle="None"
                                        CssClass="mytextUrdu" Font-Size="Medium" Height="150px" TabIndex="3" TextMode="MultiLine"
                                        Width="100%"></asp:TextBox></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="bottomMain" style="width: 100%">
                        &nbsp;
                        <asp:Button ID="bttnPrint" runat="server" OnClientClick="window.print()" CssClass="buttonA"
                            Font-Bold="True" Text="<< Print Page >>" Width="136px" />
                        <asp:Button ID="bttnReminder1" runat="server" CssClass="buttonA" Font-Bold="True"
                            Text="Reminder 1" Enabled="False" />&nbsp;<asp:Button ID="bttnReminder2" runat="server" CssClass="buttonA"
                                Font-Bold="True" Text="Reminder 2" Enabled="False" />
                        <asp:Button ID="bttnEditMaster" runat="server" CssClass="buttonA"
                                Font-Bold="True" Text="Goto Tape Entry Screen" />
                        <asp:Label ID="Emp1" runat="server" Visible="False"></asp:Label>
                        <asp:Label ID="Emp2" runat="server" Visible="False"></asp:Label>
                        <asp:Button ID="Button1" runat="server" Text="Button" Visible="False" />
                        <asp:Label ID="lblEmployeeName" runat="server" Visible="False"></asp:Label>
                        <asp:Label ID="lbl_UserName" runat="server" Visible="False"></asp:Label>
                        <asp:Label ID="lblMasterID" runat="server" Visible="False"></asp:Label></td>
                </tr>
            </table>
        </div>
        &nbsp;<br />
        &nbsp;
    </form>
</body>
</html>
