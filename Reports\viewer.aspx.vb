Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Web.Design
Imports System.IO
Imports System.Data
Imports System.Data.SqlClient

Partial Class viewer
    Inherits System.Web.UI.Page
    'Protected WithEvents crv As CrystalDecisions.Web.CrystalReportViewer
    Dim rpt As New CrystalDecisions.CrystalReports.Engine.ReportDocument

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim reportName As String = Request("ReportName")
        Dim strparams As String
        Dim qryParam As Integer = Request.QueryString.Count
        Dim keys() As String = Request.QueryString.AllKeys
        Dim paramValues As New Hashtable
        Dim i As Integer = 0
        For i = 1 To keys.Length - 1
            If keys(i) <> "@TopN" Then
                paramValues.Add(keys(i), Request.QueryString.Get(keys(i)))
            End If
        Next
        Try
            showReport(reportName, paramValues)
        Catch ex As Exception
            Throw
        End Try

    End Sub
    Private Sub showReport(ByVal reportName As String, ByVal paramValues As Hashtable)
        Try
            rpt.Load(Server.MapPath(reportName))
            '' Dim strConnection As String = "server=KHI-CCS-SRV\TRM;database=DAMS_NewDB;uid=sa;password=**********"
            Dim strConnection As String = "Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********"

            'Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString") '"server=MISOPS1\Dams;database=DAMS_NewDB;uid=dams_user;password=**********"

            Dim connection As New SqlClient.SqlConnection(strConnection)
            Dim logInfo As New CrystalDecisions.Shared.TableLogOnInfo()
            Dim connInfo As New CrystalDecisions.Shared.ConnectionInfo()
            connection.Open()
            logInfo = rpt.Database.Tables(0).LogOnInfo
            connInfo = rpt.Database.Tables(0).LogOnInfo.ConnectionInfo
            connInfo.ServerName = connection.DataSource

            connInfo.DatabaseName = connection.Database
            'connInfo.UserID = "sa"
            'connInfo.UserID = "dams_user"
            'connInfo.Password = "**********"
            connInfo.UserID = "sa"
            connInfo.Password = "**********"
            logInfo.ConnectionInfo = connInfo
            rpt.Database.Tables(0).ApplyLogOnInfo(logInfo)

            Dim keyCollection As ICollection = paramValues.Keys()
            Dim enumerator As IEnumerator = keyCollection.GetEnumerator
            While enumerator.MoveNext
                Dim param As New CrystalDecisions.Shared.ParameterField()
                Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue()
                param.ParameterFieldName = CType(enumerator.Current, String)
                paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
                param.CurrentValues.Add(paramValue)
                rpt.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
            End While

            crv.ReportSource = rpt

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub BindCfpcmReport(ByVal reportName As String, ByVal paramValues As Hashtable)
        Try
            '
            rpt.Load(Server.MapPath("Reports\" & reportName))
            Dim strConnection As String = System.Configuration.ConfigurationManager.AppSettings("CfpcmConnectionString")

            'Dim connection As New SqlClient.SqlConnection("server=MISOPS1;database=DAMS_NewDB;uid=sa;password=**********")
            Dim connection As New SqlClient.SqlConnection(System.Configuration.ConfigurationSettings.AppSettings("ConnectionString"))

            Dim logInfo As New CrystalDecisions.Shared.TableLogOnInfo()
            Dim connInfo As New CrystalDecisions.Shared.ConnectionInfo()
            connection.Close()
            connection.Open()
            logInfo = rpt.Database.Tables(0).LogOnInfo
            connInfo = rpt.Database.Tables(0).LogOnInfo.ConnectionInfo
            connInfo.ServerName = "KHI-CCS-SRV\TRM"
            connInfo.DatabaseName = "DAMS_NewDB"
            'connInfo.UserID = "sa"
            connInfo.UserID = "sa"
            connInfo.Password = "**********"
            logInfo.ConnectionInfo = connInfo
            rpt.Database.Tables(0).ApplyLogOnInfo(logInfo)

            Dim keyCollection As ICollection = paramValues.Keys()
            Dim enumerator As IEnumerator = keyCollection.GetEnumerator
            While enumerator.MoveNext
                Dim param As New CrystalDecisions.Shared.ParameterField()
                Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue()
                param.ParameterFieldName = CType(enumerator.Current, String)
                paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
                param.CurrentValues.Add(paramValue)
                rpt.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
            End While
            crv.ReportSource = rpt
        Catch ex As Exception
            Throw
        End Try
    End Sub
End Class

