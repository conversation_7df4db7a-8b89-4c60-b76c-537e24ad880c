
Partial Class ApplicationSetup_frmProgramChild
    Inherits System.Web.UI.Page
    Dim I As Integer
    Dim dt As New Data.DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then

            ViewState("Search") = Nothing

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            BindCombo()
            FillGrid()
            If Not Request.QueryString.Get("ProgramMasterID") = "" Then
                Dim MasterID As String
                MasterID = Request.QueryString.Get("ProgramMasterID")
                txt_ProgramMasterID.Text = MasterID
                ddl_ProgramMaster.Enabled = False
                ddl_ProgramMaster.SelectedValue = Convert.ToInt32(txt_ProgramMasterID.Text)
            Else
                If chkProgramMaster.Checked = False Then
                    ddl_ProgramMaster.Enabled = True
                End If
            End If

        End If
    End Sub

    Private Sub BindCombo()

        ddl_ProgramMaster.DataSource = New BusinessFacade.ProgramMaster().GetRecords()
        ddl_ProgramMaster.DataTextField = "ProgramMasterName"
        ddl_ProgramMaster.DataValueField = "ProgramMasterID"
        ddl_ProgramMaster.DataBind()
        ddl_ProgramMaster.Items.Insert(0, "--Select--")

        ddlGenre.DataSource = New BusinessFacade.ProgramChild().GetGenre()
        ddlGenre.DataTextField = "GenreName"
        ddlGenre.DataValueField = "GenreID"
        ddlGenre.DataBind()

        ddlGenreFlavor.DataSource = New BusinessFacade.ProgramChild().GetGenreFlavor()
        ddlGenreFlavor.DataTextField = "GenreFlavirName"
        ddlGenreFlavor.DataValueField = "GenreFlavorID"
        ddlGenreFlavor.DataBind()

        End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.ProgramChild().IsExists_ProgramChild(txt_ProgramChildName.Text)

        If txt_ProgramChildID.Text = "" Then
            If IsExists <> 0 Then
                lblErr.Text = "Attention : ProgramChild already Exists !"
            Else
                If chkProgramMaster.Checked = False Then
                    If ddl_ProgramMaster.SelectedIndex = 0 Then
                        lblErr.Text = "Please Select Program Master!!"
                    Else
                        If txt_ProgramChildID.Text = "" Then
                            SaveRecord()
                        Else
                            'UpdateRecord()
                            ''******************************''
                            ''*** BaseStation Validation ***''
                            ''******************************''

                            Dim objValidation As New BusinessFacade.ProgramChild()
                            objValidation.ProgramChildID = txt_ProgramChildID.Text()
                            Dim BaseStationID As Integer = objValidation.ProgramChild_BaseStationValidation()
                            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                            If BaseStationID = CokieBaseStationID Then
                                UpdateRecord()
                            Else
                                lblErr.Text = "You are not allowed to Edit this Record!!"
                            End If
                            ''************ End *************''
                            ''******************************''
                        End If
                    End If
                Else
                    If txt_ProgramChildID.Text = "" Then

                        ''****************************************''
                        ''************ Get User ID ***************''
                        ''****************************************''

                        Dim UserID As Integer
                        Dim objUserID As New BusinessFacade.Employee()
                        objUserID.SM_LoginID = lbl_UserName.Text
                        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                        Dim isSave As Integer
                        Dim OBjSave As New BusinessFacade.ProgramChild()
                        OBjSave.ProgramChildName = txt_ProgramChildName.Text
                        OBjSave.UserID = UserID
                        isSave = OBjSave.Save_ProgramMaster_ProgramChild()

                        If isSave <> 0 Then

                            ''******************************''
                            Dim ObjUpdateGenre As New BusinessFacade.ProgramChild()
                            ObjUpdateGenre.UpdateGenreAndGenreFlavor(isSave, ddlGenre.SelectedValue, ddlGenreFlavor.SelectedValue)
                            ''******************************''

                            FillGrid()
                            lblErr.Text = "Record has been Saved!!"
                            ClearAuditHistory()
                        End If
                    Else
                        ' UpdateRecord()
                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.ProgramChild()
                        objValidation.ProgramChildID = txt_ProgramChildID.Text()
                        Dim BaseStationID As Integer = objValidation.ProgramChild_BaseStationValidation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            UpdateRecord()
                        Else
                            lblErr.Text = "You are not allowed to Edit this Record!!"
                        End If
                        ''************ End *************''
                        ''******************************'''
                    End If
                End If

            End If
            dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            clrscr()

        Else
          
            If chkProgramMaster.Checked = False Then
                If ddl_ProgramMaster.SelectedIndex = 0 Then
                    lblErr.Text = "Please Select Program Master!!"
                Else
                    If txt_ProgramChildID.Text = "" Then
                        SaveRecord()
                    Else
                        'UpdateRecord()
                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.ProgramChild()
                        objValidation.ProgramChildID = txt_ProgramChildID.Text()
                        Dim BaseStationID As Integer = objValidation.ProgramChild_BaseStationValidation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            UpdateRecord()
                        Else
                            lblErr.Text = "You are not allowed to Edit this Record!!"
                        End If
                        ''************ End *************''
                        ''******************************'''
                    End If
                End If
            Else
                If txt_ProgramChildID.Text = "" Then

                    ''****************************************''
                    ''************ Get User ID ***************''
                    ''****************************************''

                    Dim UserID As Integer
                    Dim objUserID As New BusinessFacade.Employee()
                    objUserID.SM_LoginID = lbl_UserName.Text
                    UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                    Dim isSave As Integer
                    Dim OBjSave As New BusinessFacade.ProgramChild()
                    OBjSave.ProgramChildName = txt_ProgramChildName.Text
                    OBjSave.UserID = UserID
                    isSave = OBjSave.Save_ProgramMaster_ProgramChild()
                    If isSave <> 0 Then
                        FillGrid()
                        lblErr.Text = "Record has been Saved!!"
                        ClearAuditHistory()
                    End If
                Else
                    'UpdateRecord()
                    ''******************************''
                    ''*** BaseStation Validation ***''
                    ''******************************''

                    Dim objValidation As New BusinessFacade.ProgramChild()
                    objValidation.ProgramChildID = txt_ProgramChildID.Text()
                    Dim BaseStationID As Integer = objValidation.ProgramChild_BaseStationValidation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID = CokieBaseStationID Then
                        UpdateRecord()
                    Else
                        lblErr.Text = "You are not allowed to Edit this Record!!"
                    End If
                    ''************ End *************''
                    ''******************************'''
                End If
            End If


            dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            clrscr()

        End If
    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        Dim ObjPrgChild As New BusinessFacade.ProgramChild()

        If txt_ProgramChildName.Text = "" Then
            ObjPrgChild.ProgramChildName = " "
        Else
            ObjPrgChild.ProgramChildName = txt_ProgramChildName.Text
        End If

        ObjPrgChild.ProgramMasterID = ddl_ProgramMaster.SelectedValue

        If txt_Duration.Text = "         " Then
            ObjPrgChild.Duration = 0
        Else
            ObjPrgChild.Duration = txt_Duration.Text
        End If

        If ddlGenre.SelectedValue = "-1" Then
            ObjPrgChild.Genre = -1
        Else
            ObjPrgChild.Genre = ddlGenre.SelectedValue
        End If

        'If txt_Genre.Text = "         " Then
        '    ObjPrgChild.Genre = 0
        'Else
        '    ObjPrgChild.Genre = txt_Genre.Text
        'End If

        If ddlGenreFlavor.SelectedValue = "-1" Then
            ObjPrgChild.GenreFalvour = -1
        Else
            ObjPrgChild.GenreFalvour = ddlGenreFlavor.SelectedValue
        End If

        'If txt_GenreFalvour.Text = "         " Then
        '    ObjPrgChild.GenreFalvour = 0
        'Else
        '    ObjPrgChild.GenreFalvour = txt_GenreFalvour.Text
        'End If

        If txt_SubTitle.Text = "" Then
            ObjPrgChild.SubTitle = " "
        Else
            ObjPrgChild.SubTitle = txt_SubTitle.Text
        End If

        If txt_Alias.Text = "" Then
            ObjPrgChild.Alias = " "
        Else
            ObjPrgChild.Alias = txt_Alias.Text
        End If

        If chkIsProgram.Checked = True Then
            ObjPrgChild.IsProgram = 1
        Else
            ObjPrgChild.IsProgram = 0
        End If
        ObjPrgChild.UserID = UserID
        ObjPrgChild.SaveRecord()
        FillGrid()
        lblErr.Text = "Record has been Saved!!"
        ClearAuditHistory()
    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        Dim ObjPrgChild As New BusinessFacade.ProgramChild()
        ObjPrgChild.ProgramChildID = txt_ProgramChildID.Text
        ObjPrgChild.ProgramChildName = txt_ProgramChildName.Text
        ObjPrgChild.ProgramMasterID = ddl_ProgramMaster.SelectedValue

        'ObjPrgChild.Duration = txt_Duration.Text
        If txt_Duration.Text = "         " Then
            ObjPrgChild.Duration = 0
        Else
            ObjPrgChild.Duration = txt_Duration.Text
        End If

        ObjPrgChild.SubTitle = txt_SubTitle.Text
        ObjPrgChild.Alias = txt_Alias.Text

        If ddlGenre.SelectedValue = "-1" Then
            ObjPrgChild.Genre = -1
        Else
            ObjPrgChild.Genre = ddlGenre.SelectedValue
        End If
        'If txt_Genre.Text = "         " Then
        '    ObjPrgChild.Genre = 0
        'Else
        '    ObjPrgChild.Genre = txt_Genre.Text
        'End If

        'ObjPrgChild.GenreFalvour = txt_GenreFalvour.Text
        If ddlGenreFlavor.SelectedValue = "-1" Then
            ObjPrgChild.GenreFalvour = -1
        Else
            ObjPrgChild.GenreFalvour = ddlGenreFlavor.SelectedValue
        End If

        'If txt_GenreFalvour.Text = "         " Then
        '    ObjPrgChild.GenreFalvour = 0
        'Else
        '    ObjPrgChild.GenreFalvour = txt_GenreFalvour.Text
        'End If

        If chkIsProgram.Checked = True Then
            ObjPrgChild.IsProgram = 1
        Else
            ObjPrgChild.IsProgram = 0
        End If

        ObjPrgChild.UserID = UserID
        ObjPrgChild.UpdateRecord()

        ''******************************''
        Dim ObjUpdateGenre As New BusinessFacade.ProgramChild()
        ObjUpdateGenre.UpdateGenreAndGenreFlavor(txt_ProgramChildID.Text, ddlGenre.SelectedValue, ddlGenreFlavor.SelectedValue)
        ''******************************''

        If Not ViewState("Search") Is Nothing Then
            fillgrid_search()
        Else
            FillGrid()
        End If
        lblErr.Text = "Record has been Updated!!"
        ClearAuditHistory()
    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.ProgramChild().GetRecords()
        dg_ProgramChild.DataSource() = dt
        dg_ProgramChild.Columns(0).Visible = True
        dg_ProgramChild.Columns(2).Visible = True
        dg_ProgramChild.DataBind()
        dg_ProgramChild.Columns(0).Visible = False
        dg_ProgramChild.Columns(2).Visible = False
        lblTotalRecords.Text = "Total Records : " & Convert.ToString(dt.Rows.Count)

        BindCombo()
    End Sub


    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_ProgramChild.SelectedIndexChanged
        ddl_ProgramMaster.Enabled = True
        chkProgramMaster.Checked = False
        lblErr.Text = String.Empty
        I = dg_ProgramChild.SelectedIndex.ToString
        txt_ProgramChildID.Text = Convert.ToInt32(dg_ProgramChild.Rows(I).Cells(1).Text)
        txt_ProgramChildName.Text = dg_ProgramChild.Rows(I).Cells(2).Text
        ddl_ProgramMaster.SelectedValue = dg_ProgramChild.Rows(I).Cells(3).Text
        'ddl_ProgramMaster.SelectedItem.Text = dg_ProgramChild.Rows(I).Cells(4).Text.ToString
        txt_Duration.Text = dg_ProgramChild.Rows(I).Cells(5).Text
        txt_SubTitle.Text = dg_ProgramChild.Rows(I).Cells(6).Text
        txt_Alias.Text = dg_ProgramChild.Rows(I).Cells(7).Text
        'txt_Genre.Text = dg_ProgramChild.Rows(I).Cells(8).Text
        'txt_GenreFalvour.Text = dg_ProgramChild.Rows(I).Cells(9).Text
        ddlGenre.SelectedIndex = ddlGenre.Items.IndexOf(ddlGenre.Items.FindByText(dg_ProgramChild.Rows(I).Cells(8).Text))
        ddlGenreFlavor.SelectedIndex = ddlGenreFlavor.Items.IndexOf(ddlGenreFlavor.Items.FindByText(dg_ProgramChild.Rows(I).Cells(9).Text))

        If dg_ProgramChild.Rows(I).Cells(10).Text = True Then
            chkIsProgram.Checked = True
        Else
            chkIsProgram.Checked = False
        End If
        dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.ProgramChild()
        ObjAudit.ProgramChildID = txt_ProgramChildID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_ProgramChild()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_ProgramChildID.Text <> "" Then

                Dim ObjPrgChild As New BusinessFacade.ProgramChild()

                Dim IsDataExit As Integer = ObjPrgChild.ProgramChild_DeleteRecord_CheckIssue(txt_ProgramChildID.Text)
                If IsDataExit = "-2" Then
                    lblErr.Text = "Attention: This Program Child Name is Already in Used !"
                    Exit Sub
                End If


                ObjPrgChild.ProgramChildID = txt_ProgramChildID.Text
                ObjPrgChild.DeleteRecord(ObjPrgChild.ProgramChildID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            Else
                lblErr.Text = "Please Select Record!!"
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Program Child Name is Already in Used !"
            clrscr()
            dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
        
    End Sub

    Private Sub clrscr()
        txt_ProgramChildID.Text = String.Empty
        txt_ProgramChildName.Text = String.Empty
        ddl_ProgramMaster.SelectedIndex = "0"
        txt_Duration.Text = String.Empty
        txt_SubTitle.Text = String.Empty
        txt_Alias.Text = String.Empty
        'txt_Genre.Text = String.Empty
        ddlGenre.SelectedIndex = -1
        ddlGenreFlavor.SelectedIndex = -1
        'txt_GenreFalvour.Text = String.Empty
        txt_Alias.Text = String.Empty

    End Sub

    Protected Sub dg_ProgramChild_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_ProgramChild.PageIndexChanging
        dg_ProgramChild.PageIndex = e.NewPageIndex()
        If txt_SearchProgramChild.Text = "" Then
            FillGrid()
        Else
            fillgrid_search()
        End If
        dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_ProgramChild.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        txt_SearchProgramChild.Text = String.Empty
        ViewState("Search") = Nothing
        FillGrid()
        ClearAuditHistory()
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Response.Redirect("frmProgramMaster.aspx")
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSearch.Click
        ViewState("Search") = Nothing
        fillgrid_search()
    End Sub

    Private Sub fillgrid_search()
        Dim objSearch As New BusinessFacade.ProgramChild()
        objSearch.ProgramChildName = txt_SearchProgramChild.Text
        dt = objSearch.GetSingleProgramChild(objSearch.ProgramChildName)

        If dt.Rows(0).Item(0).ToString <> "0" Then
            dg_ProgramChild.DataSource = dt
            dg_ProgramChild.Columns(0).Visible = True
            dg_ProgramChild.Columns(2).Visible = True
            dg_ProgramChild.DataBind()
            dg_ProgramChild.Columns(0).Visible = False
            dg_ProgramChild.Columns(2).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
        Else
            lblErr.Text = "Search Results: Program Child Name is not valid !!"
        End If
        ViewState("Search") = dt

        BindCombo()

    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

    Protected Sub chkProgramMaster_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkProgramMaster.CheckedChanged
        If chkProgramMaster.Checked = True Then
            ddl_ProgramMaster.Enabled = False
        Else
            ddl_ProgramMaster.Enabled = True
        End If
    End Sub
End Class
