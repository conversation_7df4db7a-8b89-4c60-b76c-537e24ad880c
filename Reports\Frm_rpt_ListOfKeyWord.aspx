<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_ListOfKeyWord.aspx.vb" Inherits="Frm_rpt_ListOfKeyWord" title="Other Reports > Q 3. How Can I View Keyword Wise Report?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Archival Reports > How Can I View Keyword Vs Program/Slug wise report ?" Width="712px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server"><contenttemplate>
<TABLE __designer:dtid="1970324836974606"><TBODY><TR><TD style="HEIGHT: 13px" class="mytext"></TD><TD style="HEIGHT: 13px" class="mytext"></TD><TD style="HEIGHT: 13px" class="mytext"></TD><TD class="mytext"></TD><TD style="WIDTH: 255px; HEIGHT: 13px" class="mytext"></TD></TR><TR><TD class="mytext">Content Type</TD><TD style="WIDTH: 265px; HEIGHT: 21px" class="mytext">Entertainment Keyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chk_EntKW" runat="server" Text="Ignore" __designer:dtid="1970324836974612" Checked="True" __designer:wfdid="w27" AutoPostBack="True" OnCheckedChanged="ChkKeywords_CheckedChanged"></asp:CheckBox></TD><TD style="WIDTH: 255px; HEIGHT: 21px" class="mytext">News Keyword&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:CheckBox id="chk_NewKW" runat="server" Text="Ignore" Checked="True" __designer:wfdid="w28" OnCheckedChanged="chk_NewKW_CheckedChanged"></asp:CheckBox></TD><TD class="mytext">Station</TD><TD style="WIDTH: 255px; HEIGHT: 21px" class="mytext"><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w1"></asp:Label></TD></TR><TR __designer:dtid="1970324836974613"><TD style="HEIGHT: 22px" class="mytext"><asp:DropDownList id="ddlContentType" runat="server" Width="72px" CssClass="mytext" __designer:wfdid="w1"><asp:ListItem>Ent</asp:ListItem>
<asp:ListItem>News</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 100px; HEIGHT: 22px" class="mytext" __designer:dtid="1970324836974614"><asp:TextBox id="txtEntKeywords" runat="server" __designer:dtid="1970324836974615" Width="256px" CssClass="mytext" __designer:wfdid="w29"></asp:TextBox></TD><TD style="WIDTH: 100px; HEIGHT: 22px" class="mytext" __designer:dtid="1970324836974617"><asp:TextBox id="txtNewKeywords" runat="server" __designer:dtid="1970324836974618" Width="240px" CssClass="mytext" __designer:wfdid="w30"></asp:TextBox></TD><TD class="mytext"><asp:DropDownList id="ddlBaseStation" runat="server" __designer:dtid="562949953421399" Width="88px" CssClass="mytext" __designer:wfdid="w2"><asp:ListItem Value="1" __designer:dtid="562949953421401">KARACHI</asp:ListItem>
<asp:ListItem Value="3" __designer:dtid="562949953421402">LAHORE</asp:ListItem>
<asp:ListItem Value="4" __designer:dtid="562949953421403">ISLAMABAD</asp:ListItem>
<asp:ListItem Value="5" __designer:dtid="562949953421404">Peshawar</asp:ListItem>
<asp:ListItem Value="-1" __designer:dtid="562949953421400">IGNORE</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 255px; HEIGHT: 22px" class="mytext"><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w49"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD></TR></TBODY></TABLE><cc1:AutoCompleteExtender id="AutoCompleteExtender_Ent_KW1" runat="server" __designer:wfdid="w31" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="EntKeywords" ServicePath="AutoComplete.asmx" TargetControlID="txtEntKeywords"></cc1:AutoCompleteExtender> <cc1:AutoCompleteExtender id="AutoCompleteExtender_NewsKeywords_1" runat="server" __designer:dtid="3377699720527977" __designer:wfdid="w33" CompletionInterval="1" CompletionSetCount="12" EnableCaching="true" ServiceMethod="GetNewKeywords" ServicePath="AutoComplete.asmx" TargetControlID="txtNewKeywords"></cc1:AutoCompleteExtender>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
                    </asp:UpdatePanel>
                    <br />
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%; height: 29px;">
                                &nbsp;
                                <asp:Button ID="Button1" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" Font-Bold="True" />
                                <asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Text="View Report"
                                    Width="88px" UseSubmitBehavior="False" Visible="False" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="-- Show Parameter Form (How Can I View Keyword Vs Program wise report ?) --"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="-- Hide Parameter Form (How Can I View Keyword Vs Program wise report ?) --"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

