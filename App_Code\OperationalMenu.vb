Imports System.Data

Namespace DAMS.UI

    Public Class OperationalMenu

        'dim K as New Infragistics.WebUI.UltraWebNavigator.
        Private Shared Sub AddChild(ByRef ParentMeunuItem As Infragistics.WebUI.UltraWebNavigator.Item, ByVal dtable As DataTable, ByVal iParentMenuID As Integer)

            Dim ParentColumName As String = dtable.Columns(2).ColumnName()

            Dim dRows() As DataRow = dtable.Select(ParentColumName & "=" & iParentMenuID.ToString & "")
            If dRows.Length > 0 Then
                Dim dRow As DataRow
                For Each dRow In dRows
                    Dim MenuItem As New Infragistics.WebUI.UltraWebNavigator.Item
                    Dim iMenuID As Integer = CInt(dRow(0))
                    MenuItem.Text = dRow(1)

                    Dim childRows() As DataRow = dtable.Select(ParentColumName & "=" & iMenuID.ToString & "")
                    If childRows.Length > 0 Then
                        AddChild(MenuItem, dtable, iMenuID)
                    Else
                        If Not dRow("MenuLink") Is DBNull.Value Then MenuItem.TargetUrl = dRow("MenuLink")
                        'If Not dRow("TargetFrame") Is DBNull.Value Then MenuItem.TargetFrame = dRow("TargetFrame")
                    End If
                    ParentMeunuItem.Items.Add(MenuItem)
                Next
            End If
        End Sub

        Public Shared Function CreateReportMenu(ByRef menu As Infragistics.WebUI.UltraWebNavigator.UltraWebMenu, ByRef dtable As DataTable)
            If ValidTableStructure(dtable) Then
                Dim ParentColumName As String = dtable.Columns(2).ColumnName()
                menu.Items.Clear()
                Try
                    If dtable.Rows.Count > 0 Then
                        'Dim dRows() As DataRow = dtable.Select(ParentColumName & " is Not Null and MenuGroupID=7")
                        Dim dRows() As DataRow = dtable.Select(ParentColumName & " is Not Null")
                        Dim dRow As DataRow
                        For Each dRow In dRows
                            Dim iMenuID As Integer = CInt(dRow(0))
                            Dim ParentMenu As New Infragistics.WebUI.UltraWebNavigator.Item

                            ParentMenu.Style.Width = New System.Web.UI.WebControls.Unit(CStr(dRow(1)).Length * 10)
                            ParentMenu.Text = dRow(1)

                            AddChild(ParentMenu, dtable, iMenuID)
                            menu.Items.Add(ParentMenu)
                        Next
                        Return menu
                    Else
                        Return Nothing
                    End If
                Catch ex As Exception
                    menu.Items.Clear()
                    Return Nothing
                End Try
            Else
                Throw New Exception("Invalid table structure. Hint: Check your table structure, it must be as follows: First Feild : Numeric (For Unique ID). SecondField: For Description. Third Feild: For ParentID (if any). Fourth Feild: For URL (if any)")
            End If
        End Function

        Public Shared Function Create(ByRef menu As Infragistics.WebUI.UltraWebNavigator.UltraWebMenu, ByRef dtable As DataTable)
            If ValidTableStructure(dtable) Then
                Dim ParentColumName As String = dtable.Columns(2).ColumnName()
                menu.Items.Clear()
                Try
                    If dtable.Rows.Count > 0 Then
                        Dim dRows() As DataRow = dtable.Select(ParentColumName & "=0")
                        Dim dRow As DataRow
                        For Each dRow In dRows
                            Dim iMenuID As Integer = CInt(dRow(0))
                            Dim ParentMenu As New Infragistics.WebUI.UltraWebNavigator.Item

                            ParentMenu.Style.Width = New System.Web.UI.WebControls.Unit(CStr(dRow(1)).Length * 10)
                            ParentMenu.Text = dRow(1)

                            AddChild(ParentMenu, dtable, iMenuID)
                            menu.Items.Add(ParentMenu)
                        Next
                        Return menu
                    Else
                        Return Nothing
                    End If
                Catch ex As Exception
                    menu.Items.Clear()
                    Return Nothing
                End Try
            Else
                Throw New Exception("Invalid table structure. Hint: Check your table structure, it must be as follows: First Feild : Numeric (For Unique ID). SecondField: For Description. Third Feild: For ParentID (if any). Fourth Feild: For URL (if any)")
            End If
        End Function



#Region " Code for validating table structure "
        Private Shared Function ValidTableStructure(ByRef dtable As DataTable) As Boolean
            'If dtable.Columns.Count >= 3 Then
            '    If dtable.Columns(0).DataType.GetType(dtable.Columns(0).DataType) = TypeCode.Int16 Or dtable.Columns(0).DataType.GetTypeCode(dtable.Columns(0).DataType) = TypeCode.Int32 Or dtable.Columns(0).DataType.GetTypeCode(dtable.Columns(0).DataType) = TypeCode.Int64 Then
            '        'If dtable.Columns(0).DataType.GetTypeCode(dtable.Columns(0).DataType) = TypeCode.Int16 Or dtable.Columns(0).DataType.GetTypeCode(dtable.Columns(0).DataType) = TypeCode.Int32 Or dtable.Columns(0).DataType.GetTypeCode(dtable.Columns(0).DataType) = TypeCode.Int64 Then
            '        If dtable.Columns(1).DataType.GetTypeCode(dtable.Columns(1).DataType) = TypeCode.String Then
            '            If dtable.Columns(2).DataType.GetTypeCode(dtable.Columns(2).DataType) = TypeCode.Int16 Or dtable.Columns(2).DataType.GetTypeCode(dtable.Columns(2).DataType) = TypeCode.Int32 Or dtable.Columns(2).DataType.GetTypeCode(dtable.Columns(2).DataType) = TypeCode.Int64 Then
            '                If dtable.Columns(3).DataType.GetTypeCode(dtable.Columns(3).DataType) = TypeCode.String Then
            '                    Return True
            '                Else
            '                    Return False
            '                End If

            '            Else
            '                Return False
            '            End If
            '        Else
            '            Return False
            '        End If
            '    Else
            '        Return False
            '    End If
            'Else
            '    Return False
            'End If

            Return True

        End Function

#End Region

    End Class
End Namespace