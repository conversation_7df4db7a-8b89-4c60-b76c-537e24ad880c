Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_LostDamage
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                BindCombo()
                chkTapeNumber.Checked = True
                chkIgnoredate.Checked = True
                chkEmployee.Checked = True
              

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        ddl_Employee.DataTextField = "EmployeeName"
        ddl_Employee.DataValueField = "EmployeeID"
        ddl_Employee.DataSource = New BusinessFacade.LostDamage().LostDamage_GetEmployees()
        ddl_Employee.DataBind()
        ddl_Employee.Items.Insert(0, "--Select--")

        ddl_TapeNumber.DataTextField = "TapeNumber"
        ddl_TapeNumber.DataValueField = "TapeLibraryID"
        ddl_TapeNumber.DataSource = New BusinessFacade.LostDamage().LostDamage_GetTapeNumbers()
        ddl_TapeNumber.DataBind()
        ddl_TapeNumber.Items.Insert(0, "--Select--")

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click

        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_Lost_Damage_CostRecord_DisposeOff.rpt"


            Dim TapeNumber As String
            Dim EmpID As String
            Dim FromDate As String
            Dim ToDate As String

            If chkTapeNumber.Checked = True Then
                TapeNumber = "0"
            Else
                TapeNumber = ddl_TapeNumber.SelectedValue
            End If

            If chkEmployee.Checked = True Then
                EmpID = "0"
            Else
                EmpID = ddl_Employee.SelectedValue
            End If


            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If


            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_Lost_Damage_CostRecord_DisposeOff.rpt&" + "@TapeLibraryID=" & TapeNumber & "&@FromDate=" & FromDate & "&@ToDate=" & ToDate & "&@EmployeeID=" & EmpID

            'Response.Redirect(qryString)

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_Lost_Damage_CostRecord_DisposeOff.rpt&@TapeLibraryID=" + TapeNumber + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@EmployeeID=" + EmpID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_Lost_Damage_CostRecord_DisposeOff.rpt&@TapeLibraryID=" + TapeNumber + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@EmployeeID=" + EmpID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_LostDamage.aspx"
            ObjSave.ReportName = "Lost & Damage --> Q 1. How Can I View Lost/Damage Tape Employee Wise ?"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            ddl_Employee.Enabled = False
        End If
        If chkEmployee.Checked = False Then
            ddl_Employee.Enabled = True
        End If
    End Sub
End Class
