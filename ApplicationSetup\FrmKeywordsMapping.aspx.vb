Imports System
Imports System.Data
Imports System.Data.SqlClient

Partial Class ApplicationSetup_FrmKeywordsMapping
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")
            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)
        End If


        'If (lbl_UserName.Text.ToLower <> "turab.ali") Or (lbl_UserName.Text.ToLower <> "sobia.aziz") Then
        '    bttnMappKeyword.Enabled = True
        '    bttnMappKeyword_News.Enabled = True
        'Else
        '    lblErr.Text = "You are not Authorize for Entertainment Keyword Mapping !"
        '    lblErr_2.Text = "You are not Authorize for News Keyword Mapping !"
        'End If

        If Not Page.IsPostBack = True Then

            bttnMappKeyword.Enabled = False
            lblErr.Text = "You are not Authorize for Entertainment Keyword Mapping !"
            lblErr_2.Text = "You are not Authorize for News Keyword Mapping !"

            'If (lbl_UserName.Text.ToLower = "turab.ali") Or (lbl_UserName.Text.ToLower = "sobia.aziz") Or (lbl_UserName.Text.ToLower = "muhammad.shariq") Then
            '    bttnMappKeyword.Enabled = True
            '    lblErr.Text = ""
            '    lblErr_2.Text = ""
            '    bttnMappKeyword_News.Enabled = True
            'End If

            ''***************************''
            Dim objUsers As New BusinessFacade.Employee
            Dim IsValid As Integer = objUsers.GetMappingUsers("Keyword", lbl_UserName.Text.ToLower)
            If IsValid = 1 Then
                bttnMappKeyword.Enabled = True
                lblErr.Text = ""
                lblErr_2.Text = ""
                bttnMappKeyword_News.Enabled = True
            Else
                bttnMappKeyword.Enabled = False
                bttnMappKeyword_News.Enabled = False
                lblErr.Text = "You are not Authorize for Entertainment Keyword Mapping !"
                lblErr_2.Text = "You are not Authorize for News Keyword Mapping !"

            End If

            ''***************************''


        End If

    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        txtNewKeyword.Text = String.Empty
        txtOldkeyword.Text = String.Empty
        lblErr.Text = String.Empty
    End Sub

    Protected Sub Page_Unload(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Unload
        Dim script As String
        script = "<script language='javascript' type='text/javascript'>"
        script = script + "window.onbeforeunload = function() {"
        script = script + "return ""Closing the page now may result in data loss."";"
        script = script + "}</script>"
        Page.RegisterClientScriptBlock("test", script)
    End Sub

    Protected Sub bttnMappKeyword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappKeyword.Click
        Try
            '*************************************************'
            '**************** Get Old KeywordID **************'
            '*************************************************'

            Dim OldKeywordID As Integer
            Dim ObjKWID As New BusinessFacade.EntertainmentKeyword()
            ObjKWID.EntertainmentKeyword = txtOldkeyword.Text
            OldKeywordID = ObjKWID.GetKeywordID_AutoComplete(ObjKWID.EntertainmentKeyword)


            '*************************************************'
            '**************** Get New KeywordID **************'
            '*************************************************'
            Dim NewKeywordID As Integer
            Dim ObjNewKWID As New BusinessFacade.EntertainmentKeyword()
            ObjNewKWID.EntertainmentKeyword = txtNewKeyword.Text
            NewKeywordID = ObjNewKWID.GetKeywordID_AutoComplete(ObjNewKWID.EntertainmentKeyword)

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If (OldKeywordID <> "0") And (NewKeywordID <> "0") And (OldKeywordID <> NewKeywordID) Then

                ''****************************************************''
                ''*********** Insert in Employee Mapping *************''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "KeywordsMapping " & OldKeywordID & ",'" & txtOldkeyword.Text & "'," & NewKeywordID & ",'" & txtNewKeyword.Text & "'," & UserID & ",'Ent'"
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr.Text = "Entertainment Keyword has been Mapped Successfully."
                txtNewKeyword.Text = String.Empty
                txtOldkeyword.Text = String.Empty

                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmKeywordsMapping.aspx" + ";"""
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)

            ElseIf OldKeywordID = NewKeywordID Then
                lblErr.Text = "Old Keyword and NewKeyword must be different from each Other!"
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnMappKeyword_News_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnMappKeyword_News.Click
        Try
            '*************************************************'
            '**************** Get Old KeywordID **************'
            '*************************************************'

            Dim OldKeywordID As Integer
            Dim ObjKWID As New BusinessFacade.NewsKeyword()
            ObjKWID.NewsKeyword = txtOldkeyword_News.Text
            OldKeywordID = ObjKWID.GetKeywordID_News_AutoComplete(ObjKWID.NewsKeyword)


            '*************************************************'
            '**************** Get New KeywordID **************'
            '*************************************************'
            Dim NewKeywordID As Integer
            Dim ObjNewKWID As New BusinessFacade.NewsKeyword()
            ObjNewKWID.NewsKeyword = txtNewKeyword_News.Text
            NewKeywordID = ObjNewKWID.GetKeywordID_News_AutoComplete(ObjNewKWID.NewsKeyword)

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If (OldKeywordID <> "0") And (NewKeywordID <> "0") And (OldKeywordID <> NewKeywordID) Then

                ''****************************************************''
                ''*********** Insert in Employee Mapping *************''
                ''****************************************************''

                Dim DS2 As System.Data.DataSet
                Dim Con2 As System.Data.SqlClient.SqlConnection
                Dim connStr As String
                connStr = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
                Con2 = New System.Data.SqlClient.SqlConnection(connStr)
                DS2 = New System.Data.DataSet
                Dim strCommand As String = "KeywordsMapping " & OldKeywordID & ",'" & txtOldkeyword_News.Text & "'," & NewKeywordID & ",'" & txtNewKeyword_News.Text & "'," & UserID & ",'News'"
                Dim cmd4 = New SqlClient.SqlCommand(strCommand)
                If Con2.State = ConnectionState.Closed Then
                    Con2.Open()
                End If
                cmd4.Connection = Con2
                cmd4.CommandTimeout = 0
                cmd4.ExecuteNonQuery().ToString()
                Con2.Close()

                lblErr_2.Text = "News Keyword has been Mapped Successfully."
                txtNewKeyword_News.Text = String.Empty
                txtOldkeyword_News.Text = String.Empty

                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "function RefreshForm() {window.opener.location.href=" + """ + FrmKeywordsMapping.aspx" + ";"""
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)

            ElseIf OldKeywordID = NewKeywordID Then
                lblErr.Text = "Old Keyword and NewKeyword must be different from each Other!"
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnClear_News_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear_News.Click
        txtNewKeyword_News.Text = String.Empty
        txtOldkeyword_News.Text = String.Empty
        lblErr_2.Text = String.Empty
    End Sub
End Class
