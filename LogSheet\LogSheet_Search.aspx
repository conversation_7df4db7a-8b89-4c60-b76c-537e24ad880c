<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="LogSheet_Search.aspx.vb" Inherits="LogSheet_LogSheet_Search" title="Home > Material Deposit Sheet" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ Register Assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7dd5c3163f2cd0cb"
    Namespace="Infragistics.WebUI.UltraWebGrid" TagPrefix="igtbl" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%; text-decoration: underline;" class="labelheading">
                <asp:LinkButton ID="lnkHomePage" runat="server" CssClass="labelheading">Home</asp:LinkButton>
                &gt; Material Deposit Sheet</td>
        </tr>
        <tr class="mytext">
            <td>
                <table style="width: 760px">
                    <tr class="mytext">
                        <td style="width: 161px; height: 22px;">
                            From Date &nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;<asp:CheckBox ID="Chk_Ignore" runat="server"
                                AutoPostBack="True" Text="Ignore" /></td>
                        <td style="width: 152px; height: 22px">
                            To Date</td>
                        <td style="width: 169px; height: 22px">
                            Karachi Unbranded&nbsp;
                            <asp:CheckBox ID="ChkTapeKhi" runat="server" Text="Ignore" /></td>
                        <td style="width: 167px; height: 22px">
                            Dubai Unbranded &nbsp;&nbsp;
                            <asp:CheckBox ID="ChkTapeDbx" runat="server" Text="Ignore" /></td>
                        <td>
                            Description &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;
                        </td>
                    </tr>
                    <tr class="mytext">
                        <td style="width: 161px; height: 21px" valign="top">
                            <asp:TextBox ID="txt_FromDate" runat="server"></asp:TextBox>&nbsp;
                        </td>
                        <td style="width: 152px; height: 21px" valign="top">
                            <asp:TextBox ID="txt_ToDate" runat="server"></asp:TextBox>
                            &nbsp;&nbsp;
                        </td>
                        <td style="width: 169px; height: 21px" valign="top">
                            <asp:TextBox ID="Txt_TapeKhi" runat="server"></asp:TextBox></td>
                        <td style="width: 167px; height: 21px" valign="top">
                            <asp:TextBox ID="txt_TapeDbx" runat="server"></asp:TextBox></td>
                        <td style="height: 21px; width: 200px;" valign="top">
                            <asp:TextBox ID="txt_Description" runat="server" Width="336px" TextMode="MultiLine"></asp:TextBox><br />
                            * Enter Description with ~ separation</td>
                    </tr>
                </table>
                <asp:Label ID="lblErr" runat="server" Font-Bold="True" ForeColor="Red" Width="512px"></asp:Label></td>
        </tr>
        <tr>
            <td class="bottomMain" style="width: 100%; height: 29px">
                &nbsp;<asp:Button ID="bttnSearch" runat="server" CssClass="buttonA" Text="Search" />
                <asp:Button ID="bttnAdd" runat="server" CssClass="buttonA" Text="Add" Width="64px" />&nbsp;
                <asp:Button ID="bttnDelete" runat="server" CssClass="buttonA" Text="Delete" Width="64px" Visible="False" />
                <asp:Button ID="bttnEdit" runat="server" CssClass="buttonA" Text="Edit" Width="64px" Visible="False" /></td>
        </tr>
        <tr>
            <td style="width: 100px">
                <igtbl:UltraWebGrid ID="UltraWebGrid1" runat="server">
                    <Bands>
                        <igtbl:UltraGridBand SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="Single">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="LogID" HeaderText="LogID" Hidden="True">
                                    <Header Caption="LogID">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogDate" Format="dd-MMM-yyyy" HeaderText="Log Date"
                                    Width="150px">
                                    <Header Caption="Log Date">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogOriginDept" HeaderText="Log Origin Dept"
                                    Width="130px">
                                    <HeaderStyle Width="160px" />
                                    <Header Caption="Log Origin Dept">
                                        <Style Width="160px"></Style>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogDestinationDept" HeaderText="Log Destination Dept"
                                    Width="130px">
                                    <Header Caption="Log Destination Dept">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogDestContactPerson" HeaderText="Log Dest.Contact Person"
                                    Width="130px">
                                    <Header Caption="Log Dest.Contact Person">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogOrginContactPerson" HeaderText="Log Orgin Contact Person"
                                    Width="130px">
                                    <Header Caption="Log Orgin Contact Person">
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="5" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="LogCreatedOn" Format="dd-MMM-yyyy" HeaderText="Log CreatedOn"
                                    Width="150px">
                                    <Header Caption="Log CreatedOn">
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="6" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                        <igtbl:UltraGridBand RowSelectors="No" SelectTypeCell="None" SelectTypeCol="None" SelectTypeRow="None">
                            <AddNewRow View="NotSet" Visible="NotSet">
                            </AddNewRow>
                            <FilterOptions AllString="" EmptyString="" NonEmptyString="">
                                <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                    CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                    Font-Size="11px" Width="200px">
                                    <Padding Left="2px" />
                                </FilterDropDownStyle>
                                <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                                </FilterHighlightRowStyle>
                            </FilterOptions>
                            <Columns>
                                <igtbl:UltraGridColumn BaseColumnName="TapeKhi" HeaderText="Tape Khi" Width="130px">
                                    <Header Caption="Tape Khi">
                                    </Header>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="TapeDbx" HeaderText="Tape Dbx" Width="130px">
                                    <Header Caption="Tape Dbx">
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="1" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="DBXBranded" HeaderText="DBX Branded" Width="130px">
                                    <Header Caption="DBX Branded">
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="2" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="Description" HeaderText="Description" Width="200px">
                                    <Header Caption="Description">
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="3" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                                <igtbl:UltraGridColumn BaseColumnName="Duration" HeaderText="Duration" Width="50px">
                                    <Header Caption="Duration">
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Header>
                                    <Footer>
                                        <RowLayoutColumnInfo OriginX="4" />
                                    </Footer>
                                </igtbl:UltraGridColumn>
                            </Columns>
                        </igtbl:UltraGridBand>
                    </Bands>
                    <DisplayLayout AllowColSizingDefault="Free" AllowColumnMovingDefault="OnServer" AllowDeleteDefault="Yes"
                        AllowSortingDefault="OnClient" AllowUpdateDefault="Yes" AutoGenerateColumns="False"
                        BorderCollapseDefault="Separate" HeaderClickActionDefault="SortMulti" Name="UltraWebGrid1"
                        RowHeightDefault="20px" SelectTypeRowDefault="Single" Version="4.00"
                        ViewType="OutlookGroupBy">
                        <GroupByBox Hidden="True">
                            <Style BackColor="ActiveBorder" BorderColor="Window"></Style>
                        </GroupByBox>
                        <GroupByRowStyleDefault BackColor="Control" BorderColor="Window">
                        </GroupByRowStyleDefault>
                        <FooterStyleDefault BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </FooterStyleDefault>
                        <RowStyleDefault BackColor="Window" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px">
                            <BorderDetails ColorLeft="Window" ColorTop="Window" />
                            <Padding Left="3px" />
                        </RowStyleDefault>
                        <FilterOptionsDefault AllString="(All)" EmptyString="(Empty)" NonEmptyString="(NonEmpty)">
                            <FilterDropDownStyle BackColor="White" BorderColor="Silver" BorderStyle="Solid" BorderWidth="1px"
                                CustomRules="overflow:auto;" Font-Names="Verdana,Arial,Helvetica,sans-serif"
                                Font-Size="11px" Width="200px">
                                <Padding Left="2px" />
                            </FilterDropDownStyle>
                            <FilterHighlightRowStyle BackColor="#151C55" ForeColor="White">
                            </FilterHighlightRowStyle>
                        </FilterOptionsDefault>
                        <HeaderStyleDefault BackColor="#5774C2" BorderStyle="Solid" HorizontalAlign="Center" ForeColor="White" Font-Bold="False" Font-Names="Arial">
                            <BorderDetails ColorLeft="White" ColorTop="White" WidthLeft="1px" WidthTop="1px" />
                        </HeaderStyleDefault>
                        <EditCellStyleDefault BorderStyle="None" BorderWidth="0px">
                        </EditCellStyleDefault>
                        <FrameStyle BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid"
                            BorderWidth="1px" Font-Names="Microsoft Sans Serif" Font-Size="8.25pt">
                        </FrameStyle>
                        <Pager>
                            <Style BackColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </Pager>
                        <AddNewBox Hidden="False">
                            <Style BackColor="Window" BorderColor="InactiveCaption" BorderStyle="Solid" BorderWidth="1px">
<BorderDetails ColorTop="White" WidthLeft="1px" WidthTop="1px" ColorLeft="White"></BorderDetails>
</Style>
                        </AddNewBox>
                        <SelectedRowStyleDefault BackColor="#F09D21" ForeColor="White">
                        </SelectedRowStyleDefault>
                    </DisplayLayout>
                </igtbl:UltraWebGrid><br />
                <asp:GridView ID="dgTest" runat="server">
                </asp:GridView>
            </td>
        </tr>
        <tr>
            <td style="width: 100px; height: 24px;">
                <asp:TextBox ID="txt_MasterID" runat="server" Visible="False" Width="64px"></asp:TextBox></td>
        </tr>
    </table>
    <asp:ScriptManager id="ScriptManager_MDS" runat="server">
    </asp:ScriptManager>
    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" PopupPosition="TopLeft"
        TargetControlID="txt_ToDate" Format="dd-MMM-yyyy" CssClass="MyCalendar">
    </cc1:CalendarExtender>
    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" PopupPosition="TopLeft"
        TargetControlID="txt_FromDate" Format="dd-MMM-yyyy" CssClass="MyCalendar">
    </cc1:CalendarExtender>
    &nbsp; &nbsp;
    <br />
</asp:Content>
   

