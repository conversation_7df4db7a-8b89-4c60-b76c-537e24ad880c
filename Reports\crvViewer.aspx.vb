Imports System.Data
Imports System.Data.SqlClient

Partial Class Reports_crvViewer
    Inherits System.Web.UI.Page
    'Protected WithEvents crv As CrystalDecisions.Web.CrystalReportViewer
    Dim rpt As New CrystalDecisions.CrystalReports.Engine.ReportDocument
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim reportName As String = Request("ReportName")

        Dim qryParam As Integer = Request.QueryString.Count
        Dim keys() As String = Request.QueryString.AllKeys
        'Dim paramValues As New ArrayList()
        Dim paramValues As New Hashtable()
        Dim i As Integer = 0
        For i = 1 To keys.Length - 1
            paramValues.Add(keys(i), Request.QueryString.Get(keys(i)))
        Next
        showReport(reportName, paramValues)
        If Not Page.IsPostBack Then
        End If
    End Sub
    Private Sub showReport(ByVal reportName As String, ByVal paramValues As Hashtable)
        Try

            rpt.Load(Server.MapPath(reportName))
            Dim strConnection As String = "Data Source=SOFT-SERVER\SOFT;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********"
            Dim connection As New SqlClient.SqlConnection(strConnection)
            Dim logInfo As New CrystalDecisions.Shared.TableLogOnInfo()
            Dim connInfo As New CrystalDecisions.Shared.ConnectionInfo()
            connection.Open()
            logInfo = rpt.Database.Tables(0).LogOnInfo
            connInfo = rpt.Database.Tables(0).LogOnInfo.ConnectionInfo
            connInfo.ServerName = connection.DataSource

            If reportName = "rptProgramAccepted.rpt" Or reportName = "ProgramRejected.rpt" Or reportName = "RptProgramEvaluation.rpt" Then
                connInfo.DatabaseName = "Admin_Billing"
            Else
                connInfo.DatabaseName = connection.Database
            End If
            connInfo.UserID = "sa"
            connInfo.Password = "**********"
            logInfo.ConnectionInfo = connInfo
            rpt.Database.Tables(0).ApplyLogOnInfo(logInfo)

            Dim keyCollection As ICollection = paramValues.Keys()
            Dim enumerator As IEnumerator = keyCollection.GetEnumerator
            While enumerator.MoveNext
                Dim param As New CrystalDecisions.Shared.ParameterField()
                Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue()
                param.ParameterFieldName = CType(enumerator.Current, String)
                paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
                param.CurrentValues.Add(paramValue)
                rpt.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
            End While
            CrystalReportViewer1.ReportSource = rpt

        Catch ex As Exception
            Throw
        End Try


    End Sub

End Class
