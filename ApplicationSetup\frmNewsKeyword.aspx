<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmNewsKeyword.aspx.vb" Inherits="ApplicationSetup_frmNewsKeyword" title="Home > News Keyword > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="WIDTH: 709px; HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w9">Home</asp:LinkButton> &gt; News Keyword &gt; Add New</TD></TR><TR><TD style="WIDTH: 709px"><TABLE><TBODY><TR class="mytext"><TD style="HEIGHT: 17px" vAlign=top>News Keyword</TD><TD style="WIDTH: 138px; HEIGHT: 17px" vAlign=top><asp:TextBox id="txt_NewsKeyword" runat="server" CssClass="mytext"></asp:TextBox></TD><TD style="WIDTH: 89px; HEIGHT: 17px" vAlign=top>Sub Content Type</TD><TD style="HEIGHT: 17px" vAlign=top><asp:DropDownList id="ddl_SubContentType" runat="server" CssClass="mytext" __designer:wfdid="w1" Width="152px">
                            </asp:DropDownList></TD><TD style="WIDTH: 49px; HEIGHT: 17px" vAlign=top>&nbsp;Key type </TD><TD style="WIDTH: 49px; HEIGHT: 17px" vAlign=top><asp:ListBox id="lstNews" runat="server" CssClass="mytext" __designer:wfdid="w59" Width="120px" Height="40px"></asp:ListBox></TD><TD style="HEIGHT: 17px"><asp:TextBox id="txt_TKeytype" runat="server" CssClass="mytext" __designer:wfdid="w2" Width="24px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Width="472px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 709px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w11" Font-Bold="True"></asp:Label></TD></TR><TR><TD><asp:GridView id="dg_NewsKeyword" runat="server" CssClass="gridContent" Width="616px" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False" AllowPaging="True" OnPageIndexChanging="dg_NewsKeyword_PageIndexChanging"><Columns>
<asp:BoundField DataField="NewsKeywordID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="NewsKeyword" HeaderText="News Keyword">
<ItemStyle Width="150px"></ItemStyle>

<HeaderStyle Width="120px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="SubContentTypeID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="SubContentTypeName" HeaderText="Sub-ContentType">
<HeaderStyle Width="150px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="TKeytype" HeaderText="Key Type">
<ItemStyle Width="250px"></ItemStyle>
</asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> </TD></TR><TR><TD style="WIDTH: 709px; HEIGHT: 22px"><asp:Label id="lblAuditHistory" runat="server" CssClass="labelheading" __designer:wfdid="w3" Visible="False">Audit History - News Keyword</asp:Label></TD></TR><TR><TD style="WIDTH: 709px; HEIGHT: 22px"><asp:GridView id="dgAuditHistory" runat="server" CssClass="gridContent" __designer:wfdid="w1" Width="100%" PageSize="25" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="AddedBy" HeaderText="Added By"></asp:BoundField>
<asp:BoundField DataField="AddedDate" HeaderText="Added Date"></asp:BoundField>
<asp:BoundField DataField="ModifiedBy" HeaderText="ModifiedBy"></asp:BoundField>
<asp:BoundField DataField="ModifiedDate" HeaderText="ModifiedDate"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView></TD></TR><TR><TD style="WIDTH: 709px; HEIGHT: 22px"><TABLE class="mytext" __designer:dtid="5066549580791812"><TBODY><TR __designer:dtid="5066549580791813"><TD style="WIDTH: 140px; HEIGHT: 26px" class="labelheading" __designer:dtid="5066549580791814">Search Keyword</TD><TD style="WIDTH: 100px; HEIGHT: 26px" __designer:dtid="5066549580791815"></TD></TR><TR __designer:dtid="5066549580791816"><TD style="WIDTH: 200px; HEIGHT: 13px" __designer:dtid="5066549580791817">News&nbsp;Keyword</TD><TD style="WIDTH: 100px; HEIGHT: 13px" __designer:dtid="5066549580791818"></TD></TR><TR __designer:dtid="5066549580791819"><TD style="WIDTH: 100px" __designer:dtid="5066549580791820"><asp:TextBox id="txt_SearchKW" runat="server" __designer:dtid="5066549580791821" CssClass="mytext" __designer:wfdid="w8" Width="192px"></asp:TextBox></TD><TD style="WIDTH: 100px" __designer:dtid="5066549580791822"><asp:LinkButton id="lnkSearch" onclick="lnkSearch_Click" runat="server" __designer:dtid="5066549580791823" __designer:wfdid="w9">Search</asp:LinkButton></TD></TR></TBODY></TABLE></TD></TR><TR><TD style="WIDTH: 709px; HEIGHT: 22px"><asp:TextBox id="txt_NewsKeywordID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox> <asp:Label id="lbl_UserName" runat="server" __designer:wfdid="w3" Visible="False"></asp:Label></TD></TR></TBODY></TABLE>&nbsp;<cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w20" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

