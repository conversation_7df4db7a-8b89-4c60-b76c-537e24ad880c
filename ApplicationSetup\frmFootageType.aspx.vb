
Partial Class ApplicationSetup_frmFootageType

    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            FillGrid()
        End If
    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.FootageType().IsExists_FootageType(txt_FootageTypeName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : FootageType already Exists !"
        Else
            If txt_FootageTypeID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_FootageType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        If txt_FootageTypeName.Text = "" Then
            lblErr.Text = "Please insert Recycle Turn!!"
        Else
            Dim ObjFootageType As New BusinessFacade.FootageType()
            ObjFootageType.FootageTypeName = txt_FootageTypeName.Text
            ObjFootageType.UserID = UserID
            ObjFootageType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
            ClearAuditHistory()
        End If
    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        Dim ObjFootageType As New BusinessFacade.FootageType()
        ObjFootageType.FootageTypeID = txt_FootageTypeID.Text
        ObjFootageType.FootageTypeName = txt_FootageTypeName.Text
        ObjFootageType.UserID = UserID
        ObjFootageType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
        ClearAuditHistory()
    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.FootageType().GetRecords()
        dg_FootageType.DataSource() = dt
        dg_FootageType.Columns(0).Visible = True
        dg_FootageType.DataBind()
        dg_FootageType.Columns(0).Visible = False
        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_FootageTypeID.Text = "" Then
                lblErr.Text = "Please select Footage Type First!!"
            Else
                Dim objFootageType As New BusinessFacade.FootageType()
                objFootageType.FootageTypeID = txt_FootageTypeID.Text
                objFootageType.DeleteRecord(objFootageType.FootageTypeID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_FootageType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This FootageType is already in Used !"
            clrscr()
            dg_FootageType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_FootageTypeName.Text = String.Empty
        txt_FootageTypeID.Text = String.Empty
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_FootageType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
        ClearAuditHistory()
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub dg_FootageType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs)
        dg_FootageType.PageIndex = e.NewPageIndex()
        FillGrid()

    End Sub

    Protected Sub dg_FootageType_SelectedIndexChanged1(ByVal sender As Object, ByVal e As System.EventArgs)
        lblErr.Text = String.Empty
        I = dg_FootageType.SelectedIndex.ToString
        txt_FootageTypeID.Text = Convert.ToInt32(dg_FootageType.Rows(I).Cells(1).Text)
        txt_FootageTypeName.Text = dg_FootageType.Rows(I).Cells(2).Text
        dg_FootageType.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.FootageType()
        ObjAudit.FootageTypeID = txt_FootageTypeID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_FootageType()
        dgAuditHistory.DataBind()

    End Sub


    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
