Imports System
Imports System.Data

Partial Class TapeManagement_ArchiveTapeIssueEntry
    Inherits System.Web.UI.Page
    Dim strCommand As String
    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

    Dim dtEntIssue As DataTable
    Dim dtEntIssueNew As DataTable
    Dim dtNewsIssue As DataTable
    Dim dtNewsIssueNew As DataTable

    Dim dtReturn As DataTable
    Dim dtReturnNew As DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")

                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)

                End If

                Chk_Program.Checked = True
                Chk_TapeNo.Checked = True
                txtEntryDate_Issue.Text = Date.Now().ToString("dd-MMM-yyyy")
                txtEntryDate_Return.Text = Date.Now().ToString("dd-MMM-yyyy")

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttnIssue_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnIssue.Click

        bttnIssue.Enabled = False
      
        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_IssueToEmp.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        '********************* End ********************' 

        If EmployeeID = 0 Then
            Err_Employee_Issue.Visible = True
        Else
            Err_Employee_Issue.Visible = False
        End If

        If EmployeeID <> "0" And txtEntryDate_Issue.Text <> "" Then

            ''**********************************************''
            ''************ Get Department ID ***************''
            ''**********************************************''

            Dim DepartmentID As Integer
            Dim objDepartmentID As New BusinessFacade.TapeIssuance()
            objDepartmentID.EmployeeID = EmployeeID
            DepartmentID = objDepartmentID.GetDepartmentID_by_EmployeeID(objDepartmentID.EmployeeID)

            '********************* End ********************' 

            Dim P As Integer
            Dim Count As Integer
            For P = 0 To dg_TapeIssue.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(P).Cells(1).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then
                    Count = Count + 1
                End If
            Next

            Dim q As Integer
            Dim Cnt As Integer
            For q = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
                Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(q).Cells(1).Controls(1), CheckBox)
                If MyCheckBox.Checked = True Then
                    Cnt = Cnt + 1
                End If
            Next

            Dim IsTapeChecked As Integer = 0

            If Count <> 0 Then

                Dim T As Integer
                For T = 0 To dg_TapeIssue.Rows.Count - 1
                    Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(T).Cells(1).Controls(1), CheckBox)
                    If MyCheckBox.Checked = True Then


                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dg_TapeIssue.Rows(T).Cells(3).Text
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then

                            imgWait.Visible = True

                            ''*******************************************************'
                            ''***************** Save Tape Issuance ******************'
                            ''*******************************************************'
                            Dim LibID As Integer = dg_TapeIssue.Rows(T).Cells(3).Text
                            Dim ProgID As Integer = 0

                            strCommand = "TapeIssuance_OneByOne_SaveRecord_ArchivalEntry_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_Issue.Text & "'," & LibID & "," & ProgID
                            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                            If Con.State = Data.ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd1By1_Save.Connection = Con
                            cmd1By1_Save.executenonquery()
                            Con.Close()
                            lblErr_Issue.Text = "Tapes have been Issued!!"
                            IsTapeChecked = 1
                            ''*********************** End **************************'

                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
                        End If
                        ''************ End *************''
                        ''******************************''

                    End If
                Next

                Try
                    If IsTapeChecked = 1 Then
                        Dim ObjReminder As New BusinessFacade.ReminderService()
                        ObjReminder.EmployeeName = txt_IssueToEmp.Text
                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                        If EmailAddress <> "N/A" Then
                            Dim Arr As Array = Split(EmailAddress, "@")
                            If Arr.Length = 2 Then
                                sendissuancemail(EmailAddress)
                            Else
                                lblErr_Issue.Text += "But Email Not send due to Invalid Email Address!"
                            End If
                        Else
                            lblErr_Issue.Text += "But Email Not send b/c Email Address not Exists!"

                        End If
                    End If
                Catch ex As Exception

                End Try

                ViewState("dtEntIssueNew") = Nothing
                'dtEntIssueNew = Nothing

                dg_TapeIssue.DataSource = Nothing
                dg_TapeIssue.DataBind()

                lblEntTapes.Visible = False

            End If

            If Cnt <> 0 Then

                Dim f As Integer
                For f = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
                    Dim MyChkBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(f).Cells(1).Controls(1), CheckBox)
                    If MyChkBox.Checked = True Then

                        ''******************************''
                        ''*** BaseStation Validation ***''
                        ''******************************''

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dg_TapeIssuance_SlugWise.Rows(f).Cells(3).Text
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then

                            imgWait.Visible = True

                            Dim ProgID As Integer = 0
                            Dim LibID As Integer = dg_TapeIssuance_SlugWise.Rows(f).Cells(3).Text

                            strCommand = "TapeIssuance_OneByOne_SaveRecord_ArchivalEntry_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_Issue.Text & "'," & LibID & "," & ProgID
                            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                            If Con.State = Data.ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd1By1_Save.Connection = Con
                            cmd1By1_Save.executenonquery()
                            Con.Close()
                            lblErr_Issue.Text = "Tapes have been Issued!!"
                            IsTapeChecked = 1
                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!!"
                        End If
                        ''************ End *************''
                        ''******************************''

                       

                    End If
                Next

                Try
                    If IsTapeChecked = 1 Then

                        Dim ObjReminder As New BusinessFacade.ReminderService()
                        ObjReminder.EmployeeName = txt_IssueToEmp.Text
                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                        If EmailAddress <> "N/A" Then
                            Dim Arr As Array = Split(EmailAddress, "@")
                            If Arr.Length = 2 Then
                                sendissuancemail_Slug(EmailAddress)
                            Else
                                lblErr_Issue.Text += "But Email Not send due to Invalid Email Address!"
                            End If
                        Else
                            lblErr_Issue.Text += "But Email Not send b/c Email Address not Exists!"

                        End If

                    End If
                Catch ex As Exception

                End Try


                ViewState("dtNewsIssueNew") = Nothing
                lblNewsTapes.Visible = False

                Clrscr()

                dg_TapeIssuance_SlugWise.DataSource = Nothing
                dg_TapeIssuance_SlugWise.DataBind()

            End If
        ElseIf txtEntryDate_Issue.Text = "" Then
            lblErr_Issue.Text = "Please Select Date !!"
        Else
            lblErr_Issue.Text = "Please Select Employee !!"
        End If

        ''********* Clear Controls ***********''

        txt_ProgramName.Text = String.Empty
        txt_TapeNumber_Ent.Text = String.Empty
        txt_SlugName.Text = String.Empty
        txt_TapeNumber_News.Text = String.Empty

        bttnIssue.Enabled = True
        imgWait.Visible = False

    End Sub

    Private Sub Clrscr()
        Dim N As Integer
        For N = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(N).Cells(0).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next
        txt_TapeNo.Text = String.Empty
       
    End Sub

    Protected Sub bttnClearSel_Click(ByVal sender As Object, ByVal e As System.EventArgs)

        Dim L As Integer
        For L = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(L).Cells(0).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

        txt_TapeNo.Text = String.Empty

    End Sub

    Protected Sub ddl_Employee_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_Employee.SelectedIndexChanged
        BindGrid()
    End Sub

    Private Sub BindGrid()

        lblErr_Return.Text = String.Empty

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''*******************************************''
        ''*********** Get TapeLibraryID *************''
        ''*******************************************''

        Dim Arr As Array
        If txtTapeNumber.Text <> "" Then
            Arr = Split(txtTapeNumber.Text, "#")
            If Arr.Length = 2 Then
                txtTapeNumber.Text = Arr(1)
            End If
        End If

        Dim LibraryID As Integer = 0
        Dim dtibraryID As Data.DataTable = Nothing
        If txtTapeNumber.Text <> "" Then
            Dim objLibID As New BusinessFacade.TapeIssuance()
            objLibID.TapeNumber = txtTapeNumber.Text
            '  LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)
            dtibraryID = objLibID.TapeIssuance_GetLibraryID_datatable(objLibID.TapeNumber)
        End If

        If EmployeeID <> "0" Then

            ''********************************************************************************************************************''

            Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
            Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
            Dim DS As DataSet
            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "Archival_TapeReturn_GetRecord"
            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
            currentPageIndex.Value = EmployeeID
            cmd.Connection = Con
            cmd.CommandTimeout = 0
            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS)

            dg_OneByOne.DataSource = DS.Tables(0).DefaultView
            dg_OneByOne.Columns(1).Visible = True
            dg_OneByOne.Columns(2).Visible = True
            dg_OneByOne.DataBind()
            dg_OneByOne.Columns(1).Visible = False
            dg_OneByOne.Columns(2).Visible = False
           

            ''********************************************************************************************************************''

            If dg_OneByOne.Rows.Count = 0 Then
                lblErr_Return.Text = "There is no Tape agaiast Employee: " & txt_EmpReturn.Text
            End If

        ElseIf dtibraryID.Rows.Count <> 0 Then
            Dim ObjUser As New BusinessFacade.TapeReturn()
            If dtibraryID.Rows.Count = 2 Then
                dg_OneByOne.DataSource = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, dtibraryID.Rows(1).Item(0).ToString)
            ElseIf dtibraryID.Rows.Count = 1 Then
                dg_OneByOne.DataSource = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, 0)
            End If
            dg_OneByOne.Columns(1).Visible = True
            dg_OneByOne.Columns(2).Visible = True
            dg_OneByOne.DataBind()
            dg_OneByOne.Columns(1).Visible = False
            dg_OneByOne.Columns(2).Visible = False

            If dg_OneByOne.Rows.Count = 0 Then
                lblErr_Return.Text = "There is no Record against Tape mentioned !!"
            End If
        Else
            lblErr_Return.Text = "Employee/Tape Number is not valid !!"
        End If

        If dg_OneByOne.Rows.Count > 0 Then
            lblRecordCount.Visible = True
            lblRecordCount.Text = "No of Records : " + CStr(dg_OneByOne.Rows.Count)
        Else
            lblRecordCount.Visible = False
            lblRecordCount.Text = String.Empty
        End If

    End Sub

    Protected Sub bttnReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReturn.Click

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        ''*******************************************''
        ''*********** Get TapeLibraryID *************''
        ''*******************************************''

        Dim Arr As Array
        If txtTapeNumber.Text <> "" Then
            Arr = Split(txtTapeNumber.Text, "#")
            If Arr.Length = 2 Then
                txtTapeNumber.Text = Arr(1)
            End If
        End If

        Dim LibraryID As Integer
        Dim objLibID As New BusinessFacade.TapeIssuance()
        objLibID.TapeNumber = txtTapeNumber.Text
        LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

        '**********************************************'

        Dim CountChk As Integer
        Dim Q As Integer
        Dim DateError As Integer
        For Q = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(Q).Cells(4).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                CountChk = CountChk + 1
                If Convert.ToDateTime(dg_OneByOne.Rows(Q).Cells(7).Text).ToString("dd-MMM-yyyy") > Convert.ToDateTime(txtEntryDate_Return.Text) Then
                    DateError = DateError + 1
                End If
            End If
        Next


        If CountChk <> 0 Then
            If txtEntryDate_Return.Text = "" Then
                lblErr_Return.Text = "Please Select Date !!"
            ElseIf DateError > 0 Then
                lblErr_Return.Text = "Attention: Return can't be greater than Issue Date!"
            Else
                '''''''''''''' Save Detail Record ''''''''''''''''''
                Dim P As Integer
                Dim IssuanceID As Integer

                Dim IsTapeChecked As Integer = 0


                Try
                    For P = 0 To dg_OneByOne.Rows.Count - 1
                        Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(4).Controls(1), CheckBox)
                        If MyCheckBox.Checked = True Then

                            ''******************************''
                            ''*** BaseStation Validation ***''
                            ''******************************''

                            Dim objValidation As New BusinessFacade.NewTapeNumber()
                            objValidation.TapeLibraryID = dg_OneByOne.Rows(P).Cells(2).Text
                            Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                            If BaseStationID = CokieBaseStationID Then
                                IssuanceID = Convert.ToInt32(dg_OneByOne.Rows(P).Cells(1).Text.ToString)
                                Dim ObjReturn1by1 As New BusinessFacade.TapeReturn()
                                ObjReturn1by1.TapeIssuanceID = IssuanceID
                                ObjReturn1by1.Emp_1By1 = EmployeeID
                                ObjReturn1by1.TapeLibraryID = dg_OneByOne.Rows(P).Cells(2).Text
                                ObjReturn1by1.EntryDate = txtEntryDate_Return.Text
                                ObjReturn1by1.UserID = UserID
                                ObjReturn1by1.OneByOneTapeReturn_saveRecord_Archival()
                                lblErr_Return.Text = "Tape has been Returned!!"
                                IsTapeChecked = 1
                            Else
                                lblErr_Return.Text = "You are not allowed to Return this Tape!!"
                            End If
                            ''************ End *************''
                            ''******************************''
                        End If
                    Next

                    Try
                        If IsTapeChecked = 1 Then
                            Dim ObjReminder As New BusinessFacade.ReminderService()
                            ObjReminder.EmployeeName = dg_OneByOne.Rows(0).Cells(5).Text.ToString
                            Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                            If EmailAddress <> "N/A" Then
                                Dim ArrEmail As Array = Split(EmailAddress, "@")
                                If ArrEmail.Length = 2 Then
                                    'sendmail(EmailAddress)
                                    sendmail_Multiple()
                                Else
                                    lblErr_Return.Text += "But Email Not send due to Invalid Email Address!"
                                End If
                            Else
                                lblErr_Return.Text += "But Email Not send b/c Email Address not Exists!"
                            End If
                            ' sendmail()
                        End If

                    Catch ex As Exception

                    End Try



                    ''********************************************************************************************************************''
                    dg_OneByOne.DataSource = Nothing
                    dg_OneByOne.DataBind()
                    txtIsEmployeeSearch.Text = ""
                    ViewState("dtReturnNew") = Nothing


                    clrscr_2()

                Catch ex As Exception
                    Throw
                End Try
            End If
        Else
            lblErr_Return.Text = "Please Select Tapes!"
        End If


        

        'txtTapeNumber.Text = String.Empty

    End Sub

    Private Sub clrscr_2()
        Dim Q As Integer
        For Q = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(Q).Cells(4).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next
    End Sub

    Protected Sub bttnClearScreen_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClearScreen.Click
        dg_OneByOne.DataSource = Nothing
        dg_OneByOne.DataBind()

        txtIsEmployeeSearch.Text = ""
        ViewState("dtReturnNew") = Nothing


        txt_EmpReturn.Text = String.Empty
        lblErr_Return.Text = String.Empty
        txtEntryDate_Return.Text = Date.Now().ToString("dd-MMM-yyyy")
        txtTapeNumber.Text = String.Empty
        lblRecordCount.Text = String.Empty
        lblRecordCount.Visible = False
    End Sub

    Protected Sub bttnClearSel_Click1(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClearSel.Click
        Dim L As Integer
        For L = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(L).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

        Dim M As Integer
        For M = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(M).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then
                MyCheckBox.Checked = False
            End If
        Next

        txt_TapeNumber_News.Text = String.Empty
        txt_SlugName.Text = String.Empty
        txt_TapeNumber_Ent.Text = String.Empty
        txt_ProgramName.Text = String.Empty
        lblErr_Issue.Text = String.Empty
        txt_IssueToEmp.Text = String.Empty
        txtEntryDate_Issue.Text = Date.Now().ToString("dd-MMM-yyyy")

        dg_TapeIssuance_SlugWise.DataSource = Nothing
        dg_TapeIssuance_SlugWise.DataBind()

        dg_TapeIssue.DataSource = Nothing
        dg_TapeIssue.DataBind()

        ViewState("dtEntIssueNew") = Nothing
        
        ViewState("dtNewsIssueNew") = Nothing
        
    End Sub

    Protected Sub ddl_IssueToEmp_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_IssueToEmp.SelectedIndexChanged
        lblErr_Issue.Text = String.Empty
    End Sub

    Protected Sub lnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub lnkSearch_Ent_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSearch_Ent.Click
        lblErr_Issue.Text = String.Empty
        'GetRecord_Ent_search()


        Dim arr As Array = Split(txt_TapeNumber_Ent.Text, "#")
        Dim Current_Tape As String = ""
        If arr.Length = 2 Then
            Current_Tape = arr(1)
        End If

        Dim IsExists As Integer = 0
        For I As Integer = 0 To dg_TapeIssue.Rows.Count - 1
            If Current_Tape = dg_TapeIssue.Rows(I).Cells(2).Text Then
                IsExists = 1
            End If
        Next

        If IsExists = 0 Then
            GetRecord_Ent_search_Multiple()
            ShowLabels()
        Else
            lblErr_Issue.Text = "This Tape Already Selected!!!"
        End If
        


    End Sub

    Sub ShowLabels()
        If dg_TapeIssue.Rows.Count > 0 Then
            lblEntTapes.Visible = True
        Else
            lblEntTapes.Visible = False
        End If

        If dg_TapeIssuance_SlugWise.Rows.Count > 0 Then
            lblNewsTapes.Visible = True
        Else
            lblNewsTapes.Visible = False
        End If
    End Sub

    Protected Sub lnkSearch_News_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkAudit_News.Click
        lblErr_Issue.Text = String.Empty


        Dim arr As Array = Split(txt_TapeNumber_News.Text, "#")
        Dim Current_Tape As String = ""
        If arr.Length = 2 Then
            Current_Tape = arr(1)
        End If

        Dim IsExists As Integer = 0
        For I As Integer = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            If Current_Tape = dg_TapeIssuance_SlugWise.Rows(I).Cells(2).Text Then
                IsExists = 1
            End If
        Next


        If IsExists = 0 Then
            'GetRecord_News_search()
            GetRecord_News_search_Multiple()
            ShowLabels()
        Else
            lblErr_Issue.Text = "This Tape Already Selected!!!"
        End If
        

    End Sub

    Private Sub GetRecord_Ent_search()
        Try
            Dim A As String
            Dim B As String
            If txt_ProgramName.Text = "" Then
                A = "All"
            Else
                A = txt_ProgramName.Text
            End If

            If txt_TapeNumber_Ent.Text = "" Then
                B = "All"
            Else
                Dim arr As Array = Split(txt_TapeNumber_Ent.Text, "#")
                If arr.Length = 2 Then
                    B = arr(1)
                Else
                    B = "All"
                End If
            End If
            Dim objUser As New BusinessFacade.TapeIssuance()
            objUser.ProgramSlugName = A
            objUser.TapeNumber = B
            dg_TapeIssue.Visible = True


            dg_TapeIssue.DataSource = objUser.ArchivalTapeIssuance_GetRecords()
            dg_TapeIssue.Columns(2).Visible = True
            dg_TapeIssue.DataBind()
            dg_TapeIssue.Columns(2).Visible = False
            dg_TapeIssuance_SlugWise.Visible = False

            Dim EmployeeName As String = "0"
            If txt_TapeNumber_Ent.Text <> "" Then
                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber(ObjTapeNumber.TapeNumber)
            End If

            If dg_TapeIssue.Rows.Count = "0" And EmployeeName <> "0" Then
                lblErr_Issue.Text = "Tape has been already issued to " & EmployeeName
            ElseIf dg_TapeIssue.Rows.Count = "0" Then
                lblErr_Issue.Text = "There is no record available on given criteria!!"
            End If


        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetRecord_Ent_search_Multiple()
        Try
            Dim A As String
            Dim B As String
            If txt_ProgramName.Text = "" Then
                A = "All"
            Else
                A = txt_ProgramName.Text
            End If

            If txt_TapeNumber_Ent.Text = "" Then
                B = "All"
            Else
                Dim arr As Array = Split(txt_TapeNumber_Ent.Text, "#")
                If arr.Length = 2 Then
                    B = arr(1)
                Else
                    B = "All"
                End If
            End If

            ''****************************************************''
            ''*************** Check Already Issued ***************''
            ''****************************************************''

            Dim EmployeeName As String = "0"
            If txt_TapeNumber_Ent.Text <> "" Then
                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber(ObjTapeNumber.TapeNumber)
            End If

            If (EmployeeName <> "0" And Not EmployeeName Is Nothing) Then
                lblErr_Issue.Text = "Tape has been already issued to " & EmployeeName
                Exit Sub
            End If

            Dim objUser As New BusinessFacade.TapeIssuance()
            objUser.ProgramSlugName = A
            objUser.TapeNumber = B
            dg_TapeIssue.Visible = True


            If dg_TapeIssue.Rows.Count = 0 Then

                dtEntIssueNew = objUser.ArchivalTapeIssuance_GetRecords(CInt(Request.Cookies("userinfo")("BaseStationID")))

                ''****************************************************''

                Dim IsOtherStation As Integer = 0
                For T As Integer = 0 To dtEntIssueNew.Rows.Count - 1
                    Dim objValidation As New BusinessFacade.NewTapeNumber()
                    objValidation.TapeLibraryID = dtEntIssueNew.Rows(T)("TapeLibraryID").ToString()
                    Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID <> CokieBaseStationID Then
                        IsOtherStation = 1
                    End If
                Next

                If IsOtherStation = 0 Then
                    dg_TapeIssue.DataSource = dtEntIssueNew
                    dg_TapeIssue.Columns(3).Visible = True
                    dg_TapeIssue.DataBind()
                    dg_TapeIssue.Columns(3).Visible = False
                    
                    ViewState("dtEntIssueNew") = dtEntIssueNew

                End If
             
                ''****************************************************''


            Else

                dtEntIssueNew = ViewState("dtEntIssueNew")

                Dim dtTemp As DataTable
                dtTemp = objUser.ArchivalTapeIssuance_GetRecords(CInt(Request.Cookies("userinfo")("BaseStationID")))


                For J As Integer = 0 To dtTemp.Rows.Count - 1
                    Dim AlreadyExists As Integer = 0
                    For K As Integer = 0 To dg_TapeIssue.Rows.Count - 1
                        If dg_TapeIssue.Rows(K).Cells(2).Text = dtTemp.Rows(J)("TapeLibraryID").ToString() Then
                            AlreadyExists = 1
                        End If
                    Next

                    If AlreadyExists = "0" Then

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dtTemp.Rows(J)("TapeLibraryID").ToString
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            Dim Drow As DataRow = dtEntIssueNew.NewRow()
                            Drow(0) = dtTemp.Rows(J)("TapeLibraryID").ToString
                            Drow(1) = dtTemp.Rows(J)("TapeNumber").ToString()
                            Drow(2) = dtTemp.Rows(J)("StationName").ToString
                            Drow(3) = dtTemp.Rows(J)("TapeType").ToString
                            dtEntIssueNew.Rows.Add(Drow)
                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!"
                        End If
                    End If
                Next

                dg_TapeIssue.DataSource = dtEntIssueNew
                dg_TapeIssue.Columns(3).Visible = True
                dg_TapeIssue.DataBind()
                dg_TapeIssue.Columns(3).Visible = False
                
                ViewState("dtEntIssueNew") = dtEntIssueNew

            End If

            If dg_TapeIssue.Rows.Count = "0" Then
                lblErr_Issue.Text = "There is no record available on given criteria!!"
                Exit Sub
            End If


        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetRecord_News_search()
        Try
            Dim A As String
            Dim B As String
            If txt_SlugName.Text = "" Then
                A = "All"
            Else
                A = txt_SlugName.Text
            End If

            If txt_TapeNumber_News.Text = "" Then
                B = "All"
            Else
                Dim arr As Array = Split(txt_TapeNumber_News.Text, "#")
                If arr.Length = 2 Then
                    B = arr(1)
                Else
                    B = "All"
                End If
            End If
            Dim objUser As New BusinessFacade.TapeIssuance()
            objUser.ProgramSlugName = A
            objUser.TapeNumber = B
            dg_TapeIssuance_SlugWise.Visible = True
            dg_TapeIssuance_SlugWise.DataSource = objUser.ArchiveTapeIssuace_News_GetRecords()
            dg_TapeIssuance_SlugWise.Columns(2).Visible = True
            dg_TapeIssuance_SlugWise.DataBind()
            dg_TapeIssuance_SlugWise.Columns(2).Visible = False
            dg_TapeIssue.Visible = False

            Dim EmployeeName As String = "0"
            Dim TapeNo As String = "0"
            If txt_TapeNumber_News.Text <> "" And txt_SlugName.Text = "" Then
                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber_Archival(ObjTapeNumber.TapeNumber)
            ElseIf txt_TapeNumber_News.Text = "" And txt_SlugName.Text <> "" Then
                Dim ObjSlug As New BusinessFacade.TapeIssuance()
                ObjSlug.ProgramSlugName = txt_SlugName.Text
                TapeNo = ObjSlug.GetTapeNumber_byTapeSlug()

                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber_Archival(ObjTapeNumber.TapeNumber)

            End If

            If dg_TapeIssuance_SlugWise.Rows.Count = "0" And EmployeeName <> "0" Then
                lblErr_Issue.Text = "Tape has been already issued to " & EmployeeName
            ElseIf dg_TapeIssuance_SlugWise.Rows.Count = "0" Then
                lblErr_Issue.Text = "There is no record available on given criteria!!"
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub GetRecord_News_search_Multiple()
        Try
            Dim A As String
            Dim B As String
            If txt_SlugName.Text = "" Then
                A = "All"
            Else
                A = txt_SlugName.Text
            End If

            If txt_TapeNumber_News.Text = "" Then
                B = "All"
            Else
                Dim arr As Array = Split(txt_TapeNumber_News.Text, "#")
                If arr.Length = 2 Then
                    B = arr(1)
                Else
                    B = "All"
                End If
            End If

            ''****************************************************''
            ''*************** Check Already Issued ***************''
            ''****************************************************''

            Dim EmployeeName As String = "0"
            Dim TapeNo As String = "0"
            If txt_TapeNumber_News.Text <> "" And txt_SlugName.Text = "" Then
                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber_Archival(ObjTapeNumber.TapeNumber)
            ElseIf txt_TapeNumber_News.Text = "" And txt_SlugName.Text <> "" Then
                Dim ObjSlug As New BusinessFacade.TapeIssuance()
                ObjSlug.ProgramSlugName = txt_SlugName.Text
                TapeNo = ObjSlug.GetTapeNumber_byTapeSlug()

                Dim ObjTapeNumber As New BusinessFacade.TapeIssuance()
                ObjTapeNumber.TapeNumber = B
                EmployeeName = ObjTapeNumber.GetEmployeeName_by_TapeNumber_Archival(ObjTapeNumber.TapeNumber)

            End If

            If (EmployeeName <> "0" And Not EmployeeName Is Nothing) Then
                lblErr_Issue.Text = "Tape has been already issued to " & EmployeeName
                Exit Sub
            End If

            ''****************************************************''
            If dg_TapeIssuance_SlugWise.Rows.Count = 0 Then

                Dim objNews As New BusinessFacade.TapeIssuance()
                objNews.ProgramSlugName = A
                objNews.TapeNumber = B
                dg_TapeIssuance_SlugWise.Visible = True

                dtNewsIssueNew = objNews.ArchiveTapeIssuace_News_GetRecords(CInt(Request.Cookies("userinfo")("BaseStationID")))

                ''****************************************************''

                Dim IsOtherStation As Integer = 0
                For T As Integer = 0 To dtNewsIssueNew.Rows.Count - 1
                    Dim objValidation As New BusinessFacade.NewTapeNumber()
                    objValidation.TapeLibraryID = dtNewsIssueNew.Rows(T)("TapeLibraryID").ToString()
                    Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID <> CokieBaseStationID Then
                        IsOtherStation = 1
                    End If
                Next
                If IsOtherStation = 0 Then
                    dg_TapeIssuance_SlugWise.DataSource = dtNewsIssueNew
                    dg_TapeIssuance_SlugWise.Columns(3).Visible = True
                    dg_TapeIssuance_SlugWise.DataBind()
                    dg_TapeIssuance_SlugWise.Columns(3).Visible = False

                    ViewState("dtNewsIssueNew") = dtNewsIssueNew

                End If
            Else

                dtNewsIssueNew = ViewState("dtNewsIssueNew")

               
                Dim objUser As New BusinessFacade.TapeIssuance()
                objUser.ProgramSlugName = A
                objUser.TapeNumber = B
                dg_TapeIssuance_SlugWise.Visible = True

                Dim dtTempNews As DataTable
                dtTempNews = objUser.ArchiveTapeIssuace_News_GetRecords(CInt(Request.Cookies("userinfo")("BaseStationID")))

                For J As Integer = 0 To dtTempNews.Rows.Count - 1
                    Dim AlreadyExists As Integer = 0
                    For K As Integer = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
                        If dg_TapeIssuance_SlugWise.Rows(K).Cells(2).Text = dtTempNews.Rows(J)("TapeLibraryID").ToString() Then
                            AlreadyExists = 1
                        End If
                    Next

                    If AlreadyExists = "0" Then


                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dtTempNews.Rows(J)("TapeLibraryID").ToString
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            Dim Drow As DataRow = dtNewsIssueNew.NewRow()
                            Drow(0) = dtTempNews.Rows(J)("TapeLibraryID").ToString
                            Drow(1) = dtTempNews.Rows(J)("TapeNumber").ToString()
                            Drow(2) = dtTempNews.Rows(J)("StationName").ToString
                            Drow(3) = dtTempNews.Rows(J)("TapeType").ToString
                            dtNewsIssueNew.Rows.Add(Drow)
                        Else
                            lblErr_Issue.Text = "You are not allowed to Issue this Tape!"
                        End If
                    End If
                Next

            End If

            dg_TapeIssuance_SlugWise.DataSource = dtNewsIssueNew
            dg_TapeIssuance_SlugWise.Columns(3).Visible = True
            dg_TapeIssuance_SlugWise.DataBind()
            dg_TapeIssuance_SlugWise.Columns(3).Visible = False

            If dg_TapeIssuance_SlugWise.Rows.Count = "0" Then
                lblErr_Issue.Text = "There is no record available on given criteria!!"
            End If

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub dg_TapeIssue_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_TapeIssue.PageIndexChanging
        dg_TapeIssue.PageIndex = e.NewPageIndex()
        GetRecord_Ent_search()
    End Sub

    Protected Sub dg_TapeIssuance_SlugWise_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_TapeIssuance_SlugWise.PageIndexChanging
        dg_TapeIssuance_SlugWise.PageIndex = e.NewPageIndex()
        GetRecord_News_search()
    End Sub

    Protected Sub bttnSearchReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearchReturn.Click
        'BindGrid()
        BindGrid_MultipleSearch()
        txtTapeNumber.Text = ""

    End Sub

    Sub BindGrid_MultipleSearch()

        lblErr_Return.Text = String.Empty

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''*******************************************''
        ''*********** Get TapeLibraryID *************''
        ''*******************************************''

        Dim Arr As Array
        If txtTapeNumber.Text <> "" Then
            Arr = Split(txtTapeNumber.Text, "#")
            If Arr.Length = 2 Then
                txtTapeNumber.Text = Arr(1)
            End If
        End If

        Dim LibraryID As Integer = 0
        Dim dtibraryID As Data.DataTable = Nothing
        If txtTapeNumber.Text <> "" Then
            Dim objLibID As New BusinessFacade.TapeIssuance()
            objLibID.TapeNumber = txtTapeNumber.Text
            dtibraryID = objLibID.TapeIssuance_GetLibraryID_datatable(objLibID.TapeNumber)
        End If

        If EmployeeID <> "0" Then

            ''******* Clear Already Entered Data ********''

            ViewState("dtReturnNew") = Nothing
            dtReturnNew = ViewState("dtReturnNew")
            dg_OneByOne.DataSource = Nothing
            dg_OneByOne.DataBind()


            ''************************************************** Search For Employee Wise ****************************************************''

            Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
            Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
            Dim DS As DataSet
            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            cmd.CommandText = "Archival_TapeReturn_GetRecord"
            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
            currentPageIndex.Value = EmployeeID
            cmd.Connection = Con
            cmd.CommandTimeout = 0
            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS)

            dg_OneByOne.DataSource = DS.Tables(0).DefaultView
            dg_OneByOne.Columns(1).Visible = True
            dg_OneByOne.Columns(2).Visible = True
            dg_OneByOne.DataBind()
            dg_OneByOne.Columns(1).Visible = False
            dg_OneByOne.Columns(2).Visible = False

            txtIsEmployeeSearch.Text = 1
            ''********************************************************************************************************************''

            If dg_OneByOne.Rows.Count = 0 Then
                lblErr_Return.Text = "There is no Tape agaiast Employee: " & txt_EmpReturn.Text
            End If

        ElseIf dtibraryID.Rows.Count <> 0 Then

            Dim ObjUser As New BusinessFacade.TapeReturn()

            ''***************** New Working ************************''

            If (dg_OneByOne.Rows.Count = 0) Or (txtIsEmployeeSearch.Text = "1") Then

                If dtibraryID.Rows.Count = 2 Then
                    dtReturnNew = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, dtibraryID.Rows(1).Item(0).ToString)
                ElseIf dtibraryID.Rows.Count = 1 Then
                    dtReturnNew = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, 0)
                End If

                Dim IsOtherStation As Integer = 0
                For T As Integer = 0 To dtReturnNew.Rows.Count - 1
                    Dim objValidation As New BusinessFacade.NewTapeNumber()
                    objValidation.TapeLibraryID = dtReturnNew.Rows(T)("TapeLibraryID").ToString()
                    Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                    Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                    If BaseStationID <> CokieBaseStationID Then
                        IsOtherStation = 1
                    End If
                Next

                If IsOtherStation = 0 Then

                    dtReturnNew.DefaultView.Sort = "EmployeeName asc"

                    dg_OneByOne.DataSource = dtReturnNew
                    dg_OneByOne.Columns(1).Visible = True
                    dg_OneByOne.Columns(2).Visible = True
                    dg_OneByOne.DataBind()
                    dg_OneByOne.Columns(1).Visible = False
                    dg_OneByOne.Columns(2).Visible = False

                    SelectedNewTape(txtTapeNumber.Text)
                    txtTapeNumber.Text = ""

                    ViewState("dtReturnNew") = dtReturnNew

                    txtIsEmployeeSearch.Text = 0

                End If

                If dtReturnNew.Rows.Count = 0 Then
                    lblErr_Return.Text = "There is no Record against Tape mentioned !!"
                End If

            Else
                ''******************** Already Enterted ***************************''

                dtReturnNew = ViewState("dtReturnNew")

                Dim dtTemp_Return As DataTable = Nothing
                If dtibraryID.Rows.Count = 2 Then
                    dtTemp_Return = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, dtibraryID.Rows(1).Item(0).ToString)
                ElseIf dtibraryID.Rows.Count = 1 Then
                    dtTemp_Return = ObjUser.Archival_TapeReturn_GetRecord_TapeNumber_1(dtibraryID.Rows(0).Item(0).ToString, 0)
                End If

                For J As Integer = 0 To dtTemp_Return.Rows.Count - 1
                    Dim AlreadyExists As Integer = 0
                    For K As Integer = 0 To dg_OneByOne.Rows.Count - 1
                        If dg_OneByOne.Rows(K).Cells(2).Text = dtTemp_Return.Rows(J)("TapeLibraryID").ToString() Then
                            AlreadyExists = 1
                        End If
                    Next

                    If AlreadyExists = "0" Then
                        Dim dt As DataTable = dtReturnNew

                        Dim objValidation As New BusinessFacade.NewTapeNumber()
                        objValidation.TapeLibraryID = dtTemp_Return.Rows(J)("TapeLibraryID").ToString
                        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
                        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                        If BaseStationID = CokieBaseStationID Then
                            Dim Drow As DataRow = dtReturnNew.NewRow()
                            Drow(0) = dtTemp_Return.Rows(J)("TapeLibraryID").ToString()
                            Drow(1) = dtTemp_Return.Rows(J)("TapeNumber").ToString
                            Drow(2) = dtTemp_Return.Rows(J)("TapeIssuanceID").ToString
                            Drow(3) = dtTemp_Return.Rows(J)("EmployeeName").ToString
                            Drow(4) = dtTemp_Return.Rows(J)("TapeIssuanceDate").ToString
                            Drow(5) = dtTemp_Return.Rows(J)("StationName").ToString
                            Drow(6) = dtTemp_Return.Rows(J)("TapeType").ToString

                            dtReturnNew.Rows.Add(Drow)
                        Else
                            lblErr_Return.Text = "You are not allowed to Issue this Tape!"
                        End If
                    End If
                Next

                dtReturnNew.DefaultView.Sort = "EmployeeName asc"

                dg_OneByOne.DataSource = dtReturnNew
                dg_OneByOne.Columns(1).Visible = True
                dg_OneByOne.Columns(2).Visible = True
                dg_OneByOne.DataBind()
                dg_OneByOne.Columns(1).Visible = False
                dg_OneByOne.Columns(2).Visible = False

                SelectedNewTape(txtTapeNumber.Text)
                txtTapeNumber.Text = ""

                ViewState("dtReturnNew") = dtReturnNew

            End If

            ''******************************************************''
        Else
            lblErr_Return.Text = "Employee/Tape Number is not valid !!"
        End If

        If dg_OneByOne.Rows.Count > 0 Then
            lblRecordCount.Visible = True
            lblRecordCount.Text = "No of Records : " + CStr(dg_OneByOne.Rows.Count)
        Else
            lblRecordCount.Visible = False
            lblRecordCount.Text = String.Empty
        End If
    End Sub

    Sub SelectedNewTape(ByVal TapeNumber As String)

        For K As Integer = 0 To dg_OneByOne.Rows.Count - 1
            If dg_OneByOne.Rows(K).Cells(3).Text.ToLower() = TapeNumber.ToLower() Then
                dg_OneByOne.Rows(K).BackColor = Drawing.Color.Pink
            End If
        Next

    End Sub

    Protected Sub dg_OneByOne_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_OneByOne.PageIndexChanging
        dg_OneByOne.PageIndex = e.NewPageIndex()
        BindGrid()
    End Sub

    Protected Sub dg_OneByOne_RowDeleting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewDeleteEventArgs) Handles dg_OneByOne.RowDeleting

        Dim RowId As Integer
        RowId = e.RowIndex

        ''******************************''
        ''*** BaseStation Validation ***''
        ''******************************''

        Dim objValidation As New BusinessFacade.NewTapeNumber()
        objValidation.TapeLibraryID = dg_OneByOne.Rows(RowId).Cells(2).Text
        Dim BaseStationID As Integer = objValidation.ValidateBaseStation()
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        If BaseStationID = CokieBaseStationID Then

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''

            Dim Objdelete As New BusinessFacade.TapeIssuance()
            Objdelete.TapeIssuanceID = dg_OneByOne.Rows(RowId).Cells(1).Text
            Objdelete.UserID = UserID
            Objdelete.DeleteRecord()

            'BindGrid()

            If txt_EmpReturn.Text <> "" Then
                BindGrid()
            Else
                dtReturnNew = ViewState("dtReturnNew")
                If Not dtReturnNew Is Nothing Then
                    If dtReturnNew.Rows.Count > 0 Then

                        dtReturnNew.Rows(RowId).Delete()
                        dtReturnNew.AcceptChanges()

                        ViewState("dtReturnNew") = dtReturnNew

                        dg_OneByOne.DataSource = dtReturnNew
                        dg_OneByOne.Columns(1).Visible = True
                        dg_OneByOne.Columns(2).Visible = True
                        dg_OneByOne.DataBind()
                        dg_OneByOne.Columns(1).Visible = False
                        dg_OneByOne.Columns(2).Visible = False

                    End If
                End If
            End If

        Else
            lblErr_Return.Text = "You are not allowed to Delete this Record!!"
        End If
        ''************ End *************''
        ''******************************''

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        sendmail_Multiple()
    End Sub

    Public Sub sendmail_Multiple()
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been returned to Archive Department on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Returned by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'


        Dim EmailAddress As String = ""

        Dim ToAddress As String = ""
        Dim oldempname As String = ""
        Dim empname As String = ""

        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(4).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                empname = dg_OneByOne.Rows(P).Cells(5).Text.ToString

                'headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_Return.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))



                ' Get Email

                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = dg_OneByOne.Rows(P).Cells(5).Text.ToString
                EmailAddress = ObjReminder.GetEmailAddress()

                If ToAddress <> "N/A" Then
                    If ToAddress.Length > 0 Then
                        If ToAddress <> EmailAddress Then
                            headermessage = headermessage.Replace("EmpName", oldempname)
                            sendreturnmail(headermessage, fullheader, ToAddress, oldempname)
                            headermessage = headermessage.Replace(oldempname, "EmpName")
                            ToAddress = EmailAddress
                            fullheader = ""
                            Sno = 0

                        End If

                    End If
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If

                Else
                    ToAddress = EmailAddress

                    If EmailAddress <> "N/A" Then
                        oldempname = empname
                    End If
                End If


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_OneByOne.Rows(P).Cells(3).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", dg_OneByOne.Rows(P).Cells(7).Text.ToString)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_OneByOne.Rows(P).Cells(8).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)


                If ToAddress <> "N/A" Then
                    fullheader = fullheader + iterativemessage
                    Sno = Sno + 1
                End If
                
            End If
        Next

        ''************ End *************''
        ''******************************''


        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        headermessage = headermessage.Replace("EmpName", empname)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage

        If EmailAddress = "N/A" Then
            'lblErr2.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If

        Dim Mail As New Email
        ToAddress = ToAddress

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Mail.sendemail(ToAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Public Sub sendreturnmail(ByVal headermessage As String, ByVal fullheader As String, ByVal emailaddress As String, ByVal empname As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Returned by</td></tr>"
        Dim iterativemessage As String = ""

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        headermessage = headermessage.Replace("EmpName", empname)



        Dim ToAddress As String = ""




        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage & fullheader & footermessage

        If emailaddress = "N/A" Then
            'lblErr.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If


        Dim Mail As New Email

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If


        ''' add EmailAddress variable in To
        Mail.sendemail(emailaddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Public Sub sendmail(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been returned to Archive Department on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Returned by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        
        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_OneByOne.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_OneByOne.Rows(P).Cells(4).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = dg_OneByOne.Rows(P).Cells(5).Text.ToString

                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_Return.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))



                ' Get Email

                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = dg_OneByOne.Rows(P).Cells(5).Text.ToString
                EmailAddress = ObjReminder.GetEmailAddress()


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_OneByOne.Rows(P).Cells(3).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", dg_OneByOne.Rows(P).Cells(7).Text.ToString)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_OneByOne.Rows(P).Cells(8).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)


                fullheader = fullheader + iterativemessage

                Sno = Sno + 1



            End If
        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage


        'If EmailAddress = "N/A" Then
        '    lblErr_Return.Text = "Tape Return Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If


        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "return")

    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        'sendissuancemail()
    End Sub

    Public Sub sendissuancemail(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>IssuanceDate  </b> at <b>IssuanceTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issued by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_TapeIssue.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssue.Rows(P).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = txt_IssueToEmp.Text

                If txt_IssueToEmp.Text.Contains("P-") Then
                    empname = txt_IssueToEmp.Text
                End If


                '' get Email Address


                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = txt_IssueToEmp.Text
                EmailAddress = ObjReminder.GetEmailAddress()



                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empissue", empname)
                headermessage = headermessage.Replace("IssuanceDate", txtEntryDate_Issue.Text)
                headermessage = headermessage.Replace("IssuanceTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Issuedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_TapeIssue.Rows(P).Cells(2).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_Issue.Text)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_TapeIssue.Rows(P).Cells(5).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Issuedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)
                iterativemessage = iterativemessage.Replace("IssuetoEmp", txt_IssueToEmp.Text)
                'IssuetoEmp
                fullheader = fullheader + iterativemessage

                Sno = Sno + 1



            End If
        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage


        'If EmailAddress = "N/A" Then
        '    lblErr_Issue.Text = "Tape Issuance Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If


        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")

    End Sub

    Public Sub sendissuancemail_Slug(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   } .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}   </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>IssuanceDate  </b> at <b>IssuanceTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</tbody></table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <br /><br /><table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; border-style: groove; border-width: thin; width: 837px; display: block;"" cellspacing =""0"" cellpadding =""0""> <tbody style =""border-style: groove; border-width: thin;""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center;"">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issuance Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;"" >Issued by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""



        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)



        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_EmpReturn.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg_TapeIssuance_SlugWise.Rows.Count - 1
            Dim MyCheckBox As CheckBox = CType(dg_TapeIssuance_SlugWise.Rows(P).Cells(1).Controls(1), CheckBox)
            If MyCheckBox.Checked = True Then

                Dim empname As String = txt_IssueToEmp.Text

                If txt_IssueToEmp.Text.Contains("P-") Then
                    empname = txt_IssueToEmp.Text
                End If


                '' get Email Address


                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = txt_IssueToEmp.Text
                EmailAddress = ObjReminder.GetEmailAddress()



                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empissue", empname)
                headermessage = headermessage.Replace("IssuanceDate", txtEntryDate_Issue.Text)
                headermessage = headermessage.Replace("IssuanceTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >Tapetype</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Issuedby</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                iterativemessage = iterativemessage.Replace("Tapenumber", dg_TapeIssuance_SlugWise.Rows(P).Cells(2).Text.ToString)
                iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_Issue.Text)
                iterativemessage = iterativemessage.Replace("Tapetype", dg_TapeIssuance_SlugWise.Rows(P).Cells(5).Text.ToString)
                iterativemessage = iterativemessage.Replace("Archivalorblank", "Archival")
                iterativemessage = iterativemessage.Replace("Issuedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)
                iterativemessage = iterativemessage.Replace("IssuetoEmp", txt_IssueToEmp.Text)
                'IssuetoEmp
                fullheader = fullheader + iterativemessage

                Sno = Sno + 1



            End If
        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        If Sno > 1 Then
            headermessage = headermessage.Replace("tape", "tapes")
        End If

        message = message & headermessage & middlemessage & fullheader & footermessage


        'If EmailAddress = "N/A" Then
        '    lblErr_Issue.Text = "Tape Issuance Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")

    End Sub

End Class
