Imports System.Data
Imports System.IO
Imports System.Xml
Imports excel = Microsoft.Office.Interop.Excel
Imports Microsoft.Office.Interop


Partial Class SearchEngine_News_Results
    Inherits System.Web.UI.Page
    Dim ConString As String = System.Configuration.ConfigurationManager.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
    Dim StrCommand As String
    Dim DS As DataSet
    Dim DSxml As DataSet

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Response.Cache.SetCacheability(HttpCacheability.NoCache)

        If Not Page.IsPostBack = True Then

            If Request.QueryString("@IsUrduSearchOnly") = 1 Then
                FillUrduScript()
            Else
                FillGrid()
            End If

            ''**************************************''
            Dim I As Integer
            For I = 1 To Convert.ToInt32(lblTotal.Text)
                ddlPageNo.Items.Add(I)
            Next

        End If

        GridRowselect()

    End Sub

    Protected Sub dg_Search_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_Search.PageIndexChanging
        dg_Search.PageIndex = e.NewPageIndex()
        lblPageIndex.Text = dg_Search.PageIndex
        FillGrid()
    End Sub

    Private Sub GridEnable()
        Dim dr As GridViewRow

        For Each dr In dg_Search.Rows
            Dim chk As New CheckBox
            chk = dr.FindControl("CheckBox1")
            If (dr.Cells(16).Text <> CInt(Request.Cookies("userinfo")("BaseStationID")).ToString) Then
                chk.Enabled = False
            End If

            If dr.Cells(17).Text = "0" Then
                chk.Enabled = False
            End If
        Next

    End Sub

    Private Sub FillGrid()
        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        Try

            '************************* Tape Number ****************************'

            Dim Pages As String
            Pages = Request.QueryString("@No_of_Record_Per_Page")
            lblPages.Text = Pages

            Dim ContentTypeID As String
            ContentTypeID = Request.QueryString("@ContentType")

            Dim IngnoreTape As String
            IngnoreTape = Request.QueryString("@IngnoreTape")

            Dim IngnoreReporterSlug As String
            IngnoreReporterSlug = Request.QueryString("@IngnoreReporterSlug")

            Dim IngnoreProposedSlug As String
            IngnoreProposedSlug = Request.QueryString("@IngnoreProposedSlug")

            Dim IngnoreKeyWords As String
            IngnoreKeyWords = Request.QueryString("@IngnoreKeyWords")

            Dim IngnoreKeyTypes As String
            IngnoreKeyTypes = Request.QueryString("@IngnoreKeyTypes")

            Dim IgnoreDate As String
            IgnoreDate = Request.QueryString("@IgnoreDate")

            Dim IgnoreReporter As String
            IgnoreReporter = Request.QueryString("@IgnoreReporter")

            Dim IgnoreEnglishScript As String
            IgnoreEnglishScript = Request.QueryString("@IgnoreEnglishScript")

            Dim IgnoreTapeType As String
            IgnoreTapeType = Request.QueryString("@IgnoreTapeType")


            Dim Tape_No_1 As String
            Dim Tape_No_2 As String
            Dim Tape_No_3 As String
            Dim Tape_No_4 As String
            Dim Tape_No_5 As String

            Tape_No_1 = Request.QueryString("@Tape1").ToString
            Tape_No_2 = Request.QueryString("@Tape2").ToString
            Tape_No_3 = Request.QueryString("@Tape3").ToString
            Tape_No_4 = Request.QueryString("@Tape4").ToString
            Tape_No_5 = Request.QueryString("@Tape5").ToString

            '*********************** Tape Number Options **********************'

            Dim Tape_Opt_1 As String
            Dim Tape_Opt_2 As String
            Dim Tape_Opt_3 As String
            Dim Tape_Opt_4 As String

            Tape_Opt_1 = Request.QueryString("@Operator_Tape1_2").ToString
            Tape_Opt_2 = Request.QueryString("@Operator_Tape2_3").ToString
            Tape_Opt_3 = Request.QueryString("@Operator_Tape3_4").ToString
            Tape_Opt_4 = Request.QueryString("@Operator_Tape4_5").ToString

            '*********************** Reporter Slug ***************************'

            Dim ReporterSlug_1 As String
            Dim ReporterSlug_2 As String
            Dim ReporterSlug_3 As String
            Dim ReporterSlug_4 As String
            Dim ReporterSlug_5 As String

            ReporterSlug_1 = Request.QueryString("@RptSlug1").ToString
            ReporterSlug_2 = Request.QueryString("@RptSlug2").ToString
            ReporterSlug_3 = Request.QueryString("@RptSlug3").ToString
            ReporterSlug_4 = Request.QueryString("@RptSlug4").ToString
            ReporterSlug_5 = Request.QueryString("@RptSlug5").ToString

            '******************** Reporter Slug Options *********************'

            Dim ReporterSlug_Opt_1 As String
            Dim ReporterSlug_Opt_2 As String
            Dim ReporterSlug_Opt_3 As String
            Dim ReporterSlug_Opt_4 As String

            ReporterSlug_Opt_1 = Request.QueryString("@Operator_RptSlug1_2").ToString
            ReporterSlug_Opt_2 = Request.QueryString("@Operator_RptSlug2_3").ToString
            ReporterSlug_Opt_3 = Request.QueryString("@Operator_RptSlug3_4").ToString
            ReporterSlug_Opt_4 = Request.QueryString("@Operator_RptSlug4_5").ToString

            '********************* Proposed Slug **************************'

            Dim ProposedSlug_1 As String
            Dim ProposedSlug_2 As String
            Dim ProposedSlug_3 As String
            Dim ProposedSlug_4 As String
            Dim ProposedSlug_5 As String

            ProposedSlug_1 = Request.QueryString("@ProposedSlug1").ToString
            ProposedSlug_2 = Request.QueryString("@ProposedSlug2").ToString
            ProposedSlug_3 = Request.QueryString("@ProposedSlug3").ToString
            ProposedSlug_4 = Request.QueryString("@ProposedSlug4").ToString
            ProposedSlug_5 = Request.QueryString("@ProposedSlug5").ToString

            '******************* Proposed Slug Options ********************'

            Dim ProposedSlug_Opt_1 As String
            Dim ProposedSlug_Opt_2 As String
            Dim ProposedSlug_Opt_3 As String
            Dim ProposedSlug_Opt_4 As String

            ProposedSlug_Opt_1 = Request.QueryString("@Operator_ProposedSlug1_2").ToString
            ProposedSlug_Opt_2 = Request.QueryString("@Operator_ProposedSlug2_3").ToString
            ProposedSlug_Opt_3 = Request.QueryString("@Operator_ProposedSlug3_4").ToString
            ProposedSlug_Opt_4 = Request.QueryString("@Operator_ProposedSlug4_5").ToString

            '************************* Keywords **************************'

            Dim Keyword_1 As String
            Dim Keyword_2 As String
            Dim Keyword_3 As String
            Dim Keyword_4 As String
            Dim Keyword_5 As String

            Keyword_1 = Request.QueryString("@KeyWord1").ToString
            Keyword_2 = Request.QueryString("@KeyWord2").ToString
            Keyword_3 = Request.QueryString("@KeyWord3").ToString
            Keyword_4 = Request.QueryString("@KeyWord4").ToString
            Keyword_5 = Request.QueryString("@KeyWord5").ToString

            '*********************** Keywords Options **********************'

            Dim Keyword_Opt_1 As String
            Dim Keyword_Opt_2 As String
            Dim Keyword_Opt_3 As String
            Dim Keyword_Opt_4 As String

            Keyword_Opt_1 = Request.QueryString("@Operator_KeyWord1_2").ToString
            Keyword_Opt_2 = Request.QueryString("@Operator_KeyWord2_3").ToString
            Keyword_Opt_3 = Request.QueryString("@Operator_KeyWord3_4").ToString
            Keyword_Opt_4 = Request.QueryString("@Operator_KeyWord4_5").ToString

            '************************* Keytypes **************************'

            Dim Keytype_1 As String
            Dim Keytype_2 As String
            Dim Keytype_3 As String
            Dim Keytype_4 As String
            Dim Keytype_5 As String

            Keytype_1 = Request.QueryString("@KeyType1").ToString
            Keytype_2 = Request.QueryString("@KeyType2").ToString
            Keytype_3 = Request.QueryString("@KeyType3").ToString
            Keytype_4 = Request.QueryString("@KeyType4").ToString
            Keytype_5 = Request.QueryString("@KeyType5").ToString

            '*********************** Keytypes Options **********************'

            Dim Keytypes_Opt_1 As String
            Dim Keytypes_Opt_2 As String
            Dim Keytypes_Opt_3 As String
            Dim Keytypes_Opt_4 As String

            Keytypes_Opt_1 = Request.QueryString("@Operator_KeyType1_2").ToString
            Keytypes_Opt_2 = Request.QueryString("@Operator_KeyType2_3").ToString
            Keytypes_Opt_3 = Request.QueryString("@Operator_KeyType3_4").ToString
            Keytypes_Opt_4 = Request.QueryString("@Operator_KeyType4_5").ToString

            '**************************** Entry Date ********************'

            Dim EntryDate As String
            EntryDate = Request.QueryString("@EntryDate")

            Dim ToDate As String
            ToDate = Request.QueryString("@ToDate")

            '************************* Reporter Name ********************'

            Dim ReporterName As String
            ReporterName = Request.QueryString("@ReporterName")


            '************************* English Script **************************'

            Dim EnglishScript1 As String
            Dim EnglishScript2 As String
            Dim EnglishScript3 As String
            Dim EnglishScript4 As String
            Dim EnglishScript5 As String

            EnglishScript1 = Request.QueryString("@EnglishScript1").ToString
            EnglishScript2 = Request.QueryString("@EnglishScript2").ToString
            EnglishScript3 = Request.QueryString("@EnglishScript3").ToString
            EnglishScript4 = Request.QueryString("@EnglishScript4").ToString
            EnglishScript5 = Request.QueryString("@EnglishScript5").ToString

            '*********************** Eng Script Options **********************'

            Dim EnglishScript_Opt_1 As String
            Dim EnglishScript_Opt_2 As String
            Dim EnglishScript_Opt_3 As String
            Dim EnglishScript_Opt_4 As String

            EnglishScript_Opt_1 = Request.QueryString("@Operator_EnglishScript1_2").ToString
            EnglishScript_Opt_2 = Request.QueryString("@Operator_EnglishScript2_3").ToString
            EnglishScript_Opt_3 = Request.QueryString("@Operator_EnglishScript3_4").ToString
            EnglishScript_Opt_4 = Request.QueryString("@Operator_EnglishScript4_5").ToString

            '**************************** Tape Type ***********************'

            Dim TapeType1 As String
            TapeType1 = Request.QueryString("@TapeType1").ToString

            '********************* Store Procedure **********************'

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure

            If Request.QueryString("@UrduScript1").ToString = "%%" Then
                'cmd.CommandText = "getSearchResultsNews_With2Footages"
                cmd.CommandText = "Proc_SearchEngine_News_WithOutUrduScript"
            Else
                cmd.CommandText = "Proc_SearchEngine_News"
            End If

            cmd.Connection = Con
            cmd.CommandTimeout = 0

            '****************************************************************--

            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@currentPageIndex", Data.SqlDbType.Int)
            currentPageIndex.Value = Convert.ToInt32(lblPageIndex.Text)

            Dim NoOfRecordOnPage As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@NoOfRecordOnPage", Data.SqlDbType.Int)
            NoOfRecordOnPage.Value = Pages

            Dim ContentType As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ContentType", Data.SqlDbType.Text)
            ContentType.Value = 29

            Dim IngnoreTape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreTape", Data.SqlDbType.Text)
            IngnoreTape1.Value = IngnoreTape

            Dim IngnoreReporterSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreReporterSlug", Data.SqlDbType.Text)
            IngnoreReporterSlug1.Value = IngnoreReporterSlug

            Dim IngnoreProposedSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreProposedSlug", Data.SqlDbType.Text)
            IngnoreProposedSlug1.Value = IngnoreProposedSlug

            Dim IngnoreKeyWords1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyWords", Data.SqlDbType.Text)
            IngnoreKeyWords1.Value = IngnoreKeyWords

            Dim IngnoreKeyTypes1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyTypes", Data.SqlDbType.Text)
            IngnoreKeyTypes1.Value = IngnoreKeyTypes

            Dim IgnoreDate1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreDate", Data.SqlDbType.Text)
            IgnoreDate1.Value = IgnoreDate

            Dim IgnoreReporter1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreReporter", Data.SqlDbType.Text)
            IgnoreReporter1.Value = IgnoreReporter

            Dim IgnoreEnglishScript1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreEnglishScript", Data.SqlDbType.Text)
            IgnoreEnglishScript1.Value = IgnoreEnglishScript

            Dim IgnoreTapeType1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreTapeType", Data.SqlDbType.Text)
            IgnoreTapeType1.Value = IgnoreTapeType

            '****************************************************************--

            Dim Tape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape1", Data.SqlDbType.Text)
            Tape1.Value = Tape_No_1

            Dim Tape2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape2", Data.SqlDbType.Text)
            Tape2.Value = Tape_No_2

            Dim Tape3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape3", Data.SqlDbType.Text)
            Tape3.Value = Tape_No_3

            Dim Tape4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape4", Data.SqlDbType.Text)
            Tape4.Value = Tape_No_4

            Dim Tape5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape5", Data.SqlDbType.Text)
            Tape5.Value = Tape_No_5

            Dim Tape_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape1_2", Data.SqlDbType.Text)
            Tape_opt1.Value = Tape_Opt_1

            Dim Tape_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape2_3", Data.SqlDbType.Text)
            Tape_opt2.Value = Tape_Opt_2

            Dim Tape_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape3_4", Data.SqlDbType.Text)
            Tape_opt3.Value = Tape_Opt_3

            Dim Tape_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape4_5", Data.SqlDbType.Text)
            Tape_opt4.Value = Tape_Opt_4

            Dim RS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug1", Data.SqlDbType.Text)
            RS_1.Value = ReporterSlug_1

            Dim RS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug2", Data.SqlDbType.Text)
            RS_2.Value = ReporterSlug_2

            Dim RS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug3", Data.SqlDbType.Text)
            RS_3.Value = ReporterSlug_3

            Dim RS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug4", Data.SqlDbType.Text)
            RS_4.Value = ReporterSlug_4

            Dim RS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug5", Data.SqlDbType.Text)
            RS_5.Value = ReporterSlug_5

            Dim RS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug1_2", Data.SqlDbType.Text)
            RS_Opt_1.Value = ReporterSlug_Opt_1

            Dim RS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug2_3", Data.SqlDbType.Text)
            RS_Opt_2.Value = ReporterSlug_Opt_2

            Dim RS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug3_4", Data.SqlDbType.Text)
            RS_Opt_3.Value = ReporterSlug_Opt_3

            Dim RS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug4_5", Data.SqlDbType.Text)
            RS_Opt_4.Value = ReporterSlug_Opt_4

            Dim PS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug1", Data.SqlDbType.Text)
            PS_1.Value = ProposedSlug_1

            Dim PS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug2", Data.SqlDbType.Text)
            PS_2.Value = ProposedSlug_2

            Dim PS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug3", Data.SqlDbType.Text)
            PS_3.Value = ProposedSlug_3

            Dim PS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug4", Data.SqlDbType.Text)
            PS_4.Value = ProposedSlug_4

            Dim PS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug5", Data.SqlDbType.Text)
            PS_5.Value = ProposedSlug_5

            Dim PS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug1_2", Data.SqlDbType.Text)
            PS_Opt_1.Value = ProposedSlug_Opt_1

            Dim PS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug2_3", Data.SqlDbType.Text)
            PS_Opt_2.Value = ProposedSlug_Opt_2

            Dim PS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug3_4", Data.SqlDbType.Text)
            PS_Opt_3.Value = ProposedSlug_Opt_3

            Dim PS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug4_5", Data.SqlDbType.Text)
            PS_Opt_4.Value = ProposedSlug_Opt_4

            Dim KW1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord1", Data.SqlDbType.Text)
            KW1.Value = Keyword_1

            Dim KW2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord2", Data.SqlDbType.Text)
            KW2.Value = Keyword_2

            Dim KW3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord3", Data.SqlDbType.Text)
            KW3.Value = Keyword_3

            Dim KW4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord4", Data.SqlDbType.Text)
            KW4.Value = Keyword_4

            Dim KW5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord5", Data.SqlDbType.Text)
            KW5.Value = Keyword_5

            Dim KW_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord1_2", Data.SqlDbType.Text)
            KW_opt1.Value = Keyword_Opt_1

            Dim KW_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord2_3", Data.SqlDbType.Text)
            KW_opt2.Value = Keyword_Opt_2

            Dim KW_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord3_4", Data.SqlDbType.Text)
            KW_opt3.Value = Keyword_Opt_3

            Dim KW_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord4_5", Data.SqlDbType.Text)
            KW_opt4.Value = Keyword_Opt_4

            Dim KT1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType1", Data.SqlDbType.Text)
            KT1.Value = Keytype_1

            Dim KT2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType2", Data.SqlDbType.Text)
            KT2.Value = Keytype_2

            Dim KT3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType3", Data.SqlDbType.Text)
            KT3.Value = Keytype_3

            Dim KT4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType4", Data.SqlDbType.Text)
            KT4.Value = Keytype_4

            Dim KT5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType5", Data.SqlDbType.Text)
            KT5.Value = Keytype_5

            Dim KT_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType1_2", Data.SqlDbType.Text)
            KT_opt1.Value = Keytypes_Opt_1

            Dim KT_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType2_3", Data.SqlDbType.Text)
            KT_opt2.Value = Keytypes_Opt_2

            Dim KT_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType3_4", Data.SqlDbType.Text)
            KT_opt3.Value = Keytypes_Opt_3

            Dim KT_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType4_5", Data.SqlDbType.Text)
            KT_opt4.Value = Keytypes_Opt_4

            Dim FrmDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EntryDate", Data.SqlDbType.Text)
            FrmDate.Value = EntryDate

            Dim To_Date As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
            To_Date.Value = ToDate

            Dim ReporterName1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReporterName", Data.SqlDbType.Text)
            ReporterName1.Value = ReporterName

            Dim EnglishScript11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript1", Data.SqlDbType.Text)
            EnglishScript11.Value = EnglishScript1

            Dim EnglishScript22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript2", Data.SqlDbType.Text)
            EnglishScript22.Value = EnglishScript2

            Dim EnglishScript33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript3", Data.SqlDbType.Text)
            EnglishScript33.Value = EnglishScript3

            Dim EnglishScript44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript4", Data.SqlDbType.Text)
            EnglishScript44.Value = EnglishScript4

            Dim EnglishScript55 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript5", Data.SqlDbType.Text)
            EnglishScript55.Value = EnglishScript5

            Dim EnglishScript_Opt_11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript1_2", Data.SqlDbType.Text)
            EnglishScript_Opt_11.Value = EnglishScript_Opt_1

            Dim EnglishScript_Opt_22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript2_3", Data.SqlDbType.Text)
            EnglishScript_Opt_22.Value = EnglishScript_Opt_2

            Dim EnglishScript_Opt_33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript3_4", Data.SqlDbType.Text)
            EnglishScript_Opt_33.Value = EnglishScript_Opt_3

            Dim EnglishScript_Opt_44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript4_5", Data.SqlDbType.Text)
            EnglishScript_Opt_44.Value = EnglishScript_Opt_4

            Dim TapeType01 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeType1", Data.SqlDbType.Text)
            TapeType01.Value = TapeType1

            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
            BaseStationID.Value = CInt(Request.QueryString("@BaseStationID").ToString)

            If Request.QueryString("@UrduScript1").ToString <> "%%" Then
                Dim p1s As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
                p1s.Value = Request.QueryString("@UrduScript1").ToString

                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
                p2.Value = Request.QueryString("@UrduScript2").ToString

                Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
                p3.Value = Request.QueryString("@UrduScript3").ToString

                Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
                p4.Value = Request.QueryString("@UrduScript4").ToString

                Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
                p5.Value = Request.QueryString("@UrduScript5").ToString

            End If

            Dim da As New SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS)

            dg_Search.DataSource = DS.Tables(0).DefaultView
            dg_Search.Columns(9).Visible = True
            dg_Search.Columns(10).Visible = True
            dg_Search.Columns(13).Visible = True
            dg_Search.Columns(15).Visible = True
            dg_Search.Columns(16).Visible = True
            dg_Search.DataBind()
            dg_Search.Columns(9).Visible = False
            dg_Search.Columns(10).Visible = False
            dg_Search.Columns(13).Visible = False
            dg_Search.Columns(15).Visible = False
            dg_Search.Columns(16).Visible = False

            lbl_RecordCount.Text = "Total Records found :- " & (DS.Tables(1).Rows(0)(0))

            ''*********** TotalePages **************''
            Dim pq1 As Integer = Convert.ToInt32(lblPages.Text)
            Dim pq2 As Integer = (DS.Tables(1).Rows(0)(0))
            Dim Reminder As Integer = pq2 Mod pq1
            Dim pq3 As Integer = pq2 / pq1

            If Reminder = 0 Then
                lblTotalPages.Text = "Total Pages :- " & pq3
            Else
                pq3 = pq3 + 1
                lblTotalPages.Text = "Total Pages :- " & pq3
            End If

            lblTotal.Text = pq3

            ''********************************************''
            ''*********** Fill High Light Controls *******''
            ''********************************************''

            RS1.Text = ReporterSlug_1
            RS2.Text = ReporterSlug_2
            RS3.Text = ReporterSlug_3
            RS4.Text = ReporterSlug_4
            RS5.Text = ReporterSlug_5

            ENDATE.Text = EntryDate
            Tape.Text = Tape_No_1
            Rpt.Text = ReporterName
            T_type.Text = TapeType1

            GridRowsColor()
            GridEnable()

        Catch ex As Exception
            Throw
        End Try

    End Sub

    Private Sub GridRowsColor()
        Dim I As Integer
        For I = 0 To dg_Search.Rows.Count - 1
            If dg_Search.Rows(I).Cells(11).Text = "Yes" Then
                dg_Search.Rows(I).BackColor = Drawing.Color.Pink
            End If
        Next
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
        FillGrid()
    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        If lblPageIndex.Text <> 0 Then
            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
            FillGrid()
        End If
    End Sub

    Protected Sub dg_Search_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Search.SelectedIndexChanged


        Dim TapeNumber As String
        Dim ReportSlug As String

        Dim I As Integer
        I = dg_Search.SelectedIndex.ToString
        TapeNumber = dg_Search.Rows(I).Cells(4).Text
        'ReportSlug = dg_Search.Rows(I).Cells(3).Text
        ReportSlug = dg_Search.Rows(I).Cells(10).Text

        Me.Session.Add("rowindex", I)

        Response.Redirect("NewsTapeDetail.aspx?TapeNumber=" & TapeNumber & "&ReportSlug=" & ReportSlug)
        'Response.Write("<script type='text/javascript'>detailedresults=window.open('NewsTapeDetail.aspx?TapeNumber=" & TapeNumber & "&ReportSlug=" & ReportSlug & "');</script>")

    End Sub

    Private Sub GridRowselect()
        Try
            ' get row index from session
            Dim rowindex As Integer = Integer.Parse(Me.Session("rowindex"))

            If rowindex <> -1 Then
                dg_Search.Rows(rowindex).BackColor = Drawing.Color.Cyan
            End If


        Catch ex As Exception

        End Try
    End Sub


    Protected Sub ImageButton2_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles ImageButton2.Click
        If lblPageIndex.Text <> 0 Then
            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
            FillGrid()
        End If
    End Sub

    Protected Sub ImageButton1_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles ImageButton1.Click
        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
        FillGrid()
    End Sub

    Protected Sub bttnExport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnExport.Click
        If Request.QueryString("@IsUrduSearchOnly") = 1 Then
            FillUrduScript_ExcelConvert()
        Else
            FillGrid_ExcelConvert()
        End If


        'GridViewExportUtil.Export("News.xls", Me.GridView1)
    End Sub

    Private Sub FillGrid_ExcelConvert()
        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        Try

            '************************* Tape Number ****************************'

            Dim Pages As String
            Pages = Request.QueryString("@No_of_Record_Per_Page")
            lblPages.Text = Pages

            Dim ContentTypeID As String
            ContentTypeID = Request.QueryString("@ContentType")

            Dim IngnoreTape As String
            IngnoreTape = Request.QueryString("@IngnoreTape")

            Dim IngnoreReporterSlug As String
            IngnoreReporterSlug = Request.QueryString("@IngnoreReporterSlug")

            Dim IngnoreProposedSlug As String
            IngnoreProposedSlug = Request.QueryString("@IngnoreProposedSlug")

            Dim IngnoreKeyWords As String
            IngnoreKeyWords = Request.QueryString("@IngnoreKeyWords")

            Dim IngnoreKeyTypes As String
            IngnoreKeyTypes = Request.QueryString("@IngnoreKeyTypes")

            'Dim IgnoreFromDate As String
            'IgnoreFromDate = Request.QueryString("@IgnoreFromDate")

            'Dim IgnoreToDate As String
            'IgnoreToDate = Request.QueryString("@IgnoreToDate")

            Dim IgnoreDate As String
            IgnoreDate = Request.QueryString("@IgnoreDate")

            Dim IgnoreReporter As String
            IgnoreReporter = Request.QueryString("@IgnoreReporter")

            Dim IgnoreEnglishScript As String
            IgnoreEnglishScript = Request.QueryString("@IgnoreEnglishScript")

            Dim IgnoreTapeType As String
            IgnoreTapeType = Request.QueryString("@IgnoreTapeType")


            Dim Tape_No_1 As String
            Dim Tape_No_2 As String
            Dim Tape_No_3 As String
            Dim Tape_No_4 As String
            Dim Tape_No_5 As String

            Tape_No_1 = Request.QueryString("@Tape1").ToString
            Tape_No_2 = Request.QueryString("@Tape2").ToString
            Tape_No_3 = Request.QueryString("@Tape3").ToString
            Tape_No_4 = Request.QueryString("@Tape4").ToString
            Tape_No_5 = Request.QueryString("@Tape5").ToString

            '*********************** Tape Number Options **********************'

            Dim Tape_Opt_1 As String
            Dim Tape_Opt_2 As String
            Dim Tape_Opt_3 As String
            Dim Tape_Opt_4 As String

            Tape_Opt_1 = Request.QueryString("@Operator_Tape1_2").ToString
            Tape_Opt_2 = Request.QueryString("@Operator_Tape2_3").ToString
            Tape_Opt_3 = Request.QueryString("@Operator_Tape3_4").ToString
            Tape_Opt_4 = Request.QueryString("@Operator_Tape4_5").ToString

            '*********************** Reporter Slug ***************************'

            Dim ReporterSlug_1 As String
            Dim ReporterSlug_2 As String
            Dim ReporterSlug_3 As String
            Dim ReporterSlug_4 As String
            Dim ReporterSlug_5 As String

            ReporterSlug_1 = Request.QueryString("@RptSlug1").ToString
            ReporterSlug_2 = Request.QueryString("@RptSlug2").ToString
            ReporterSlug_3 = Request.QueryString("@RptSlug3").ToString
            ReporterSlug_4 = Request.QueryString("@RptSlug4").ToString
            ReporterSlug_5 = Request.QueryString("@RptSlug5").ToString

            '******************** Reporter Slug Options *********************'

            Dim ReporterSlug_Opt_1 As String
            Dim ReporterSlug_Opt_2 As String
            Dim ReporterSlug_Opt_3 As String
            Dim ReporterSlug_Opt_4 As String

            ReporterSlug_Opt_1 = Request.QueryString("@Operator_RptSlug1_2").ToString
            ReporterSlug_Opt_2 = Request.QueryString("@Operator_RptSlug2_3").ToString
            ReporterSlug_Opt_3 = Request.QueryString("@Operator_RptSlug3_4").ToString
            ReporterSlug_Opt_4 = Request.QueryString("@Operator_RptSlug4_5").ToString

            '********************* Proposed Slug **************************'

            Dim ProposedSlug_1 As String
            Dim ProposedSlug_2 As String
            Dim ProposedSlug_3 As String
            Dim ProposedSlug_4 As String
            Dim ProposedSlug_5 As String

            ProposedSlug_1 = Request.QueryString("@ProposedSlug1").ToString
            ProposedSlug_2 = Request.QueryString("@ProposedSlug2").ToString
            ProposedSlug_3 = Request.QueryString("@ProposedSlug3").ToString
            ProposedSlug_4 = Request.QueryString("@ProposedSlug4").ToString
            ProposedSlug_5 = Request.QueryString("@ProposedSlug5").ToString

            '******************* Proposed Slug Options ********************'

            Dim ProposedSlug_Opt_1 As String
            Dim ProposedSlug_Opt_2 As String
            Dim ProposedSlug_Opt_3 As String
            Dim ProposedSlug_Opt_4 As String

            ProposedSlug_Opt_1 = Request.QueryString("@Operator_ProposedSlug1_2").ToString
            ProposedSlug_Opt_2 = Request.QueryString("@Operator_ProposedSlug2_3").ToString
            ProposedSlug_Opt_3 = Request.QueryString("@Operator_ProposedSlug3_4").ToString
            ProposedSlug_Opt_4 = Request.QueryString("@Operator_ProposedSlug4_5").ToString

            '************************* Keywords **************************'

            Dim Keyword_1 As String
            Dim Keyword_2 As String
            Dim Keyword_3 As String
            Dim Keyword_4 As String
            Dim Keyword_5 As String

            Keyword_1 = Request.QueryString("@KeyWord1").ToString
            Keyword_2 = Request.QueryString("@KeyWord2").ToString
            Keyword_3 = Request.QueryString("@KeyWord3").ToString
            Keyword_4 = Request.QueryString("@KeyWord4").ToString
            Keyword_5 = Request.QueryString("@KeyWord5").ToString

            '*********************** Keywords Options **********************'

            Dim Keyword_Opt_1 As String
            Dim Keyword_Opt_2 As String
            Dim Keyword_Opt_3 As String
            Dim Keyword_Opt_4 As String

            Keyword_Opt_1 = Request.QueryString("@Operator_KeyWord1_2").ToString
            Keyword_Opt_2 = Request.QueryString("@Operator_KeyWord2_3").ToString
            Keyword_Opt_3 = Request.QueryString("@Operator_KeyWord3_4").ToString
            Keyword_Opt_4 = Request.QueryString("@Operator_KeyWord4_5").ToString

            '************************* Keytypes **************************'

            Dim Keytype_1 As String
            Dim Keytype_2 As String
            Dim Keytype_3 As String
            Dim Keytype_4 As String
            Dim Keytype_5 As String

            Keytype_1 = Request.QueryString("@KeyType1").ToString
            Keytype_2 = Request.QueryString("@KeyType2").ToString
            Keytype_3 = Request.QueryString("@KeyType3").ToString
            Keytype_4 = Request.QueryString("@KeyType4").ToString
            Keytype_5 = Request.QueryString("@KeyType5").ToString

            '*********************** Keytypes Options **********************'

            Dim Keytypes_Opt_1 As String
            Dim Keytypes_Opt_2 As String
            Dim Keytypes_Opt_3 As String
            Dim Keytypes_Opt_4 As String

            Keytypes_Opt_1 = Request.QueryString("@Operator_KeyType1_2").ToString
            Keytypes_Opt_2 = Request.QueryString("@Operator_KeyType2_3").ToString
            Keytypes_Opt_3 = Request.QueryString("@Operator_KeyType3_4").ToString
            Keytypes_Opt_4 = Request.QueryString("@Operator_KeyType4_5").ToString

            '**************************** From, To Date ********************'

            'Dim FromDate As String
            'FromDate = Request.QueryString("FromDate")

            'Dim ToDate As String
            'ToDate = Request.QueryString("ToDate")

            '**************************** Entry Date ********************'

            Dim EntryDate As String
            EntryDate = Request.QueryString("@EntryDate")

            Dim ToDate As String
            ToDate = Request.QueryString("@ToDate")

            '************************* Reporter Name ********************'

            Dim ReporterName As String
            ReporterName = Request.QueryString("@ReporterName")


            '************************* English Script **************************'

            Dim EnglishScript1 As String
            Dim EnglishScript2 As String
            Dim EnglishScript3 As String
            Dim EnglishScript4 As String
            Dim EnglishScript5 As String

            EnglishScript1 = Request.QueryString("@EnglishScript1").ToString
            EnglishScript2 = Request.QueryString("@EnglishScript2").ToString
            EnglishScript3 = Request.QueryString("@EnglishScript3").ToString
            EnglishScript4 = Request.QueryString("@EnglishScript4").ToString
            EnglishScript5 = Request.QueryString("@EnglishScript5").ToString

            '*********************** Eng Script Options **********************'

            Dim EnglishScript_Opt_1 As String
            Dim EnglishScript_Opt_2 As String
            Dim EnglishScript_Opt_3 As String
            Dim EnglishScript_Opt_4 As String

            EnglishScript_Opt_1 = Request.QueryString("@Operator_EnglishScript1_2").ToString
            EnglishScript_Opt_2 = Request.QueryString("@Operator_EnglishScript2_3").ToString
            EnglishScript_Opt_3 = Request.QueryString("@Operator_EnglishScript3_4").ToString
            EnglishScript_Opt_4 = Request.QueryString("@Operator_EnglishScript4_5").ToString

            '**************************** Tape Type ***********************'

            Dim TapeType1 As String

            TapeType1 = Request.QueryString("@TapeType1").ToString


            '********************* Store Procedure **********************'

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure

            'cmd.CommandText = "getSearchResultsNews_ExcelConvert2"


            If Request.QueryString("@UrduScript1").ToString = "%%" Then
                cmd.CommandText = "Proc_SearchEngine_News_WithOutUrduScript_Excel_11Dec13"
            Else
                cmd.CommandText = "Proc_SearchEngine_News_Excel_11Dec13"
            End If

            cmd.Connection = Con
            cmd.CommandTimeout = 0

            '****************************************************************--

            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@currentPageIndex", Data.SqlDbType.Int)
            currentPageIndex.Value = Convert.ToInt32(lblPageIndex.Text)

            Dim NoOfRecordOnPage As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@NoOfRecordOnPage", Data.SqlDbType.Int)
            NoOfRecordOnPage.Value = Pages

            Dim ContentType As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ContentType", Data.SqlDbType.Text)
            ContentType.Value = 29

            Dim IngnoreTape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreTape", Data.SqlDbType.Text)
            IngnoreTape1.Value = IngnoreTape

            Dim IngnoreReporterSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreReporterSlug", Data.SqlDbType.Text)
            IngnoreReporterSlug1.Value = IngnoreReporterSlug

            Dim IngnoreProposedSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreProposedSlug", Data.SqlDbType.Text)
            IngnoreProposedSlug1.Value = IngnoreProposedSlug

            Dim IngnoreKeyWords1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyWords", Data.SqlDbType.Text)
            IngnoreKeyWords1.Value = IngnoreKeyWords

            Dim IngnoreKeyTypes1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyTypes", Data.SqlDbType.Text)
            IngnoreKeyTypes1.Value = IngnoreKeyTypes

            Dim IgnoreDate1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreDate", Data.SqlDbType.Text)
            IgnoreDate1.Value = IgnoreDate

            Dim IgnoreReporter1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreReporter", Data.SqlDbType.Text)
            IgnoreReporter1.Value = IgnoreReporter

            Dim IgnoreEnglishScript1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreEnglishScript", Data.SqlDbType.Text)
            IgnoreEnglishScript1.Value = IgnoreEnglishScript

            Dim IgnoreTapeType1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreTapeType", Data.SqlDbType.Text)
            IgnoreTapeType1.Value = IgnoreTapeType

            '****************************************************************--

            Dim Tape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape1", Data.SqlDbType.Text)
            Tape1.Value = Tape_No_1

            Dim Tape2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape2", Data.SqlDbType.Text)
            Tape2.Value = Tape_No_2

            Dim Tape3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape3", Data.SqlDbType.Text)
            Tape3.Value = Tape_No_3

            Dim Tape4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape4", Data.SqlDbType.Text)
            Tape4.Value = Tape_No_4

            Dim Tape5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape5", Data.SqlDbType.Text)
            Tape5.Value = Tape_No_5

            Dim Tape_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape1_2", Data.SqlDbType.Text)
            Tape_opt1.Value = Tape_Opt_1

            Dim Tape_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape2_3", Data.SqlDbType.Text)
            Tape_opt2.Value = Tape_Opt_2

            Dim Tape_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape3_4", Data.SqlDbType.Text)
            Tape_opt3.Value = Tape_Opt_3

            Dim Tape_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape4_5", Data.SqlDbType.Text)
            Tape_opt4.Value = Tape_Opt_4

            Dim RS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug1", Data.SqlDbType.Text)
            RS_1.Value = ReporterSlug_1

            Dim RS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug2", Data.SqlDbType.Text)
            RS_2.Value = ReporterSlug_2

            Dim RS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug3", Data.SqlDbType.Text)
            RS_3.Value = ReporterSlug_3

            Dim RS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug4", Data.SqlDbType.Text)
            RS_4.Value = ReporterSlug_4

            Dim RS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug5", Data.SqlDbType.Text)
            RS_5.Value = ReporterSlug_5

            Dim RS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug1_2", Data.SqlDbType.Text)
            RS_Opt_1.Value = ReporterSlug_Opt_1

            Dim RS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug2_3", Data.SqlDbType.Text)
            RS_Opt_2.Value = ReporterSlug_Opt_2

            Dim RS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug3_4", Data.SqlDbType.Text)
            RS_Opt_3.Value = ReporterSlug_Opt_3

            Dim RS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug4_5", Data.SqlDbType.Text)
            RS_Opt_4.Value = ReporterSlug_Opt_4

            Dim PS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug1", Data.SqlDbType.Text)
            PS_1.Value = ProposedSlug_1

            Dim PS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug2", Data.SqlDbType.Text)
            PS_2.Value = ProposedSlug_2

            Dim PS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug3", Data.SqlDbType.Text)
            PS_3.Value = ProposedSlug_3

            Dim PS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug4", Data.SqlDbType.Text)
            PS_4.Value = ProposedSlug_4

            Dim PS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug5", Data.SqlDbType.Text)
            PS_5.Value = ProposedSlug_5

            Dim PS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug1_2", Data.SqlDbType.Text)
            PS_Opt_1.Value = ProposedSlug_Opt_1

            Dim PS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug2_3", Data.SqlDbType.Text)
            PS_Opt_2.Value = ProposedSlug_Opt_2

            Dim PS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug3_4", Data.SqlDbType.Text)
            PS_Opt_3.Value = ProposedSlug_Opt_3

            Dim PS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug4_5", Data.SqlDbType.Text)
            PS_Opt_4.Value = ProposedSlug_Opt_4

            Dim KW1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord1", Data.SqlDbType.Text)
            KW1.Value = Keyword_1

            Dim KW2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord2", Data.SqlDbType.Text)
            KW2.Value = Keyword_2

            Dim KW3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord3", Data.SqlDbType.Text)
            KW3.Value = Keyword_3

            Dim KW4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord4", Data.SqlDbType.Text)
            KW4.Value = Keyword_4

            Dim KW5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord5", Data.SqlDbType.Text)
            KW5.Value = Keyword_5

            Dim KW_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord1_2", Data.SqlDbType.Text)
            KW_opt1.Value = Keyword_Opt_1

            Dim KW_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord2_3", Data.SqlDbType.Text)
            KW_opt2.Value = Keyword_Opt_2

            Dim KW_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord3_4", Data.SqlDbType.Text)
            KW_opt3.Value = Keyword_Opt_3

            Dim KW_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord4_5", Data.SqlDbType.Text)
            KW_opt4.Value = Keyword_Opt_4

            Dim KT1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType1", Data.SqlDbType.Text)
            KT1.Value = Keytype_1

            Dim KT2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType2", Data.SqlDbType.Text)
            KT2.Value = Keytype_2

            Dim KT3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType3", Data.SqlDbType.Text)
            KT3.Value = Keytype_3

            Dim KT4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType4", Data.SqlDbType.Text)
            KT4.Value = Keytype_4

            Dim KT5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType5", Data.SqlDbType.Text)
            KT5.Value = Keytype_5

            Dim KT_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType1_2", Data.SqlDbType.Text)
            KT_opt1.Value = Keytypes_Opt_1

            Dim KT_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType2_3", Data.SqlDbType.Text)
            KT_opt2.Value = Keytypes_Opt_2

            Dim KT_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType3_4", Data.SqlDbType.Text)
            KT_opt3.Value = Keytypes_Opt_3

            Dim KT_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType4_5", Data.SqlDbType.Text)
            KT_opt4.Value = Keytypes_Opt_4

            Dim FrmDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EntryDate", Data.SqlDbType.Text)
            FrmDate.Value = EntryDate

            Dim To_Date As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
            To_Date.Value = ToDate

            Dim ReporterName1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReporterName", Data.SqlDbType.Text)
            ReporterName1.Value = ReporterName

            Dim EnglishScript11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript1", Data.SqlDbType.Text)
            EnglishScript11.Value = EnglishScript1

            Dim EnglishScript22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript2", Data.SqlDbType.Text)
            EnglishScript22.Value = EnglishScript2

            Dim EnglishScript33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript3", Data.SqlDbType.Text)
            EnglishScript33.Value = EnglishScript3

            Dim EnglishScript44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript4", Data.SqlDbType.Text)
            EnglishScript44.Value = EnglishScript4

            Dim EnglishScript55 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript5", Data.SqlDbType.Text)
            EnglishScript55.Value = EnglishScript5

            Dim EnglishScript_Opt_11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript1_2", Data.SqlDbType.Text)
            EnglishScript_Opt_11.Value = EnglishScript_Opt_1

            Dim EnglishScript_Opt_22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript2_3", Data.SqlDbType.Text)
            EnglishScript_Opt_22.Value = EnglishScript_Opt_2

            Dim EnglishScript_Opt_33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript3_4", Data.SqlDbType.Text)
            EnglishScript_Opt_33.Value = EnglishScript_Opt_3

            Dim EnglishScript_Opt_44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript4_5", Data.SqlDbType.Text)
            EnglishScript_Opt_44.Value = EnglishScript_Opt_4

            Dim TapeType01 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeType1", Data.SqlDbType.Text)
            TapeType01.Value = TapeType1

            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
            BaseStationID.Value = CInt(Request.QueryString("@BaseStationID").ToString)

            If Request.QueryString("@UrduScript1").ToString <> "%%" Then
                Dim p1s As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
                p1s.Value = Request.QueryString("@UrduScript1").ToString

                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
                p2.Value = Request.QueryString("@UrduScript2").ToString

                Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
                p3.Value = Request.QueryString("@UrduScript3").ToString

                Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
                p4.Value = Request.QueryString("@UrduScript4").ToString

                Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
                p5.Value = Request.QueryString("@UrduScript5").ToString

            End If

            Dim da As New System.Data.SqlClient.SqlDataAdapter
            DS = New DataSet

            da.SelectCommand = cmd
            da.Fill(DS)
            DS.AcceptChanges()
            GridView1.DataSource = DS.Tables(0).DefaultView
            GridView1.DataBind()
            'DS.Tables(0).Columns.Remove(4)
            ConvertToXML(DS) '''added by arsalan amin 14Sept2014 as Urdu was appear in Excel properly


        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub bttn_Next_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles bttn_Next.Click
        'SaveTape()
        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
        FillGrid()
    End Sub

    Protected Sub bttn_Prev_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles bttn_Prev.Click
        If lblPageIndex.Text <> 0 Then
            '   SaveTape()
            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
            FillGrid()
        End If
    End Sub

    Protected Sub dg_Search_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_Search.RowDataBound
        For Each tc As TableCell In e.Row.Cells
            tc.Attributes("style") = "border-color:#99BAE9"
        Next

        'If e.Row.RowType = DataControlRowType.DataRow Then
        '    e.Row.Attributes.Add("onmouseover", "MouseEvents(this, event)")
        '    e.Row.Attributes.Add("onmouseout", "MouseEvents(this, event)")
        'End If

        Try
            If (e.Row.Cells(14).Text <> "SpecialNote") Then
                If e.Row.Cells(14).Text <> "&nbsp;" Then
                    e.Row.ToolTip = e.Row.Cells(14).Text
                    e.Row.BackColor = Drawing.Color.LimeGreen
                End If
            End If
        Catch ex As Exception

        End Try

    End Sub

    Protected Sub ddlPageNo_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlPageNo.SelectedIndexChanged
        'SaveTape()
        lblPageIndex.Text = ddlPageNo.SelectedValue - 1
        FillGrid()
    End Sub

    Protected Sub dg_Search_Sorting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSortEventArgs) Handles dg_Search.Sorting
        FillGrid()
        Dim dv As New DataView(DS.Tables(0))
        dv.Sort = e.SortExpression()

        dg_Search.DataSource = dv

        'g_Search.DataBind()
        dg_Search.Columns(9).Visible = True
        dg_Search.Columns(10).Visible = True
        dg_Search.Columns(13).Visible = True
        dg_Search.DataBind()
        dg_Search.Columns(9).Visible = False
        dg_Search.Columns(10).Visible = False
        dg_Search.Columns(13).Visible = False

        For I As Integer = 0 To dv.Count - 1
            If dv.Item(I).Item(14).ToString() <> "" Then
                dg_Search.Rows(I).ToolTip = dv.Item(I).Item(14).ToString()
                dg_Search.Rows(I).BackColor = Drawing.Color.LimeGreen
            Else
                dg_Search.Rows(I).ToolTip = Nothing
                dg_Search.Rows(I).BackColor = Drawing.Color.Transparent
            End If
        Next

    End Sub

    Private Sub FillUrduScript()
        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)

        Dim Pages As String
        Pages = Request.QueryString("@No_of_Record_Per_Page")
        lblPages.Text = Pages

        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetSearch_ByUrduScript_withBaseStation"
        cmd.Connection = Con
        cmd.CommandTimeout = 0

        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
        p1.Value = "%" + Request.QueryString("@UrduScript1").ToString + "%"

        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
        p2.Value = "%" + Request.QueryString("@UrduScript2").ToString + "%"

        Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
        p3.Value = "%" + Request.QueryString("@UrduScript3").ToString + "%"

        Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
        p4.Value = "%" + Request.QueryString("@UrduScript4").ToString + "%"

        Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
        p5.Value = "%" + Request.QueryString("@UrduScript5").ToString + "%"


        Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
        p6.Value = Request.QueryString("@BaseStationID").ToString

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS = New DataSet
        da.SelectCommand = cmd
        da.Fill(DS, "Master")
        dg_Search.DataSource = DS.Tables("Master")
        dg_Search.Columns(10).Visible = True
        dg_Search.Columns(11).Visible = True
        dg_Search.Columns(15).Visible = True
        dg_Search.Columns(16).Visible = True
        dg_Search.DataBind()
        dg_Search.Columns(10).Visible = False
        dg_Search.Columns(11).Visible = False
        dg_Search.Columns(15).Visible = False
        dg_Search.Columns(16).Visible = False

        lbl_RecordCount.Text = "Total Records found :- " & (DS.Tables(1).Rows(0)(0))

        ''*********** TotalePages **************''
        Dim pq1 As Integer = Convert.ToInt32(lblPages.Text)
        Dim pq2 As Integer = (DS.Tables(1).Rows(0)(0))
        Dim Reminder As Integer = pq2 Mod pq1
        Dim pq3 As Integer = pq2 / pq1
        If Reminder = 0 Then
            lblTotalPages.Text = "Total Pages :- " & pq3
        Else
            pq3 = pq3 + 1
            lblTotalPages.Text = "Total Pages :- " & pq3
        End If

        lblTotal.Text = pq3

        GridEnable()

    End Sub

    Private Sub FillUrduScript_ExcelConvert()
        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)

        Dim Pages As String
        Pages = Request.QueryString("@No_of_Record_Per_Page")
        lblPages.Text = Pages

        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
        Dim DS As DataSet
        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        cmd.CommandText = "GetSearch_ByUrduScript_withBaseStation_Excel_11Dec13"
        cmd.Connection = Con
        cmd.CommandTimeout = 0

        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
        p1.Value = "%" + Request.QueryString("@UrduScript1").ToString + "%"

        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
        p2.Value = "%" + Request.QueryString("@UrduScript2").ToString + "%"

        Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
        p3.Value = "%" + Request.QueryString("@UrduScript3").ToString + "%"

        Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
        p4.Value = "%" + Request.QueryString("@UrduScript4").ToString + "%"

        Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
        p5.Value = "%" + Request.QueryString("@UrduScript5").ToString + "%"


        Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
        p6.Value = Request.QueryString("@BaseStationID").ToString

        Dim da As New System.Data.SqlClient.SqlDataAdapter
        DS = New DataSet

        da.SelectCommand = cmd
        da.Fill(DS)

        GridView1.DataSource = DS.Tables(0).DefaultView
        GridView1.DataBind()

        DS.Tables(0).Columns.Remove("IsAvailable")

        ConvertToXML(DS)

    End Sub

    Protected Sub dg_Search_RowCommand(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles dg_Search.RowCommand

        If e.CommandName = "Play" Then
            Dim TapeSlugID As String
            TapeSlugID = Convert.ToInt32(e.CommandArgument)
            '  Dim row As GridViewRow = dg_Search.Rows(index)
            ' SlugID = Server.HtmlDecode(row.Cells(4).Text)

            Dim dt As DataTable
            Dim ObjVideo As New BusinessFacade.SearchEngine()
            ObjVideo.TapeSlugID = TapeSlugID
            dt = ObjVideo.GetVideoDetails()

            If dt.Rows.Count > 0 Then
                Response.Write("<script type='text/javascript'>detailedresults=window.open('PlayVideo.aspx?FilePath=" + dt.Rows(0).Item(2).ToString + "');</script>")
            End If

        End If
    End Sub

    Protected Sub btnSaveTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSaveTape.Click
        Try
            SaveTape()
            lblErr.Text = "Tape Saved"

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub SaveTape()
        Dim dr As GridViewRow
        Dim cls As New BusinessFacade.SearchEngine
        For Each dr In dg_Search.Rows
            Dim chk As New CheckBox
            chk = dr.FindControl("CheckBox1")
            If chk.Checked = True Then
                cls.SaveTapeforIssueLater(dr.Cells(4).Text, Request.Cookies("userinfo")("username").ToString, "News")
            End If
        Next
    End Sub

    Protected Sub btnViewTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnViewTape.Click
        'Response.Write("<script type='text/javascript'>detailedresults=window.open('frmTapeAddforIssue.aspx','_blank','toolbar=yes, scrollbars=yes, resizable=yes, top=100, left=100, width=400, height=400');</script>")
        Response.Redirect("~/TapeManagement/IssueSelectedTape.aspx")
    End Sub

    Private Sub ConvertToXML(ByVal DS As DataSet) '''added by arsalan amin 14Sept2014 as Urdu was appear in Excel properly

        'DS.Tables(0).TableName = "NewsResult"
        'DS.WriteXml("Product.xml")
        Try


            DSxml = New DataSet

            'This is important, without this it will result in error
            'when you try to copy the datatable from one dataset to another

            DS.Tables(0).TableName = "NewTableName"

            'DS.Tables(0).Columns.Remove(4)
            'DS.Tables(0).Columns.Remove("IsAvailable")
            DSxml.Tables.Add(DS.Tables(0).Copy)
            DSxml.Tables(0).TableName = "tblXML"

            DSxml.Tables("tblXML").Columns.Remove("isAvailable")
            DSxml.Tables("tblXML").Columns.Remove("isDamage")
            DSxml.Tables("tblXML").Columns.Remove("isVideoExists")
            DSxml.Tables("tblXML").Columns.Remove("TapeSlugID")
            DSxml.Tables("tblXML").Columns.Remove("BaseStationID")
            DSxml.Tables("tblXML").Columns.Remove("SpecialNote")
            'DSxml.Tables("tblXML").Columns.Remove("EnglishScript")

            For ro As Integer = 0 To DSxml.Tables(0).Rows.Count - 1

                'If DSxml.Tables(0).Rows(ro).Item("EnglishScript") = " " Then
                '    MsgBox(DSxml.Tables(0).Rows(ro).Item("EnglishScript"))
                'End If

                DSxml.Tables(0).Rows(ro).Item("EnglishScript") = DSxml.Tables(0).Rows(ro).Item("EnglishScript").ToString.Trim
                'For co As Integer = 0 To DSxml.Tables(0).Columns.Count
                '    If DSxml.Tables(0).Columns(ro).ColumnName = "EnglishScript" Then
                '        If DSxml.Tables(0).Rows(ro).Item("EnglishScript") = "0" Then
                '            MsgBox(DSxml.Tables(0).Rows(ro).Item(co))
                '        End If
                '    End If
                'If DSxml.Tables(0).Rows(ro).Item(co) = "" Then  ''''<-- What to put here?
                '    MsgBox(DSxml.Tables(0).Rows(ro).Item(co))
                'End If
            Next
            'Next

            Dim SB As New StringBuilder
            Dim SW As New IO.StringWriter(SB)
            Dim filepath As String
            'filepath = Server.MapPath("~") + "\\NewsResults.xml"
            filepath = Server.MapPath("~") + "\\NewsResults.xls"
            Dim stream As New IO.MemoryStream
            Dim xtw As New System.Xml.XmlTextWriter(stream, System.Text.Encoding.Unicode)

            xtw.WriteProcessingInstruction("xml", "version='1.0'")
            'DS.WriteXml(SW, Data.XmlWriteMode.WriteSchema)
            DSxml.WriteXml(xtw)

            ''Create an XmlTextWriter for the FileStream.
            xtw.Flush()

            'Dim xmlDoc As New XmlDocument
            'xmlDoc.LoadXml(DSxml.GetXml)
            'xmlDoc.InnerXml.Replace(" xml:space=""preserve""", "")

            '''' Convert the memory stream to an array of bytes.
            Dim b() As Byte
            b = stream.ToArray()

            ''' Send the XML file to the web browser for download.
            Response.Clear()
            'Response.AppendHeader("Content-Disposition", "filename=MyExportedFile.xml")
            'HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment; filename={0}", "NewsResults.xml"))
            HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment; filename={0}", "NewsResults.xls"))
            Response.AppendHeader("Content-Length", b.Length.ToString())
            Response.ContentType = "application/octet-stream"
            Response.BinaryWrite(b)
            'xtw.Close()
            HttpContext.Current.Response.Flush()
            'HttpContext.Current.Response.[End]()


            'Dim xml As String = SB.ToString()

            ' ''Response.Clear()
            'Dim xmlDoc As New XmlDocument
            'xmlDoc.LoadXml(xtw.ToString)
            'xmlDoc.Save(Response.OutputStream)
            'Response.ContentEncoding = System.Text.Encoding.UTF8
            'Response.ContentType = "text/xml"
            ''Response.ContentType = "application/msword"
            ' ''Response.ContentType = "application/vnd.ms-excel"

            'Response.End()


            ''Create the FileStream to write with.
            'Dim strFilename As String
            'Dim FileUsername As String
            'FileUsername = Request.Cookies("userinfo")("username")
            ' ''strFilename = "C:\NewsResults.xml"
            ' '' strFilename = "C:\Users\<USER>\Documents\NewsResults.xml"
            'strFilename = "NewsResults.xml"


            ''Dim fs As New System.IO.FileStream(Response.OutputStream, System.IO.FileMode.Create)

            ''Create an XmlTextWriter for the FileStream.
            'Dim xtw As New System.Xml.XmlTextWriter(Response.OutputStream, System.Text.Encoding.Unicode)

            ''Add processing instructions to the beginning of the XML file, one 
            ''of which indicates a style sheet.
            'xtw.WriteProcessingInstruction("xml", "version='1.0'")

            ''xtw.WriteProcessingInstruction( _
            ''    "xml-stylesheet", "type='text/xsl' href='NewsResults.xsl'")
            ''Write the XML from the dataset to the file.

            'DS.WriteXml(xtw)
            'xtw.Close()

            ''HttpContext.Current.Response.Clear()
            'HttpContext.Current.Response.ContentType = "application/vnd.ms-excel"
            'HttpContext.Current.Response.Write(xtw)
            ''HttpContext.Current.Response.End()

            'HttpContext.Current.Response.Flush()
            'HttpContext.Current.Response.[End]()


            'Dim xmldd As New XmlDocument
            'xmldd.Load("C:\NewsResults.xml")
            'Dim node As XmlNode
            'Dim list As XmlNodeList
            'list = xmldd.SelectNodes("//General")

            'Dim app As New Microsoft.Office.Interop.Excel.Application
            'Dim exbook As Microsoft.Office.Interop.Excel.Workbook
            'Dim exsheet As Microsoft.Office.Interop.Excel.Worksheet

            'app.Workbooks.OpenXML("C:\NewsResults.xml")

            'exbook = app.Workbooks.Add
            'exsheet = exbook.Sheets(1)
            'Dim xx As Integer
            'For Each node In list
            '    xx += 1
            '    exsheet.Cells(xx, 1) = node.Attributes("File").Value
            '    exsheet.Cells(xx, 2) = node.Attributes("Table").Value
            'Next
            'exbook.SaveAs("C:\book1.xls")
            'app.Visible = True

            'MsgBox("News Results sucessfully tranfered in C:\NewsResults.xml ,Kindly open it in Excel")

            'MsgBox("Done")
        Catch ex As Exception
            Throw
        End Try
    End Sub

End Class
'Imports System.Data

'Partial Class SearchEngine_News_Results
'    Inherits System.Web.UI.Page
'    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
'    Dim ConString As String = System.Configuration.ConfigurationManager.AppSettings("ConnectionString")
'    Dim DS As DataSet
'    Dim DSxml As DataSet
'    Dim StrCommand As String
'    Protected Sub btnSaveTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSaveTape.Click
'        Try
'            SaveTape()
'            lblErr.Text = "Tape Saved"
'        Catch ex As Exception
'            Throw
'        End Try
'    End Sub

'    Protected Sub btnViewTape_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnViewTape.Click
'        'Response.Write("<script type='text/javascript'>detailedresults=window.open('frmTapeAddforIssue.aspx','_blank','toolbar=yes, scrollbars=yes, resizable=yes, top=100, left=100, width=400, height=400');</script>")
'        Response.Redirect("~/TapeManagement/IssueSelectedTape.aspx")
'    End Sub

'    Protected Sub bttn_Next_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles bttn_Next.Click
'        'SaveTape()
'        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
'        FillGrid()
'    End Sub

'    Protected Sub bttn_Prev_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles bttn_Prev.Click
'        If lblPageIndex.Text <> 0 Then
'            '   SaveTape()
'            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
'            FillGrid()
'        End If
'    End Sub

'    Protected Sub bttnExport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnExport.Click
'        If Request.QueryString("@IsUrduSearchOnly") = 1 Then
'            FillUrduScript_ExcelConvert()
'        Else
'            FillGrid_ExcelConvert()
'        End If

'        'GridViewExportUtil.Export("News.xls", Me.GridView1)
'    End Sub

'    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
'        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
'        FillGrid()
'    End Sub

'    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
'        If lblPageIndex.Text <> 0 Then
'            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
'            FillGrid()
'        End If
'    End Sub

'    Protected Sub ddlPageNo_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlPageNo.SelectedIndexChanged
'        'SaveTape()
'        lblPageIndex.Text = ddlPageNo.SelectedValue - 1
'        FillGrid()
'    End Sub

'    Protected Sub dg_Search_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_Search.PageIndexChanging
'        dg_Search.PageIndex = e.NewPageIndex()
'        lblPageIndex.Text = dg_Search.PageIndex
'        FillGrid()
'    End Sub

'    Protected Sub dg_Search_RowCommand(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles dg_Search.RowCommand

'        If e.CommandName = "Play" Then
'            Dim TapeSlugID As String
'            TapeSlugID = Convert.ToInt32(e.CommandArgument)
'            '  Dim row As GridViewRow = dg_Search.Rows(index)
'            ' SlugID = Server.HtmlDecode(row.Cells(4).Text)

'            Dim dt As DataTable
'            Dim ObjVideo As New BusinessFacade.SearchEngine()
'            ObjVideo.TapeSlugID = TapeSlugID
'            dt = ObjVideo.GetVideoDetails()

'            If dt.Rows.Count > 0 Then
'                Response.Write("<script type='text/javascript'>detailedresults=window.open('PlayVideo.aspx?FilePath=" + dt.Rows(0).Item(2).ToString + "');</script>")
'            End If

'        End If
'    End Sub

'    Protected Sub dg_Search_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_Search.RowDataBound
'        For Each tc As TableCell In e.Row.Cells
'            tc.Attributes("style") = "border-color:#99BAE9"
'        Next

'        'If e.Row.RowType = DataControlRowType.DataRow Then
'        '    e.Row.Attributes.Add("onmouseover", "MouseEvents(this, event)")
'        '    e.Row.Attributes.Add("onmouseout", "MouseEvents(this, event)")
'        'End If

'        Try
'            If (e.Row.Cells(14).Text <> "SpecialNote") Then
'                If e.Row.Cells(14).Text <> "&nbsp;" Then
'                    e.Row.ToolTip = e.Row.Cells(14).Text
'                    e.Row.BackColor = Drawing.Color.LimeGreen
'                End If
'            End If
'        Catch ex As Exception

'        End Try

'    End Sub

'    Protected Sub dg_Search_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Search.SelectedIndexChanged

'        Dim TapeNumber As String
'        Dim ReportSlug As String

'        Dim I As Integer
'        I = dg_Search.SelectedIndex.ToString
'        TapeNumber = dg_Search.Rows(I).Cells(4).Text
'        'ReportSlug = dg_Search.Rows(I).Cells(3).Text
'        ReportSlug = dg_Search.Rows(I).Cells(10).Text

'        Me.Session.Add("rowindex", I)

'        Response.Redirect("NewsTapeDetail.aspx?TapeNumber=" & TapeNumber & "&ReportSlug=" & ReportSlug)
'        'Response.Write("<script type='text/javascript'>detailedresults=window.open('NewsTapeDetail.aspx?TapeNumber=" & TapeNumber & "&ReportSlug=" & ReportSlug & "');</script>")

'    End Sub

'    Protected Sub dg_Search_Sorting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSortEventArgs) Handles dg_Search.Sorting
'        FillGrid()
'        Dim dv As New DataView(DS.Tables(0))
'        dv.Sort = e.SortExpression()

'        dg_Search.DataSource = dv

'        'g_Search.DataBind()
'        dg_Search.Columns(9).Visible = True
'        dg_Search.Columns(10).Visible = True
'        dg_Search.Columns(13).Visible = True
'        dg_Search.DataBind()
'        dg_Search.Columns(9).Visible = False
'        dg_Search.Columns(10).Visible = False
'        dg_Search.Columns(13).Visible = False

'        For I As Integer = 0 To dv.Count - 1
'            If dv.Item(I).Item(14).ToString() <> "" Then
'                dg_Search.Rows(I).ToolTip = dv.Item(I).Item(14).ToString()
'                dg_Search.Rows(I).BackColor = Drawing.Color.LimeGreen
'            Else
'                dg_Search.Rows(I).ToolTip = Nothing
'                dg_Search.Rows(I).BackColor = Drawing.Color.Transparent
'            End If
'        Next

'    End Sub

'    Protected Sub ImageButton1_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles ImageButton1.Click
'        lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) + 1
'        FillGrid()
'    End Sub

'    Protected Sub ImageButton2_Click(ByVal sender As Object, ByVal e As System.Web.UI.ImageClickEventArgs) Handles ImageButton2.Click
'        If lblPageIndex.Text <> 0 Then
'            lblPageIndex.Text = Convert.ToInt32(lblPageIndex.Text) - 1
'            FillGrid()
'        End If
'    End Sub

'    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

'        Response.Cache.SetCacheability(HttpCacheability.NoCache)

'        If Not Page.IsPostBack = True Then

'            If Request.QueryString("@IsUrduSearchOnly") = 1 Then
'                FillUrduScript()
'            Else
'                FillGrid()
'            End If

'            ''**************************************''
'            Dim I As Integer
'            For I = 1 To Convert.ToInt32(lblTotal.Text)
'                ddlPageNo.Items.Add(I)
'            Next

'        End If

'        GridRowselect()

'    End Sub
'    Private Sub ConvertToXML(ByVal DS As DataSet) '''added by arsalan amin 14Sept2014 as Urdu was appear in Excel properly

'        'DS.Tables(0).TableName = "NewsResult"
'        'DS.WriteXml("Product.xml")
'        Try

'            DSxml = New DataSet

'            'This is important, without this it will result in error
'            'when you try to copy the datatable from one dataset to another

'            DS.Tables(0).TableName = "NewTableName"

'            'DS.Tables(0).Columns.Remove(4)
'            'DS.Tables(0).Columns.Remove("IsAvailable")
'            DSxml.Tables.Add(DS.Tables(0).Copy)
'            DSxml.Tables(0).TableName = "tblXML"

'            DSxml.Tables("tblXML").Columns.Remove("isAvailable")
'            DSxml.Tables("tblXML").Columns.Remove("isDamage")
'            DSxml.Tables("tblXML").Columns.Remove("isVideoExists")
'            DSxml.Tables("tblXML").Columns.Remove("TapeSlugID")
'            DSxml.Tables("tblXML").Columns.Remove("BaseStationID")
'            DSxml.Tables("tblXML").Columns.Remove("SpecialNote")
'            'DSxml.Tables("tblXML").Columns.Remove("EnglishScript")

'            For ro As Integer = 0 To DSxml.Tables(0).Rows.Count - 1

'                'If DSxml.Tables(0).Rows(ro).Item("EnglishScript") = " " Then
'                '    MsgBox(DSxml.Tables(0).Rows(ro).Item("EnglishScript"))
'                'End If

'                DSxml.Tables(0).Rows(ro).Item("EnglishScript") = DSxml.Tables(0).Rows(ro).Item("EnglishScript").ToString.Trim
'                'For co As Integer = 0 To DSxml.Tables(0).Columns.Count
'                '    If DSxml.Tables(0).Columns(ro).ColumnName = "EnglishScript" Then
'                '        If DSxml.Tables(0).Rows(ro).Item("EnglishScript") = "0" Then
'                '            MsgBox(DSxml.Tables(0).Rows(ro).Item(co))
'                '        End If
'                '    End If
'                'If DSxml.Tables(0).Rows(ro).Item(co) = "" Then  ''''<-- What to put here?
'                '    MsgBox(DSxml.Tables(0).Rows(ro).Item(co))
'                'End If
'            Next
'            'Next

'            Dim SB As New StringBuilder
'            Dim SW As New IO.StringWriter(SB)
'            Dim filepath As String
'            'filepath = Server.MapPath("~") + "\\NewsResults.xml"
'            filepath = Server.MapPath("~") + "\\NewsResults.xls"
'            Dim stream As New IO.MemoryStream
'            Dim xtw As New System.Xml.XmlTextWriter(stream, System.Text.Encoding.Unicode)

'            xtw.WriteProcessingInstruction("xml", "version='1.0'")
'            'DS.WriteXml(SW, Data.XmlWriteMode.WriteSchema)
'            DSxml.WriteXml(xtw)

'            ''Create an XmlTextWriter for the FileStream.
'            xtw.Flush()

'            'Dim xmlDoc As New XmlDocument
'            'xmlDoc.LoadXml(DSxml.GetXml)
'            'xmlDoc.InnerXml.Replace(" xml:space=""preserve""", "")

'            '''' Convert the memory stream to an array of bytes.
'            Dim b() As Byte
'            b = stream.ToArray()

'            ''' Send the XML file to the web browser for download.
'            Response.Clear()
'            'Response.AppendHeader("Content-Disposition", "filename=MyExportedFile.xml")
'            'HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment; filename={0}", "NewsResults.xml"))
'            HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment; filename={0}", "NewsResults.xls"))
'            Response.AppendHeader("Content-Length", b.Length.ToString())
'            Response.ContentType = "application/octet-stream"
'            Response.BinaryWrite(b)
'            'xtw.Close()
'            HttpContext.Current.Response.Flush()
'            'HttpContext.Current.Response.[End]()

'            'Dim xml As String = SB.ToString()

'            ' ''Response.Clear()
'            'Dim xmlDoc As New XmlDocument
'            'xmlDoc.LoadXml(xtw.ToString)
'            'xmlDoc.Save(Response.OutputStream)
'            'Response.ContentEncoding = System.Text.Encoding.UTF8
'            'Response.ContentType = "text/xml"
'            ''Response.ContentType = "application/msword"
'            ' ''Response.ContentType = "application/vnd.ms-excel"

'            'Response.End()

'            ''Create the FileStream to write with.
'            'Dim strFilename As String
'            'Dim FileUsername As String
'            'FileUsername = Request.Cookies("userinfo")("username")
'            ' ''strFilename = "C:\NewsResults.xml"
'            ' '' strFilename = "C:\Users\<USER>\Documents\NewsResults.xml"
'            'strFilename = "NewsResults.xml"

'            ''Dim fs As New System.IO.FileStream(Response.OutputStream, System.IO.FileMode.Create)

'            ''Create an XmlTextWriter for the FileStream.
'            'Dim xtw As New System.Xml.XmlTextWriter(Response.OutputStream, System.Text.Encoding.Unicode)

'            ''Add processing instructions to the beginning of the XML file, one
'            ''of which indicates a style sheet.
'            'xtw.WriteProcessingInstruction("xml", "version='1.0'")

'            ''xtw.WriteProcessingInstruction( _
'            ''    "xml-stylesheet", "type='text/xsl' href='NewsResults.xsl'")
'            ''Write the XML from the dataset to the file.

'            'DS.WriteXml(xtw)
'            'xtw.Close()

'            ''HttpContext.Current.Response.Clear()
'            'HttpContext.Current.Response.ContentType = "application/vnd.ms-excel"
'            'HttpContext.Current.Response.Write(xtw)
'            ''HttpContext.Current.Response.End()

'            'HttpContext.Current.Response.Flush()
'            'HttpContext.Current.Response.[End]()

'            'Dim xmldd As New XmlDocument
'            'xmldd.Load("C:\NewsResults.xml")
'            'Dim node As XmlNode
'            'Dim list As XmlNodeList
'            'list = xmldd.SelectNodes("//General")

'            'Dim app As New Microsoft.Office.Interop.Excel.Application
'            'Dim exbook As Microsoft.Office.Interop.Excel.Workbook
'            'Dim exsheet As Microsoft.Office.Interop.Excel.Worksheet

'            'app.Workbooks.OpenXML("C:\NewsResults.xml")

'            'exbook = app.Workbooks.Add
'            'exsheet = exbook.Sheets(1)
'            'Dim xx As Integer
'            'For Each node In list
'            '    xx += 1
'            '    exsheet.Cells(xx, 1) = node.Attributes("File").Value
'            '    exsheet.Cells(xx, 2) = node.Attributes("Table").Value
'            'Next
'            'exbook.SaveAs("C:\book1.xls")
'            'app.Visible = True

'            'MsgBox("News Results sucessfully tranfered in C:\NewsResults.xml ,Kindly open it in Excel")

'            'MsgBox("Done")
'        Catch ex As Exception
'            Throw
'        End Try
'    End Sub

'    Private Sub FillGrid()
'        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        Try

'            '************************* Tape Number ****************************'

'            Dim Pages As String
'            Pages = Request.QueryString("@No_of_Record_Per_Page")
'            lblPages.Text = Pages

'            Dim ContentTypeID As String
'            ContentTypeID = Request.QueryString("@ContentType")

'            Dim IngnoreTape As String
'            IngnoreTape = Request.QueryString("@IngnoreTape")

'            Dim IngnoreReporterSlug As String
'            IngnoreReporterSlug = Request.QueryString("@IngnoreReporterSlug")

'            Dim IngnoreProposedSlug As String
'            IngnoreProposedSlug = Request.QueryString("@IngnoreProposedSlug")

'            Dim IngnoreKeyWords As String
'            IngnoreKeyWords = Request.QueryString("@IngnoreKeyWords")

'            Dim IngnoreKeyTypes As String
'            IngnoreKeyTypes = Request.QueryString("@IngnoreKeyTypes")

'            Dim IgnoreDate As String
'            IgnoreDate = Request.QueryString("@IgnoreDate")

'            Dim IgnoreReporter As String
'            IgnoreReporter = Request.QueryString("@IgnoreReporter")

'            Dim IgnoreEnglishScript As String
'            IgnoreEnglishScript = Request.QueryString("@IgnoreEnglishScript")

'            Dim IgnoreTapeType As String
'            IgnoreTapeType = Request.QueryString("@IgnoreTapeType")

'            Dim Tape_No_1 As String
'            Dim Tape_No_2 As String
'            Dim Tape_No_3 As String
'            Dim Tape_No_4 As String
'            Dim Tape_No_5 As String

'            Tape_No_1 = Request.QueryString("@Tape1").ToString
'            Tape_No_2 = Request.QueryString("@Tape2").ToString
'            Tape_No_3 = Request.QueryString("@Tape3").ToString
'            Tape_No_4 = Request.QueryString("@Tape4").ToString
'            Tape_No_5 = Request.QueryString("@Tape5").ToString

'            '*********************** Tape Number Options **********************'

'            Dim Tape_Opt_1 As String
'            Dim Tape_Opt_2 As String
'            Dim Tape_Opt_3 As String
'            Dim Tape_Opt_4 As String

'            Tape_Opt_1 = Request.QueryString("@Operator_Tape1_2").ToString
'            Tape_Opt_2 = Request.QueryString("@Operator_Tape2_3").ToString
'            Tape_Opt_3 = Request.QueryString("@Operator_Tape3_4").ToString
'            Tape_Opt_4 = Request.QueryString("@Operator_Tape4_5").ToString

'            '*********************** Reporter Slug ***************************'

'            Dim ReporterSlug_1 As String
'            Dim ReporterSlug_2 As String
'            Dim ReporterSlug_3 As String
'            Dim ReporterSlug_4 As String
'            Dim ReporterSlug_5 As String

'            ReporterSlug_1 = Request.QueryString("@RptSlug1").ToString
'            ReporterSlug_2 = Request.QueryString("@RptSlug2").ToString
'            ReporterSlug_3 = Request.QueryString("@RptSlug3").ToString
'            ReporterSlug_4 = Request.QueryString("@RptSlug4").ToString
'            ReporterSlug_5 = Request.QueryString("@RptSlug5").ToString

'            '******************** Reporter Slug Options *********************'

'            Dim ReporterSlug_Opt_1 As String
'            Dim ReporterSlug_Opt_2 As String
'            Dim ReporterSlug_Opt_3 As String
'            Dim ReporterSlug_Opt_4 As String

'            ReporterSlug_Opt_1 = Request.QueryString("@Operator_RptSlug1_2").ToString
'            ReporterSlug_Opt_2 = Request.QueryString("@Operator_RptSlug2_3").ToString
'            ReporterSlug_Opt_3 = Request.QueryString("@Operator_RptSlug3_4").ToString
'            ReporterSlug_Opt_4 = Request.QueryString("@Operator_RptSlug4_5").ToString

'            '********************* Proposed Slug **************************'

'            Dim ProposedSlug_1 As String
'            Dim ProposedSlug_2 As String
'            Dim ProposedSlug_3 As String
'            Dim ProposedSlug_4 As String
'            Dim ProposedSlug_5 As String

'            ProposedSlug_1 = Request.QueryString("@ProposedSlug1").ToString
'            ProposedSlug_2 = Request.QueryString("@ProposedSlug2").ToString
'            ProposedSlug_3 = Request.QueryString("@ProposedSlug3").ToString
'            ProposedSlug_4 = Request.QueryString("@ProposedSlug4").ToString
'            ProposedSlug_5 = Request.QueryString("@ProposedSlug5").ToString

'            '******************* Proposed Slug Options ********************'

'            Dim ProposedSlug_Opt_1 As String
'            Dim ProposedSlug_Opt_2 As String
'            Dim ProposedSlug_Opt_3 As String
'            Dim ProposedSlug_Opt_4 As String

'            ProposedSlug_Opt_1 = Request.QueryString("@Operator_ProposedSlug1_2").ToString
'            ProposedSlug_Opt_2 = Request.QueryString("@Operator_ProposedSlug2_3").ToString
'            ProposedSlug_Opt_3 = Request.QueryString("@Operator_ProposedSlug3_4").ToString
'            ProposedSlug_Opt_4 = Request.QueryString("@Operator_ProposedSlug4_5").ToString

'            '************************* Keywords **************************'

'            Dim Keyword_1 As String
'            Dim Keyword_2 As String
'            Dim Keyword_3 As String
'            Dim Keyword_4 As String
'            Dim Keyword_5 As String

'            Keyword_1 = Request.QueryString("@KeyWord1").ToString
'            Keyword_2 = Request.QueryString("@KeyWord2").ToString
'            Keyword_3 = Request.QueryString("@KeyWord3").ToString
'            Keyword_4 = Request.QueryString("@KeyWord4").ToString
'            Keyword_5 = Request.QueryString("@KeyWord5").ToString

'            '*********************** Keywords Options **********************'

'            Dim Keyword_Opt_1 As String
'            Dim Keyword_Opt_2 As String
'            Dim Keyword_Opt_3 As String
'            Dim Keyword_Opt_4 As String

'            Keyword_Opt_1 = Request.QueryString("@Operator_KeyWord1_2").ToString
'            Keyword_Opt_2 = Request.QueryString("@Operator_KeyWord2_3").ToString
'            Keyword_Opt_3 = Request.QueryString("@Operator_KeyWord3_4").ToString
'            Keyword_Opt_4 = Request.QueryString("@Operator_KeyWord4_5").ToString

'            '************************* Keytypes **************************'

'            Dim Keytype_1 As String
'            Dim Keytype_2 As String
'            Dim Keytype_3 As String
'            Dim Keytype_4 As String
'            Dim Keytype_5 As String

'            Keytype_1 = Request.QueryString("@KeyType1").ToString
'            Keytype_2 = Request.QueryString("@KeyType2").ToString
'            Keytype_3 = Request.QueryString("@KeyType3").ToString
'            Keytype_4 = Request.QueryString("@KeyType4").ToString
'            Keytype_5 = Request.QueryString("@KeyType5").ToString

'            '*********************** Keytypes Options **********************'

'            Dim Keytypes_Opt_1 As String
'            Dim Keytypes_Opt_2 As String
'            Dim Keytypes_Opt_3 As String
'            Dim Keytypes_Opt_4 As String

'            Keytypes_Opt_1 = Request.QueryString("@Operator_KeyType1_2").ToString
'            Keytypes_Opt_2 = Request.QueryString("@Operator_KeyType2_3").ToString
'            Keytypes_Opt_3 = Request.QueryString("@Operator_KeyType3_4").ToString
'            Keytypes_Opt_4 = Request.QueryString("@Operator_KeyType4_5").ToString

'            '**************************** Entry Date ********************'

'            Dim EntryDate As String
'            EntryDate = Request.QueryString("@EntryDate")

'            Dim ToDate As String
'            ToDate = Request.QueryString("@ToDate")

'            '************************* Reporter Name ********************'

'            Dim ReporterName As String
'            ReporterName = Request.QueryString("@ReporterName")

'            '************************* English Script **************************'

'            Dim EnglishScript1 As String
'            Dim EnglishScript2 As String
'            Dim EnglishScript3 As String
'            Dim EnglishScript4 As String
'            Dim EnglishScript5 As String

'            EnglishScript1 = Request.QueryString("@EnglishScript1").ToString
'            EnglishScript2 = Request.QueryString("@EnglishScript2").ToString
'            EnglishScript3 = Request.QueryString("@EnglishScript3").ToString
'            EnglishScript4 = Request.QueryString("@EnglishScript4").ToString
'            EnglishScript5 = Request.QueryString("@EnglishScript5").ToString

'            '*********************** Eng Script Options **********************'

'            Dim EnglishScript_Opt_1 As String
'            Dim EnglishScript_Opt_2 As String
'            Dim EnglishScript_Opt_3 As String
'            Dim EnglishScript_Opt_4 As String

'            EnglishScript_Opt_1 = Request.QueryString("@Operator_EnglishScript1_2").ToString
'            EnglishScript_Opt_2 = Request.QueryString("@Operator_EnglishScript2_3").ToString
'            EnglishScript_Opt_3 = Request.QueryString("@Operator_EnglishScript3_4").ToString
'            EnglishScript_Opt_4 = Request.QueryString("@Operator_EnglishScript4_5").ToString

'            '**************************** Tape Type ***********************'

'            Dim TapeType1 As String
'            TapeType1 = Request.QueryString("@TapeType1").ToString

'            '********************* Store Procedure **********************'

'            Dim cmd As New System.Data.SqlClient.SqlCommand
'            cmd.CommandType = Data.CommandType.StoredProcedure

'            If Request.QueryString("@UrduScript1").ToString = "%%" Then
'                'cmd.CommandText = "getSearchResultsNews_With2Footages"
'                cmd.CommandText = "Proc_SearchEngine_News_WithOutUrduScript"
'            Else
'                cmd.CommandText = "Proc_SearchEngine_News"
'            End If

'            cmd.Connection = Con
'            cmd.CommandTimeout = 0

'            '****************************************************************--

'            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@currentPageIndex", Data.SqlDbType.Int)
'            currentPageIndex.Value = Convert.ToInt32(lblPageIndex.Text)

'            Dim NoOfRecordOnPage As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@NoOfRecordOnPage", Data.SqlDbType.Int)
'            NoOfRecordOnPage.Value = Pages

'            Dim ContentType As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ContentType", Data.SqlDbType.Text)
'            ContentType.Value = 29

'            Dim IngnoreTape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreTape", Data.SqlDbType.Text)
'            IngnoreTape1.Value = IngnoreTape

'            Dim IngnoreReporterSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreReporterSlug", Data.SqlDbType.Text)
'            IngnoreReporterSlug1.Value = IngnoreReporterSlug

'            Dim IngnoreProposedSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreProposedSlug", Data.SqlDbType.Text)
'            IngnoreProposedSlug1.Value = IngnoreProposedSlug

'            Dim IngnoreKeyWords1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyWords", Data.SqlDbType.Text)
'            IngnoreKeyWords1.Value = IngnoreKeyWords

'            Dim IngnoreKeyTypes1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyTypes", Data.SqlDbType.Text)
'            IngnoreKeyTypes1.Value = IngnoreKeyTypes

'            Dim IgnoreDate1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreDate", Data.SqlDbType.Text)
'            IgnoreDate1.Value = IgnoreDate

'            Dim IgnoreReporter1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreReporter", Data.SqlDbType.Text)
'            IgnoreReporter1.Value = IgnoreReporter

'            Dim IgnoreEnglishScript1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreEnglishScript", Data.SqlDbType.Text)
'            IgnoreEnglishScript1.Value = IgnoreEnglishScript

'            Dim IgnoreTapeType1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreTapeType", Data.SqlDbType.Text)
'            IgnoreTapeType1.Value = IgnoreTapeType

'            '****************************************************************--

'            Dim Tape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape1", Data.SqlDbType.Text)
'            Tape1.Value = Tape_No_1

'            Dim Tape2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape2", Data.SqlDbType.Text)
'            Tape2.Value = Tape_No_2

'            Dim Tape3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape3", Data.SqlDbType.Text)
'            Tape3.Value = Tape_No_3

'            Dim Tape4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape4", Data.SqlDbType.Text)
'            Tape4.Value = Tape_No_4

'            Dim Tape5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape5", Data.SqlDbType.Text)
'            Tape5.Value = Tape_No_5

'            Dim Tape_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape1_2", Data.SqlDbType.Text)
'            Tape_opt1.Value = Tape_Opt_1

'            Dim Tape_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape2_3", Data.SqlDbType.Text)
'            Tape_opt2.Value = Tape_Opt_2

'            Dim Tape_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape3_4", Data.SqlDbType.Text)
'            Tape_opt3.Value = Tape_Opt_3

'            Dim Tape_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape4_5", Data.SqlDbType.Text)
'            Tape_opt4.Value = Tape_Opt_4

'            Dim RS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug1", Data.SqlDbType.Text)
'            RS_1.Value = ReporterSlug_1

'            Dim RS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug2", Data.SqlDbType.Text)
'            RS_2.Value = ReporterSlug_2

'            Dim RS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug3", Data.SqlDbType.Text)
'            RS_3.Value = ReporterSlug_3

'            Dim RS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug4", Data.SqlDbType.Text)
'            RS_4.Value = ReporterSlug_4

'            Dim RS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug5", Data.SqlDbType.Text)
'            RS_5.Value = ReporterSlug_5

'            Dim RS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug1_2", Data.SqlDbType.Text)
'            RS_Opt_1.Value = ReporterSlug_Opt_1

'            Dim RS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug2_3", Data.SqlDbType.Text)
'            RS_Opt_2.Value = ReporterSlug_Opt_2

'            Dim RS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug3_4", Data.SqlDbType.Text)
'            RS_Opt_3.Value = ReporterSlug_Opt_3

'            Dim RS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug4_5", Data.SqlDbType.Text)
'            RS_Opt_4.Value = ReporterSlug_Opt_4

'            Dim PS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug1", Data.SqlDbType.Text)
'            PS_1.Value = ProposedSlug_1

'            Dim PS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug2", Data.SqlDbType.Text)
'            PS_2.Value = ProposedSlug_2

'            Dim PS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug3", Data.SqlDbType.Text)
'            PS_3.Value = ProposedSlug_3

'            Dim PS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug4", Data.SqlDbType.Text)
'            PS_4.Value = ProposedSlug_4

'            Dim PS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug5", Data.SqlDbType.Text)
'            PS_5.Value = ProposedSlug_5

'            Dim PS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug1_2", Data.SqlDbType.Text)
'            PS_Opt_1.Value = ProposedSlug_Opt_1

'            Dim PS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug2_3", Data.SqlDbType.Text)
'            PS_Opt_2.Value = ProposedSlug_Opt_2

'            Dim PS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug3_4", Data.SqlDbType.Text)
'            PS_Opt_3.Value = ProposedSlug_Opt_3

'            Dim PS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug4_5", Data.SqlDbType.Text)
'            PS_Opt_4.Value = ProposedSlug_Opt_4

'            Dim KW1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord1", Data.SqlDbType.Text)
'            KW1.Value = Keyword_1

'            Dim KW2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord2", Data.SqlDbType.Text)
'            KW2.Value = Keyword_2

'            Dim KW3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord3", Data.SqlDbType.Text)
'            KW3.Value = Keyword_3

'            Dim KW4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord4", Data.SqlDbType.Text)
'            KW4.Value = Keyword_4

'            Dim KW5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord5", Data.SqlDbType.Text)
'            KW5.Value = Keyword_5

'            Dim KW_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord1_2", Data.SqlDbType.Text)
'            KW_opt1.Value = Keyword_Opt_1

'            Dim KW_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord2_3", Data.SqlDbType.Text)
'            KW_opt2.Value = Keyword_Opt_2

'            Dim KW_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord3_4", Data.SqlDbType.Text)
'            KW_opt3.Value = Keyword_Opt_3

'            Dim KW_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord4_5", Data.SqlDbType.Text)
'            KW_opt4.Value = Keyword_Opt_4

'            Dim KT1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType1", Data.SqlDbType.Text)
'            KT1.Value = Keytype_1

'            Dim KT2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType2", Data.SqlDbType.Text)
'            KT2.Value = Keytype_2

'            Dim KT3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType3", Data.SqlDbType.Text)
'            KT3.Value = Keytype_3

'            Dim KT4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType4", Data.SqlDbType.Text)
'            KT4.Value = Keytype_4

'            Dim KT5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType5", Data.SqlDbType.Text)
'            KT5.Value = Keytype_5

'            Dim KT_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType1_2", Data.SqlDbType.Text)
'            KT_opt1.Value = Keytypes_Opt_1

'            Dim KT_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType2_3", Data.SqlDbType.Text)
'            KT_opt2.Value = Keytypes_Opt_2

'            Dim KT_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType3_4", Data.SqlDbType.Text)
'            KT_opt3.Value = Keytypes_Opt_3

'            Dim KT_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType4_5", Data.SqlDbType.Text)
'            KT_opt4.Value = Keytypes_Opt_4

'            Dim FrmDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EntryDate", Data.SqlDbType.Text)
'            FrmDate.Value = EntryDate

'            Dim To_Date As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
'            To_Date.Value = ToDate

'            Dim ReporterName1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReporterName", Data.SqlDbType.Text)
'            ReporterName1.Value = ReporterName

'            Dim EnglishScript11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript1", Data.SqlDbType.Text)
'            EnglishScript11.Value = EnglishScript1

'            Dim EnglishScript22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript2", Data.SqlDbType.Text)
'            EnglishScript22.Value = EnglishScript2

'            Dim EnglishScript33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript3", Data.SqlDbType.Text)
'            EnglishScript33.Value = EnglishScript3

'            Dim EnglishScript44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript4", Data.SqlDbType.Text)
'            EnglishScript44.Value = EnglishScript4

'            Dim EnglishScript55 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript5", Data.SqlDbType.Text)
'            EnglishScript55.Value = EnglishScript5

'            Dim EnglishScript_Opt_11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript1_2", Data.SqlDbType.Text)
'            EnglishScript_Opt_11.Value = EnglishScript_Opt_1

'            Dim EnglishScript_Opt_22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript2_3", Data.SqlDbType.Text)
'            EnglishScript_Opt_22.Value = EnglishScript_Opt_2

'            Dim EnglishScript_Opt_33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript3_4", Data.SqlDbType.Text)
'            EnglishScript_Opt_33.Value = EnglishScript_Opt_3

'            Dim EnglishScript_Opt_44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript4_5", Data.SqlDbType.Text)
'            EnglishScript_Opt_44.Value = EnglishScript_Opt_4

'            Dim TapeType01 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeType1", Data.SqlDbType.Text)
'            TapeType01.Value = TapeType1

'            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
'            BaseStationID.Value = CInt(Request.QueryString("@BaseStationID").ToString)

'            If Request.QueryString("@UrduScript1").ToString <> "%%" Then
'                Dim p1s As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
'                p1s.Value = Request.QueryString("@UrduScript1").ToString

'                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
'                p2.Value = Request.QueryString("@UrduScript2").ToString

'                Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
'                p3.Value = Request.QueryString("@UrduScript3").ToString

'                Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
'                p4.Value = Request.QueryString("@UrduScript4").ToString

'                Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
'                p5.Value = Request.QueryString("@UrduScript5").ToString

'            End If

'            Dim da As New SqlClient.SqlDataAdapter
'            DS = New DataSet

'            da.SelectCommand = cmd
'            da.Fill(DS)

'            dg_Search.DataSource = DS.Tables(0).DefaultView
'            dg_Search.Columns(9).Visible = True
'            dg_Search.Columns(10).Visible = True
'            dg_Search.Columns(13).Visible = True
'            dg_Search.Columns(15).Visible = True
'            dg_Search.Columns(16).Visible = True
'            dg_Search.DataBind()
'            dg_Search.Columns(9).Visible = False
'            dg_Search.Columns(10).Visible = False
'            dg_Search.Columns(13).Visible = False
'            dg_Search.Columns(15).Visible = False
'            dg_Search.Columns(16).Visible = False

'            lbl_RecordCount.Text = "Total Records found :- " & (DS.Tables(1).Rows(0)(0))

'            ''*********** TotalePages **************''
'            Dim pq1 As Integer = Convert.ToInt32(lblPages.Text)
'            Dim pq2 As Integer = (DS.Tables(1).Rows(0)(0))
'            Dim Reminder As Integer = pq2 Mod pq1
'            Dim pq3 As Integer = pq2 / pq1

'            If Reminder = 0 Then
'                lblTotalPages.Text = "Total Pages :- " & pq3
'            Else
'                pq3 = pq3 + 1
'                lblTotalPages.Text = "Total Pages :- " & pq3
'            End If

'            lblTotal.Text = pq3

'            ''********************************************''
'            ''*********** Fill High Light Controls *******''
'            ''********************************************''

'            RS1.Text = ReporterSlug_1
'            RS2.Text = ReporterSlug_2
'            RS3.Text = ReporterSlug_3
'            RS4.Text = ReporterSlug_4
'            RS5.Text = ReporterSlug_5

'            ENDATE.Text = EntryDate
'            Tape.Text = Tape_No_1
'            Rpt.Text = ReporterName
'            T_type.Text = TapeType1

'            GridRowsColor()
'            GridEnable()
'        Catch ex As Exception
'            Throw
'        End Try

'    End Sub

'    Private Sub FillGrid_ExcelConvert()
'        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        Try

'            '************************* Tape Number ****************************'

'            Dim Pages As String
'            Pages = Request.QueryString("@No_of_Record_Per_Page")
'            lblPages.Text = Pages

'            Dim ContentTypeID As String
'            ContentTypeID = Request.QueryString("@ContentType")

'            Dim IngnoreTape As String
'            IngnoreTape = Request.QueryString("@IngnoreTape")

'            Dim IngnoreReporterSlug As String
'            IngnoreReporterSlug = Request.QueryString("@IngnoreReporterSlug")

'            Dim IngnoreProposedSlug As String
'            IngnoreProposedSlug = Request.QueryString("@IngnoreProposedSlug")

'            Dim IngnoreKeyWords As String
'            IngnoreKeyWords = Request.QueryString("@IngnoreKeyWords")

'            Dim IngnoreKeyTypes As String
'            IngnoreKeyTypes = Request.QueryString("@IngnoreKeyTypes")

'            'Dim IgnoreFromDate As String
'            'IgnoreFromDate = Request.QueryString("@IgnoreFromDate")

'            'Dim IgnoreToDate As String
'            'IgnoreToDate = Request.QueryString("@IgnoreToDate")

'            Dim IgnoreDate As String
'            IgnoreDate = Request.QueryString("@IgnoreDate")

'            Dim IgnoreReporter As String
'            IgnoreReporter = Request.QueryString("@IgnoreReporter")

'            Dim IgnoreEnglishScript As String
'            IgnoreEnglishScript = Request.QueryString("@IgnoreEnglishScript")

'            Dim IgnoreTapeType As String
'            IgnoreTapeType = Request.QueryString("@IgnoreTapeType")

'            Dim Tape_No_1 As String
'            Dim Tape_No_2 As String
'            Dim Tape_No_3 As String
'            Dim Tape_No_4 As String
'            Dim Tape_No_5 As String

'            Tape_No_1 = Request.QueryString("@Tape1").ToString
'            Tape_No_2 = Request.QueryString("@Tape2").ToString
'            Tape_No_3 = Request.QueryString("@Tape3").ToString
'            Tape_No_4 = Request.QueryString("@Tape4").ToString
'            Tape_No_5 = Request.QueryString("@Tape5").ToString

'            '*********************** Tape Number Options **********************'

'            Dim Tape_Opt_1 As String
'            Dim Tape_Opt_2 As String
'            Dim Tape_Opt_3 As String
'            Dim Tape_Opt_4 As String

'            Tape_Opt_1 = Request.QueryString("@Operator_Tape1_2").ToString
'            Tape_Opt_2 = Request.QueryString("@Operator_Tape2_3").ToString
'            Tape_Opt_3 = Request.QueryString("@Operator_Tape3_4").ToString
'            Tape_Opt_4 = Request.QueryString("@Operator_Tape4_5").ToString

'            '*********************** Reporter Slug ***************************'

'            Dim ReporterSlug_1 As String
'            Dim ReporterSlug_2 As String
'            Dim ReporterSlug_3 As String
'            Dim ReporterSlug_4 As String
'            Dim ReporterSlug_5 As String

'            ReporterSlug_1 = Request.QueryString("@RptSlug1").ToString
'            ReporterSlug_2 = Request.QueryString("@RptSlug2").ToString
'            ReporterSlug_3 = Request.QueryString("@RptSlug3").ToString
'            ReporterSlug_4 = Request.QueryString("@RptSlug4").ToString
'            ReporterSlug_5 = Request.QueryString("@RptSlug5").ToString

'            '******************** Reporter Slug Options *********************'

'            Dim ReporterSlug_Opt_1 As String
'            Dim ReporterSlug_Opt_2 As String
'            Dim ReporterSlug_Opt_3 As String
'            Dim ReporterSlug_Opt_4 As String

'            ReporterSlug_Opt_1 = Request.QueryString("@Operator_RptSlug1_2").ToString
'            ReporterSlug_Opt_2 = Request.QueryString("@Operator_RptSlug2_3").ToString
'            ReporterSlug_Opt_3 = Request.QueryString("@Operator_RptSlug3_4").ToString
'            ReporterSlug_Opt_4 = Request.QueryString("@Operator_RptSlug4_5").ToString

'            '********************* Proposed Slug **************************'

'            Dim ProposedSlug_1 As String
'            Dim ProposedSlug_2 As String
'            Dim ProposedSlug_3 As String
'            Dim ProposedSlug_4 As String
'            Dim ProposedSlug_5 As String

'            ProposedSlug_1 = Request.QueryString("@ProposedSlug1").ToString
'            ProposedSlug_2 = Request.QueryString("@ProposedSlug2").ToString
'            ProposedSlug_3 = Request.QueryString("@ProposedSlug3").ToString
'            ProposedSlug_4 = Request.QueryString("@ProposedSlug4").ToString
'            ProposedSlug_5 = Request.QueryString("@ProposedSlug5").ToString

'            '******************* Proposed Slug Options ********************'

'            Dim ProposedSlug_Opt_1 As String
'            Dim ProposedSlug_Opt_2 As String
'            Dim ProposedSlug_Opt_3 As String
'            Dim ProposedSlug_Opt_4 As String

'            ProposedSlug_Opt_1 = Request.QueryString("@Operator_ProposedSlug1_2").ToString
'            ProposedSlug_Opt_2 = Request.QueryString("@Operator_ProposedSlug2_3").ToString
'            ProposedSlug_Opt_3 = Request.QueryString("@Operator_ProposedSlug3_4").ToString
'            ProposedSlug_Opt_4 = Request.QueryString("@Operator_ProposedSlug4_5").ToString

'            '************************* Keywords **************************'

'            Dim Keyword_1 As String
'            Dim Keyword_2 As String
'            Dim Keyword_3 As String
'            Dim Keyword_4 As String
'            Dim Keyword_5 As String

'            Keyword_1 = Request.QueryString("@KeyWord1").ToString
'            Keyword_2 = Request.QueryString("@KeyWord2").ToString
'            Keyword_3 = Request.QueryString("@KeyWord3").ToString
'            Keyword_4 = Request.QueryString("@KeyWord4").ToString
'            Keyword_5 = Request.QueryString("@KeyWord5").ToString

'            '*********************** Keywords Options **********************'

'            Dim Keyword_Opt_1 As String
'            Dim Keyword_Opt_2 As String
'            Dim Keyword_Opt_3 As String
'            Dim Keyword_Opt_4 As String

'            Keyword_Opt_1 = Request.QueryString("@Operator_KeyWord1_2").ToString
'            Keyword_Opt_2 = Request.QueryString("@Operator_KeyWord2_3").ToString
'            Keyword_Opt_3 = Request.QueryString("@Operator_KeyWord3_4").ToString
'            Keyword_Opt_4 = Request.QueryString("@Operator_KeyWord4_5").ToString

'            '************************* Keytypes **************************'

'            Dim Keytype_1 As String
'            Dim Keytype_2 As String
'            Dim Keytype_3 As String
'            Dim Keytype_4 As String
'            Dim Keytype_5 As String

'            Keytype_1 = Request.QueryString("@KeyType1").ToString
'            Keytype_2 = Request.QueryString("@KeyType2").ToString
'            Keytype_3 = Request.QueryString("@KeyType3").ToString
'            Keytype_4 = Request.QueryString("@KeyType4").ToString
'            Keytype_5 = Request.QueryString("@KeyType5").ToString

'            '*********************** Keytypes Options **********************'

'            Dim Keytypes_Opt_1 As String
'            Dim Keytypes_Opt_2 As String
'            Dim Keytypes_Opt_3 As String
'            Dim Keytypes_Opt_4 As String

'            Keytypes_Opt_1 = Request.QueryString("@Operator_KeyType1_2").ToString
'            Keytypes_Opt_2 = Request.QueryString("@Operator_KeyType2_3").ToString
'            Keytypes_Opt_3 = Request.QueryString("@Operator_KeyType3_4").ToString
'            Keytypes_Opt_4 = Request.QueryString("@Operator_KeyType4_5").ToString

'            '**************************** From, To Date ********************'

'            'Dim FromDate As String
'            'FromDate = Request.QueryString("FromDate")

'            'Dim ToDate As String
'            'ToDate = Request.QueryString("ToDate")

'            '**************************** Entry Date ********************'

'            Dim EntryDate As String
'            EntryDate = Request.QueryString("@EntryDate")

'            Dim ToDate As String
'            ToDate = Request.QueryString("@ToDate")

'            '************************* Reporter Name ********************'

'            Dim ReporterName As String
'            ReporterName = Request.QueryString("@ReporterName")

'            '************************* English Script **************************'

'            Dim EnglishScript1 As String
'            Dim EnglishScript2 As String
'            Dim EnglishScript3 As String
'            Dim EnglishScript4 As String
'            Dim EnglishScript5 As String

'            EnglishScript1 = Request.QueryString("@EnglishScript1").ToString
'            EnglishScript2 = Request.QueryString("@EnglishScript2").ToString
'            EnglishScript3 = Request.QueryString("@EnglishScript3").ToString
'            EnglishScript4 = Request.QueryString("@EnglishScript4").ToString
'            EnglishScript5 = Request.QueryString("@EnglishScript5").ToString

'            '*********************** Eng Script Options **********************'

'            Dim EnglishScript_Opt_1 As String
'            Dim EnglishScript_Opt_2 As String
'            Dim EnglishScript_Opt_3 As String
'            Dim EnglishScript_Opt_4 As String

'            EnglishScript_Opt_1 = Request.QueryString("@Operator_EnglishScript1_2").ToString
'            EnglishScript_Opt_2 = Request.QueryString("@Operator_EnglishScript2_3").ToString
'            EnglishScript_Opt_3 = Request.QueryString("@Operator_EnglishScript3_4").ToString
'            EnglishScript_Opt_4 = Request.QueryString("@Operator_EnglishScript4_5").ToString

'            '**************************** Tape Type ***********************'

'            Dim TapeType1 As String

'            TapeType1 = Request.QueryString("@TapeType1").ToString

'            '********************* Store Procedure **********************'

'            Dim cmd As New System.Data.SqlClient.SqlCommand
'            cmd.CommandType = Data.CommandType.StoredProcedure

'            'cmd.CommandText = "getSearchResultsNews_ExcelConvert2"

'            If Request.QueryString("@UrduScript1").ToString = "%%" Then
'                cmd.CommandText = "Proc_SearchEngine_News_WithOutUrduScript_Excel_11Dec13"
'            Else
'                cmd.CommandText = "Proc_SearchEngine_News_Excel_11Dec13"
'            End If

'            cmd.Connection = Con
'            cmd.CommandTimeout = 0

'            '****************************************************************--

'            Dim currentPageIndex As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@currentPageIndex", Data.SqlDbType.Int)
'            currentPageIndex.Value = Convert.ToInt32(lblPageIndex.Text)

'            Dim NoOfRecordOnPage As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@NoOfRecordOnPage", Data.SqlDbType.Int)
'            NoOfRecordOnPage.Value = Pages

'            Dim ContentType As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ContentType", Data.SqlDbType.Text)
'            ContentType.Value = 29

'            Dim IngnoreTape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreTape", Data.SqlDbType.Text)
'            IngnoreTape1.Value = IngnoreTape

'            Dim IngnoreReporterSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreReporterSlug", Data.SqlDbType.Text)
'            IngnoreReporterSlug1.Value = IngnoreReporterSlug

'            Dim IngnoreProposedSlug1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreProposedSlug", Data.SqlDbType.Text)
'            IngnoreProposedSlug1.Value = IngnoreProposedSlug

'            Dim IngnoreKeyWords1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyWords", Data.SqlDbType.Text)
'            IngnoreKeyWords1.Value = IngnoreKeyWords

'            Dim IngnoreKeyTypes1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IngnoreKeyTypes", Data.SqlDbType.Text)
'            IngnoreKeyTypes1.Value = IngnoreKeyTypes

'            Dim IgnoreDate1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreDate", Data.SqlDbType.Text)
'            IgnoreDate1.Value = IgnoreDate

'            Dim IgnoreReporter1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreReporter", Data.SqlDbType.Text)
'            IgnoreReporter1.Value = IgnoreReporter

'            Dim IgnoreEnglishScript1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreEnglishScript", Data.SqlDbType.Text)
'            IgnoreEnglishScript1.Value = IgnoreEnglishScript

'            Dim IgnoreTapeType1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IgnoreTapeType", Data.SqlDbType.Text)
'            IgnoreTapeType1.Value = IgnoreTapeType

'            '****************************************************************--

'            Dim Tape1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape1", Data.SqlDbType.Text)
'            Tape1.Value = Tape_No_1

'            Dim Tape2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape2", Data.SqlDbType.Text)
'            Tape2.Value = Tape_No_2

'            Dim Tape3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape3", Data.SqlDbType.Text)
'            Tape3.Value = Tape_No_3

'            Dim Tape4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape4", Data.SqlDbType.Text)
'            Tape4.Value = Tape_No_4

'            Dim Tape5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Tape5", Data.SqlDbType.Text)
'            Tape5.Value = Tape_No_5

'            Dim Tape_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape1_2", Data.SqlDbType.Text)
'            Tape_opt1.Value = Tape_Opt_1

'            Dim Tape_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape2_3", Data.SqlDbType.Text)
'            Tape_opt2.Value = Tape_Opt_2

'            Dim Tape_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape3_4", Data.SqlDbType.Text)
'            Tape_opt3.Value = Tape_Opt_3

'            Dim Tape_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_Tape4_5", Data.SqlDbType.Text)
'            Tape_opt4.Value = Tape_Opt_4

'            Dim RS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug1", Data.SqlDbType.Text)
'            RS_1.Value = ReporterSlug_1

'            Dim RS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug2", Data.SqlDbType.Text)
'            RS_2.Value = ReporterSlug_2

'            Dim RS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug3", Data.SqlDbType.Text)
'            RS_3.Value = ReporterSlug_3

'            Dim RS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug4", Data.SqlDbType.Text)
'            RS_4.Value = ReporterSlug_4

'            Dim RS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@RptSlug5", Data.SqlDbType.Text)
'            RS_5.Value = ReporterSlug_5

'            Dim RS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug1_2", Data.SqlDbType.Text)
'            RS_Opt_1.Value = ReporterSlug_Opt_1

'            Dim RS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug2_3", Data.SqlDbType.Text)
'            RS_Opt_2.Value = ReporterSlug_Opt_2

'            Dim RS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug3_4", Data.SqlDbType.Text)
'            RS_Opt_3.Value = ReporterSlug_Opt_3

'            Dim RS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_RptSlug4_5", Data.SqlDbType.Text)
'            RS_Opt_4.Value = ReporterSlug_Opt_4

'            Dim PS_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug1", Data.SqlDbType.Text)
'            PS_1.Value = ProposedSlug_1

'            Dim PS_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug2", Data.SqlDbType.Text)
'            PS_2.Value = ProposedSlug_2

'            Dim PS_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug3", Data.SqlDbType.Text)
'            PS_3.Value = ProposedSlug_3

'            Dim PS_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug4", Data.SqlDbType.Text)
'            PS_4.Value = ProposedSlug_4

'            Dim PS_5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ProposedSlug5", Data.SqlDbType.Text)
'            PS_5.Value = ProposedSlug_5

'            Dim PS_Opt_1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug1_2", Data.SqlDbType.Text)
'            PS_Opt_1.Value = ProposedSlug_Opt_1

'            Dim PS_Opt_2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug2_3", Data.SqlDbType.Text)
'            PS_Opt_2.Value = ProposedSlug_Opt_2

'            Dim PS_Opt_3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug3_4", Data.SqlDbType.Text)
'            PS_Opt_3.Value = ProposedSlug_Opt_3

'            Dim PS_Opt_4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_ProposedSlug4_5", Data.SqlDbType.Text)
'            PS_Opt_4.Value = ProposedSlug_Opt_4

'            Dim KW1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord1", Data.SqlDbType.Text)
'            KW1.Value = Keyword_1

'            Dim KW2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord2", Data.SqlDbType.Text)
'            KW2.Value = Keyword_2

'            Dim KW3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord3", Data.SqlDbType.Text)
'            KW3.Value = Keyword_3

'            Dim KW4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord4", Data.SqlDbType.Text)
'            KW4.Value = Keyword_4

'            Dim KW5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyWord5", Data.SqlDbType.Text)
'            KW5.Value = Keyword_5

'            Dim KW_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord1_2", Data.SqlDbType.Text)
'            KW_opt1.Value = Keyword_Opt_1

'            Dim KW_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord2_3", Data.SqlDbType.Text)
'            KW_opt2.Value = Keyword_Opt_2

'            Dim KW_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord3_4", Data.SqlDbType.Text)
'            KW_opt3.Value = Keyword_Opt_3

'            Dim KW_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyWord4_5", Data.SqlDbType.Text)
'            KW_opt4.Value = Keyword_Opt_4

'            Dim KT1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType1", Data.SqlDbType.Text)
'            KT1.Value = Keytype_1

'            Dim KT2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType2", Data.SqlDbType.Text)
'            KT2.Value = Keytype_2

'            Dim KT3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType3", Data.SqlDbType.Text)
'            KT3.Value = Keytype_3

'            Dim KT4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType4", Data.SqlDbType.Text)
'            KT4.Value = Keytype_4

'            Dim KT5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@KeyType5", Data.SqlDbType.Text)
'            KT5.Value = Keytype_5

'            Dim KT_opt1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType1_2", Data.SqlDbType.Text)
'            KT_opt1.Value = Keytypes_Opt_1

'            Dim KT_opt2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType2_3", Data.SqlDbType.Text)
'            KT_opt2.Value = Keytypes_Opt_2

'            Dim KT_opt3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType3_4", Data.SqlDbType.Text)
'            KT_opt3.Value = Keytypes_Opt_3

'            Dim KT_opt4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_KeyType4_5", Data.SqlDbType.Text)
'            KT_opt4.Value = Keytypes_Opt_4

'            Dim FrmDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EntryDate", Data.SqlDbType.Text)
'            FrmDate.Value = EntryDate

'            Dim To_Date As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
'            To_Date.Value = ToDate

'            Dim ReporterName1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ReporterName", Data.SqlDbType.Text)
'            ReporterName1.Value = ReporterName

'            Dim EnglishScript11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript1", Data.SqlDbType.Text)
'            EnglishScript11.Value = EnglishScript1

'            Dim EnglishScript22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript2", Data.SqlDbType.Text)
'            EnglishScript22.Value = EnglishScript2

'            Dim EnglishScript33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript3", Data.SqlDbType.Text)
'            EnglishScript33.Value = EnglishScript3

'            Dim EnglishScript44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript4", Data.SqlDbType.Text)
'            EnglishScript44.Value = EnglishScript4

'            Dim EnglishScript55 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EnglishScript5", Data.SqlDbType.Text)
'            EnglishScript55.Value = EnglishScript5

'            Dim EnglishScript_Opt_11 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript1_2", Data.SqlDbType.Text)
'            EnglishScript_Opt_11.Value = EnglishScript_Opt_1

'            Dim EnglishScript_Opt_22 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript2_3", Data.SqlDbType.Text)
'            EnglishScript_Opt_22.Value = EnglishScript_Opt_2

'            Dim EnglishScript_Opt_33 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript3_4", Data.SqlDbType.Text)
'            EnglishScript_Opt_33.Value = EnglishScript_Opt_3

'            Dim EnglishScript_Opt_44 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Operator_EnglishScript4_5", Data.SqlDbType.Text)
'            EnglishScript_Opt_44.Value = EnglishScript_Opt_4

'            Dim TapeType01 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@TapeType1", Data.SqlDbType.Text)
'            TapeType01.Value = TapeType1

'            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
'            BaseStationID.Value = CInt(Request.QueryString("@BaseStationID").ToString)

'            If Request.QueryString("@UrduScript1").ToString <> "%%" Then
'                Dim p1s As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
'                p1s.Value = Request.QueryString("@UrduScript1").ToString

'                Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
'                p2.Value = Request.QueryString("@UrduScript2").ToString

'                Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
'                p3.Value = Request.QueryString("@UrduScript3").ToString

'                Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
'                p4.Value = Request.QueryString("@UrduScript4").ToString

'                Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
'                p5.Value = Request.QueryString("@UrduScript5").ToString

'            End If

'            Dim da As New System.Data.SqlClient.SqlDataAdapter
'            DS = New DataSet

'            da.SelectCommand = cmd
'            da.Fill(DS)
'            DS.AcceptChanges()
'            GridView1.DataSource = DS.Tables(0).DefaultView
'            GridView1.DataBind()
'            'DS.Tables(0).Columns.Remove(4)
'            ConvertToXML(DS) '''added by arsalan amin 14Sept2014 as Urdu was appear in Excel properly
'        Catch ex As Exception
'            Throw
'        End Try
'    End Sub

'    Private Sub FillUrduScript()
'        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)

'        Dim Pages As String
'        Pages = Request.QueryString("@No_of_Record_Per_Page")
'        lblPages.Text = Pages

'        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
'        Dim DS As DataSet
'        Dim cmd As New System.Data.SqlClient.SqlCommand
'        cmd.CommandType = Data.CommandType.StoredProcedure
'        cmd.CommandText = "GetSearch_ByUrduScript_withBaseStation"
'        cmd.Connection = Con
'        cmd.CommandTimeout = 0

'        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
'        p1.Value = "%" + Request.QueryString("@UrduScript1").ToString + "%"

'        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
'        p2.Value = "%" + Request.QueryString("@UrduScript2").ToString + "%"

'        Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
'        p3.Value = "%" + Request.QueryString("@UrduScript3").ToString + "%"

'        Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
'        p4.Value = "%" + Request.QueryString("@UrduScript4").ToString + "%"

'        Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
'        p5.Value = "%" + Request.QueryString("@UrduScript5").ToString + "%"

'        Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
'        p6.Value = Request.QueryString("@BaseStationID").ToString

'        Dim da As New System.Data.SqlClient.SqlDataAdapter
'        DS = New DataSet
'        da.SelectCommand = cmd
'        da.Fill(DS, "Master")
'        dg_Search.DataSource = DS.Tables("Master")
'        dg_Search.Columns(10).Visible = True
'        dg_Search.Columns(11).Visible = True
'        dg_Search.Columns(15).Visible = True
'        dg_Search.Columns(16).Visible = True
'        dg_Search.DataBind()
'        dg_Search.Columns(10).Visible = False
'        dg_Search.Columns(11).Visible = False
'        dg_Search.Columns(15).Visible = False
'        dg_Search.Columns(16).Visible = False

'        lbl_RecordCount.Text = "Total Records found :- " & (DS.Tables(1).Rows(0)(0))

'        ''*********** TotalePages **************''
'        Dim pq1 As Integer = Convert.ToInt32(lblPages.Text)
'        Dim pq2 As Integer = (DS.Tables(1).Rows(0)(0))
'        Dim Reminder As Integer = pq2 Mod pq1
'        Dim pq3 As Integer = pq2 / pq1
'        If Reminder = 0 Then
'            lblTotalPages.Text = "Total Pages :- " & pq3
'        Else
'            pq3 = pq3 + 1
'            lblTotalPages.Text = "Total Pages :- " & pq3
'        End If

'        lblTotal.Text = pq3

'        GridEnable()

'    End Sub

'    Private Sub FillUrduScript_ExcelConvert()
'        lbl_CurrentPage_No.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)
'        lblPageNo2.Text = "Page No:" & (Convert.ToInt32(lblPageIndex.Text) + 1)

'        Dim Pages As String
'        Pages = Request.QueryString("@No_of_Record_Per_Page")
'        lblPages.Text = Pages

'        Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)
'        Dim DS As DataSet
'        Dim cmd As New System.Data.SqlClient.SqlCommand
'        cmd.CommandType = Data.CommandType.StoredProcedure
'        cmd.CommandText = "GetSearch_ByUrduScript_withBaseStation_Excel_11Dec13"
'        cmd.Connection = Con
'        cmd.CommandTimeout = 0

'        Dim p1 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript1", Data.SqlDbType.NText)
'        p1.Value = "%" + Request.QueryString("@UrduScript1").ToString + "%"

'        Dim p2 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript2", Data.SqlDbType.NText)
'        p2.Value = "%" + Request.QueryString("@UrduScript2").ToString + "%"

'        Dim p3 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript3", Data.SqlDbType.NText)
'        p3.Value = "%" + Request.QueryString("@UrduScript3").ToString + "%"

'        Dim p4 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript4", Data.SqlDbType.NText)
'        p4.Value = "%" + Request.QueryString("@UrduScript4").ToString + "%"

'        Dim p5 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@UrduScript5", Data.SqlDbType.NText)
'        p5.Value = "%" + Request.QueryString("@UrduScript5").ToString + "%"

'        Dim p6 As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
'        p6.Value = Request.QueryString("@BaseStationID").ToString

'        Dim da As New System.Data.SqlClient.SqlDataAdapter
'        DS = New DataSet

'        da.SelectCommand = cmd
'        da.Fill(DS)

'        GridView1.DataSource = DS.Tables(0).DefaultView
'        GridView1.DataBind()

'        DS.Tables(0).Columns.Remove("IsAvailable")

'        ConvertToXML(DS)

'    End Sub

'    Private Sub GridEnable()
'        Dim dr As GridViewRow

'        For Each dr In dg_Search.Rows
'            Dim chk As New CheckBox
'            chk = dr.FindControl("CheckBox1")
'            If (dr.Cells(16).Text <> CInt(Request.Cookies("userinfo")("BaseStationID")).ToString) Then
'                chk.Enabled = False
'            End If

'            If dr.Cells(17).Text = "0" Then
'                chk.Enabled = False
'            End If
'        Next

'    End Sub
'    Private Sub GridRowsColor()
'        Dim I As Integer
'        For I = 0 To dg_Search.Rows.Count - 1
'            If dg_Search.Rows(I).Cells(11).Text = "Yes" Then
'                dg_Search.Rows(I).BackColor = Drawing.Color.Pink
'            End If
'        Next
'    End Sub
'    Private Sub GridRowselect()
'        Try
'            ' get row index from session
'            Dim rowindex As Integer = Integer.Parse(Me.Session("rowindex"))

'            If rowindex <> -1 Then
'                dg_Search.Rows(rowindex).BackColor = Drawing.Color.Cyan
'            End If
'        Catch ex As Exception

'        End Try
'    End Sub
'    Private Sub SaveTape()
'        Dim dr As GridViewRow
'        Dim cls As New BusinessFacade.SearchEngine
'        For Each dr In dg_Search.Rows
'            Dim chk As New CheckBox
'            chk = dr.FindControl("CheckBox1")
'            If chk.Checked = True Then
'                cls.SaveTapeforIssueLater(dr.Cells(4).Text, Request.Cookies("userinfo")("username").ToString, "News")
'            End If
'        Next
'    End Sub
'End Class