Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_Archival_DepartmentWiseIssueReturnSummary
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkIgnoredate.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try

            Dim Dept As String
            Dim FromDate As String
            Dim ToDate As String

            Dept = ddlDepartment.SelectedValue

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            'qryString = "ReportViewer.aspx?ReportName=" + "rpt_ListofArchivalTapeDeptWise_New.rpt&" + "@emp=" & Emp & "&@dept=" & Dept & "&@fromdate=" & FromDate & "&@todate=" & ToDate

            'Response.Redirect(qryString)

            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=RptDepartmentWiseIssueReturnSummary_Archival.rpt&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_Archival_DepartmentWiseIssueReturnSummary.aspx"
            ObjSave.ReportName = "Archival --> Q 7. How Can I View Department Wise Issue Return Summary?"
            ObjSave.SaveRecord()

            ''******************************************************''



        Catch ex As Exception
            Throw
        End Try

    End Sub



    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub
End Class
