<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmSlugEdit.aspx.vb" Inherits="frmSlugEdit" title="Home > Tape Slug > Edit Slug" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_ContentType" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="WIDTH: 616px; HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w1" CssClass="labelheading" Width="24px">Home</asp:LinkButton>&nbsp;&nbsp;&nbsp;&nbsp; &gt; Edit Tape Slug</TD></TR><TR><TD style="WIDTH: 616px"><TABLE style="WIDTH: 728px"><TBODY><TR class="mytext"><TD style="WIDTH: 52px">Tape Slug</TD><TD style="WIDTH: 509px"><asp:TextBox id="txtSlug" runat="server" CssClass="mytext" Width="648px" Height="80px" TextMode="MultiLine"></asp:TextBox></TD><TD style="WIDTH: 3px"></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="608px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="WIDTH: 616px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Update" CssClass="buttonA" Width="64px"></asp:Button>&nbsp; <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button> </TD></TR><TR><TD style="WIDTH: 616px">&nbsp;</TD></TR><TR><TD style="WIDTH: 616px; HEIGHT: 22px"><asp:TextBox id="txtSlugID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE><cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w11" TargetControlID="bttnSave" ConfirmText="Do you want to Update Slug !"></cc1:ConfirmButtonExtender> 
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

