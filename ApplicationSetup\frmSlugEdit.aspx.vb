Imports System
Imports System.Data

Partial Class frmSlugEdit
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            FillControls()

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
        End If
    End Sub

    Private Sub FillControls()
        Dim dt As New DataTable
        Dim ObjSlug As New BusinessFacade.TapeSlug()
        ObjSlug.TapeSlugID = Request.QueryString("TapeSlugID")
        dt = ObjSlug.GetTapeSlug_bySlugID()
        txtSlugID.Text = dt.Rows(0)(0).ToString()
        txtSlug.Text = dt.Rows(0)(1).ToString()

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        
        UpdateRecord()
        
        txtSlugID.Text = ""
        txtSlug.Text = ""

    End Sub

    Private Sub UpdateRecord()
        Dim ObjSlug As New BusinessFacade.TapeSlug()
        ObjSlug.TapeSlugID = txtSlugID.Text
        ObjSlug.TapeSlug = txtSlug.Text
        ObjSlug.UpdateRecord()
        lblErr.Text = "Record has been Updated!!"
        clrscr()
    End Sub

    Private Sub clrscr()
        txtSlug.Text = String.Empty
        txtSlugID.Text = String.Empty
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        lblErr.Text = String.Empty
        clrscr()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
