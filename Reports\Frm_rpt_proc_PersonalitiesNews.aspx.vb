Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_proc_PersonalitiesNews
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try

            Dim KW As String
            Dim Slug As String

            ''*********************************************************''
            ''************* For Getting Program Child ID **************''
            ''*********************************************************''
            Dim KWID As Integer
            Dim ObjKWID As New BusinessFacade.NewsKeyword()
            ObjKWID.NewsKeyword = txtNewKeywords.Text
            KWID = ObjKWID.GetKeywordID_News_AutoComplete(ObjKWID.NewsKeyword)


            If chk_KW.Checked = True Then
                KW = "-1"
            Else
                KW = KWID
            End If

            If CHk_Slug.Checked = True Then
                Slug = "-1"
            Else
                Slug = txtSlug.Text
            End If

            Dim BaseStationID As String
            If Me.chkStation.Checked = True Then
                BaseStationID = "-1"
            Else
                BaseStationID = ddlBaseStation.SelectedValue
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_proc_PersonalitiesNews.rpt&@KeywordID=" + KW + "&@TapeSlug=" + Slug + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "

                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_proc_PersonalitiesNews.rpt&@KeywordID=" + KW + "&@TapeSlug=" + Slug + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "

                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If
            

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            'Dim ObjSave As New BusinessFacade.Reports()
            'ObjSave.MostViewForm = "rpt_proc_PersonalitiesEnt.aspx"
            'ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub
End Class
