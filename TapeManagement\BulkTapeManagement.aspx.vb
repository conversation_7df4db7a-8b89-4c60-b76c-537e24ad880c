
Partial Class TapeManagement_BulkTapeManagement
    Inherits System.Web.UI.Page
    Dim DS As New System.Data.DataSet
    Dim strCommand As String
    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Request.Cookies("UserInfo") Is Nothing Then
            Response.Redirect("../Login.aspx")
        Else
            Master.FooterText = Request.Cookies("userinfo")("username")

            lbl_UserName.Text = Master.FooterText
            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            lbl_UserName.Text = arr_UserID(1)

        End If

        Bind_Temp()

        If Not Page.IsPostBack Then
            Me.txtSave.Text = ""

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")

                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)

            End If

            If Not Request.QueryString.Get("BulkTapeIssuanceID") = "" Then
                txtBulkTapeIssuanceID.Text = Request.QueryString.Get("BulkTapeIssuanceID").ToString
                Me.BindEditControls(Convert.ToInt32(txtBulkTapeIssuanceID.Text))
                Me.FillEditGrid(Convert.ToInt32(txtBulkTapeIssuanceID.Text))
            Else
                FillGrid()
                txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
            End If

            txtEntryDate_1By1.Text = Date.Now().ToString("dd-MMM-yyyy")

            If Request.QueryString.Get("Refresh") = "1" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "function RefreshForm() {window.opener.location.href=" + """ + BulkTapeManagement.aspx" + ";"""
                script = script + "}</script>"
                Page.RegisterClientScriptBlock("test", script)
            End If

            'Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            'If CokieBaseStationID <> 1 Then
            '    Dim K As Integer
            '    For K = 0 To dg_tapeNumber.Rows.Count - 1
            '        dg_tapeNumber.Rows(K).Cells(0).Enabled = False
            '    Next
            'End If

        End If
    End Sub

    Private Sub BindEditControls(ByVal ID As Integer)
        Dim Dt As Data.DataTable
        Dim ObjUpdate As New BusinessFacade.TapeIssuance()
        ObjUpdate.BulkTapeIssuanceID = ID
        Dt = ObjUpdate.BindEditControls(ID)

        txtEntryDate.Text = Convert.ToDateTime(Dt.Rows(0).Item(1).ToString).ToString("dd-MMM-yyyy")
        txt_EmployeeName.Text = Dt.Rows(0).Item(2).ToString
        txt_DepartmentName.Text = Dt.Rows(0).Item(3).ToString
        txt_CityName.Text = Dt.Rows(0).Item(4).ToString
        txt_RefNo.Text = Dt.Rows(0).Item(5).ToString

    End Sub

    Private Sub FillEditGrid(ByVal ID As Integer)

        Dim ObjUpdate As New BusinessFacade.TapeIssuance()
        ObjUpdate.BulkTapeIssuanceID = ID
        Dg_Edit.DataSource = ObjUpdate.BindEditGrid(ID)
        Dg_Edit.DataBind()
        bttnSave.Text = "Update"
    End Sub

    Private Sub FillGrid()

        ''*********************************************''
        ''******* Bulk Tape Issuance (Grid Fill) ******''
        ''*********************************************''

        'dg.DataSource = New BusinessFacade.TapeIssuance().GetRecords_BuldTapeIssuance()
        dg.DataSource = New BusinessFacade.TapeIssuance().GetRecords_TapeumberIssuance()
        dg.Columns(0).Visible = True
        dg.DataBind()
        dg.Columns(0).Visible = False

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
        If CokieBaseStationID <> 1 Then
            lblErr.Text = "You are not allowed to Enter Bulk Tapes!!"
            Exit Sub
        End If

        If bttnSave.Text = "Save" Then
            ''**********************************************''
            ''********* Check Department Vs Employee *******''
            ''**********************************************''

            Dim DepartmentName As String
            Dim objDeptVsEmp As New BusinessFacade.TapeIssuance()
            objDeptVsEmp.EmployeeName = txt_EmployeeName.Text
            DepartmentName = objDeptVsEmp.GetDepartmentName_by_EmployeeName(objDeptVsEmp.EmployeeName)

            If (txt_DepartmentName.Text).ToLower = DepartmentName.ToLower Then

                ''****************************************''
                ''************ Get User ID ***************''
                ''****************************************''

                Dim UserID As Integer
                Dim objUserID As New BusinessFacade.Employee()
                objUserID.SM_LoginID = lbl_UserName.Text
                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


                ''***************************************''
                ''********** Get DepartmentID ***********''
                ''***************************************''

                Dim DepartmentID As Integer
                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_DepartmentName.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


                ''***************************************''
                ''************ Get CityID ***************''
                ''***************************************''

                Dim CityID As Integer
                Dim objCityID As New BusinessFacade.TapeIssuance()
                objCityID.CityName = txt_CityName.Text
                CityID = objCityID.GetCityID_byCityName(objCityID.CityName)

                ''***************************************''
                ''********** Get EmployeeID *************''
                ''***************************************''

                Dim EmployeeID As Integer
                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                objEmployeeID.EmployeeName = txt_EmployeeName.Text
                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


                If CityID = 0 Then
                    Err_City.Visible = True
                Else
                    Err_City.Visible = False
                End If

                If DepartmentID = 0 Then
                    Err_Department.Visible = True
                Else
                    Err_Department.Visible = False
                End If

                If EmployeeID = 0 Then
                    Err_Employee_Bulk.Visible = True
                Else
                    Err_Employee_Bulk.Visible = False
                End If
                If txtEntryDate.Text = "" Then
                    ErrDate.Visible = True
                Else
                    ErrDate.Visible = False
                End If
                'Dim DeptVsEmployee As Integer
                'If EmployeeID = 0 And DeptIssueorNot.SelectedItem.ToString = "No" Then
                '    Err_Employee_Bulk.Visible = True
                '    DeptVsEmployee = 1
                'Else
                '    Err_Employee_Bulk.Visible = False
                '    DeptVsEmployee = 0
                'End If

                If txt_RefNo.Text = "" Then
                    txt_RefNo.Text = "-N/A-"
                    '    Err_RefNo.Visible = True
                Else
                    '    Err_RefNo.Visible = False
                End If
                ''***************************************''
                ''*** Count No of Checked Check Boxes ***''
                ''***************************************''

                Dim R As Integer
                Dim Count As Integer = 0
                For R = 0 To dg.Rows.Count - 1
                    Dim MyTextBox_1 As TextBox = CType(dg.Rows(R).Cells(2).Controls(1), TextBox)
                    If MyTextBox_1.Text <> "" Then
                        Count = Count + 1
                    End If
                Next

                ''***********************************************************************''
                ''****************************** < Save Record > ************************''
                ''***********************************************************************''

                'If txt_RefNo.Text = "" Then
                '    lblErr.Text = "Please Enter Ref. No !!"
                'Else
                If DepartmentID = 0 Then
                    lblErr.Text = "Please Select Department"
                ElseIf CityID = 0 Then
                    lblErr.Text = "Please Select City !!"
                ElseIf EmployeeID = 0 Then
                    lblErr.Text = "Please Select Employee !!"
                ElseIf txtEntryDate.Text = "" Then
                    lblErr.Text = "Please Select Entry Date !!"
                ElseIf Count = 0 Then
                    lblErr.Text = "Please Enter Quantity !!"
                Else

                    ''**********************************************''
                    ''***** Bilk Tape Issuance ( Save Record ) *****''
                    ''**********************************************''

                    strCommand = "BulkTapeManagement_SaveRecord '" & txtEntryDate.Text & "','" & txt_RefNo.Text & "'," & UserID & "," & DeptIssueorNot.SelectedValue & "," & CityID & "," & DepartmentID & "," & EmployeeID
                    Dim cmd1 = New System.Data.SqlClient.SqlCommand(strCommand)
                    If Con.State = Data.ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd1.Connection = Con
                    cmd1.executenonquery()
                    Con.Close()

                    ''************************''
                    ''******* MasterID *******''
                    ''************************''

                    Dim MasterID As Integer
                    Dim ObjBulkID As New BusinessFacade.TapeIssuance()
                    MasterID = ObjBulkID.MaxBulkTapeIssanceID()


                    ''***********************************************''
                    ''** Bilk Tape Issuance Detail ( Save Record ) **''
                    ''***********************************************''

                    Dim P As Integer
                    For P = 0 To dg.Rows.Count - 1
                        Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
                        If MyTextBox.Text <> "" Then
                            strCommand = "BulkTapeManagement_Detail_SaveRecord " & MasterID & "," & dg.Rows(P).Cells(0).Text & "," & MyTextBox.Text
                            Dim cmd2 = New System.Data.SqlClient.SqlCommand(strCommand)
                            If Con.State = Data.ConnectionState.Closed Then
                                Con.Open()
                            End If
                            cmd2.Connection = Con
                            cmd2.executenonquery()
                        End If
                    Next


                    ''***********************************************''
                    ''***************** Send Email ******************''
                    Try
                        sendBulkmail()
                    Catch ex As Exception

                    End Try



                    ''********************* End *********************''
                    ''***********************************************''

                    lblErr.Text = "Record has been Saved !!"
                    Clrscr()
                    FillGrid()
                End If

            Else
                lblErr.Text = "Employee not found in " & txt_DepartmentName.Text & " Department !!"
            End If

        Else
            UpdateIssueRecord()
        End If

    End Sub

    Private Sub UpdateIssueRecord()
        ''**********************************************''
        ''********* Check Department Vs Employee *******''
        ''**********************************************''

        Dim DepartmentName As String
        Dim objDeptVsEmp As New BusinessFacade.TapeIssuance()
        objDeptVsEmp.EmployeeName = txt_EmployeeName.Text
        DepartmentName = objDeptVsEmp.GetDepartmentName_by_EmployeeName(objDeptVsEmp.EmployeeName)

        If (txt_DepartmentName.Text).ToLower = DepartmentName.ToLower Then

            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


            ''***************************************''
            ''********** Get DepartmentID ***********''
            ''***************************************''

            Dim DepartmentID As Integer
            Dim objDeptID As New BusinessFacade.TapeIssuance()
            objDeptID.DepartmentName = txt_DepartmentName.Text
            DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


            ''***************************************''
            ''************ Get CityID ***************''
            ''***************************************''

            Dim CityID As Integer
            Dim objCityID As New BusinessFacade.TapeIssuance()
            objCityID.CityName = txt_CityName.Text
            CityID = objCityID.GetCityID_byCityName(objCityID.CityName)

            ''***************************************''
            ''********** Get EmployeeID *************''
            ''***************************************''

            Dim EmployeeID As Integer
            Dim objEmployeeID As New BusinessFacade.TapeIssuance()
            objEmployeeID.EmployeeName = txt_EmployeeName.Text
            EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


            If CityID = 0 Then
                Err_City.Visible = True
            Else
                Err_City.Visible = False
            End If

            If DepartmentID = 0 Then
                Err_Department.Visible = True
            Else
                Err_Department.Visible = False
            End If

            If EmployeeID = 0 Then
                Err_Employee_Bulk.Visible = True
            Else
                Err_Employee_Bulk.Visible = False
            End If
            If txtEntryDate.Text = "" Then
                ErrDate.Visible = True
            Else
                ErrDate.Visible = False
            End If

            If txt_RefNo.Text = "" Then
                txt_RefNo.Text = "-N/A-"
            End If
            ''***************************************''
            ''*** Count No of Checked Check Boxes ***''
            ''***************************************''

            Dim R As Integer
            Dim Count As Integer = 0
            For R = 0 To Dg_Edit.Rows.Count - 1
                Dim MyTextBox_1 As TextBox = CType(Dg_Edit.Rows(R).Cells(2).Controls(1), TextBox)
                If MyTextBox_1.Text <> "" Then
                    Count = Count + 1
                End If
            Next

            ''***********************************************************************''
            ''*************************** < Updatye Record > ************************''
            ''***********************************************************************''

            If DepartmentID = 0 Then
                lblErr.Text = "Please Select Department"
            ElseIf CityID = 0 Then
                lblErr.Text = "Please Select City !!"
            ElseIf EmployeeID = 0 Then
                lblErr.Text = "Please Select Employee !!"
            ElseIf txtEntryDate.Text = "" Then
                lblErr.Text = "Please Select Entry Date !!"
            ElseIf Count = 0 Then
                lblErr.Text = "Please Enter Quantity !!"
            Else

                ''**********************************************''
                ''**** Bilk Tape Issuance ( Update Record ) ****''
                ''**********************************************''

                strCommand = "BulkTapeManagement_Update '" & txtEntryDate.Text & "','" & txt_RefNo.Text & "'," & UserID & "," & DeptIssueorNot.SelectedValue & "," & CityID & "," & DepartmentID & "," & EmployeeID & "," & txtBulkTapeIssuanceID.Text
                Dim cmd1 = New System.Data.SqlClient.SqlCommand(strCommand)
                If Con.State = Data.ConnectionState.Closed Then
                    Con.Open()
                End If
                cmd1.Connection = Con
                cmd1.executenonquery()
                Con.Close()

                ''*************************************************''
                ''** Bilk Tape Issuance Detail ( Update Record ) **''
                ''*************************************************''

                Dim P As Integer
                For P = 0 To Dg_Edit.Rows.Count - 1
                    Dim MyTextBox As TextBox = CType(Dg_Edit.Rows(P).Cells(2).Controls(1), TextBox)
                    If MyTextBox.Text <> "" Then
                        strCommand = "BulkTapeManagement_Detail_Update " & Convert.ToInt32(txtBulkTapeIssuanceID.Text) & "," & Dg_Edit.Rows(P).Cells(0).Text & "," & MyTextBox.Text
                        Dim cmd2 = New System.Data.SqlClient.SqlCommand(strCommand)
                        If Con.State = Data.ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd2.Connection = Con
                        cmd2.executenonquery()
                    End If
                Next

                ''********************* End *********************''

                lblErr.Text = "Record has been Udpated !!"
                Clrscr()
                FillGrid()
                txtBulkTapeIssuanceID.Text = String.Empty
                bttnSave.Text = "Save"
            End If

        Else
            lblErr.Text = "Employee not found in " & txt_DepartmentName.Text & " Department !!"
        End If
    End Sub

    Protected Sub bttnCencel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCencel.Click
        Clrscr()
        lblErr.Text = String.Empty
    End Sub

    Private Sub Clrscr()
        txt_RefNo.Text = String.Empty
        txt_CityName.Text = String.Empty
        txt_CityName.Text = String.Empty
        txt_DepartmentName.Text = String.Empty
        DeptIssueorNot.SelectedIndex = "0"
        txt_EmployeeName.Text = String.Empty
        txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")

        Dim J As Integer
        For J = 0 To dg.Rows.Count - 1
            Dim MyTextBox As TextBox = CType(dg.Rows(J).Cells(2).Controls(1), TextBox)
            If MyTextBox.Text <> "" Then
                MyTextBox.Text = String.Empty
            End If

        Next

    End Sub

    Protected Sub bttnAddTapeNO_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAddTapeNO.Click

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As String
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        If UserID <> "0" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('BulkNewTapeNumber.aspx?@UserID=" + UserID + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If


    End Sub

    Protected Sub bttnSave_1By1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_1By1.Click

        Me.txtSave.Text = "Save"
        Err_TapeNo.Visible = False


        ''**********************************************************''
        ''******************** Get Employee ID *********************''
        ''**********************************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


        ''**********************************************************''
        ''******************** Get Employee ID *********************''
        ''**********************************************************''

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


        ''**********************************************************''
        ''********************Get Program ChildID ******************''
        ''**********************************************************''

        Dim ProgramChildID As Integer
        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)


        ''***********************************************************''
        ''************** Get DapartmentID by EmployeeID *************''
        ''***********************************************************''

        Dim DepartmentID As Integer
        If EmployeeID <> 0 Then
            Dim ObjDepartmentID As New BusinessFacade.Employee()
            ObjDepartmentID.EmployeeID = EmployeeID
            DepartmentID = ObjDepartmentID.GetDepartmentID_by_EmployeeID(ObjDepartmentID.EmployeeID)
        End If

        ''************************************************************''

        Dim Cnt As Integer
        Cnt = dg_tapeNumber.Rows.Count

        If Cnt = 0 Then

            If EmployeeID = 0 Then
                Err_Employee.Visible = True
            Else
                Err_Employee.Visible = False
            End If

            If ProgramChildID = 0 Then
                Err_Program.Visible = True
            Else
                Err_Program.Visible = False
            End If

            If EmployeeID = 0 Then
                lblErr_2.Text = "Please Select Employee !!"
            ElseIf ProgramChildID = 0 Then
                If txt_ProgramChild_1by1.Text = "" Then
                    lblErr_2.Text = "Please Select Program !!"
                Else
                    lblErr_2.Text = "Selected Program is Not Valid !!"
                End If
            Else
                Dim H As Integer
                Dim G As Integer
                Dim L As Integer
                If txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" And txt_TapeNumber.Text <> "" Then


                    Dim arr As Array = Split(txt_TapeNumber.Text, "#")
                    If arr.Length = 2 Then
                        txt_TapeNumber.Text = arr(1)
                    End If

                    ''*********************************************''
                    ''******** Get Stock Register DetailID ********''
                    ''*********************************************''

                    Dim ObjID As New BusinessFacade.TapeIssuance()
                    ObjID.TapeNumber = txt_TapeNumber.Text
                    H = ObjID.TapeIssuance_GetSRDID(ObjID.TapeNumber)


                    ''*********************************************''
                    ''************** Get Tape Type ID *************''
                    ''*********************************************''

                    Dim ObJTapeTyep As New BusinessFacade.TapeIssuance()
                    ObJTapeTyep.TapeNumber = txt_TapeNumber.Text
                    G = ObJTapeTyep.TapeIssuance_GetTapeType(ObJTapeTyep.TapeNumber)


                    ''*********************************************''
                    ''************* Get Tape Library ID ***********''
                    ''*********************************************''

                    Dim objLibID As New BusinessFacade.TapeIssuance()
                    objLibID.TapeNumber = txt_TapeNumber.Text
                    L = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

                End If

                ''*******************************************************************''
                ''********************* Check Tape Availability *********************''
                ''*******************************************************************''

                Dim ObjCheckTape_Available As New BusinessFacade.NewTapeNumber()
                ObjCheckTape_Available.TapeLibraryID = L
                Dim IsAvail As Integer = ObjCheckTape_Available.TapeNumber_IsAvailable_GetRecord1()

                ''*******************************************************************''
                ''********************* Check Tape IsBlank **************************''
                ''*******************************************************************''

                Dim ObjCheckTape_isBlank As New BusinessFacade.NewTapeNumber()
                ObjCheckTape_isBlank.TapeLibraryID = L
                Dim IsBlank As Integer = ObjCheckTape_isBlank.TapeNumber_IsBlank()

                ''*******************************************************************''
                ''******************** Check Tape IsAvailSearch *********************''
                ''*******************************************************************''

                Dim ObjCheckTape_AvailableSearch As New BusinessFacade.NewTapeNumber()
                ObjCheckTape_AvailableSearch.TapeLibraryID = L
                Dim IsAvailSearch As Integer = ObjCheckTape_AvailableSearch.TapeNumber_IsAvailableSearch_GetRecord1()

                If IsAvail = 0 Then
                    ''*******************************************''
                    ''*********** Check Tape Issuance ***********''
                    ''*******************************************''
                    Dim ObjSetIsAvailable As New BusinessFacade.TapeIssuance()
                    ObjSetIsAvailable.TapeLibraryID = L
                    IsAvail = ObjSetIsAvailable.SetIsAvailable()

                End If

                '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                If H = "0" And G = "0" And txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" Then
                    lblErr_2.Text = "Please add new TapeNumber!!"
                    Err_TapeNo.Visible = True

                ElseIf IsAvailSearch = 0 Then
                    lblErr_2.Text = "This Tape has not been Available further!!"
                    Err_TapeNo.Visible = True
                ElseIf IsAvail = 0 Then
                    lblErr_2.Text = "This Tape has been issued already!!"
                    Err_TapeNo.Visible = True
                ElseIf IsBlank = 0 Then
                    lblErr_2.Text = "This Tape is Already Archived !!"
                ElseIf txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" Then
                    txt_SRDID.Text = H
                    txt_TapeTypeID.Text = G
                    Dim TapeLibID As Integer = G

                    ''**********************************************************************''
                    ''****************** Tape Issuance ( Save Record ) *********************''
                    ''**********************************************************************''

                    If DepartmentID <> 0 Then

                        Dim IsRecycle As Integer = 0
                        If chkIsRecycle.Checked = True Then
                            IsRecycle = 1
                        Else
                            IsRecycle = 0
                        End If

                        'strCommand = "TapeIssuance_OneByOne_SaveRecord_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & L & "," & ProgramChildID
                        strCommand = "TapeIssuance_OneByOne_SaveRecord_WithRecycle " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & L & "," & ProgramChildID & "," & IsRecycle

                        Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                        If Con.State = Data.ConnectionState.Closed Then
                            Con.Open()
                        End If
                        cmd1By1_Save.Connection = Con
                        cmd1By1_Save.executenonquery()
                        Con.Close()
                        lblErr_2.Text = "Record has been Saved!!"


                        Try
                            Dim ObjReminder As New BusinessFacade.ReminderService()
                            ObjReminder.EmployeeName = txt_Employee_1By1.Text
                            Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                            If EmailAddress <> "N/A" Then
                                Dim Arr As Array = Split(EmailAddress, "@")
                                If Arr.Length = 2 Then
                                    sendmail(EmailAddress)
                                Else
                                    lblErr_2.Text += "But Email Not send due to Invalid Email Address!"
                                End If
                            Else
                                lblErr_2.Text += "But Email Not send b/c Email Address not Exists!"
                            End If
                        Catch ex As Exception

                        End Try


                        Clrscr_2()



                    End If

                End If

            End If
        End If

        ''************************************************************''
        ''*********** Save Record for More Than One Tapes ************''
        ''************************************************************''

        If Cnt <> 0 Then

            If EmployeeID = 0 Then
                Err_Employee.Visible = True
            Else
                Err_Employee.Visible = False
            End If

            If ProgramChildID = 0 Then
                Err_Program.Visible = True
            Else
                Err_Program.Visible = False
            End If

            If EmployeeID = 0 Then
                lblErr_2.Text = "Please Select Employee !!"
            ElseIf ProgramChildID = 0 Then
                lblErr_2.Text = "Please Select Program !!"
            Else

                SaveRecord_Issuance_1()

                ''*********************************************''
                ''*********** Clear dbo.Temp table ************''
                ''*********************************************''

                Dim ObjTemp As New BusinessFacade.NewTapeNumber
                ObjTemp.UserID = UserID
                ObjTemp.Delete_Temp_New()


                ''******************* End *********************''
                ''*********************************************''

                Bind_Temp()

            End If
        End If

    End Sub

    Private Sub Update_TapeLibrary()
        Dim TapeLibraryID As Integer
        Dim ObjLib As New BusinessFacade.TapeIssuance()
        TapeLibraryID = ObjLib.MaxLianraryID()

        Dim ObjUpdate As New BusinessFacade.NewTapeNumber()
        ObjUpdate.IsAvailable = 0
        ObjUpdate.TapeLibraryID = TapeLibraryID
        ObjUpdate.TapeIsAvailable_Update()
    End Sub

    Private Sub SaveRecord_Issuance()

        '''''''''' Get DepartmentID '''''''''''''''
        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        '''''''''' For Getting Program Child ID ''''''''''''''''
        Dim ProgramChildID As Integer
        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

        '''''''''''''''''''''''''''''''''''''''''''''''''''''''
        If ProgramChildID <> 0 Then
            ' strCommand = "TapeIssuance_OneByOne_SaveRecord " & 3 & "," & ddl_Employee_1By1.SelectedValue
            strCommand = "TapeIssuance_OneByOne_SaveRecord " & 3 & "," & EmployeeID
            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
            If Con.State = Data.ConnectionState.Closed Then
                Con.Open()
            End If
            cmd1By1_Save.Connection = Con
            cmd1By1_Save.executenonquery()
            Con.Close()

            '''''''''''''''''''''''''''''''''''
            Dim IssID As Integer
            Dim Objuser As New BusinessFacade.TapeIssuance()
            IssID = Objuser.MaxTapeIssanceID()

            ''''''''''''''''''''''''''''''''''''
            Dim LibID As Integer
            Dim ObjLib As New BusinessFacade.TapeIssuance()
            LibID = ObjLib.MaxLianraryID()

            ''''''''''''''''''''''''''''''''''''''
            'strCommand = "TapeIssuanceDetail_OneByOne_SaveRecord " & IssID & "," & LibID & "," & ddl_Program_1by1.SelectedValue
            strCommand = "TapeIssuanceDetail_OneByOne_SaveRecord " & IssID & "," & LibID & "," & ProgramChildID
            Dim cmdDetail = New System.Data.SqlClient.SqlCommand(strCommand)
            If Con.State = Data.ConnectionState.Closed Then
                Con.Open()
            End If
            cmdDetail.Connection = Con
            cmdDetail.executenonquery()
            Con.Close()
            lblErr_2.Text = "Record has been Saved!!"
            Clrscr_2()
            txt_SRDID.Text = String.Empty
            txt_TapeTypeID.Text = String.Empty
        End If


    End Sub

    Private Sub SaveRecord_Issuance_1()

        ''**********************************************************''
        ''******************** Get Employee ID *********************''
        ''**********************************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


        ''**********************************************************''
        ''******************** Get Employee ID *********************''
        ''**********************************************************''

        Dim EmployeeID As Integer
        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

        ''**********************************************************''
        ''********************Get Program ChildID ******************''
        ''**********************************************************''

        Dim ProgramChildID As Integer
        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

        ''***********************************************************''
        ''************** Get DapartmentID by EmployeeID *************''
        ''***********************************************************''

        Dim DepartmentID As Integer
        If EmployeeID <> 0 Then
            Dim ObjDepartmentID As New BusinessFacade.Employee()
            ObjDepartmentID.EmployeeID = EmployeeID
            DepartmentID = ObjDepartmentID.GetDepartmentID_by_EmployeeID(ObjDepartmentID.EmployeeID)
        End If

        ''***********************************************************''


        Dim IsTapeChecked As Integer = 0

        If ProgramChildID <> 0 Then

            If DepartmentID <> 0 Then

                Dim i As Integer
                For i = 0 To dg_tapeNumber.Rows.Count - 1


                    ''********************************************''
                    ''** Save Tape Number in Tape Library Table **''
                    ''********************************************''

                    Dim objSave As New BusinessFacade.NewTapeNumber()
                    objSave.TapeTypeID = dg_tapeNumber.Rows(i).Cells(1).Text

                    Dim qty3 As String
                    Dim txt3 As TextBox
                    txt3 = CType(dg_tapeNumber.Rows(i).Cells(2).FindControl("txt_Karachi"), TextBox)
                    qty3 = txt3.Text
                    objSave.TapeNumber = qty3

                    objSave.stockRegisterDetaiID = dg_tapeNumber.Rows(i).Cells(0).Text
                    objSave.StationID = dg_tapeNumber.Rows(i).Cells(4).Text
                    objSave.SaveRecord()

                    ''********************************************''
                    ''************ Get TapeLibraryID *************''
                    ''********************************************''

                    Dim LibraryID As Integer
                    Dim objLibID As New BusinessFacade.TapeIssuance()
                    Dim qty As String
                    Dim txt As TextBox
                    txt = CType(dg_tapeNumber.Rows(i).Cells(2).FindControl("txt_Karachi"), TextBox)
                    qty = txt.Text
                    objLibID.TapeNumber = qty

                    LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

                    ''**********************************************************************''
                    ''****************** Tape Issuance ( Save Record ) *********************''
                    ''**********************************************************************''
                    Dim IsRecycle As Integer = 0
                    If chkIsRecycle.Checked = True Then
                        IsRecycle = 1
                    Else
                        IsRecycle = 0
                    End If


                    strCommand = "TapeIssuance_OneByOne_SaveRecord_WithRecycle " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & LibraryID & "," & ProgramChildID & "," & IsRecycle
                    'strCommand = "TapeIssuance_OneByOne_SaveRecord_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & LibraryID & "," & ProgramChildID 
                    Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
                    If Con.State = Data.ConnectionState.Closed Then
                        Con.Open()
                    End If
                    cmd1By1_Save.Connection = Con
                    cmd1By1_Save.executenonquery()
                    Con.Close()

                    IsTapeChecked = 1
                    ''*************************** End **************************************''
                    ''**********************************************************************''

                Next
                lblErr_2.Text = "Record has been Saved!!"

                Try
                    If IsTapeChecked = 1 Then
                        Dim ObjReminder As New BusinessFacade.ReminderService()
                        ObjReminder.EmployeeName = txt_Employee_1By1.Text
                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                        If EmailAddress <> "N/A" Then
                            Dim Arr As Array = Split(EmailAddress, "@")
                            If Arr.Length = 2 Then
                                sendmail(EmailAddress)
                            Else
                                lblErr_2.Text += "But Email Not send due to Invalid Email Address!"
                            End If
                        Else
                            lblErr_2.Text += "But Email Not send b/c Email Address not Exists!"
                        End If

                        ' sendmail()
                    End If
                Catch ex As Exception

                End Try

                Clrscr_2()
                txt_SRDID.Text = String.Empty
                txt_TapeTypeID.Text = String.Empty
            End If
        Else
            If Trim(txt_ProgramChild_1by1.Text) = "" Then
                lblErr_2.Text = "Please Enter Program!!"
            Else
                lblErr_2.Text = "Selected Program is Not Valid!!"
            End If
        End If
    End Sub

    Private Sub Clrscr_2()

        txt_TapeNumber.Text = String.Empty
        txt_SRDID.Text = String.Empty
        txt_TapeTypeID.Text = String.Empty

        txt_Employee_1By1.Text = String.Empty
        txt_ProgramChild_1by1.Text = String.Empty
        txtEntryDate_1By1.Text = Date.Now().ToString("dd-MMM-yyyy")
        lblErr_2.ForeColor = Drawing.Color.Red
        Err_TapeNo.Visible = False

        chkIsRecycle.Checked = False
    End Sub

    Protected Sub bttnCancel_1By1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_1By1.Click

        ''**********************************************************''
        ''******************** Get Employee ID *********************''
        ''**********************************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        Dim ObjTemp As New BusinessFacade.NewTapeNumber
        'ObjTemp.Delete_Temp()
        ObjTemp.UserID = UserID
        ObjTemp.Delete_Temp_New()
        Bind_Temp()

        Clrscr_2()
        lblErr_2.Text = String.Empty
    End Sub

    Private Sub Bind_Temp()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


        Dim objuser As New BusinessFacade.NewTapeNumber()
        'dg_tapeNumber.DataSource = objuser.GetRecord_Temp()
        objuser.UserID = UserID
        dg_tapeNumber.DataSource = objuser.GetRecord_Temp_New()
        dg_tapeNumber.Columns(0).Visible = True
        dg_tapeNumber.Columns(1).Visible = True
        dg_tapeNumber.DataBind()
        dg_tapeNumber.Columns(0).Visible = False
        dg_tapeNumber.Columns(1).Visible = False

    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub LnkAddProgram_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkAddProgram.Click
        Try
            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            If Trim(txt_ProgramChild_1by1.Text) <> "" Then
                Dim isSave As Integer
                Dim OBjSave As New BusinessFacade.ProgramChild()
                OBjSave.ProgramChildName = txt_ProgramChild_1by1.Text
                OBjSave.UserID = UserID
                isSave = OBjSave.Save_ProgramMaster_ProgramChild()

                If isSave = 0 Then
                    lblErr_2.Text = "Attention :- This Program is Already Exists..You Dont Need to Add Again !!"
                Else
                    lblErr_2.Text = "Program has been Added.. Please Procede Further !!"
                    lblErr_2.ForeColor = Drawing.Color.Green
                    Err_Program.Visible = False
                End If

                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "function RefreshForm() {window.opener.location.href=" + """ + BulkTapeManagement.aspx" + ";"""
                script = script + "}</script>"
                Page.RegisterClientScriptBlock("test", script)
            Else
                lblErr_2.Text = "Attention :- Please Enter New Program First !!"
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        ' sendmail()

    End Sub



    Public Sub sendmail(ByVal EmailAddress As String)
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Program</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Issued Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;  font-size:small; "" >Issued by</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Get TapeType ***************''
        ''****************************************''



        Dim TapeType As String
        Dim objTapeType As New BusinessFacade.TapeType()
        TapeType = objTapeType.GetTapeType_byTapeNumber(txt_TapeNumber.Text.Replace("#", ""))



        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'

        If dg_tapeNumber.Rows.Count > 0 Then


            Dim P As Integer
            Dim Sno As Integer = 0
            For P = 0 To dg_tapeNumber.Rows.Count - 1
                Dim MyCheckBox As TextBox = CType(dg_tapeNumber.Rows(P).Cells(2).Controls(1), TextBox)
                If MyCheckBox.Text.Length > 0 Then

                    Dim empname As String = txt_Employee_1By1.Text

                    If empname.Contains("-") Then
                        headermessage = headermessage.Replace("EmpName", empname)
                        headermessage = headermessage.Replace("empname1", empname)
                    Else
                        headermessage = headermessage.Replace("EmpName", empname)
                        headermessage = headermessage.Replace("empname1", empname)
                    End If

                    headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1By1.Text)
                    headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


                    Dim ObjReminder As New BusinessFacade.ReminderService()
                    ObjReminder.EmployeeName = empname
                    EmailAddress = ObjReminder.GetEmailAddress()


                    iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

                    iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
                    iterativemessage = iterativemessage.Replace("Tapenumber", MyCheckBox.Text)
                    iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_1By1.Text)
                    iterativemessage = iterativemessage.Replace("Tapetype", dg_tapeNumber.Rows(P).Cells(3).Text.ToString)
                    iterativemessage = iterativemessage.Replace("Program", txt_ProgramChild_1by1.Text)
                    iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
                    iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

                    fullheader = fullheader + iterativemessage

                    Sno = Sno + 1

                End If

                ''************ End *************''
                ''******************************''



            Next
        Else

            Dim empname As String = txt_Employee_1By1.Text

            If empname.Contains("-") Then
                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empname1", empname)
            Else
                headermessage = headermessage.Replace("EmpName", empname)
                headermessage = headermessage.Replace("empname1", empname)
            End If

            headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1By1.Text)
            headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


            Dim ObjReminder As New BusinessFacade.ReminderService()
            ObjReminder.EmployeeName = empname
            EmailAddress = ObjReminder.GetEmailAddress()


            iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

            iterativemessage = iterativemessage.Replace("autonumber", "1")
            iterativemessage = iterativemessage.Replace("Tapenumber", txt_TapeNumber.Text.Replace("#", ""))
            iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_1By1.Text)
            iterativemessage = iterativemessage.Replace("Tapetype", TapeType)
            iterativemessage = iterativemessage.Replace("Program", txt_ProgramChild_1by1.Text)
            iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
            iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

            fullheader = fullheader + iterativemessage


        End If
        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage & fullheader & footermessage

        'If EmailAddress = "N/A" Then
        '    lblErr_2.Text = "Tape Return Acknowledgement not send because of not get of Email"
        '    Return
        'End If

        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If

        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")




    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        sendBulkmail()
    End Sub

    Public Sub sendBulkmail()
        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tapes in bulk has been issued to you on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
        Dim middlemessage1 As String = "<table border =""0"" style=""padding: inherit; width: 400px; "" cellspacing =""0"" cellpadding =""0""><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Ref No:</td><td  style ="" text-align:left; font-size:small; "">refno</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Department:</td><td  style ="" text-align:left; font-size:small; "">Dept</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">City:</td><td  style ="" text-align:left; font-size:small; "">Cty</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Issuance Date:</td><td  style ="" text-align:left; font-size:small; "">Issuancedate</td></tr></table><br />"
        Dim middlemessage As String = "<table  style=""padding: inherit; width: 600px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Quantity</td></tr>"
        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
        Dim fullheader As String = ""

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Get TapeType ***************''
        ''****************************************''



        Dim TapeType As String
        Dim objTapeType As New BusinessFacade.TapeType()
        TapeType = objTapeType.GetTapeType_byTapeNumber(txt_TapeNumber.Text.Replace("#", ""))



        '**********************************************'
        '**************** Get EmployeeID **************'
        '**********************************************'


        Dim EmailAddress As String = ""


        '  If dg_tapeNumber.Rows.Count > 0 Then


        Dim P As Integer
        Dim Sno As Integer = 0
        For P = 0 To dg.Rows.Count - 1
            Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
            If MyTextBox.Text <> "" Then

                Dim empname As String = txt_EmployeeName.Text()

                If empname.Contains("-") Then
                    headermessage = headermessage.Replace("EmpName", empname)
                    headermessage = headermessage.Replace("empname1", empname)
                Else
                    headermessage = headermessage.Replace("EmpName", empname)
                    headermessage = headermessage.Replace("empname1", empname)
                End If

                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate.Text)
                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))

                middlemessage1 = middlemessage1.Replace("refno", txt_RefNo.Text)
                middlemessage1 = middlemessage1.Replace("Dept", txt_DepartmentName.Text)
                middlemessage1 = middlemessage1.Replace("Cty", txt_CityName.Text)
                middlemessage1 = middlemessage1.Replace("Issuancedate", txtEntryDate.Text)




                Dim ObjReminder As New BusinessFacade.ReminderService()
                ObjReminder.EmployeeName = empname
                EmailAddress = ObjReminder.GetEmailAddress()


                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Quantity</td></tr>"

                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)

                iterativemessage = iterativemessage.Replace("Tapetype", dg.Rows(P).Cells(1).Text.ToString)
                iterativemessage = iterativemessage.Replace("Quantity", MyTextBox.Text)
                'lbl_UserName.Text)

                fullheader = fullheader + iterativemessage

                Sno = Sno + 1

            End If

            ''************ End *************''
            ''******************************''



        Next

        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

        message = message & headermessage & middlemessage1 & middlemessage & fullheader & footermessage

        If EmailAddress = "N/A" Then
            lblErr_2.Text = "Tape Return Acknowledgement not send because of not get of Email"
            Return
        End If


        Dim strFrom As String
        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
            ''****** For Lahore *******''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
            ''****** For Islamabad *****''
            strFrom = "<EMAIL>"
        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
            ''****** For Peshawar *****''
            strFrom = "<EMAIL>"
        Else
            ''****** For Karachi *******''
            strFrom = "<EMAIL>"
        End If


        Dim Mail As New Email
        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")




    End Sub



End Class

'Partial Class TapeManagement_BulkTapeManagement
'    Inherits System.Web.UI.Page
'    Dim DS As New System.Data.DataSet
'    Dim strCommand As String
'    Dim connStr As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
'    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(connStr)

'    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

'        If Request.Cookies("UserInfo") Is Nothing Then
'            Response.Redirect("../Login.aspx")
'        Else
'            Master.FooterText = Request.Cookies("userinfo")("username")

'            lbl_UserName.Text = Master.FooterText
'            Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
'            lbl_UserName.Text = arr_UserID(1)

'        End If

'        Bind_Temp()

'        If Not Page.IsPostBack Then
'            Me.txtSave.Text = ""

'            If Request.Cookies("UserInfo") Is Nothing Then
'                Response.Redirect("../Login.aspx")
'            Else
'                Master.FooterText = Request.Cookies("userinfo")("username")

'                lbl_UserName.Text = Master.FooterText
'                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
'                lbl_UserName.Text = arr_UserID(1)

'            End If

'            If Not Request.QueryString.Get("BulkTapeIssuanceID") = "" Then
'                txtBulkTapeIssuanceID.Text = Request.QueryString.Get("BulkTapeIssuanceID").ToString
'                Me.BindEditControls(Convert.ToInt32(txtBulkTapeIssuanceID.Text))
'                Me.FillEditGrid(Convert.ToInt32(txtBulkTapeIssuanceID.Text))
'            Else
'                FillGrid()
'                txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")
'            End If

'            txtEntryDate_1By1.Text = Date.Now().ToString("dd-MMM-yyyy")

'            If Request.QueryString.Get("Refresh") = "1" Then
'                Dim script As String
'                script = "<script language='javascript' type='text/javascript'>"
'                script = script + "function RefreshForm() {window.opener.location.href=" + """ + BulkTapeManagement.aspx" + ";"""
'                script = script + "}</script>"
'                Page.RegisterClientScriptBlock("test", script)
'            End If

'            'Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
'            'If CokieBaseStationID <> 1 Then
'            '    Dim K As Integer
'            '    For K = 0 To dg_tapeNumber.Rows.Count - 1
'            '        dg_tapeNumber.Rows(K).Cells(0).Enabled = False
'            '    Next
'            'End If

'        End If
'    End Sub

'    Private Sub BindEditControls(ByVal ID As Integer)
'        Dim Dt As Data.DataTable
'        Dim ObjUpdate As New BusinessFacade.TapeIssuance()
'        ObjUpdate.BulkTapeIssuanceID = ID
'        Dt = ObjUpdate.BindEditControls(ID)

'        txtEntryDate.Text = Convert.ToDateTime(Dt.Rows(0).Item(1).ToString).ToString("dd-MMM-yyyy")
'        txt_EmployeeName.Text = Dt.Rows(0).Item(2).ToString
'        txt_DepartmentName.Text = Dt.Rows(0).Item(3).ToString
'        txt_CityName.Text = Dt.Rows(0).Item(4).ToString
'        txt_RefNo.Text = Dt.Rows(0).Item(5).ToString

'    End Sub

'    Private Sub FillEditGrid(ByVal ID As Integer)

'        Dim ObjUpdate As New BusinessFacade.TapeIssuance()
'        ObjUpdate.BulkTapeIssuanceID = ID
'        Dg_Edit.DataSource = ObjUpdate.BindEditGrid(ID)
'        Dg_Edit.DataBind()
'        bttnSave.Text = "Update"
'    End Sub

'    Private Sub FillGrid()

'        ''*********************************************''
'        ''******* Bulk Tape Issuance (Grid Fill) ******''
'        ''*********************************************''

'        'dg.DataSource = New BusinessFacade.TapeIssuance().GetRecords_BuldTapeIssuance()
'        dg.DataSource = New BusinessFacade.TapeIssuance().GetRecords_TapeumberIssuance()
'        dg.Columns(0).Visible = True
'        dg.DataBind()
'        dg.Columns(0).Visible = False

'    End Sub

'    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
'        Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
'        If CokieBaseStationID <> 1 Then
'            lblErr.Text = "You are not allowed to Enter Bulk Tapes!!"
'            Exit Sub
'        End If

'        If bttnSave.Text = "Save" Then
'            ''**********************************************''
'            ''********* Check Department Vs Employee *******''
'            ''**********************************************''

'            Dim DepartmentName As String
'            Dim objDeptVsEmp As New BusinessFacade.TapeIssuance()
'            objDeptVsEmp.EmployeeName = txt_EmployeeName.Text
'            DepartmentName = objDeptVsEmp.GetDepartmentName_by_EmployeeName(objDeptVsEmp.EmployeeName)

'            If (txt_DepartmentName.Text).ToLower = DepartmentName.ToLower Then

'                ''****************************************''
'                ''************ Get User ID ***************''
'                ''****************************************''

'                Dim UserID As Integer
'                Dim objUserID As New BusinessFacade.Employee()
'                objUserID.SM_LoginID = lbl_UserName.Text
'                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


'                ''***************************************''
'                ''********** Get DepartmentID ***********''
'                ''***************************************''

'                Dim DepartmentID As Integer
'                Dim objDeptID As New BusinessFacade.TapeIssuance()
'                objDeptID.DepartmentName = txt_DepartmentName.Text
'                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


'                ''***************************************''
'                ''************ Get CityID ***************''
'                ''***************************************''

'                Dim CityID As Integer
'                Dim objCityID As New BusinessFacade.TapeIssuance()
'                objCityID.CityName = txt_CityName.Text
'                CityID = objCityID.GetCityID_byCityName(objCityID.CityName)

'                ''***************************************''
'                ''********** Get EmployeeID *************''
'                ''***************************************''

'                Dim EmployeeID As Integer
'                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
'                objEmployeeID.EmployeeName = txt_EmployeeName.Text
'                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


'                If CityID = 0 Then
'                    Err_City.Visible = True
'                Else
'                    Err_City.Visible = False
'                End If

'                If DepartmentID = 0 Then
'                    Err_Department.Visible = True
'                Else
'                    Err_Department.Visible = False
'                End If

'                If EmployeeID = 0 Then
'                    Err_Employee_Bulk.Visible = True
'                Else
'                    Err_Employee_Bulk.Visible = False
'                End If
'                If txtEntryDate.Text = "" Then
'                    ErrDate.Visible = True
'                Else
'                    ErrDate.Visible = False
'                End If
'                'Dim DeptVsEmployee As Integer
'                'If EmployeeID = 0 And DeptIssueorNot.SelectedItem.ToString = "No" Then
'                '    Err_Employee_Bulk.Visible = True
'                '    DeptVsEmployee = 1
'                'Else
'                '    Err_Employee_Bulk.Visible = False
'                '    DeptVsEmployee = 0
'                'End If

'                If txt_RefNo.Text = "" Then
'                    txt_RefNo.Text = "-N/A-"
'                    '    Err_RefNo.Visible = True
'                Else
'                    '    Err_RefNo.Visible = False
'                End If
'                ''***************************************''
'                ''*** Count No of Checked Check Boxes ***''
'                ''***************************************''

'                Dim R As Integer
'                Dim Count As Integer = 0
'                For R = 0 To dg.Rows.Count - 1
'                    Dim MyTextBox_1 As TextBox = CType(dg.Rows(R).Cells(2).Controls(1), TextBox)
'                    If MyTextBox_1.Text <> "" Then
'                        Count = Count + 1
'                    End If
'                Next

'                ''***********************************************************************''
'                ''****************************** < Save Record > ************************''
'                ''***********************************************************************''

'                'If txt_RefNo.Text = "" Then
'                '    lblErr.Text = "Please Enter Ref. No !!"
'                'Else
'                If DepartmentID = 0 Then
'                    lblErr.Text = "Please Select Department"
'                ElseIf CityID = 0 Then
'                    lblErr.Text = "Please Select City !!"
'                ElseIf EmployeeID = 0 Then
'                    lblErr.Text = "Please Select Employee !!"
'                ElseIf txtEntryDate.Text = "" Then
'                    lblErr.Text = "Please Select Entry Date !!"
'                ElseIf Count = 0 Then
'                    lblErr.Text = "Please Enter Quantity !!"
'                Else

'                    ''**********************************************''
'                    ''***** Bilk Tape Issuance ( Save Record ) *****''
'                    ''**********************************************''

'                    strCommand = "BulkTapeManagement_SaveRecord '" & txtEntryDate.Text & "','" & txt_RefNo.Text & "'," & UserID & "," & DeptIssueorNot.SelectedValue & "," & CityID & "," & DepartmentID & "," & EmployeeID
'                    Dim cmd1 = New System.Data.SqlClient.SqlCommand(strCommand)
'                    If Con.State = Data.ConnectionState.Closed Then
'                        Con.Open()
'                    End If
'                    cmd1.Connection = Con
'                    cmd1.executenonquery()
'                    Con.Close()

'                    ''************************''
'                    ''******* MasterID *******''
'                    ''************************''

'                    Dim MasterID As Integer
'                    Dim ObjBulkID As New BusinessFacade.TapeIssuance()
'                    MasterID = ObjBulkID.MaxBulkTapeIssanceID()


'                    ''***********************************************''
'                    ''** Bilk Tape Issuance Detail ( Save Record ) **''
'                    ''***********************************************''

'                    Dim P As Integer
'                    For P = 0 To dg.Rows.Count - 1
'                        Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
'                        If MyTextBox.Text <> "" Then
'                            strCommand = "BulkTapeManagement_Detail_SaveRecord " & MasterID & "," & dg.Rows(P).Cells(0).Text & "," & MyTextBox.Text
'                            Dim cmd2 = New System.Data.SqlClient.SqlCommand(strCommand)
'                            If Con.State = Data.ConnectionState.Closed Then
'                            End If
'                            cmd2.Connection = Con
'                        End If
'                    Next


'                    ''***********************************************''
'                    ''***************** Send Email ******************''
'                    Try

'                    Catch ex As Exception

'                    End Try



'                    ''********************* End *********************''
'                    ''***********************************************''

'                    lblErr.Text = "Record has been Saved !!"
'                    Clrscr()
'                    FillGrid()
'                End If

'            Else
'                lblErr.Text = "Employee not found in " & txt_DepartmentName.Text & " Department !!"
'            End If

'        Else
'            UpdateIssueRecord()
'        End If

'    End Sub

'    Private Sub UpdateIssueRecord()
'        ''**********************************************''
'        ''********* Check Department Vs Employee *******''
'        ''**********************************************''

'        Dim DepartmentName As String
'        Dim objDeptVsEmp As New BusinessFacade.TapeIssuance()
'        objDeptVsEmp.EmployeeName = txt_EmployeeName.Text
'        DepartmentName = objDeptVsEmp.GetDepartmentName_by_EmployeeName(objDeptVsEmp.EmployeeName)

'        If (txt_DepartmentName.Text).ToLower = DepartmentName.ToLower Then

'            ''****************************************''
'            ''************ Get User ID ***************''
'            ''****************************************''

'            Dim UserID As Integer
'            Dim objUserID As New BusinessFacade.Employee()
'            objUserID.SM_LoginID = lbl_UserName.Text
'            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


'            ''***************************************''
'            ''********** Get DepartmentID ***********''
'            ''***************************************''

'            Dim DepartmentID As Integer
'            Dim objDeptID As New BusinessFacade.TapeIssuance()
'            objDeptID.DepartmentName = txt_DepartmentName.Text
'            DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)


'            ''***************************************''
'            ''************ Get CityID ***************''
'            ''***************************************''

'            Dim CityID As Integer
'            Dim objCityID As New BusinessFacade.TapeIssuance()
'            objCityID.CityName = txt_CityName.Text
'            CityID = objCityID.GetCityID_byCityName(objCityID.CityName)

'            ''***************************************''
'            ''********** Get EmployeeID *************''
'            ''***************************************''

'            Dim EmployeeID As Integer
'            Dim objEmployeeID As New BusinessFacade.TapeIssuance()
'            objEmployeeID.EmployeeName = txt_EmployeeName.Text
'            EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


'            If CityID = 0 Then
'                Err_City.Visible = True
'            Else
'                Err_City.Visible = False
'            End If

'            If DepartmentID = 0 Then
'                Err_Department.Visible = True
'            Else
'                Err_Department.Visible = False
'            End If

'            If EmployeeID = 0 Then
'                Err_Employee_Bulk.Visible = True
'            Else
'                Err_Employee_Bulk.Visible = False
'            End If
'            If txtEntryDate.Text = "" Then
'                ErrDate.Visible = True
'            Else
'                ErrDate.Visible = False
'            End If

'            If txt_RefNo.Text = "" Then
'                txt_RefNo.Text = "-N/A-"
'            End If
'            ''***************************************''
'            ''*** Count No of Checked Check Boxes ***''
'            ''***************************************''

'            Dim R As Integer
'            Dim Count As Integer = 0
'            For R = 0 To Dg_Edit.Rows.Count - 1
'                Dim MyTextBox_1 As TextBox = CType(Dg_Edit.Rows(R).Cells(2).Controls(1), TextBox)
'                If MyTextBox_1.Text <> "" Then
'                    Count = Count + 1
'                End If
'            Next

'            ''***********************************************************************''
'            ''*************************** < Updatye Record > ************************''
'            ''***********************************************************************''

'            If DepartmentID = 0 Then
'                lblErr.Text = "Please Select Department"
'            ElseIf CityID = 0 Then
'                lblErr.Text = "Please Select City !!"
'            ElseIf EmployeeID = 0 Then
'                lblErr.Text = "Please Select Employee !!"
'            ElseIf txtEntryDate.Text = "" Then
'                lblErr.Text = "Please Select Entry Date !!"
'            ElseIf Count = 0 Then
'                lblErr.Text = "Please Enter Quantity !!"
'            Else

'                ''**********************************************''
'                ''**** Bilk Tape Issuance ( Update Record ) ****''
'                ''**********************************************''

'                strCommand = "BulkTapeManagement_Update '" & txtEntryDate.Text & "','" & txt_RefNo.Text & "'," & UserID & "," & DeptIssueorNot.SelectedValue & "," & CityID & "," & DepartmentID & "," & EmployeeID & "," & txtBulkTapeIssuanceID.Text
'                Dim cmd1 = New System.Data.SqlClient.SqlCommand(strCommand)
'                If Con.State = Data.ConnectionState.Closed Then
'                    Con.Open()
'                End If
'                cmd1.Connection = Con
'                cmd1.executenonquery()
'                Con.Close()

'                ''*************************************************''
'                ''** Bilk Tape Issuance Detail ( Update Record ) **''
'                ''*************************************************''

'                Dim P As Integer
'                For P = 0 To Dg_Edit.Rows.Count - 1
'                    Dim MyTextBox As TextBox = CType(Dg_Edit.Rows(P).Cells(2).Controls(1), TextBox)
'                    If MyTextBox.Text <> "" Then
'                        strCommand = "BulkTapeManagement_Detail_Update " & Convert.ToInt32(txtBulkTapeIssuanceID.Text) & "," & Dg_Edit.Rows(P).Cells(0).Text & "," & MyTextBox.Text
'                        Dim cmd2 = New System.Data.SqlClient.SqlCommand(strCommand)
'                        If Con.State = Data.ConnectionState.Closed Then
'                            Con.Open()
'                        End If
'                        cmd2.Connection = Con
'                        cmd2.executenonquery()
'                    End If
'                Next

'                ''********************* End *********************''

'                lblErr.Text = "Record has been Udpated !!"
'                Clrscr()
'                FillGrid()
'                txtBulkTapeIssuanceID.Text = String.Empty
'                bttnSave.Text = "Save"
'            End If

'        Else
'            lblErr.Text = "Employee not found in " & txt_DepartmentName.Text & " Department !!"
'        End If
'    End Sub

'    Protected Sub bttnCencel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCencel.Click
'        Clrscr()
'        lblErr.Text = String.Empty
'    End Sub

'    Private Sub Clrscr()
'        txt_RefNo.Text = String.Empty
'        txt_CityName.Text = String.Empty
'        txt_CityName.Text = String.Empty
'        txt_DepartmentName.Text = String.Empty
'        DeptIssueorNot.SelectedIndex = "0"
'        txt_EmployeeName.Text = String.Empty
'        txtEntryDate.Text = Date.Now().ToString("dd-MMM-yyyy")

'        Dim J As Integer
'        For J = 0 To dg.Rows.Count - 1
'            Dim MyTextBox As TextBox = CType(dg.Rows(J).Cells(2).Controls(1), TextBox)
'            If MyTextBox.Text <> "" Then
'                MyTextBox.Text = String.Empty
'            End If

'        Next

'    End Sub

'    Protected Sub bttnAddTapeNO_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnAddTapeNO.Click

'        ''****************************************''
'        ''************ Get User ID ***************''
'        ''****************************************''

'        Dim UserID As String
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

'        If UserID <> "0" Then
'            Dim script As String
'            script = "<script language='javascript' type='text/javascript'>"
'            script = script + "window.onload=function OpenReport() {"
'            script = script + "var mywindow = window.open('BulkNewTapeNumber.aspx?@UserID=" + UserID + "', 'mywindow'); "
'            script = script + "}</script>"

'            Page.RegisterClientScriptBlock("test", script)
'        End If


'    End Sub

'    Protected Sub bttnSave_1By1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave_1By1.Click

'        Me.txtSave.Text = "Save"
'        Err_TapeNo.Visible = False


'        ''**********************************************************''
'        ''******************** Get Employee ID *********************''
'        ''**********************************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


'        ''**********************************************************''
'        ''******************** Get Employee ID *********************''
'        ''**********************************************************''

'        Dim EmployeeID As Integer
'        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
'        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
'        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)


'        ''**********************************************************''
'        ''********************Get Program ChildID ******************''
'        ''**********************************************************''

'        Dim ProgramChildID As Integer
'        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
'        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
'        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)


'        ''***********************************************************''
'        ''************** Get DapartmentID by EmployeeID *************''
'        ''***********************************************************''

'        Dim DepartmentID As Integer
'        If EmployeeID <> 0 Then
'            Dim ObjDepartmentID As New BusinessFacade.Employee()
'            ObjDepartmentID.EmployeeID = EmployeeID
'            DepartmentID = ObjDepartmentID.GetDepartmentID_by_EmployeeID(ObjDepartmentID.EmployeeID)
'        End If

'        ''************************************************************''

'        Dim Cnt As Integer
'        Cnt = dg_tapeNumber.Rows.Count

'        If Cnt = 0 Then

'            If EmployeeID = 0 Then
'                Err_Employee.Visible = True
'            Else
'                Err_Employee.Visible = False
'            End If

'            If ProgramChildID = 0 Then
'                Err_Program.Visible = True
'            Else
'                Err_Program.Visible = False
'            End If

'            If EmployeeID = 0 Then
'                lblErr_2.Text = "Please Select Employee !!"
'            ElseIf ProgramChildID = 0 Then
'                If txt_ProgramChild_1by1.Text = "" Then
'                    lblErr_2.Text = "Please Select Program !!"
'                Else
'                    lblErr_2.Text = "Selected Program is Not Valid !!"
'                End If
'            Else
'                Dim H As Integer
'                Dim G As Integer
'                Dim L As Integer
'                If txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" And txt_TapeNumber.Text <> "" Then


'                    Dim arr As Array = Split(txt_TapeNumber.Text, "#")
'                    If arr.Length = 2 Then
'                        txt_TapeNumber.Text = arr(1)
'                    End If

'                    ''*********************************************''
'                    ''******** Get Stock Register DetailID ********''
'                    ''*********************************************''

'                    Dim ObjID As New BusinessFacade.TapeIssuance()
'                    ObjID.TapeNumber = txt_TapeNumber.Text
'                    H = ObjID.TapeIssuance_GetSRDID(ObjID.TapeNumber)


'                    ''*********************************************''
'                    ''************** Get Tape Type ID *************''
'                    ''*********************************************''

'                    Dim ObJTapeTyep As New BusinessFacade.TapeIssuance()
'                    ObJTapeTyep.TapeNumber = txt_TapeNumber.Text
'                    G = ObJTapeTyep.TapeIssuance_GetTapeType(ObJTapeTyep.TapeNumber)


'                    ''*********************************************''
'                    ''************* Get Tape Library ID ***********''
'                    ''*********************************************''

'                    Dim objLibID As New BusinessFacade.TapeIssuance()
'                    objLibID.TapeNumber = txt_TapeNumber.Text
'                    L = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

'                End If

'                ''*******************************************************************''
'                ''********************* Check Tape Availability *********************''
'                ''*******************************************************************''

'                Dim ObjCheckTape_Available As New BusinessFacade.NewTapeNumber()
'                ObjCheckTape_Available.TapeLibraryID = L
'                Dim IsAvail As Integer = ObjCheckTape_Available.TapeNumber_IsAvailable_GetRecord1()

'                ''*******************************************************************''
'                ''********************* Check Tape IsBlank **************************''
'                ''*******************************************************************''

'                Dim ObjCheckTape_isBlank As New BusinessFacade.NewTapeNumber()
'                ObjCheckTape_isBlank.TapeLibraryID = L
'                Dim IsBlank As Integer = ObjCheckTape_isBlank.TapeNumber_IsBlank()

'                ''*******************************************************************''
'                ''******************** Check Tape IsAvailSearch *********************''
'                ''*******************************************************************''

'                Dim ObjCheckTape_AvailableSearch As New BusinessFacade.NewTapeNumber()
'                ObjCheckTape_AvailableSearch.TapeLibraryID = L
'                Dim IsAvailSearch As Integer = ObjCheckTape_AvailableSearch.TapeNumber_IsAvailableSearch_GetRecord1()

'                If IsAvail = 0 Then
'                    ''*******************************************''
'                    ''*********** Check Tape Issuance ***********''
'                    ''*******************************************''
'                    Dim ObjSetIsAvailable As New BusinessFacade.TapeIssuance()
'                    ObjSetIsAvailable.TapeLibraryID = L
'                    IsAvail = ObjSetIsAvailable.SetIsAvailable()

'                End If

'                '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
'                If H = "0" And G = "0" And txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" Then
'                    lblErr_2.Text = "Please add new TapeNumber!!"
'                    Err_TapeNo.Visible = True

'                ElseIf IsAvailSearch = 0 Then
'                    lblErr_2.Text = "This Tape has not been Available further!!"
'                    Err_TapeNo.Visible = True
'                ElseIf IsAvail = 0 Then
'                    lblErr_2.Text = "This Tape has been issued already!!"
'                    Err_TapeNo.Visible = True
'                ElseIf IsBlank = 0 Then
'                    lblErr_2.Text = "This Tape is Already Archived !!"
'                ElseIf txt_SRDID.Text = "" And txt_TapeTypeID.Text = "" Then
'                    txt_SRDID.Text = H
'                    txt_TapeTypeID.Text = G
'                    Dim TapeLibID As Integer = G

'                    ''**********************************************************************''
'                    ''****************** Tape Issuance ( Save Record ) *********************''
'                    ''**********************************************************************''

'                    If DepartmentID <> 0 Then

'                        Dim IsRecycle As Integer = 0
'                        If chkIsRecycle.Checked = True Then
'                            IsRecycle = 1
'                        Else
'                            IsRecycle = 0
'                        End If

'                        'strCommand = "TapeIssuance_OneByOne_SaveRecord_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & L & "," & ProgramChildID
'                        strCommand = "TapeIssuance_OneByOne_SaveRecord_WithRecycle " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & L & "," & ProgramChildID & "," & IsRecycle

'                        Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
'                        If Con.State = Data.ConnectionState.Closed Then
'                            Con.Open()
'                        End If
'                        cmd1By1_Save.Connection = Con
'                        cmd1By1_Save.executenonquery()
'                        Con.Close()
'                        lblErr_2.Text = "Record has been Saved!!"


'                        Try
'                            Dim ObjReminder As New BusinessFacade.ReminderService()
'                            ObjReminder.EmployeeName = txt_Employee_1By1.Text
'                            Dim EmailAddress As String = ObjReminder.GetEmailAddress()
'                            If EmailAddress <> "N/A" Then
'                                Dim Arr As Array = Split(EmailAddress, "@")
'                                If Arr.Length = 2 Then
'                                    sendmail(EmailAddress)
'                                Else
'                                    lblErr_2.Text += "But Email Not send due to Invalid Email Address!"
'                                End If
'                            Else
'                                lblErr_2.Text += "But Email Not send b/c Email Address not Exists!"
'                            End If
'                        Catch ex As Exception

'                        End Try


'                        Clrscr_2()



'                    End If

'                End If

'            End If
'        End If

'        ''************************************************************''
'        ''*********** Save Record for More Than One Tapes ************''
'        ''************************************************************''

'        If Cnt <> 0 Then

'            If EmployeeID = 0 Then
'                Err_Employee.Visible = True
'            Else
'                Err_Employee.Visible = False
'            End If

'            If ProgramChildID = 0 Then
'                Err_Program.Visible = True
'            Else
'                Err_Program.Visible = False
'            End If

'            If EmployeeID = 0 Then
'                lblErr_2.Text = "Please Select Employee !!"
'            ElseIf ProgramChildID = 0 Then
'                lblErr_2.Text = "Please Select Program !!"
'            Else

'                SaveRecord_Issuance_1()

'                ''*********************************************''
'                ''*********** Clear dbo.Temp table ************''
'                ''*********************************************''

'                Dim ObjTemp As New BusinessFacade.NewTapeNumber
'                ObjTemp.UserID = UserID
'                ObjTemp.Delete_Temp_New()


'                ''******************* End *********************''
'                ''*********************************************''

'                Bind_Temp()

'            End If
'        End If

'    End Sub

'    Private Sub Update_TapeLibrary()
'        Dim TapeLibraryID As Integer
'        Dim ObjLib As New BusinessFacade.TapeIssuance()
'        TapeLibraryID = ObjLib.MaxLianraryID()

'        Dim ObjUpdate As New BusinessFacade.NewTapeNumber()
'        ObjUpdate.IsAvailable = 0
'        ObjUpdate.TapeLibraryID = TapeLibraryID
'        ObjUpdate.TapeIsAvailable_Update()
'    End Sub

'    Private Sub SaveRecord_Issuance()

'        '''''''''' Get DepartmentID '''''''''''''''
'        Dim EmployeeID As Integer
'        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
'        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
'        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

'        '''''''''' For Getting Program Child ID ''''''''''''''''
'        Dim ProgramChildID As Integer
'        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
'        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
'        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

'        '''''''''''''''''''''''''''''''''''''''''''''''''''''''
'        If ProgramChildID <> 0 Then
'            ' strCommand = "TapeIssuance_OneByOne_SaveRecord " & 3 & "," & ddl_Employee_1By1.SelectedValue
'            strCommand = "TapeIssuance_OneByOne_SaveRecord " & 3 & "," & EmployeeID
'            Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
'            If Con.State = Data.ConnectionState.Closed Then
'                Con.Open()
'            End If
'            cmd1By1_Save.Connection = Con
'            cmd1By1_Save.executenonquery()
'            Con.Close()

'            '''''''''''''''''''''''''''''''''''
'            Dim IssID As Integer
'            Dim Objuser As New BusinessFacade.TapeIssuance()
'            IssID = Objuser.MaxTapeIssanceID()

'            ''''''''''''''''''''''''''''''''''''
'            Dim LibID As Integer
'            Dim ObjLib As New BusinessFacade.TapeIssuance()
'            LibID = ObjLib.MaxLianraryID()

'            ''''''''''''''''''''''''''''''''''''''
'            'strCommand = "TapeIssuanceDetail_OneByOne_SaveRecord " & IssID & "," & LibID & "," & ddl_Program_1by1.SelectedValue
'            strCommand = "TapeIssuanceDetail_OneByOne_SaveRecord " & IssID & "," & LibID & "," & ProgramChildID
'            Dim cmdDetail = New System.Data.SqlClient.SqlCommand(strCommand)
'            If Con.State = Data.ConnectionState.Closed Then
'                Con.Open()
'            End If
'            cmdDetail.Connection = Con
'            cmdDetail.executenonquery()
'            Con.Close()
'            lblErr_2.Text = "Record has been Saved!!"
'            Clrscr_2()
'            txt_SRDID.Text = String.Empty
'            txt_TapeTypeID.Text = String.Empty
'        End If


'    End Sub

'    Private Sub SaveRecord_Issuance_1()

'        ''**********************************************************''
'        ''******************** Get Employee ID *********************''
'        ''**********************************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


'        ''**********************************************************''
'        ''******************** Get Employee ID *********************''
'        ''**********************************************************''

'        Dim EmployeeID As Integer
'        Dim objEmployeeID As New BusinessFacade.TapeIssuance()
'        objEmployeeID.EmployeeName = txt_Employee_1By1.Text
'        EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)

'        ''**********************************************************''
'        ''********************Get Program ChildID ******************''
'        ''**********************************************************''

'        Dim ProgramChildID As Integer
'        Dim ObjProgramChildID As New BusinessFacade.ProgramChild()
'        ObjProgramChildID.ProgramChildName = txt_ProgramChild_1by1.Text
'        ProgramChildID = ObjProgramChildID.GetProgramChildID_AutoComplete(ObjProgramChildID.ProgramChildName)

'        ''***********************************************************''
'        ''************** Get DapartmentID by EmployeeID *************''
'        ''***********************************************************''

'        Dim DepartmentID As Integer
'        If EmployeeID <> 0 Then
'            Dim ObjDepartmentID As New BusinessFacade.Employee()
'            ObjDepartmentID.EmployeeID = EmployeeID
'            DepartmentID = ObjDepartmentID.GetDepartmentID_by_EmployeeID(ObjDepartmentID.EmployeeID)
'        End If

'        ''***********************************************************''


'        Dim IsTapeChecked As Integer = 0

'        If ProgramChildID <> 0 Then

'            If DepartmentID <> 0 Then

'                Dim i As Integer
'                For i = 0 To dg_tapeNumber.Rows.Count - 1


'                    ''********************************************''
'                    ''** Save Tape Number in Tape Library Table **''
'                    ''********************************************''

'                    Dim objSave As New BusinessFacade.NewTapeNumber()
'                    objSave.TapeTypeID = dg_tapeNumber.Rows(i).Cells(1).Text

'                    Dim qty3 As String
'                    Dim txt3 As TextBox
'                    txt3 = CType(dg_tapeNumber.Rows(i).Cells(2).FindControl("txt_Karachi"), TextBox)
'                    qty3 = txt3.Text
'                    objSave.TapeNumber = qty3

'                    objSave.stockRegisterDetaiID = dg_tapeNumber.Rows(i).Cells(0).Text
'                    objSave.StationID = dg_tapeNumber.Rows(i).Cells(4).Text
'                    objSave.SaveRecord()

'                    ''********************************************''
'                    ''************ Get TapeLibraryID *************''
'                    ''********************************************''

'                    Dim LibraryID As Integer
'                    Dim objLibID As New BusinessFacade.TapeIssuance()
'                    Dim qty As String
'                    Dim txt As TextBox
'                    txt = CType(dg_tapeNumber.Rows(i).Cells(2).FindControl("txt_Karachi"), TextBox)
'                    qty = txt.Text
'                    objLibID.TapeNumber = qty

'                    LibraryID = objLibID.TapeIssuance_GetLibraryID(objLibID.TapeNumber)

'                    ''**********************************************************************''
'                    ''****************** Tape Issuance ( Save Record ) *********************''
'                    ''**********************************************************************''
'                    Dim IsRecycle As Integer = 0
'                    If chkIsRecycle.Checked = True Then
'                        IsRecycle = 1
'                    Else
'                        IsRecycle = 0
'                    End If


'                    strCommand = "TapeIssuance_OneByOne_SaveRecord_WithRecycle " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & LibraryID & "," & ProgramChildID & "," & IsRecycle
'                    'strCommand = "TapeIssuance_OneByOne_SaveRecord_New " & UserID & "," & EmployeeID & "," & DepartmentID & ",'" & txtEntryDate_1By1.Text & "'," & LibraryID & "," & ProgramChildID 
'                    Dim cmd1By1_Save = New System.Data.SqlClient.SqlCommand(strCommand)
'                    If Con.State = Data.ConnectionState.Closed Then
'                        Con.Open()
'                    End If
'                    cmd1By1_Save.Connection = Con
'                    cmd1By1_Save.executenonquery()
'                    Con.Close()

'                    IsTapeChecked = 1
'                    ''*************************** End **************************************''
'                    ''**********************************************************************''

'                Next
'                lblErr_2.Text = "Record has been Saved!!"

'                Try
'                    If IsTapeChecked = 1 Then
'                        Dim ObjReminder As New BusinessFacade.ReminderService()
'                        ObjReminder.EmployeeName = txt_Employee_1By1.Text
'                        Dim EmailAddress As String = ObjReminder.GetEmailAddress()
'                        If EmailAddress <> "N/A" Then
'                            Dim Arr As Array = Split(EmailAddress, "@")
'                            If Arr.Length = 2 Then
'                                sendmail(EmailAddress)
'                            Else
'                                lblErr_2.Text += "But Email Not send due to Invalid Email Address!"
'                            End If
'                        Else
'                            lblErr_2.Text += "But Email Not send b/c Email Address not Exists!"
'                        End If

'                        ' sendmail()
'                    End If
'                Catch ex As Exception

'                End Try

'                Clrscr_2()
'                txt_SRDID.Text = String.Empty
'                txt_TapeTypeID.Text = String.Empty
'            End If
'        Else
'            If Trim(txt_ProgramChild_1by1.Text) = "" Then
'                lblErr_2.Text = "Please Enter Program!!"
'            Else
'                lblErr_2.Text = "Selected Program is Not Valid!!"
'            End If
'        End If
'    End Sub

'    Private Sub Clrscr_2()

'        txt_TapeNumber.Text = String.Empty
'        txt_SRDID.Text = String.Empty
'        txt_TapeTypeID.Text = String.Empty

'        txt_Employee_1By1.Text = String.Empty
'        txt_ProgramChild_1by1.Text = String.Empty
'        txtEntryDate_1By1.Text = Date.Now().ToString("dd-MMM-yyyy")
'        lblErr_2.ForeColor = Drawing.Color.Red
'        Err_TapeNo.Visible = False

'        chkIsRecycle.Checked = False
'    End Sub

'    Protected Sub bttnCancel_1By1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnCancel_1By1.Click

'        ''**********************************************************''
'        ''******************** Get Employee ID *********************''
'        ''**********************************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

'        Dim ObjTemp As New BusinessFacade.NewTapeNumber
'        'ObjTemp.Delete_Temp()
'        ObjTemp.UserID = UserID
'        ObjTemp.Delete_Temp_New()
'        Bind_Temp()

'        '''Clrscr_2()
'        lblErr_2.Text = String.Empty
'    End Sub

'    Private Sub Bind_Temp()

'        ''****************************************''
'        ''************ Get User ID ***************''
'        ''****************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)


'        Dim objuser As New BusinessFacade.NewTapeNumber()
'        'dg_tapeNumber.DataSource = objuser.GetRecord_Temp()
'        objuser.UserID = UserID
'        dg_tapeNumber.DataSource = objuser.GetRecord_Temp_New()
'        dg_tapeNumber.Columns(0).Visible = True
'        dg_tapeNumber.Columns(1).Visible = True
'        dg_tapeNumber.DataBind()
'        dg_tapeNumber.Columns(0).Visible = False
'        dg_tapeNumber.Columns(1).Visible = False

'    End Sub

'    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
'        Response.Redirect("../Home/Home.aspx")
'    End Sub

'    Protected Sub LnkAddProgram_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkAddProgram.Click
'        Try
'            ''****************************************''
'            ''************ Get User ID ***************''
'            ''****************************************''

'            Dim UserID As Integer
'            Dim objUserID As New BusinessFacade.Employee()
'            objUserID.SM_LoginID = lbl_UserName.Text
'            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

'            If Trim(txt_ProgramChild_1by1.Text) <> "" Then
'                Dim isSave As Integer
'                Dim OBjSave As New BusinessFacade.ProgramChild()
'                OBjSave.ProgramChildName = txt_ProgramChild_1by1.Text
'                OBjSave.UserID = UserID
'                isSave = OBjSave.Save_ProgramMaster_ProgramChild()

'                If isSave = 0 Then
'                    lblErr_2.Text = "Attention :- This Program is Already Exists..You Dont Need to Add Again !!"
'                Else
'                    lblErr_2.Text = "Program has been Added.. Please Procede Further !!"
'                    lblErr_2.ForeColor = Drawing.Color.Green
'                    Err_Program.Visible = False
'                End If

'                Dim script As String
'                script = "<script language='javascript' type='text/javascript'>"
'                script = script + "function RefreshForm() {window.opener.location.href=" + """ + BulkTapeManagement.aspx" + ";"""
'                script = script + "}</script>"
'                Page.RegisterClientScriptBlock("test", script)
'            Else
'                lblErr_2.Text = "Attention :- Please Enter New Program First !!"
'            End If
'        Catch ex As Exception
'            Throw
'        End Try
'    End Sub

'    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
'       ' sendmail()

'    End Sub



'    Public Sub sendmail(ByVal EmailAddress As String)
'        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
'        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tape have been issued to you on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
'        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
'        Dim middlemessage As String = "<table  style=""padding: inherit; width: 837px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Number</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Program</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Issued Date</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Archival / Blank</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center;  font-size:small; "" >Issued by</td></tr>"
'        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
'        Dim fullheader As String = ""

'        ''****************************************''
'        ''************ Get User ID ***************''
'        ''****************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

'        ''****************************************''
'        ''************ Get TapeType ***************''
'        ''****************************************''



'        Dim TapeType As String
'        Dim objTapeType As New BusinessFacade.TapeType()
'        TapeType = objTapeType.GetTapeType_byTapeNumber(txt_TapeNumber.Text.Replace("#", ""))



'        '**********************************************'
'        '**************** Get EmployeeID **************'
'        '**********************************************'

'        If dg_tapeNumber.Rows.Count > 0 Then


'            Dim P As Integer
'            Dim Sno As Integer = 0
'            For P = 0 To dg_tapeNumber.Rows.Count - 1
'                Dim MyCheckBox As TextBox = CType(dg_tapeNumber.Rows(P).Cells(2).Controls(1), TextBox)
'                If MyCheckBox.Text.Length > 0 Then

'                    Dim empname As String = txt_Employee_1By1.Text

'                    If empname.Contains("-") Then
'                        headermessage = headermessage.Replace("EmpName", empname)
'                        headermessage = headermessage.Replace("empname1", empname)
'                    Else
'                        headermessage = headermessage.Replace("EmpName", empname)
'                        headermessage = headermessage.Replace("empname1", empname)
'                    End If

'                    headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1By1.Text)
'                    headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


'                    Dim ObjReminder As New BusinessFacade.ReminderService()
'                    ObjReminder.EmployeeName = empname
'                    EmailAddress = ObjReminder.GetEmailAddress()


'                    iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

'                    iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)
'                    iterativemessage = iterativemessage.Replace("Tapenumber", MyCheckBox.Text)
'                    iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_1By1.Text)
'                    iterativemessage = iterativemessage.Replace("Tapetype", dg_tapeNumber.Rows(P).Cells(3).Text.ToString)
'                    iterativemessage = iterativemessage.Replace("Program", txt_ProgramChild_1by1.Text)
'                    iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
'                    iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

'                    fullheader = fullheader + iterativemessage

'                    Sno = Sno + 1

'                End If

'                ''************ End *************''
'                ''******************************''



'            Next
'        Else

'            Dim empname As String = txt_Employee_1By1.Text

'            If empname.Contains("-") Then
'                headermessage = headermessage.Replace("EmpName", empname)
'                headermessage = headermessage.Replace("empname1", empname)
'            Else
'                headermessage = headermessage.Replace("EmpName", empname)
'                headermessage = headermessage.Replace("empname1", empname)
'            End If

'            headermessage = headermessage.Replace("ReturnedDate", txtEntryDate_1By1.Text)
'            headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))


'            Dim ObjReminder As New BusinessFacade.ReminderService()
'            ObjReminder.EmployeeName = empname
'            EmailAddress = ObjReminder.GetEmailAddress()


'            iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder""  style ="" text-align:center; font-size:small;  "">Tapenumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Program</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;  font-size:small;  "">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center; font-size:small;   "" >Returnedby</td></tr>"

'            iterativemessage = iterativemessage.Replace("autonumber", "1")
'            iterativemessage = iterativemessage.Replace("Tapenumber", txt_TapeNumber.Text.Replace("#", ""))
'            iterativemessage = iterativemessage.Replace("IssuedDate", txtEntryDate_1By1.Text)
'            iterativemessage = iterativemessage.Replace("Tapetype", TapeType)
'            iterativemessage = iterativemessage.Replace("Program", txt_ProgramChild_1by1.Text)
'            iterativemessage = iterativemessage.Replace("Archivalorblank", "Blank")
'            iterativemessage = iterativemessage.Replace("Returnedby", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

'            fullheader = fullheader + iterativemessage


'        End If
'        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

'        message = message & headermessage & middlemessage & fullheader & footermessage

'        'If EmailAddress = "N/A" Then
'        '    lblErr_2.Text = "Tape Return Acknowledgement not send because of not get of Email"
'        '    Return
'        'End If

'        Dim strFrom As String
'        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
'            ''****** For Lahore *******''
'            strFrom = "<EMAIL>"
'        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
'            ''****** For Islamabad *****''
'            strFrom = "<EMAIL>"
'        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
'            ''****** For Peshawar *****''
'            strFrom = "<EMAIL>"
'        Else
'            ''****** For Karachi *******''
'            strFrom = "<EMAIL>"
'        End If

'        Dim Mail As New Email
'        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")




'    End Sub

'    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
'        sendBulkmail()
'    End Sub

'    Public Sub sendBulkmail()
'        Dim message As String = "<html><head>    <title></title>        <style>  .body{ font-family:Times New Roman; font-size:small  }  .tableborder{   border:1px solid black;border-collapse:collapse;font-family:Times New Roman; font-size:small;   }  .tableheader{border:1px solid black;border-collapse:collapse;background-color:#c9c9c9;}  </style></head><body class=""body"">"
'        Dim headermessage As String = " <table><tr><td>Dear <b>&nbsp;EmpName,</b></td></tr></table><br /><table><tr><td> This is to acknowledge that following tapes in bulk has been issued to you on <b>ReturnedDate  </b> at <b>RetunedTime.</b> </td> </tr></table><br />"
'        Dim footermessage As String = "</table> <table><tr><td><br />Regards,</td><br /></tr><tr><td><b>DAMSUser </b></td></tr><tr><td>Archive Department</td></tr></table><br /><br /><br /> <table width =""100%""><tr><td align =""left""><b>This is auto generated mail, generated by system.</b></td></tr></table></body></html>"
'        Dim middlemessage1 As String = "<table border =""0"" style=""padding: inherit; width: 400px; "" cellspacing =""0"" cellpadding =""0""><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Ref No:</td><td  style ="" text-align:left; font-size:small; "">refno</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Department:</td><td  style ="" text-align:left; font-size:small; "">Dept</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">City:</td><td  style ="" text-align:left; font-size:small; "">Cty</td></tr><tr><td  style ="" font-weight:bold;  text-align:left; font-size:small; "">Issuance Date:</td><td  style ="" text-align:left; font-size:small; "">Issuancedate</td></tr></table><br />"
'        Dim middlemessage As String = "<table  style=""padding: inherit; width: 600px; "" cellspacing =""0"" cellpadding =""0""><tr><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small; "">S.No</td><td class =""tableheader""  style ="" font-weight:bold;  text-align:center; font-size:small;  "">Tape Type</td><td class =""tableheader"" style ="" font-weight:bold;  text-align:center; font-size:small;  "" >Quantity</td></tr>"
'        Dim iterativemessage As String = " <tr><td  class =""tableborder"" style ="" text-align:center;"">autonumber</td><td class =""tableborder""  style ="" text-align:center;"">Tapenumber</td><td class =""tableborder"" style ="" text-align:center;"" >TapeType</td><td class =""tableborder"" style ="" text-align:center;"" >IssuedDate</td><td class =""tableborder""  style ="" text-align:center;"">Archivalorblank</td><td class =""tableborder"" style ="" text-align:center;"" >Returnedby</td></tr>"
'        Dim fullheader As String = ""

'        ''****************************************''
'        ''************ Get User ID ***************''
'        ''****************************************''

'        Dim UserID As Integer
'        Dim objUserID As New BusinessFacade.Employee()
'        objUserID.SM_LoginID = lbl_UserName.Text
'        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

'        ''****************************************''
'        ''************ Get TapeType ***************''
'        ''****************************************''



'        Dim TapeType As String
'        Dim objTapeType As New BusinessFacade.TapeType()
'        TapeType = objTapeType.GetTapeType_byTapeNumber(txt_TapeNumber.Text.Replace("#", ""))



'        '**********************************************'
'        '**************** Get EmployeeID **************'
'        '**********************************************'


'        Dim EmailAddress As String = ""


'        '  If dg_tapeNumber.Rows.Count > 0 Then


'        Dim P As Integer
'        Dim Sno As Integer = 0
'        For P = 0 To dg.Rows.Count - 1
'            Dim MyTextBox As TextBox = CType(dg.Rows(P).Cells(2).Controls(1), TextBox)
'            If MyTextBox.Text <> "" Then

'                Dim empname As String = txt_EmployeeName.Text()

'                If empname.Contains("-") Then
'                    headermessage = headermessage.Replace("EmpName", empname)
'                    headermessage = headermessage.Replace("empname1", empname)
'                Else
'                    headermessage = headermessage.Replace("EmpName", empname)
'                    headermessage = headermessage.Replace("empname1", empname)
'                End If

'                headermessage = headermessage.Replace("ReturnedDate", txtEntryDate.Text)
'                headermessage = headermessage.Replace("RetunedTime", System.DateTime.Now.ToString("HH:mm:ss tt"))

'                middlemessage1 = middlemessage1.Replace("refno", txt_RefNo.Text)
'                middlemessage1 = middlemessage1.Replace("Dept", txt_DepartmentName.Text)
'                middlemessage1 = middlemessage1.Replace("Cty", txt_CityName.Text)
'                middlemessage1 = middlemessage1.Replace("Issuancedate", txtEntryDate.Text)




'                Dim ObjReminder As New BusinessFacade.ReminderService()
'                ObjReminder.EmployeeName = empname
'                EmailAddress = ObjReminder.GetEmailAddress()


'                iterativemessage = "<tr><td  class =""tableborder"" style ="" text-align:center; font-size:small; "">autonumber</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Tapetype</td><td class =""tableborder"" style ="" text-align:center; font-size:small;  "" >Quantity</td></tr>"

'                iterativemessage = iterativemessage.Replace("autonumber", Sno + 1)

'                iterativemessage = iterativemessage.Replace("Tapetype", dg.Rows(P).Cells(1).Text.ToString)
'                iterativemessage = iterativemessage.Replace("Quantity", MyTextBox.Text)
'                'lbl_UserName.Text)

'                fullheader = fullheader + iterativemessage

'                Sno = Sno + 1

'            End If

'            ''************ End *************''
'            ''******************************''



'        Next

'        footermessage = footermessage.Replace("DAMSUser", Request.Cookies("userinfo")("userfullname")) 'lbl_UserName.Text)

'        message = message & headermessage & middlemessage1 & middlemessage & fullheader & footermessage

'        If EmailAddress = "N/A" Then
'            lblErr_2.Text = "Tape Return Acknowledgement not send because of not get of Email"
'            Return
'        End If


'        Dim strFrom As String
'        If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
'            ''****** For Lahore *******''
'            strFrom = "<EMAIL>"
'        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
'            ''****** For Islamabad *****''
'            strFrom = "<EMAIL>"
'        ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
'            ''****** For Peshawar *****''
'            strFrom = "<EMAIL>"
'        Else
'            ''****** For Karachi *******''
'            strFrom = "<EMAIL>"
'        End If


'        Dim Mail As New Email
'        Mail.sendemail(EmailAddress, message, strFrom, System.DateTime.Now.Date.ToString("dd-MM-yyyy"), "issue")




'    End Sub



'End Class
