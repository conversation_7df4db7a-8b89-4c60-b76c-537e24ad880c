<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmKeyType.aspx.vb" Inherits="ApplicationSetup_frmKeyType" title="Home > Key Type > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="lnkHomePage" onclick="lnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w8">Home</asp:LinkButton> &gt; Key Type &gt; Add New</TD></TR><TR><TD style="HEIGHT: 39px"><TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 47px">Key Type</TD><TD><asp:TextBox id="txt_KeyType" runat="server" CssClass="mytext"></asp:TextBox></TD><TD>Content Type</TD><TD><asp:DropDownList id="ddl_ContentType" runat="server" CssClass="mytext" Width="152px">
                            </asp:DropDownList></TD></TR></TBODY></TABLE>&nbsp;<asp:Label id="lblErr" runat="server" ForeColor="Red" Width="320px" Font-Bold="True"></asp:Label></TD></TR><TR><TD style="HEIGHT: 27px" class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD align=right><asp:Label id="lblTotalRecords" runat="server" ForeColor="Red" __designer:wfdid="w10" Font-Bold="True"></asp:Label></TD></TR><TR><TD><asp:GridView id="dg_KeyType" runat="server" CssClass="gridContent" Width="632px" AllowPaging="True" OnSelectedIndexChanged="dg_KeyType_SelectedIndexChanged" OnPageIndexChanging="dg_KeyType_PageIndexChanging" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False"><Columns>
<asp:BoundField DataField="KeyTypeID" Visible="False" ApplyFormatInEditMode="True">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="KeyType" HeaderText="Key Type" ApplyFormatInEditMode="True"></asp:BoundField>
<asp:BoundField DataField="ContentTypeID" Visible="False">
<ItemStyle Width="0px" ForeColor="#F2F5FE"></ItemStyle>

<HeaderStyle Width="0px"></HeaderStyle>
</asp:BoundField>
<asp:BoundField DataField="ContentTypeName" HeaderText="ContentType Name"></asp:BoundField>
</Columns>

<SelectedRowStyle BackColor="#FFE0C0"></SelectedRowStyle>

<HeaderStyle CssClass="gridheader"></HeaderStyle>
</asp:GridView> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w18" ConfirmText="Do you want to Delete !" TargetControlID="BttnDelete"></cc1:ConfirmButtonExtender></TD></TR><TR><TD style="HEIGHT: 22px"><asp:TextBox id="txt_KeyTypeID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

