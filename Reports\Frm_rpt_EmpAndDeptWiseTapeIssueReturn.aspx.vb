Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_EmpAndDeptWiseTapeIssueReturn
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
                'If Session("ApplicationTicket") Is Nothing Then
                '    Response.Redirect("../Login.aspx")
                'Else
                '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
                '    Master.FooterText = obj.UserLoginID
                'End If

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                TxtEmployee.Enabled = False

                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            Dim paramValues As New Hashtable
            Dim strRptPath As String
            Dim strKeyWords As String = ""
            Dim rpt As String
            Dim qryString As String
            rpt = "rpt_ArchivalTape_Q6_Q7_New.rpt"

            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String

            Dept = ddlDepartment.SelectedValue

            'If chkEmployee.Checked = True Then
            '    Emp = "-1"
            'Else
            '    Emp = ddlEmployee.SelectedValue
            'End If

            If chkEmployee.Checked = True Then
                Emp = "-1"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    ' Emp = ddlEmployee.SelectedValue.ToString
                    Emp = EmployeeID.ToString
                Else
                    Emp = "-1"
                End If

            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            BaseStationID = ddlBaseStation.SelectedValue

            If ddlPDF.SelectedValue = "PDF" Then
                If ddlPendings.SelectedValue = "Ignore" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                ElseIf ddlPendings.SelectedValue = "No" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New_PendingNO.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)

                ElseIf ddlPendings.SelectedValue = "Yes" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New_PendingYes.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)

                End If

            Else
                If ddlPendings.SelectedValue = "Ignore" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)
                ElseIf ddlPendings.SelectedValue = "No" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New_PendingNO.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)

                ElseIf ddlPendings.SelectedValue = "Yes" Then
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"
                    script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ArchivalTape_Q6_Q7_New_PendingYes.rpt&@emp=" + Emp + "&@dept=" + Dept + "&@fromdate=" + FromDate + "&@todate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                    script = script + "}</script>"

                    Page.RegisterClientScriptBlock("test", script)

                End If

            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_EmpAndDeptWiseTapeIssueReturn.aspx"
            ObjSave.ReportName = "Archival --> Q 3. How Can I View Employee & Department Wise Tapes Issue And Return?"
            ObjSave.SaveRecord()

            ''******************************************************''
        Catch ex As Exception
            Throw
        End Try

    End Sub



    Protected Sub ddlDepartment_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlDepartment.SelectedIndexChanged

    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub
End Class
