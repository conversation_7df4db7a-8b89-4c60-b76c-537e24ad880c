Imports System
Imports System.Data
Imports Microsoft.VisualBasic
Imports System.Web
Imports System.Web.Mail
Imports System.IO

Partial Class ReminderService_LocalOSRReminders
    Inherits System.Web.UI.Page

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Not Page.IsPostBack Then

            '' Dim strConnection As String = "server=KHI-CCS-SRV\TRM;database=DAMS_NewDB;uid=sa;pwd=**********;connection timeout=0"
            Dim strConnection As String = "Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********"

            Dim objConnection As New Data.SqlClient.SqlConnection(strConnection)

            Dim dt As New DataTable
            dt = Microsoft.ApplicationBlocks.Data.SqlHelper.ExecuteDataset(objConnection, "GetOSRReminders_ForEmail", Request("OSRReminderID")).Tables(0)

            Me.txtUserName.Text = dt.Rows(0)(2)
            Me.txtTo.Text = dt.Rows(0)(3)
            Me.txtFrom.Text = dt.Rows(0)(5)
            Me.txtBCC.Text = dt.Rows(0)(7)
            Me.txtCC.Text = dt.Rows(0)(8)
            Me.txtconcernArchive.Text = dt.Rows(0)(9)
            Me.txtExtension.Text = dt.Rows(0)(10)
            Me.txtSender.Text = dt.Rows(0)(11)
            Me.txtSubject.Text = "Friendly Reminder"

            Dim blob As Byte() = Nothing
            blob = dt.Rows(0)(6)
            Dim fs As FileStream = Nothing

            ' Dim str As String = "C:\inetpub\wwwroot\EmailService\EmailService\OSRReminder.pdf"
            Dim str As String = Server.MapPath("~/Reports/") & "OSR_Reminders" & ".pdf" '"C:\Websites\Roshni3\DAMS\DAMS\Reports\OSRReminder.pdf"

            fs = New FileStream(str, FileMode.Create, FileAccess.Write)

            fs.Write(blob, 0, blob.Length)
            fs.Close()

            ''**********************************************************************************************************************************''


            CreateBody()
        End If
    End Sub

    Sub CreateBody()
        Dim Body As String
        txtBody.Text = Body
        Body = "Dear " & Me.txtUserName.Text & ",^^"
        Body += "Please find attached the summary and detail report of issue and return of Blank tapes.^^"
        Body += "You are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us.^^"
        Body += "Regards,^^"
        Body += Me.txtconcernArchive.Text + "^"
        Body += Me.txtExtension.Text
        Body = Body.Replace("^", vbNewLine)
        txtBody.Text = Body

        Body = Body.Replace("^", vbCrLf)

        txtBody.Text = Body
    End Sub

    Private Sub bttnSend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles bttnSend.Click

        Dim TestBody As String = ""


        TestBody = TestBody + _
        "<HTML>" & _
                    "<HEAD>" & _
                        "" & _
                        "</HEAD>" & _
                        "<BODY MS_POSITIONING=""GridLayout"">" & _
                        "<table border=""0"" cellpadding=""0"" cellspacing=""0""" & _
                            "HEIGHT: 990px" & _
                            "font-family: verdana" & _
                            "BACKGROUND-COLOR: #4396ca"" width=""100%"">" & _
                "<tr>" & _
                "<td colspan=""8"" style=""font-weight: bold; font-size: 20pt; font-family: verdana; color: Black; HEIGHT: 45px; BACKGROUND-COLOR: #cccc99"" valign=""bottom"" align=""left"">  &nbsp;&nbsp;Friendly Reminder</td>" & _
                "</tr>" & _
                "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">Dear" & "&nbsp;" & Me.txtUserName.Text & ",</td></tr>" & _
                "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">Please find attached the summary and detail report of issue and return of Blank tapes. </td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                      "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">You are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us.</td></tr>"


        TestBody = TestBody + "<tr><td colspan=""8"" style=""font-size: 11pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">Regards,</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">&nbsp;</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & txtconcernArchive.Text & "</td></tr>" & _
                    "<tr><td colspan=""8"" style=""font-size: 10pt; font-family: verdana"">" & Me.txtExtension.Text & "</td></tr>" & _
                "</table>" & _
                        "</BODY>" & _
                        "</HTML>"

        Dim SMTPServer As String = "mail.geo.tv"


        Dim mailNew As MailMessage = New MailMessage

        mailNew.Cc = Me.txtCC.Text
        mailNew.Bcc = Me.txtBCC.Text
        mailNew.To = Me.txtTo.Text
        mailNew.From = Me.txtFrom.Text

        'Dim MyAttachment As New MailAttachment("C:\inetpub\wwwroot\EmailService\EmailService\OSRReminder.pdf")
        'Dim MyAttachment As New MailAttachment("C:\Websites\Roshni3\DAMS\DAMS\Reports\OSRReminder.pdf")
        Dim MyAttachment As New MailAttachment(Server.MapPath("~/Reports/") & "OSR_Reminders" & ".pdf")
        mailNew.Attachments.Add(MyAttachment)

        mailNew.Subject = "Friendly Reminder"
        mailNew.BodyFormat = System.Web.Mail.MailFormat.Html
        SmtpMail.SmtpServer = SMTPServer


        mailNew.Body = TestBody

        SmtpMail.Send(mailNew)
        Me.lbMessage.Visible = True

        System.IO.File.Delete("C:\inetpub\wwwroot\EmailService\EmailService\OSRReminder.pdf")
        bttnSend.Enabled = False

    End Sub
End Class
