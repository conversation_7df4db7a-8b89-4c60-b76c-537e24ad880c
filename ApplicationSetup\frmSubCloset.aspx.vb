
Partial Class ApplicationSetup_frmSubCloset
    Inherits System.Web.UI.Page
    Dim I As Integer
   
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If

            BindCombo()
            FillGrid()
        End If
    End Sub

    Private Sub BindCombo()
        ddl_ClosetMaster.DataSource = New BusinessFacade.ClosetMaster().GetRecords()
        ddl_ClosetMaster.DataTextField = "ClosetMasterName"
        ddl_ClosetMaster.DataValueField = "ClosetMasterID"
        ddl_ClosetMaster.DataBind()
        ddl_ClosetMaster.Items.Insert(0, "--Select--")

        ddl_TapeType.DataSource = New BusinessFacade.TapeType().GetRecords()
        ddl_TapeType.DataTextField = "TapeType"
        ddl_TapeType.DataValueField = "TapeTypeID"
        ddl_TapeType.DataBind()
        ddl_TapeType.Items.Insert(0, "--Select--")

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.SubCloset().IsExists_SubCloset(txt_SubClosetName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : SubCloset already Exists !"
        Else
            If txt_SubClosetID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_SubCloset.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()

    End Sub

    Private Sub SaveRecord()
        If txt_SubClosetName.Text = "" Then
            lblErr.Text = "Please insert SubCloset Name!!"
        ElseIf ddl_ClosetMaster.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Closet Name!!"
        ElseIf ddl_TapeType.SelectedIndex = "0" Then
            lblErr.Text = "Please Select Tape Type!!"
        Else
            Dim ObjSubCloset As New BusinessFacade.SubCloset()
            ObjSubCloset.TapeTypeID = ddl_TapeType.SelectedValue
            ObjSubCloset.ClosetMasterID = ddl_ClosetMaster.SelectedValue
            ObjSubCloset.SubClosetName = txt_SubClosetName.Text
            If txt_Capacity.Text = "" Then
                ObjSubCloset.Capacity = 0
            Else
                ObjSubCloset.Capacity = txt_Capacity.Text
            End If
            ObjSubCloset.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjSubCloset As New BusinessFacade.SubCloset()
        ObjSubCloset.SubClostID = txt_SubClosetID.Text
        ObjSubCloset.TapeTypeID = ddl_TapeType.SelectedValue
        ObjSubCloset.ClosetMasterID = ddl_ClosetMaster.SelectedValue
        ObjSubCloset.SubClosetName = txt_SubClosetName.Text
        ObjSubCloset.Capacity = txt_Capacity.Text
        ObjSubCloset.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        dg_SubCloset.DataSource() = New BusinessFacade.SubCloset().GetRecords()
        dg_SubCloset.DataBind()
        'dg_SubCloset.Columns(0).Visible = False
        'dg_SubCloset.Columns(1).Visible = False
        'dg_SubCloset.Columns(4).Visible = False
    End Sub

    Protected Sub dg_SubCloset_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_SubCloset.RowCreated
        e.Row.Cells(1).Visible = False
        e.Row.Cells(2).Visible = False
        e.Row.Cells(5).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_SubCloset.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_SubCloset.SelectedIndex.ToString
        txt_SubClosetID.Text = Convert.ToInt32(dg_SubCloset.Rows(I).Cells(1).Text)
        ddl_ClosetMaster.SelectedValue = dg_SubCloset.Rows(I).Cells(2).Text
        txt_SubClosetName.Text = dg_SubCloset.Rows(I).Cells(4).Text
        ddl_TapeType.SelectedValue = dg_SubCloset.Rows(I).Cells(5).Text
        txt_Capacity.Text = dg_SubCloset.Rows(I).Cells(7).Text
        dg_SubCloset.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_SubClosetID.Text = "" Then
                lblErr.Text = "Please Select SubCloset First!!"
            Else
                Dim ObjSubCloset As New BusinessFacade.SubCloset()
                ObjSubCloset.SubClostID = txt_SubClosetID.Text
                ObjSubCloset.DeleteRecord(ObjSubCloset.SubClostID)
                FillGrid()
                clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_SubCloset.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This SubCloset is Already in Used !"
            clrscr()
            dg_SubCloset.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub clrscr()
        txt_SubClosetName.Text = String.Empty
        txt_SubClosetID.Text = String.Empty
        ddl_ClosetMaster.SelectedIndex = "0"
        ddl_TapeType.SelectedIndex = "0"
        txt_Capacity.Text = String.Empty
    End Sub

    'Protected Sub dg_SubCloset_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_SubCloset.PageIndexChanging
    '    dg_SubCloset.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_SubCloset.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        clrscr()
        lblErr.Text = String.Empty
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
