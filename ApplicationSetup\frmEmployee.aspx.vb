
Partial Class ApplicationSetup_frmEmployee
    Inherits System.Web.UI.Page
    Dim I As Integer
    Dim dt As New Data.DataTable

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            ViewState("Search") = Nothing
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If

            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If

            FillGrid()
            BindCombo()
        End If
    End Sub

    Private Sub BindCombo()

        ddl_EmpCategory.DataSource = New BusinessFacade.EmployeeCategory().GetRecords()
        ddl_EmpCategory.DataTextField = "EmployeeCategory"
        ddl_EmpCategory.DataValueField = "EmployeeCategoryID"
        ddl_EmpCategory.DataBind()
        ddl_EmpCategory.Items.Insert(0, "--Select--")

        ddl_Department.DataSource = New BusinessFacade.Department().GetRecords()
        ddl_Department.DataTextField = "DepartmentName"
        ddl_Department.DataValueField = "DepartmentID"
        ddl_Department.DataBind()
        ddl_Department.Items.Insert(0, "--Select--")

        ddl_Designation.DataSource = New BusinessFacade.Designation().GetRecords()
        ddl_Designation.DataTextField = "Designation"
        ddl_Designation.DataValueField = "DesignationID"
        ddl_Designation.DataBind()

    End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        If txt_EmpID.Text = "" Then
            SaveRecord()
        Else
            ''******************************''
            ''*** BaseStation Validation ***''
            ''******************************''

            Dim objValidation As New BusinessFacade.Employee()
            objValidation.EmployeeID = txt_EmpID.Text
            Dim BaseStationID As Integer = objValidation.Employee_BaseStationValidation()
            Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
            If BaseStationID = CokieBaseStationID Then
                UpdateRecord()
            Else
                lblErr.Text = "You are not allowed to Edit this Record!!"
            End If
            ''************ End *************''
            ''******************************''

        End If
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        ClearAuditHistory()

    End Sub

    Private Sub SaveRecord()

        Dim IsExists As String
        IsExists = New BusinessFacade.Employee().IsExists_Employee(txt_EmpName.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : Employee already Exists !"
        Else
            ''****************************************''
            ''************ Get User ID ***************''
            ''****************************************''

            Dim UserID As Integer
            Dim objUserID As New BusinessFacade.Employee()
            objUserID.SM_LoginID = lbl_UserName.Text
            UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

            ''****************************************''
            ''************ Save Record ***************''
            ''****************************************''

            If ddl_Department.SelectedIndex = "0" Then
                lblErr.Text = "Please Select Department!!"
            ElseIf ddl_EmpCategory.SelectedIndex = "0" Then
                lblErr.Text = "Please Select Employee Category!!"
            ElseIf txt_EmpName.Text = "" Then
                lblErr.Text = "Please Enter Employee Name!!"
            Else
                Dim objEmployee As New BusinessFacade.Employee()
                objEmployee.Address = txt_Address.Text
                objEmployee.DepartmentID = ddl_Department.SelectedValue
                objEmployee.DesignationID = ddl_Designation.SelectedValue
                objEmployee.Email = txt_Email.Text
                objEmployee.EmployeeCategoryID = ddl_EmpCategory.SelectedValue
                objEmployee.EmployeeName = txt_EmpName.Text
                objEmployee.EmployeeGeoCode = txt_EmpGeoCode.Text
                objEmployee.Mobile = txt_Mobile.Text
                objEmployee.NICNumber = txt_NIC.Text
                objEmployee.Phone = txt_Phone.Text
                objEmployee.IsActive = ddl_IsActive.SelectedValue
                objEmployee.UserID = UserID
                objEmployee.SaveRecord()
                FillGrid()
                lblErr.Text = "Record has been Saved!!"
            End If
        End If

    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        Dim objEmployee As New BusinessFacade.Employee()
        objEmployee.EmployeeID = txt_EmpID.Text
        objEmployee.Address = txt_Address.Text
        objEmployee.DepartmentID = ddl_Department.SelectedValue
        objEmployee.DesignationID = ddl_Designation.SelectedValue
        objEmployee.Email = txt_Email.Text
        objEmployee.EmployeeCategoryID = ddl_EmpCategory.SelectedValue
        objEmployee.EmployeeName = txt_EmpName.Text
        objEmployee.EmployeeGeoCode = txt_EmpGeoCode.Text
        objEmployee.Mobile = txt_Mobile.Text
        objEmployee.NICNumber = txt_NIC.Text
        objEmployee.Phone = txt_Phone.Text
        objEmployee.IsActive = ddl_IsActive.SelectedValue
        objEmployee.UserID = UserID
        objEmployee.UpdateRecord()

        If Not ViewState("Search") Is Nothing Then
            fillgrid_search()
        Else
            FillGrid()
        End If
        lblErr.Text = "Record has been Updated!!"
    End Sub

    Private Sub FillGrid()
        Try
            Dim dt As Data.DataTable
            dt = New BusinessFacade.Employee().GetRecords()
            dg_ContentType.DataSource() = dt
            dg_ContentType.Columns(0).Visible = True
            dg_ContentType.Columns(1).Visible = True
            dg_ContentType.Columns(4).Visible = True
            dg_ContentType.Columns(6).Visible = True
            dg_ContentType.DataBind()
            dg_ContentType.Columns(0).Visible = False
            dg_ContentType.Columns(1).Visible = False
            dg_ContentType.Columns(4).Visible = False
            dg_ContentType.Columns(6).Visible = False
            lblTotalRecords.Text = "Total Records : " & Convert.ToString(dt.Rows.Count)

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_ContentType.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_ContentType.SelectedIndex.ToString
        txt_EmpID.Text = Convert.ToInt32(dg_ContentType.Rows(I).Cells(1).Text)
        ddl_EmpCategory.SelectedValue = Convert.ToInt32(dg_ContentType.Rows(I).Cells(2).Text)
        txt_EmpGeoCode.Text = IIf(dg_ContentType.Rows(I).Cells(3).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(3).Text) 'dg_ContentType.Rows(I).Cells(3).Text
        txt_EmpName.Text = dg_ContentType.Rows(I).Cells(4).Text
        ddl_Department.SelectedValue = Convert.ToInt32(dg_ContentType.Rows(I).Cells(5).Text)
        txt_NIC.Text = IIf(dg_ContentType.Rows(I).Cells(6).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(6).Text) 'dg_ContentType.Rows(I).Cells(6).Text
        txt_Address.Text = IIf(dg_ContentType.Rows(I).Cells(8).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(8).Text) 'dg_ContentType.Rows(I).Cells(8).Text
        txt_Mobile.Text = IIf(dg_ContentType.Rows(I).Cells(9).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(9).Text) 'dg_ContentType.Rows(I).Cells(9).Text
        txt_Phone.Text = IIf(dg_ContentType.Rows(I).Cells(10).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(10).Text) 'dg_ContentType.Rows(I).Cells(10).Text
        txt_Email.Text = IIf(dg_ContentType.Rows(I).Cells(11).Text = "&nbsp;", "N/A", dg_ContentType.Rows(I).Cells(11).Text) 'dg_ContentType.Rows(I).Cells(11).Text
        'ddl_IsActive.SelectedItem.Text = dg_ContentType.Rows(I).Cells(12).Text
        'ddl_IsActive.SelectedItem.Text = IIf(dg_ContentType.Rows(I).Cells(12).Text = "&nbsp;", "Yes", dg_ContentType.Rows(I).Cells(12).Text)
        'ddl_IsActive.SelectedIndex = ddl_IsActive.Items.IndexOf(ddl_IsActive.Items.FindByText(dg_ContentType.Rows(I).Cells(12).Text))
        If dg_ContentType.Rows(I).Cells(12).Text = "True" Then
            ddl_IsActive.SelectedIndex = 0
        Else
            ddl_IsActive.SelectedIndex = 1
        End If
        ddl_Designation.SelectedValue = dg_ContentType.Rows(I).Cells(7).Text
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.Employee()
        ObjAudit.EmployeeID = txt_EmpID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_Employee()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_EmpID.Text = "" Then
                lblErr.Text = "Please Select Employee!!"
            Else

                ''******************************''
                ''*** BaseStation Validation ***''
                ''******************************''

                Dim objValidation As New BusinessFacade.Employee()
                objValidation.EmployeeID = txt_EmpID.Text
                Dim BaseStationID As Integer = objValidation.Employee_BaseStationValidation()
                Dim CokieBaseStationID As Integer = CInt(Request.Cookies("userinfo")("BaseStationID"))
                If BaseStationID = CokieBaseStationID Then

                    ''*********************''

                    Dim ObjIsUsed As New BusinessFacade.Employee()
                    ObjIsUsed.EmployeeID = txt_EmpID.Text
                    Dim IsUsed As String
                    IsUsed = ObjIsUsed.Employee_IsUse()

                    If IsUsed = "Exists" Then
                        lblErr.Text = "Attention: This Employee is Already in Used !"
                    Else
                        Dim objEmployee As New BusinessFacade.Employee()
                        objEmployee.EmployeeID = txt_EmpID.Text
                        objEmployee.DeleteRecord(objEmployee.EmployeeID)
                        FillGrid()
                        Clrscr()
                        lblErr.Text = "Record has been Deleted!!"
                        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                        ClearAuditHistory()
                    End If

                    ''*********************''
                   
                Else
                    lblErr.Text = "You are not allowed to Delete this Record!!"
                End If
                ''************ End *************''
                ''******************************''

            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Employee is Already in Used !"
            Clrscr()
            dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
    End Sub

    Private Sub Clrscr()
        txt_EmpGeoCode.Text = String.Empty
        txt_EmpID.Text = String.Empty
        txt_EmpName.Text = String.Empty
        txt_NIC.Text = String.Empty
        txt_Address.Text = String.Empty
        txt_Mobile.Text = String.Empty
        txt_Phone.Text = String.Empty
        txt_Email.Text = String.Empty
    End Sub

    Protected Sub dg_ContentType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_ContentType.PageIndexChanging
        If txt_SearchEmployee.Text = "" Then
            dg_ContentType.PageIndex = e.NewPageIndex()
            FillGrid()
            dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            Clrscr()
        Else
            dg_ContentType.PageIndex = e.NewPageIndex()
            fillgrid_search()
            dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
            Clrscr()
        End If     
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        FillGrid()
        lblErr.Text = String.Empty
        txt_SearchEmployee.Text = String.Empty
        ViewState("Search") = Nothing
        ClearAuditHistory()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub lnkSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lnkSearch.Click
        ViewState("Search") = Nothing
        fillgrid_search()
    End Sub

    Private Sub fillgrid_search()

        Dim objSearch As New BusinessFacade.Employee()
        objSearch.EmployeeName = txt_SearchEmployee.Text
        dt = objSearch.GetSingleEmployeeRecord(objSearch.EmployeeName)

        If dt.Rows(0).Item(0).ToString <> "0" Then
            dg_ContentType.DataSource = dt
            dg_ContentType.Columns(0).Visible = True
            dg_ContentType.Columns(1).Visible = True
            dg_ContentType.Columns(4).Visible = True
            dg_ContentType.Columns(6).Visible = True
            dg_ContentType.DataBind()
            dg_ContentType.Columns(0).Visible = False
            dg_ContentType.Columns(1).Visible = False
            dg_ContentType.Columns(4).Visible = False
            dg_ContentType.Columns(6).Visible = False
            lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)
        Else
            lblErr.Text = "Search Results: Employee is not valid !!"
        End If
        ViewState("Search") = dt
    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub
End Class
