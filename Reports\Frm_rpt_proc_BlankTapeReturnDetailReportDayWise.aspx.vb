Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_proc_BlankTapeReturnDetailReportDayWise
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()
    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkIgnoredate.Checked = True
                BindCombo()

            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim FromDate As String = ""
        Dim ToDate As String = ""

        If chkIgnoredate.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
            ToDate = Convert.ToDateTime(txt_ToDate.Text).ToString("dd-MMM-yyyy")
        End If

        Dim BaseStationID As String
        BaseStationID = ddlBaseStation.SelectedValue

        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_proc_BlankTapeReturnDetailReportDayWise.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_proc_BlankTapeReturnDetailReportDayWise.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "', 'mywindow','menubar=0,resizable=1,toolbars=0'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_proc_BlankTapeReturnDetailReportDayWise.aspx"
        ObjSave.ReportName = "Blank --> Q 11. How can I view Return Date wise Blank Tape Issue & Receive Report?"
        ObjSave.SaveRecord()

        ''******************************************************''
    End Sub
End Class

