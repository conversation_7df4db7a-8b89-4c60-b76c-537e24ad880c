<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmClosetMaster.aspx.vb" Inherits="ApplicationSetup_frmClosetMaster" title="Home > Closet Master > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_ClosetMaster" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading">&nbsp;<asp:LinkButton id="lnkHomePage" onclick="lnkHomePage_Click" runat="server" CssClass="labelheading" __designer:wfdid="w17">Home</asp:LinkButton>&nbsp;&gt; Closet Master &gt; Add New</TD></TR><TR><TD style="HEIGHT: 71px"><TABLE><TBODY><TR class="mytext"><TD>Closet Master Name</TD><TD><asp:TextBox id="txt_ClosetMasterName" runat="server" CssClass="mytext" Width="152px"></asp:TextBox></TD><TD style="WIDTH: 22px"></TD></TR><TR class="mytext"><TD style="HEIGHT: 21px" vAlign=top>City Name</TD><TD style="HEIGHT: 21px" vAlign=top><asp:DropDownList id="ddl_City" runat="server" CssClass="mytext" Width="160px">
                            </asp:DropDownList></TD><TD style="WIDTH: 22px; HEIGHT: 21px"></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_ContentType" runat="server" CssClass="gridContent" Width="512px" PageSize="25" AutoGenerateSelectButton="True" AutoGenerateColumns="False">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="ClosetMasterID" HeaderText="Closet Master ID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="ClosetMasterName" HeaderText="Closet Master Name" />
                        <asp:BoundField DataField="CityID" HeaderText="City ID" />
                        <asp:BoundField DataField="CityName" HeaderText="City Name" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD style="HEIGHT: 11px"><asp:TextBox id="txt_ClosetMasterID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w10" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !"></cc1:ConfirmButtonExtender>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

