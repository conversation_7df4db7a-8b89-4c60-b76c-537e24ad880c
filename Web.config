<?xml version="1.0"?>
<configuration>
  <configSections>
    <sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <!--<sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
				<section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication"/>
				<sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
					<section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="Everywhere"/>
					<section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication"/>
					<section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication"/>
				</sectionGroup>-->
    </sectionGroup>
    <!--</sectionGroup>-->
  </configSections>
  <connectionStrings>
    <add name="DAMS_NewDBConnectionString" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********;connection timeout=0" providerName="System.Data.SqlClient"/>
    <add name="DAMS_NewDBConnectionString2" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********;connection timeout=0" providerName="System.Data.SqlClient"/>
    <add name="DAMS_NewDBConnectionString3" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;Persist Security Info=True;User ID=sa;Password=********;connection timeout=0" providerName="System.Data.SqlClient"/>
    <add name="DAMS_NewDBConnectionString4" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;Persist Security Info=True;User ID=sa;Password=********;connection timeout=0" providerName="System.Data.SqlClient"/>
    <add name="DAMS_NewDBConnectionString5" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********" providerName="System.Data.SqlClient"/>
    <add name="DAMS_NewDBConnectionString6" connectionString="Data Source=KHI-ARCHIVE-DB;Initial Catalog=DAMS_NewDB;User ID=sa;Password=********" providerName="System.Data.SqlClient"/>
    <!--<add name="DAMS_NewDBConnectionString" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********;connection timeout=0" providerName="System.Data.SqlClient"/>
		<add name="DAMS_NewDBConnectionString2" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********;connection timeout=0" providerName="System.Data.SqlClient"/>
		<add name="DAMS_NewDBConnectionString3" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;Persist Security Info=True;User ID=sa;Password=**********;connection timeout=0" providerName="System.Data.SqlClient"/>
		<add name="DAMS_NewDBConnectionString4" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;Persist Security Info=True;User ID=sa;Password=**********;connection timeout=0" providerName="System.Data.SqlClient"/>
		<add name="DAMS_NewDBConnectionString5" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********" providerName="System.Data.SqlClient"/>
		<add name="DAMS_NewDBConnectionString6" connectionString="Data Source=KHI-CCS-SRV\TRM;Initial Catalog=DAMS_NewDB;User ID=sa;Password=**********" providerName="System.Data.SqlClient"/>-->
  </connectionStrings>
  <system.web>
    
        <httpRuntime maxRequestLength="1000000" />
    
    <customErrors mode="Off"/>
    <pages>
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      </controls>
    </pages>
    <!--
          Set compilation debug="true" to insert debugging
          symbols into the compiled page. Because this
          affects performance, set this value to true only
          during development.
    -->
    <compilation debug="true">
      <assemblies>
        <add assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
        <add assembly="System.Web.Extensions.Design, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Design, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="Infragistics2.WebUI.Misc.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.WebUI.Shared.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.WebUI.UltraWebGrid.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.WebUI.WebDateChooser.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.WebUI.WebDataInput.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.WebUI.UltraWebNavigator.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Xml, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Web, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Web.Services, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Drawing.Design, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.DirectoryServices, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Configuration, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.DirectoryServices.Protocols, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.EnterpriseServices, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.ServiceProcess, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Web.RegularExpressions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="Infragistics2.WebUI.UltraWebGrid.ExcelExport.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="Infragistics2.Excel.v6.2, Version=6.2.20062.34, Culture=neutral, PublicKeyToken=7DD5C3163F2CD0CB"/>
        <add assembly="CrystalDecisions.CrystalReports.Engine, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304"/>
        <add assembly="CrystalDecisions.ReportSource, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304"/>
        <add assembly="CrystalDecisions.Shared, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304"/>
        <add assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304"/>
        <add assembly="CrystalDecisions.Windows.Forms, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304"/>
        <!--<add assembly="Microsoft.Office.Interop.Excel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71E9BCE111E9429C"/>-->
        <add assembly="CrystalDecisions.ReportAppServer.ClientDoc, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"/>
        <add assembly="CrystalDecisions.Enterprise.Framework, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"/>
        <add assembly="CrystalDecisions.Enterprise.InfoStore, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"/>
      </assemblies>
    </compilation>
    <httpHandlers>
      <remove path="*.asmx" verb="*"/>
      <add path="*.asmx" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
      <add path="*_AppService.axd" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
      <add path="ScriptResource.axd" verb="GET,HEAD" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
      <add path="CrystalImageHandler.aspx" verb="GET" type="CrystalDecisions.Web.CrystalImageHandler, CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"/>
      <add path="ChartAxd.axd" verb="*" type="Dundas.Charting.WebControl.ChartHttpHandler" validate="false"/>
    </httpHandlers>
    <httpModules>
      <add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    </httpModules>
    <sessionState mode="InProc" stateConnectionString="tcpip=127.0.0.1:42424" sqlConnectionString="data source=127.0.0.1;Trusted_Connection=yes" cookieless="false" timeout="100000"/>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules>
      <add name="ScriptModule" preCondition="integratedMode" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    </modules>
    <handlers>
      <remove name="WebServiceHandlerFactory-Integrated"/>
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    </handlers>
  </system.webServer>
  <location allowOverride="true" inheritInChildApplications="true">
    <appSettings>
      <add key="ConnectionString" value="server=KHI-ARCHIVE-DB;database=DAMS_NewDB;uid=sa;pwd=********;connection timeout=0" />
      <add key="CCSSerevrConnectionString" value="server=**********\secman;database=Security_Manager;uid=sa;pwd=**********;connection timeout=0" />
      <add key="ApplicationID" value="25" />
      <add key="CrystalImageCleaner-AutoStart" value="true" />
      <add key="CrystalImageCleaner-Sleep" value="60000" />
      <add key="CrystalImageCleaner-Age" value="120000" />
      <add key="ChartHttpHandler" value="Storage=memory;Timeout=180;Url=~/temp/;" />
    </appSettings>
  </location>
</configuration>