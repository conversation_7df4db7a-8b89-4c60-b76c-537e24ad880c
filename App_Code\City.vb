Imports Microsoft.VisualBasic
Imports DAAB = Microsoft.ApplicationBlocks.Data.SqlHelper


Public Class City

    Dim strConnection As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    'Private strConnection As String = "server=soft-server\soft;database=DAMS_NewDB;uid=sa;password=**********"
    Private objConnection As New Data.SqlClient.SqlConnection(strConnection)

    Private m_CityID As Integer
    Private m_CountryID As Integer
    Private m_CityName As String
    Private m_CityShortName As String
    Private m_IsOSR As Integer
    Private m_CityID_SM As Integer


#Region " Object Properties "

    Public Property CityID() As Integer
        Get
            Return m_CityID
        End Get
        Set(ByVal value As Integer)
            m_CityID = value
        End Set
    End Property
    Public Property CountryID() As Integer
        Get
            Return m_CountryID
        End Get
        Set(ByVal value As Integer)
            m_CountryID = value
        End Set
    End Property
    Public Property CityName() As String
        Get
            Return m_CityName
        End Get
        Set(ByVal value As String)
            m_CityName = value
        End Set
    End Property
    Public Property CityShortName() As String
        Get
            Return m_CityShortName
        End Get
        Set(ByVal value As String)
            m_CityShortName = value
        End Set
    End Property

    Public Property IsOSR() As Integer
        Get
            Return m_IsOSR
        End Get
        Set(ByVal value As Integer)
            m_IsOSR = value
        End Set
    End Property
    Public Property CityID_SM() As Integer
        Get
            Return m_CityID_SM
        End Get
        Set(ByVal value As Integer)
            m_CityID_SM = value
        End Set
    End Property


#End Region


#Region " Data Manipulation Methods "

    Public Sub SaveRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "City_SaveRecord", DBNull.Value, m_CountryID, m_CityName, m_CityShortName, m_IsOSR, m_CityID_SM)
        Catch ex As Exception
            Throw
        End Try
    End Sub


    Public Sub UpdateRecord()
        Try
            DAAB.ExecuteNonQuery(objConnection, "City_SaveRecord", m_CityID, m_CountryID, m_CityName, m_CityShortName, m_IsOSR, m_CityID_SM)
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Public Sub DeleteRecord(ByVal id As Integer)
        Try
            DAAB.ExecuteNonQuery(objConnection, "City_DeleteRecord", id)
        Catch ex As Exception
            Throw
        End Try
    End Sub


#End Region


#Region " Data Retrieval Methods "

    Public Sub GetRecord(ByVal id As Integer)
        Try
            Dim dtbl As Data.DataTable = DAAB.ExecuteDataset(objConnection, "City_GetRecords", id).Tables(0)
            If (Not dtbl Is Nothing) And (dtbl.Rows.Count > 0) Then
                m_CityID = dtbl.Rows(0)("CityID")
                m_CountryID = dtbl.Rows(0)("CountryID")
                m_CityName = dtbl.Rows(0)("CityName")
                m_CityShortName = dtbl.Rows(0)("CityShortName")
                m_CityID_SM = dtbl.Rows(0)("CityID_SM")
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    'You may overload this method for retrieving records by using parameters and filters, 
    'e.g., GetRecords(filter1) or GetRecords(filter1, filter2) etc.
    Public Function GetRecords() As Data.DataTable
        Try
            Return DAAB.ExecuteDataset(objConnection, "City_GetRecords", DBNull.Value).Tables(0)
        Catch ex As Exception
            Throw
        End Try
    End Function


#End Region

End Class