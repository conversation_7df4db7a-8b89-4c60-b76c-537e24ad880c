<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="frmCountry.aspx.vb" Inherits="ApplicationSetup_frmCountry" title="Home > Country > Add New" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <asp:ScriptManager id="ScriptManager_Country" runat="server">
    </asp:ScriptManager>
    <asp:UpdatePanel id="UpdatePanel1" runat="server">
        <contenttemplate>
<TABLE width="100%"><TBODY><TR><TD style="HEIGHT: 21px; TEXT-DECORATION: underline" class="labelheading"><asp:LinkButton id="LnkHomePage" onclick="LnkHomePage_Click" runat="server" __designer:wfdid="w2" CssClass="labelheading">Home</asp:LinkButton> &gt; Country &gt; Add New</TD></TR><TR><TD vAlign=top><TABLE><TBODY><TR class="mytext"><TD>Country Name</TD><TD><asp:TextBox id="txt_CountryName" runat="server" CssClass="mytext"></asp:TextBox></TD><TD></TD></TR></TBODY></TABLE><asp:Label id="lblErr" runat="server" ForeColor="Red" Font-Bold="True" Width="424px"></asp:Label></TD></TR><TR><TD class="bottomMain">&nbsp;<asp:Button id="bttnSave" runat="server" Text="Save" CssClass="buttonA" Width="64px"></asp:Button>&nbsp;<asp:Button id="BttnDelete" runat="server" Text="Delete" CssClass="buttonA" Width="64px"></asp:Button> <asp:Button id="bttnClear" runat="server" Text="Clear" CssClass="buttonA" Width="64px"></asp:Button></TD></TR><TR><TD><asp:GridView id="dg_Country" runat="server" CssClass="gridContent" Width="376px" AutoGenerateColumns="False" AutoGenerateSelectButton="True" PageSize="25">
                    <Columns>
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="CountryID" HeaderText="Country ID" />
                        <asp:BoundField ApplyFormatInEditMode="True" DataField="CountryName" HeaderText="Country Name" />
                    </Columns>
                    <SelectedRowStyle BackColor="#FFE0C0" />
                    <HeaderStyle CssClass="gridheader" />
                </asp:GridView> </TD></TR><TR><TD><asp:TextBox id="txt_CountryID" runat="server" CssClass="mytext" Width="48px" Visible="False"></asp:TextBox></TD></TR></TBODY></TABLE> <cc1:ConfirmButtonExtender id="ConfirmButtonExtender1" runat="server" __designer:wfdid="w1" TargetControlID="BttnDelete" ConfirmText="Do you want to Delete !!"></cc1:ConfirmButtonExtender>
</contenttemplate>
    </asp:UpdatePanel>
</asp:Content>

