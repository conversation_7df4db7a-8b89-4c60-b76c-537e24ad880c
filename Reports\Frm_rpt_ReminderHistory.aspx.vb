Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_ReminderHistory
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                chkEmployee.Checked = True
                chkIgnoredate.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        If ddlReportType.Text = "Detail" Then
            DetailReport()
        Else
            SummaryReport()
        End If
    End Sub

    Sub DetailReport()
        Try
            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String

            Dept = ddlDepartment.SelectedValue

            If chkEmployee.Checked = True Then
                Emp = "0"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    ' Emp = ddlEmployee.SelectedValue.ToString
                    Emp = EmployeeID.ToString
                Else
                    Emp = "0"
                End If

            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            Dim BaseStation As String
            If Me.chkStation.Checked = True Or Me.ddlBaseStation.SelectedIndex = 0 Then
                BaseStationID = "-1"
                BaseStation = "All"
            Else
                BaseStationID = ddlBaseStation.SelectedValue
                BaseStation = ddlBaseStation.SelectedItem.Text
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ReminderServiceHistory.rpt&@SendToEmployee=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "&@RemindersCounter=" + ddlReminderCounter.SelectedValue + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)
                Dim dt As New Hashtable
                dt.Add("@SendToEmployee", Emp)
                dt.Add("@DepartmentID", Dept)
                dt.Add("@FromDate", FromDate)
                dt.Add("@ToDate", ToDate)
                dt.Add("@BaseStationID", BaseStationID)
                dt.Add("@BaseStation", BaseStation)
                dt.Add("@RemindersCounter", ddlReminderCounter.SelectedValue)
                LoadPDFReport("rpt_ReminderServiceHistory", dt)
            Else
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ReminderServiceHistory.rpt&@SendToEmployee=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "&@RemindersCounter=" + ddlReminderCounter.SelectedValue + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)

                Dim dt As New Hashtable
                dt.Add("@SendToEmployee", Emp)
                dt.Add("@DepartmentID", Dept)
                dt.Add("@FromDate", FromDate)
                dt.Add("@ToDate", ToDate)
                dt.Add("@BaseStationID", BaseStationID)
                dt.Add("@BaseStation", BaseStation)
                dt.Add("@RemindersCounter", ddlReminderCounter.SelectedValue)
                LoadExcelReport("rpt_ReminderServiceHistory", dt)

            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_ReminderHistory.aspx"
            ObjSave.ReportName = "Reminder History --> Q1. How can I view Emp and Dept wise Reminder History?"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Sub SummaryReport()
        Try
            Dim Dept As String
            Dim Emp As String
            Dim FromDate As String
            Dim ToDate As String

            Dept = ddlDepartment.SelectedValue

            If chkEmployee.Checked = True Then
                Emp = "0"
            Else
                '**********************************************'
                '**************** Get EmployeeID **************'
                '**********************************************'
                If TxtEmployee.Text <> "" Then
                    Dim EmployeeID As Integer
                    Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                    objEmployeeID.EmployeeName = TxtEmployee.Text
                    EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                    Emp = EmployeeID.ToString
                Else
                    Emp = "0"
                End If

            End If

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim BaseStationID As String
            Dim BaseStation As String
            If Me.chkStation.Checked = True Or Me.ddlBaseStation.SelectedIndex = 0 Then
                BaseStationID = "-1"
                BaseStation = "All Stations"
            Else
                BaseStationID = ddlBaseStation.SelectedValue
                BaseStation = ddlBaseStation.SelectedItem.Text
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_ReminderServiceHistory_Summary.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)
                Dim dt As New Hashtable


                dt.Add("@EmployeeID", Emp)
                dt.Add("@DepartmentID", Dept)
                dt.Add("@FromDate", FromDate)
                dt.Add("@ToDate", ToDate)
                dt.Add("@BaseStationID", BaseStationID)
                dt.Add("@BaseStation", BaseStation)
                dt.Add("@RemindersCounter", ddlReminderCounter.SelectedValue)
                LoadPDFReport("rpt_ReminderServiceHistory_Summary", dt)
                'LoadExcelReport("rpt_ReminderServiceHistory_Summary", dt)

            Else

                Dim dt As New Hashtable


                dt.Add("@EmployeeID", Emp)
                dt.Add("@DepartmentID", Dept)
                dt.Add("@FromDate", FromDate)
                dt.Add("@ToDate", ToDate)
                dt.Add("@BaseStationID", BaseStationID)
                dt.Add("@BaseStation", BaseStation)
                dt.Add("@RemindersCounter", ddlReminderCounter.SelectedValue)

                LoadExcelReport("rpt_ReminderServiceHistory_Summary", dt)
                'Dim script As String
                'script = "<script language='javascript' type='text/javascript'>"
                'script = script + "window.onload=function OpenReport() {"
                'script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_ReminderServiceHistory_Summary.rpt&@EmployeeID=" + Emp + "&@DepartmentID=" + Dept + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@BaseStationID=" + BaseStationID + "&@BaseStation=" + BaseStation + "', 'mywindow'); "
                'script = script + "}</script>"

                'Page.RegisterClientScriptBlock("test", script)
            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_ReminderHistory.aspx"
            ObjSave.ReportName = "Reminder History --> Q1. How can I view Summary of Reminder History of Tapes?"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try
    End Sub

    Protected Sub chkEmployee_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If chkEmployee.Checked = True Then
            TxtEmployee.Enabled = False
        Else
            TxtEmployee.Enabled = True
        End If
    End Sub

    Sub LoadPDFReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        '--- Exporting Crystal Report to PDF process...


        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue

        Dim ExportPath As String = Server.MapPath("~/reports/")

        'Server.MapPath("~\reports")

        'Dim crReportDocument As New ReportDocument()
        Dim RptName As String = rpt

        rpt = Server.MapPath("~/Reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("sa", "Newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".pdf" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.PortableDocFormat
        crReportDocument.Export()
        crReportDocument.Close()

        Dim scriptString As String = "<script type='text/javascript'>" & _
           "window.onload = function(){" & _
           "var url = '" + "" + RptName + ".pdf" + "';" & _
           "var winPop = window.open(url,'winPop');" & _
           "}" & _
           "</script>"
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)
    End Sub

    Sub LoadExcelReport(ByVal rpt As String, ByVal paramValues As Hashtable)

        Dim valpWhere As New ParameterDiscreteValue
        Dim valpFilter As New ParameterDiscreteValue
        Dim ExportPath As String = Server.MapPath("~/reports/")

        Dim RptName As String = rpt

        rpt = Server.MapPath("~/Reports/") & RptName & ".rpt"

        'Dim con As New Connection

        crReportDocument.Load(rpt)
        crReportDocument.SetDatabaseLogon("sa", "Newpass102")
        'crReportDocument.SetDatabaseLogon(con.UserID, con.Password) ', con.ServerName, con.DatabaseName)
        'ApplyConnectionInfo(crReportDocument)


        Dim keyCollection As ICollection = paramValues.Keys()
        Dim enumerator As IEnumerator = keyCollection.GetEnumerator

        While enumerator.MoveNext
            Dim param As New CrystalDecisions.Shared.ParameterField
            Dim paramValue As New CrystalDecisions.Shared.ParameterDiscreteValue
            Dim paramValTyp As New CrystalDecisions.Shared.ParameterType

            param.ParameterFieldName = CType(enumerator.Current, String)
            paramValue.Value = paramValues.Item(CType(enumerator.Current, String))
            param.CurrentValues.Add(paramValue)

            crReportDocument.DataDefinition.ParameterFields(CType(enumerator.Current, String)).ApplyCurrentValues(param.CurrentValues)
        End While

        'crv.ReportSource = crReportDocument


        Dim expOptions As ExportOptions
        Dim destinationOption As DiskFileDestinationOptions = New DiskFileDestinationOptions

        destinationOption.DiskFileName = ExportPath & "\" & RptName & ".xls" 'Replace(RptName, ".rpt", ".pdf")
        expOptions = crReportDocument.ExportOptions
        expOptions.DestinationOptions = destinationOption

        expOptions.ExportDestinationType = ExportDestinationType.DiskFile

        expOptions.ExportFormatType = ExportFormatType.Excel
        crReportDocument.Export()
        crReportDocument.Close()

        Dim scriptString As String = "<script type='text/javascript'>" & _
           "window.onload = function(){" & _
           "var url = '" + "" + RptName + ".xls" + "';" & _
           "var winPop = window.open(url,'winPop');" & _
           "}" & _
           "</script>"
        ClientScript.RegisterStartupScript(Me.GetType(), "MyScript", scriptString)
    End Sub

End Class
