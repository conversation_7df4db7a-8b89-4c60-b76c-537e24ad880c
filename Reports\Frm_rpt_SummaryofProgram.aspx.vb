
Partial Class Reports_Frm_rpt_SummaryofProgram
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If
                'Chk_Ignore.Checked = True
                'txt_ProgramName.Enabled = False
                chkIgnoredate.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        
        Dim FromDate As String = ""
        Dim ToDate As String = ""

        If chkIgnoredate.Checked = True Then
            FromDate = "-1"
            ToDate = "-1"
        Else
            FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
            ToDate = Convert.ToDateTime(txt_ToDate.Text).ToString("dd-MMM-yyyy")
        End If

        Dim Var As String
        If Chk_Ignore.Checked = True Then
            Var = "All"
        Else
            Var = txt_ProgramName.Text
        End If

        Dim BaseStationID As String
        If Me.chkStation.Checked = True Then
            BaseStationID = "-1"
        Else
            BaseStationID = ddlBaseStation.SelectedValue
        End If
    
        If ddlPDF.SelectedValue = "PDF" Then
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=rpt_SummaryofProgram_New.rpt&@progname=" + Var + "&@BaseStationID=" + BaseStationID + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        Else
            Dim script As String
            script = "<script language='javascript' type='text/javascript'>"
            script = script + "window.onload=function OpenReport() {"
            script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=rpt_SummaryofProgram_New.rpt&@progname=" + Var + "&@BaseStationID=" + BaseStationID + "&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "', 'mywindow'); "
            script = script + "}</script>"

            Page.RegisterClientScriptBlock("test", script)
        End If

        ''******************************************************''
        ''************** Insert in MostViewReport **************''
        ''******************************************************''

        Dim ObjSave As New BusinessFacade.Reports()
        ObjSave.MostViewForm = "Frm_rpt_SummaryofProgram.aspx"
        ObjSave.ReportName = "Other Reports --> Q 3.How can I View Program Details?"
        ObjSave.SaveRecord()

        ''******************************************************''

    End Sub

    Protected Sub Chk_Ignore_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Chk_Ignore.CheckedChanged
        If Chk_Ignore.Checked = True Then
            txt_ProgramName.Enabled = False
        Else
            txt_ProgramName.Enabled = True
        End If
    End Sub
End Class
