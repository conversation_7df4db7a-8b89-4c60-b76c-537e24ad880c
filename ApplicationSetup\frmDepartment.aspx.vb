
Partial Class ApplicationSetup_frmDepartment
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            '    lbl_UserName.Text = Master.FooterText
            '    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
            '    lbl_UserName.Text = arr_UserID(1)
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
                lbl_UserName.Text = Master.FooterText
                Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                lbl_UserName.Text = arr_UserID(1)
            End If
            FillGrid()
            BindCombo()

        End If
    End Sub

    Private Sub BindCombo()
        ddl_City.DataSource = New BusinessFacade.City().GetRecords()
        ddl_City.DataTextField = "CityName"
        ddl_City.DataValueField = "CityID"
        ddl_City.DataBind()
        ddl_City.Items.Insert(0, "--Select--")

        'ddl_Department_SM.DataSource = New BusinessFacade.Department().Department_SM_GetRecords()
        'ddl_Department_SM.DataTextField = "DepartmentName"
        'ddl_Department_SM.DataValueField = "DepartmentID"
        'ddl_Department_SM.DataBind()
        'ddl_Department_SM.Items.Insert(0, "--Select--")

       End Sub

    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.Department().IsExists_Department(txt_DepartmentName.Text, ddl_City.SelectedValue)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : Department already Exists !"
        Else
            If txt_DepartmentID.Text = "" Then
                SaveRecord()
            Else
                UpdateRecord()
            End If
        End If
        dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        ClearAuditHistory()

    End Sub

    Private Sub SaveRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Save Record ***************''
        ''****************************************''

        If txt_DepartmentName.Text = "" Then
            lblErr.Text = "Please Insert Department Name !!"
        ElseIf ddl_City.SelectedIndex = "0" Then
            lblErr.Text = "Please Select City !!"
        Else
            Dim objDepartment As New BusinessFacade.Department()
            objDepartment.DepartmentName = txt_DepartmentName.Text
            objDepartment.CityID = ddl_City.SelectedValue
            If txt_TapeDueDays.Text = "" Then
                objDepartment.TapeDueDays = 0
            Else
                objDepartment.TapeDueDays = txt_TapeDueDays.Text
            End If
            objDepartment.UserID = UserID
            objDepartment.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved !!"
        End If

    End Sub

    Private Sub UpdateRecord()

        ''****************************************''
        ''************ Get User ID ***************''
        ''****************************************''

        Dim UserID As Integer
        Dim objUserID As New BusinessFacade.Employee()
        objUserID.SM_LoginID = lbl_UserName.Text
        UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

        ''****************************************''
        ''************ Update Record *************''
        ''****************************************''

        Dim objDepartment As New BusinessFacade.Department()
        objDepartment.DepartmentID = txt_DepartmentID.Text
        objDepartment.DepartmentName = txt_DepartmentName.Text
        objDepartment.CityID = ddl_City.SelectedValue
        objDepartment.TapeDueDays = txt_TapeDueDays.Text
        objDepartment.UserID = UserID
        objDepartment.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated !!"
    End Sub

    Private Sub FillGrid()
        Dim dt As Data.DataTable
        dt = New BusinessFacade.Department().GetRecords()
        dg_Department.DataSource() = dt
        dg_Department.Columns(0).Visible = True
        dg_Department.Columns(3).Visible = True
        dg_Department.DataBind()
        dg_Department.Columns(0).Visible = False
        dg_Department.Columns(3).Visible = False

        lblTotalRecords.Text = "Toatl Records : " & Convert.ToString(dt.Rows.Count)

    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_Department.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_Department.SelectedIndex.ToString
        txt_DepartmentID.Text = Convert.ToInt32(dg_Department.Rows(I).Cells(1).Text)
        txt_DepartmentName.Text = dg_Department.Rows(I).Cells(2).Text
        txt_TapeDueDays.Text = dg_Department.Rows(I).Cells(3).Text
        ddl_City.SelectedValue = dg_Department.Rows(I).Cells(4).Text
        dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Wheat

        lblAuditHistory.Visible = True
        Dim ObjAudit As New BusinessFacade.Department()
        ObjAudit.DepartmentID = txt_DepartmentID.Text
        dgAuditHistory.DataSource = ObjAudit.AuditHistory_Department()
        dgAuditHistory.DataBind()

    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        Try
            If txt_DepartmentID.Text = "" Then
                lblErr.Text = "Please Select Department First!!"
            Else
                Dim objDepartment As New BusinessFacade.Department()
                objDepartment.DepartmentID = txt_DepartmentID.Text
                objDepartment.DeleteRecord(objDepartment.DepartmentID)
                FillGrid()
                Clrscr()
                lblErr.Text = "Record has been Deleted!!"
                dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Transparent
                ClearAuditHistory()
            End If
        Catch ex As Exception
            lblErr.Text = "Attention: This Department is Already in Used !"
            Clrscr()
            dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        End Try
        
    End Sub

    Private Sub Clrscr()
        txt_DepartmentName.Text = String.Empty
        txt_DepartmentID.Text = String.Empty
        txt_TapeDueDays.Text = String.Empty
    End Sub

    Protected Sub dg_Department_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_Department.PageIndexChanging
        dg_Department.PageIndex = e.NewPageIndex()
        FillGrid()
        dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_Department.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        Clrscr()
        lblErr.Text = String.Empty
        ClearAuditHistory()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Private Sub ClearAuditHistory()
        lblAuditHistory.Visible = False
        dgAuditHistory.DataSource = Nothing
        dgAuditHistory.DataBind()
    End Sub

End Class
