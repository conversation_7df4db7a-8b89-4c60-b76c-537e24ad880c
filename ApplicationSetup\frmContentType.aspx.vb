
Partial Class ApplicationSetup_frmContentType
    Inherits System.Web.UI.Page
    Dim I As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            FillGrid()
            'Dim obj As JangSalesPortal.SecurityLayer.AuthenticationTicket
            'If Session("ApplicationTicket") Is Nothing Then
            '    Response.Redirect("../Login.aspx")
            'Else
            '    obj = CType(Session("ApplicationTicket"), JangSalesPortal.SecurityLayer.AuthenticationTicket)
            '    Master.FooterText = obj.UserLoginID
            'End If
            If Request.Cookies("UserInfo") Is Nothing Then
                Response.Redirect("../Login.aspx")
            Else
                Master.FooterText = Request.Cookies("userinfo")("username")
            End If
        End If
    End Sub


    Protected Sub bttnSave_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSave.Click
        Dim IsExists As String
        IsExists = New BusinessFacade.ContentType().IsExists_ContentType(txt_ContentType.Text)

        If IsExists <> 0 Then
            lblErr.Text = "Attention : Content Type already Exists !"
        Else
            If txt_ContentTypeID.Text = "" Then
                SaveRecord()
            Else
                ' UpdateRecord()
            End If
        End If
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        txt_ContentTypeID.Text = ""
        txt_ContentType.Text = ""

    End Sub

    Private Sub SaveRecord()
        If txt_ContentType.Text = "" Then
            lblErr.Text = "Please Enter Content Type!!"
        Else
            Dim ObjContentType As New BusinessFacade.ContentType()
            ObjContentType.ContentTypeName = txt_ContentType.Text
            ObjContentType.SaveRecord()
            FillGrid()
            lblErr.Text = "Record has been Saved!!"
            clrscr()
        End If
    End Sub

    Private Sub UpdateRecord()
        Dim ObjContentType As New BusinessFacade.ContentType()
        ObjContentType.ContentTypeID = txt_ContentTypeID.Text
        ObjContentType.ContentTypeName = txt_ContentType.Text
        ObjContentType.UpdateRecord()
        FillGrid()
        lblErr.Text = "Record has been Updated!!"
        clrscr()
    End Sub

    Private Sub FillGrid()
        dg_ContentType.DataSource() = New BusinessFacade.ContentType().GetRecords()
        dg_ContentType.DataBind()
        ' dg_ContentType.Columns(0).Visible = False

    End Sub

    Protected Sub dg_ContentType_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_ContentType.RowCreated
        e.Row.Cells(1).Visible = False
    End Sub

    Protected Sub dg_ContentType_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dg_ContentType.SelectedIndexChanged
        lblErr.Text = String.Empty
        I = dg_ContentType.SelectedIndex.ToString
        txt_ContentTypeID.Text = Convert.ToInt32(dg_ContentType.Rows(I).Cells(1).Text)
        txt_ContentType.Text = dg_ContentType.Rows(I).Cells(2).Text
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Wheat
    End Sub

    Protected Sub BttnDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles BttnDelete.Click
        If txt_ContentTypeID.Text = "" Then
            lblErr.Text = "Please Select Content!!"
        Else
            Dim ObjContentType As New BusinessFacade.ContentType()
            ObjContentType.ContentTypeID = txt_ContentTypeID.Text
            ObjContentType.DeleteRecord(ObjContentType.ContentTypeID)
            FillGrid()
            lblErr.Text = "Record has been Deleted!!"
            clrscr()
        End If
    End Sub

    Private Sub clrscr()
        txt_ContentType.Text = String.Empty
        txt_ContentTypeID.Text = String.Empty
    End Sub

    'Protected Sub dg_ContentType_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_ContentType.PageIndexChanging
    '    dg_ContentType.PageIndex = e.NewPageIndex()
    '    FillGrid()
    'End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click
        dg_ContentType.SelectedRowStyle.BackColor = Drawing.Color.Transparent
        lblErr.Text = String.Empty
        clrscr()
    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Response.Redirect("../Home/Home.aspx")
    End Sub
End Class
