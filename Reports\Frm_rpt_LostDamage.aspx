<%@ Page Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="Frm_rpt_LostDamage.aspx.vb" Inherits="Frm_rpt_LostDamage" title="Lost Damage > How can I view Lost, Damage, Cost Recovered, Disposed Off Tapes?" %>
<%@ MasterType VirtualPath="~/MasterPage.master" %>
<%@ Register Assembly="CrystalDecisions.Web, Version=10.2.3600.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <table style="width: 100%">
        <tr>
            <td style="width: 100%">
                </td>
        </tr>
        <tr>
            <td style="width: 100%">
                <asp:Panel ID="TitlePanel" runat="server" Width="100%" BackColor="LightSteelBlue" BorderColor="#E0E0E0">
                    &nbsp;
                    <asp:Image ID="Image1" runat="server" />
                    <asp:Label ID="Label1" runat="server" Text="Lost Damage > Q.1  How can I view Lost, Damage, Cost Recovered, Disposed Off Tapes?" Width="800px" Font-Bold="True" Font-Names="Arial" Font-Size="Medium" CssClass="heading1"></asp:Label></asp:Panel>
                <asp:Panel ID="ContentPanel" runat="server" Height="50px" Width="100%">
                    <asp:ScriptManager id="ScriptManager1" runat="server">
                    </asp:ScriptManager>
                    <asp:UpdatePanel id="UpdatePanel1" runat="server">
                        <contenttemplate>
<TABLE><TBODY><TR class="mytext"><TD style="WIDTH: 159px; HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="WIDTH: 184px; HEIGHT: 1px" vAlign=middle></TD><TD style="HEIGHT: 1px" vAlign=middle></TD><TD style="WIDTH: 300px; HEIGHT: 1px" vAlign=middle></TD></TR><TR class="mytext"><TD style="WIDTH: 159px; HEIGHT: 21px" vAlign=middle>Employee Name&nbsp;<asp:CheckBox id="chkEmployee" runat="server" Text="Ignore" Font-Bold="False" OnCheckedChanged="chkEmployee_CheckedChanged" AutoPostBack="True" __designer:wfdid="w63"></asp:CheckBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </TD><TD style="HEIGHT: 21px" vAlign=middle>Tape Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkTapeNumber" runat="server" Text="Ignore" Font-Bold="False" OnCheckedChanged="chkEmployee_CheckedChanged" AutoPostBack="True" __designer:wfdid="w64"></asp:CheckBox></TD><TD style="HEIGHT: 21px" vAlign=middle>From Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox id="chkIgnoredate" runat="server" Text="Ignore Dates" Font-Bold="False" __designer:wfdid="w65"></asp:CheckBox></TD><TD style="WIDTH: 184px; HEIGHT: 21px" vAlign=middle>To Date</TD><TD style="HEIGHT: 21px" vAlign=middle><asp:Label id="Label2" runat="server" Text="Export to" Width="48px" __designer:wfdid="w44"></asp:Label></TD><TD style="WIDTH: 800px; HEIGHT: 21px" vAlign=middle>&nbsp;&nbsp; </TD></TR><TR class="mytext"><TD style="WIDTH: 159px; HEIGHT: 22px" vAlign=top><asp:DropDownList id="ddl_Employee" runat="server" Width="144px" CssClass="mytext" __designer:wfdid="w66"></asp:DropDownList></TD><TD style="HEIGHT: 22px" vAlign=top><asp:DropDownList id="ddl_TapeNumber" runat="server" Width="144px" CssClass="mytext" __designer:wfdid="w67"></asp:DropDownList></TD><TD style="HEIGHT: 22px" vAlign=top><asp:TextBox id="txtFromdate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w68"></asp:TextBox></TD><TD style="WIDTH: 184px; HEIGHT: 22px" vAlign=top><asp:TextBox id="txtToDate" runat="server" Width="175px" CssClass="mytext" __designer:wfdid="w69"></asp:TextBox></TD><TD style="HEIGHT: 22px" vAlign=top><asp:DropDownList id="ddlPDF" runat="server" CssClass="mytext" __designer:wfdid="w45"><asp:ListItem>PDF</asp:ListItem>
<asp:ListItem>EXCEL</asp:ListItem>
</asp:DropDownList></TD><TD style="WIDTH: 300px; HEIGHT: 22px" vAlign=top></TD></TR></TBODY></TABLE><cc1:CalendarExtender id="CalendarExtender1" runat="server" CssClass="MyCalendar" __designer:wfdid="w70" TargetControlID="txtFromdate" Format="dd-MMM-yyyy"></cc1:CalendarExtender><cc1:CalendarExtender id="CalendarExtender2" runat="server" CssClass="MyCalendar" __designer:wfdid="w71" TargetControlID="txtToDate" Format="dd-MMM-yyyy"></cc1:CalendarExtender><BR />
</contenttemplate>
                    </asp:UpdatePanel>
                    <table style="width: 100%">
                        <tr>
                            <td class="bottomMain" style="width: 100%">
                                &nbsp;<asp:Button ID="bttnReport" runat="server" CssClass="buttonA" Font-Names="Verdana"
                                    Font-Size="X-Small" Text="View Report" Width="88px" /></td>
                        </tr>
                    </table>
                    <br />
                </asp:Panel>
                &nbsp;
                                       
            </td>
        </tr>
    </table>
                    <cc1:collapsiblepanelextender 
                        id="CollapsiblePanelExtender1" 
                        runat="server" 
                        collapsecontrolid="TitlePanel"
                        collapsed="false" 
                        collapsedimage="~/Images/Collapse.gif" 
                        collapsedtext="--Show Form (Lost Damage > How can I view Lost, Damage, Cost Recovered, Disposed Off Tapes?)--"
                        expandcontrolid="TitlePanel" 
                        expandedimage="~/Images/expand.gif" 
                        expandedtext="--Hide Form (Lost Damage > How can I view Lost, Damage, Cost Recovered, Disposed Off Tapes?)--"
                        imagecontrolid="Image1" 
                        suppresspostback="true"
                        textlabelid="Label1"
                        targetcontrolid="ContentPanel">
                    </cc1:collapsiblepanelextender>
</asp:Content>

