<%@ Page Language="VB" AutoEventWireup="false" CodeFile="LocalReminders_Search.aspx.vb"
    Inherits="ReminderService_LocalReminders_Search" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Untitled Page</title>
</head>
<body>
    <form id="form1" runat="server">
        <asp:TextBox ID="txtFrom" Style="z-index: 113; left: 88px; position: absolute; top: 80px"
            runat="server" Width="537px" Height="16px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:TextBox ID="txtDepartmentLogo" Style="z-index: 118; left: 376px; position: absolute;
            top: 624px" runat="server" Width="40px" Visible="False"></asp:TextBox>
        <asp:TextBox ID="txtTestBody" Style="z-index: 115; left: 248px; position: absolute;
            top: 624px" runat="server" Width="48px" TextMode="MultiLine" Height="24px" Visible="False"></asp:TextBox>
        <asp:Label ID="lbMessage" Style="z-index: 114; left: 32px; position: absolute; top: 24px"
            runat="server" Visible="False" Font-Bold="True" Font-Names="Latha" Font-Size="20pt"
            ForeColor="DarkGreen" BackColor="#C0FFC0" Font-Strikeout="False">.. Your Email has been sent to the Desired Reciepants ..</asp:Label>
        <asp:Button ID="btnClose" Style="z-index: 112; left: 688px; position: absolute; top: 32px"
            runat="server" Visible="False" Text="Close Window"></asp:Button>
        <asp:TextBox ID="txtBody" Style="z-index: 110; left: 32px; position: absolute; top: 312px"
            runat="server" Width="1000px" TextMode="MultiLine" Height="296px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:Label ID="Label5" Style="z-index: 109; left: 32px; position: absolute; top: 264px"
            runat="server" Font-Bold="True">Subject</asp:Label>
        <asp:TextBox ID="txtSubject" Style="z-index: 108; left: 88px; position: absolute;
            top: 264px" runat="server" Width="536px" Height="16px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:Label ID="Label4" Style="z-index: 107; left: 32px; position: absolute; top: 168px"
            runat="server" Font-Bold="True">Cc</asp:Label>
        <asp:Label ID="Label3" Style="z-index: 105; left: 32px; position: absolute; top: 128px"
            runat="server" Font-Bold="True">To</asp:Label>
        &nbsp;&nbsp;
        <asp:TextBox ID="txtBCC" Style="z-index: 103; left: 88px; position: absolute; top: 216px"
            runat="server" Width="537px" Height="16px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:TextBox ID="txtCC" Style="z-index: 101; left: 88px; position: absolute; top: 168px"
            runat="server" Width="537px" Height="16px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:Label ID="Label2" Style="z-index: 106; left: 32px; position: absolute; top: 216px"
            runat="server" Font-Bold="True">Bcc</asp:Label>
        <asp:TextBox ID="txtTo" Style="z-index: 102; left: 88px; position: absolute; top: 120px"
            runat="server" Width="537px" Height="16px" BackColor="WhiteSmoke"
            Font-Size="X-Small" Font-Names="Verdana"></asp:TextBox>
        <asp:Label ID="Label1" Style="z-index: 104; left: 32px; position: absolute; top: 80px"
            runat="server" Font-Bold="True">From</asp:Label>
        <asp:Button ID="btn" Style="z-index: 111; left: 40px; position: absolute; top: 624px"
            runat="server" Text="Send Email" Width="144px" Font-Bold="True" Height="56px" BackColor="#E0E0E0" BorderColor="DarkGray" BorderStyle="Outset" BorderWidth="10px" ForeColor="Black"></asp:Button>
        <asp:TextBox Style="z-index: 116; left: 432px; position: absolute; top: 624px" ID="txtTapeDetail"
            runat="server" Visible="False" Width="48px"></asp:TextBox>
        <asp:TextBox ID="txtExtensions" Style="z-index: 117; left: 320px; position: absolute;
            top: 624px" runat="server" Width="40px" Visible="False"></asp:TextBox>
    </form>
</body>
</html>
