Imports System.Data
Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports Crystaldecisions.reportsource

Partial Class Frm_rpt_RecycleTapeDetail
    Inherits System.Web.UI.Page
    Dim crReportDocument As New ReportDocument()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then
                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                End If

                chkIgnoredate.Checked = True
                ChkProgram.Checked = True
                ChkSlug.Checked = True
                BindCombo()
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindCombo()

        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()

    End Sub

    Protected Sub bttnReport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnReport.Click
        Try
            
            Dim FromDate As String
            Dim ToDate As String

            If chkIgnoredate.Checked = True Then
                FromDate = "-1"
                ToDate = "-1"
            Else
                FromDate = Convert.ToDateTime(txtFromdate.Text).ToString("dd-MMM-yyyy")
                ToDate = Convert.ToDateTime(txtToDate.Text).ToString("dd-MMM-yyyy")
            End If

            Dim Program As String
            If ChkProgram.Checked = True Then
                Program = "All"
            Else
                Program = txtProgram.Text
            End If

            Dim slug As String
            If ChkSlug.Checked = True Then
                slug = "All"
            Else
                slug = txtSlug.Text
            End If

            Dim ContentType As String
            ContentType = ddlContentType.SelectedItem.ToString

            Dim Recycle As String
            Recycle = ddlRecycle.SelectedValue

            Dim BaseStationID As String
            If Me.chkStation.Checked = True Then
                BaseStationID = "-1"
            Else
                BaseStationID = ddlBaseStation.SelectedValue
            End If

            If ddlPDF.SelectedValue = "PDF" Then
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer.aspx?ReportName=Rpt_Recycle_DV_Detail_New.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@ChannelType=" + ContentType + "&@ProgramName=" + Program + "&@Slug=" + slug + "&@Recycle=" + Recycle + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            Else
                Dim script As String
                script = "<script language='javascript' type='text/javascript'>"
                script = script + "window.onload=function OpenReport() {"
                script = script + "var mywindow = window.open('ReportViewer_excel.aspx?ReportName=Rpt_Recycle_DV_Detail_New.rpt&@FromDate=" + FromDate + "&@ToDate=" + ToDate + "&@ChannelType=" + ContentType + "&@ProgramName=" + Program + "&@Slug=" + slug + "&@Recycle=" + Recycle + "&@BaseStationID=" + BaseStationID + "', 'mywindow'); "
                script = script + "}</script>"

                Page.RegisterClientScriptBlock("test", script)
            End If

            ''******************************************************''
            ''************** Insert in MostViewReport **************''
            ''******************************************************''

            Dim ObjSave As New BusinessFacade.Reports()
            ObjSave.MostViewForm = "Frm_rpt_RecycleTapeDetail.aspx"
            ObjSave.ReportName = "Other Reports --> Q 4. How Can  I View List of Recycled Tapes ?"
            ObjSave.SaveRecord()

            ''******************************************************''

        Catch ex As Exception
            Throw
        End Try

    End Sub

End Class
