Imports System.Data
Partial Class ReminderService_ReminderService
    Inherits System.Web.UI.Page
    Dim Email As New clsEmail
    Dim ConString As String = System.Configuration.ConfigurationSettings.AppSettings("ConnectionString")
    Dim Con As System.Data.SqlClient.SqlConnection = New System.Data.SqlClient.SqlConnection(ConString)


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            If Not Page.IsPostBack = True Then

                If Request.Cookies("UserInfo") Is Nothing Then
                    Response.Redirect("../Login.aspx")
                Else
                    Master.FooterText = Request.Cookies("userinfo")("username")
                    lbl_UserName.Text = Master.FooterText
                    Dim arr_UserID As Array = Split(lbl_UserName.Text, ",")
                    lbl_UserName.Text = arr_UserID(1)
                End If

                lblErr.Text = String.Empty
                BindCombo()
                BindGrid()
                Chk_Employee.Checked = True
                chkDepartment.Checked = True
                Chk_Date.Checked = True
            End If
        Catch ex As Exception
            Throw
        End Try
    End Sub

    Private Sub BindGrid()
        Dim objSearch As New BusinessFacade.ReminderService()
        objSearch.EmployeeID = 0
        objSearch.Date = Date.Now.Date()
        objSearch.DepartmentID = 0
        objSearch.isBlank = -1
        objSearch.isSearch = 0
        objSearch.ToDate = Date.Now.Date()
        objSearch.SortBy = Me.ddlSortby.SelectedValue
        objSearch.SortOrder = Me.ddlSOrder.SelectedValue
        objSearch.BaseStationID = ddlBaseStation.SelectedValue
        'dg_search.DataSource = objSearch.GetRecords()
        'dg_search.DataBind()


        '*********************** Store Procedure **********************'

        Dim cmd As New System.Data.SqlClient.SqlCommand
        cmd.CommandType = Data.CommandType.StoredProcedure
        'cmd.CommandText = "ReminderService_Getrecords_New"
        cmd.CommandText = "ReminderService_Getrecords_New_withSlug"
        cmd.Connection = Con
        cmd.CommandTimeout = 0

        Dim FromDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Date", Data.SqlDbType.Text)
        FromDate.Value = objSearch.Date
        Dim EmployeeIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
        EmployeeIDs.Value = objSearch.EmployeeID
        Dim DepartmentIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@DepartmentID", Data.SqlDbType.Int)
        DepartmentIDs.Value = objSearch.DepartmentID
        Dim IsBlank As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsBlank", Data.SqlDbType.Int)
        IsBlank.Value = objSearch.isBlank
        Dim IsSearch As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsSearch", Data.SqlDbType.Int)
        IsSearch.Value = objSearch.isSearch
        Dim ToDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
        ToDate.Value = objSearch.ToDate
        Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
        BaseStationID.Value = objSearch.BaseStationID
        Dim SortBy As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortBy", Data.SqlDbType.Int)
        SortBy.Value = objSearch.SortBy
        Dim SortOrder As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortOrder", Data.SqlDbType.Int)
        SortOrder.Value = objSearch.SortOrder
        Dim da As New System.Data.SqlClient.SqlDataAdapter
        Dim DS As New DataSet
        da.SelectCommand = cmd
        da.Fill(DS)
        dg_search.DataSource = DS.Tables(0).DefaultView
        dg_search.DataBind()



        GridRowsColor()
    End Sub

    Private Sub GridRowsColor()
        Dim I As Integer
        For I = 0 To dg_search.Rows.Count - 1
            If dg_search.Rows(I).Cells(8).Text <> 0 Then
                dg_search.Rows(I).BackColor = Drawing.Color.Pink
            End If
        Next
    End Sub

    Private Sub BindCombo()
        Dim ObjStation As New BusinessFacade.BaseStation()
        ddlBaseStation.DataSource = ObjStation.GetBaseStations()
        ddlBaseStation.DataTextField = "BaseStationName"
        ddlBaseStation.DataValueField = "BaseStationID"
        ddlBaseStation.DataBind()
        ddlBaseStation.SelectedValue = CInt(Request.Cookies("userinfo")("BaseStationID"))

        If CInt(Request.Cookies("userinfo")("BaseStationID")) <> 1 Then
            ddlBaseStation.Enabled = False
        Else
            ddlBaseStation.Enabled = True
        End If

    End Sub

    Protected Sub LnkHomePage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LnkHomePage.Click
        Response.Redirect("../Home/Home.aspx")
    End Sub

    Protected Sub bttnSearch_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSearch.Click
        lblErr.Text = String.Empty

        Try
            Dim objSearch As New BusinessFacade.ReminderService()
            If Chk_Employee.Checked = True Then
                objSearch.EmployeeID = 0
            Else
                ''***************************************''
                ''********** Get EmployeeID *************''
                ''***************************************''
                Dim EmployeeID As Integer
                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                objEmployeeID.EmployeeName = txtEmployee.Text
                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                objSearch.EmployeeID = EmployeeID

            End If

            If chkDepartment.Checked = True Then
                objSearch.DepartmentID = 0
            Else
                ''***************************************''
                ''********** Get DepartmentID ***********''
                ''***************************************''

                Dim DepartmentID As Integer
                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_DepartmentName.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

                objSearch.DepartmentID = DepartmentID

            End If

            If Chk_Date.Checked = True Then
                objSearch.Date = 0
            Else
                objSearch.Date = txt_DueDate.Text
            End If

            objSearch.SortBy = Me.ddlSortby.SelectedValue
            objSearch.SortOrder = Me.ddlSOrder.SelectedValue
            objSearch.isBlank = ddlIsBlank.SelectedValue
            objSearch.isSearch = 1
            objSearch.ToDate = txtToDate.Text
            objSearch.BaseStationID = ddlBaseStation.SelectedValue
            '  dg_search.DataSource = objSearch.GetRecords()
            ' dg_search.DataBind()


            '*********************** Store Procedure **********************'

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            'cmd.CommandText = "ReminderService_Getrecords_New"
            cmd.CommandText = "ReminderService_Getrecords_New_withSlug"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim FromDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Date", Data.SqlDbType.Text)
            FromDate.Value = objSearch.Date
            Dim EmployeeIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
            EmployeeIDs.Value = objSearch.EmployeeID
            Dim DepartmentIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@DepartmentID", Data.SqlDbType.Int)
            DepartmentIDs.Value = objSearch.DepartmentID
            Dim IsBlank As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsBlank", Data.SqlDbType.Int)
            IsBlank.Value = objSearch.isBlank
            Dim IsSearch As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsSearch", Data.SqlDbType.Int)
            IsSearch.Value = objSearch.isSearch
            Dim ToDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
            ToDate.Value = objSearch.ToDate
            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
            BaseStationID.Value = objSearch.BaseStationID
            Dim SortBy As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortBy", Data.SqlDbType.Int)
            SortBy.Value = objSearch.SortBy
            Dim SortOrder As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortOrder", Data.SqlDbType.Int)
            SortOrder.Value = objSearch.SortOrder
            Dim da As New System.Data.SqlClient.SqlDataAdapter
            Dim DS As New DataSet
            da.SelectCommand = cmd
            da.Fill(DS)
            dg_search.DataSource = DS.Tables(0).DefaultView
            dg_search.DataBind()

            GridRowsColor()
            'ClearControls()

        Catch ex As Exception
            Me.lblErr.Text = "Error: " & ex.Message
            Me.lblErr.Visible = True
        End Try
        
    End Sub

    Protected Sub dg_search_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles dg_search.PageIndexChanging
        If Chk_Date.Checked = True And Chk_Employee.Checked = True And Date.Parse(dg_search.Rows(0).Cells(4).Text).ToString("dd-MMM-yyyy") = Date.Parse(Date.Now()).ToString("dd-MMM-yyyy") Then
            dg_search.PageIndex = e.NewPageIndex()
            BindGrid()
        Else
            dg_search.PageIndex = e.NewPageIndex()
            BindGrid2()
        End If

    End Sub

    Private Sub BindGrid2()

        Try
            Dim objSearch As New BusinessFacade.ReminderService()
            If Chk_Employee.Checked = True Then
                objSearch.EmployeeID = 0
            Else
                ''***************************************''
                ''********** Get EmployeeID *************''
                ''***************************************''
                Dim EmployeeID As Integer
                Dim objEmployeeID As New BusinessFacade.TapeIssuance()
                objEmployeeID.EmployeeName = txtEmployee.Text
                EmployeeID = objEmployeeID.GetEmployeeID_byEmployeeName(objEmployeeID.EmployeeName)
                objSearch.EmployeeID = EmployeeID

            End If

            If chkDepartment.Checked = True Then
                objSearch.DepartmentID = 0
            Else
                ''***************************************''
                ''********** Get DepartmentID ***********''
                ''***************************************''

                Dim DepartmentID As Integer
                Dim objDeptID As New BusinessFacade.TapeIssuance()
                objDeptID.DepartmentName = txt_DepartmentName.Text
                DepartmentID = objDeptID.GetDepartmentID_byDepartmentName(objDeptID.DepartmentName)

                objSearch.DepartmentID = DepartmentID

            End If

            If Chk_Date.Checked = True Then
                objSearch.Date = 0
            Else
                objSearch.Date = txt_DueDate.Text
            End If

            objSearch.SortBy = Me.ddlSortby.SelectedValue
            objSearch.SortOrder = Me.ddlSOrder.SelectedValue
            objSearch.isBlank = ddlIsBlank.SelectedValue
            objSearch.isSearch = 1
            objSearch.ToDate = txtToDate.Text
            objSearch.BaseStationID = ddlBaseStation.SelectedValue
            'dg_search.DataSource = objSearch.GetRecords()
            'dg_search.DataBind()


            '*********************** Store Procedure **********************'

            Dim cmd As New System.Data.SqlClient.SqlCommand
            cmd.CommandType = Data.CommandType.StoredProcedure
            'cmd.CommandText = "ReminderService_Getrecords_New"
            cmd.CommandText = "ReminderService_Getrecords_New_withSlug"
            cmd.Connection = Con
            cmd.CommandTimeout = 0

            Dim FromDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@Date", Data.SqlDbType.Text)
            FromDate.Value = objSearch.Date
            Dim EmployeeIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@EmployeeID", Data.SqlDbType.Int)
            EmployeeIDs.Value = objSearch.EmployeeID
            Dim DepartmentIDs As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@DepartmentID", Data.SqlDbType.Int)
            DepartmentIDs.Value = objSearch.DepartmentID
            Dim IsBlank As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsBlank", Data.SqlDbType.Int)
            IsBlank.Value = objSearch.isBlank
            Dim IsSearch As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@IsSearch", Data.SqlDbType.Int)
            IsSearch.Value = objSearch.isSearch
            Dim ToDate As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@ToDate", Data.SqlDbType.Text)
            ToDate.Value = objSearch.ToDate
            Dim BaseStationID As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@BaseStationID", Data.SqlDbType.Int)
            BaseStationID.Value = objSearch.BaseStationID
            Dim SortBy As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortBy", Data.SqlDbType.Int)
            SortBy.Value = objSearch.SortBy
            Dim SortOrder As Data.SqlClient.SqlParameter = cmd.Parameters.Add("@SortOrder", Data.SqlDbType.Int)
            SortOrder.Value = objSearch.SortOrder
            Dim da As New System.Data.SqlClient.SqlDataAdapter
            Dim DS As New DataSet
            da.SelectCommand = cmd
            da.Fill(DS)
            dg_search.DataSource = DS.Tables(0).DefaultView
            dg_search.DataBind()


            GridRowsColor()
            'ClearControls()

        Catch ex As Exception
            Me.lblErr.Text = "Error: " & ex.Message
            Me.lblErr.Visible = True
        End Try

    End Sub

    Private Sub Clrscr()
        Dim I As Integer
        For I = 0 To dg_search.Rows.Count - 1
            Dim myCheckbox As CheckBox = CType(dg_search.Rows(I).Cells(5).Controls(1), CheckBox)
            If myCheckbox.Checked = True Then
                myCheckbox.Checked = False
            End If
        Next
    End Sub

    Private Function CheckEmployeeCount()
        ''*********************************************************''
        ''*********** Chec for Multiple Employee ******************''
        ''*********************************************************''
        Dim FirstEmployee As String = ""
        Dim EmployeeCount As Integer = 0
        Dim FirstCheck As Integer = 0
        Dim J As Integer

        Dim i As Integer
        For i = 0 To dg_search.Rows.Count - 1
            Dim myCheckbox As CheckBox = CType(dg_search.Rows(i).Cells(5).Controls(1), CheckBox)
            If myCheckbox.Checked = True Then

                FirstCheck = FirstCheck + 1

                If FirstCheck = 1 Then
                    FirstEmployee = dg_search.Rows(i).Cells(2).Text
                    J = i
                End If

                If i <> J Then
                    If dg_search.Rows(i).Cells(2).Text <> FirstEmployee Then
                        EmployeeCount += 1
                    End If
                End If
            End If
        Next
        Return EmployeeCount
    End Function

    Protected Sub bttnSendMail_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnSendMail.Click
        Dim EmpCount As Integer = CheckEmployeeCount()
        Dim Counter As Integer = 0

        If EmpCount <> 0 Then
            lblErr.Text = "You Can not send Email to More than One Emplyee at a Time !!"
        Else

            ''**************************************************''
            ''************* Count Check Boxes ******************''
            ''**************************************************''
            Dim T As Integer
            Dim CheckCount As Integer = 0
            For T = 0 To dg_search.Rows.Count - 1
                Dim myCheckbox As CheckBox = CType(dg_search.Rows(T).Cells(5).Controls(1), CheckBox)
                If myCheckbox.Checked = True Then
                    CheckCount = CheckCount + 1
                End If
            Next

            ''*************************************************''
            If CheckCount = 0 Then
                lblErr.Text = "Plz Select Tape First !!"
            Else
                Dim UserID As Integer
                Dim objUserID As New BusinessFacade.Employee()
                objUserID.SM_LoginID = lbl_UserName.Text
                UserID = objUserID.GetEmployeeID_by_SM_LoginID(objUserID.SM_LoginID)

                Dim BCC As String

                'Dim strFrom As String = "<EMAIL>"
                Dim strFrom As String
                If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                    ''****** For Lahore *******''
                    strFrom = "<EMAIL>"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                    ''****** For Islamabad *****''
                    strFrom = "<EMAIL>"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                    ''****** For Peshawar *****''
                    strFrom = "<EMAIL>"
                Else
                    ''****** For Karachi *******''
                    strFrom = "<EMAIL>"
                End If


                Dim CC As String

                If txt_BCC.Text = "" Then

                    If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                        ''****** For Lahore *******''
                        BCC = "<EMAIL>"
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                        ''****** For Islamabad *****''
                        BCC = "<EMAIL>"
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                        ''****** For Peshawar *****''
                        BCC = "<EMAIL>"
                    Else
                        ''****** For Karachi *******''
                        BCC = "<EMAIL>"
                        'BCC = "<EMAIL>"
                    End If

                Else
                    If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                        ''****** For Lahore *******''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                        ''****** For Islamabad *****''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                        ''****** For Peshawar *****''
                        BCC = "<EMAIL>," & txt_BCC.Text
                    Else
                        ''****** For Karachi *******''
                        BCC = "<EMAIL>," & txt_BCC.Text
                        'BCC = "<EMAIL>," & txt_BCC.Text
                    End If
                End If

                If txt_CC.Text = "" Then
                    CC = "<EMAIL>"
                    'CC = "<EMAIL>"
                Else
                    CC = "<EMAIL>," & txt_CC.Text
                End If

                Dim subject As String = "Reminder: Tape OverDue"
                Dim Q As String
                Dim i As Integer
                Q = ""
                Dim TapeDetail As String = ""
                Dim TapeSlug As String = ""
                Dim J As String = "0"

                For i = 0 To dg_search.Rows.Count - 1

                    Dim myCheckbox As CheckBox = CType(dg_search.Rows(i).Cells(5).Controls(1), CheckBox)
                    If myCheckbox.Checked = True Then

                        If J = "0" Then
                            If CheckCount <> 1 Then
                                Q &= "Dear " & dg_search.Rows(i).Cells(2).Text & "," & " ^^The following tapes are outstanding against you. ^^"
                                Q &= "bbbccc S.No dddeee" & "~~" & "bbbccc Tape Type dddeee" & "~~" & "bbbccc Tape No. dddeee" & "~~" & "bbbccc Issued Date dddeee" & "~~" & "bbbccc Due Date dddeee" & "~~" & "bbbccc Remarks dddeee" & "~~" & "bbbccc Total Reminders dddeee^^"
                            Else
                                Q &= "Dear " & dg_search.Rows(i).Cells(2).Text & "," & " ^^The following tape is outstanding against you. ^^"
                                Q &= "bbbccc S.No dddeee" & "~~" & "bbbccc Tape Type dddeee" & "~~" & "bbbccc Tape No. dddeee" & "~~" & "bbbccc Issued Date dddeee" & "~~" & "bbbccc Due Date dddeee" & "~~" & "bbbccc Remarks dddeee" & "~~" & "bbbccc Total Reminders dddeee^^"
                            End If
                        End If

                        J = Convert.ToString(J + 1)

                        Dim IssueDate As String
                        IssueDate = CDate(dg_search.Rows(i).Cells(3).Text).ToString("dd-MMM-yyyy")
                        Dim ReturnDate As String
                        ReturnDate = CDate(dg_search.Rows(i).Cells(4).Text).ToString("dd-MMM-yyyy")

                        If CheckCount <> 1 Then
                            Counter += 1
                            Dim Cnt As String = CStr(CInt(dg_search.Rows(i).Cells(8).Text) + 1)
                            Q &= "(" & (Counter) & ")~~" & dg_search.Rows(i).Cells(9).Text & "~~" & dg_search.Rows(i).Cells(1).Text & "~~" & IssueDate & "~~" & ReturnDate & "~~" & dg_search.Rows(i).Cells(7).Text & "~~" & Cnt & "~~" & dg_search.Rows(i).Cells(10).Text & "^^"
                            TapeDetail &= (Counter) & "-+" & dg_search.Rows(i).Cells(1).Text & "-+" & dg_search.Rows(i).Cells(9).Text & "-+" & IssueDate & "-+" & ReturnDate & "-+" & dg_search.Rows(i).Cells(7).Text & "-+" & Cnt & "-+" & dg_search.Rows(i).Cells(10).Text & "$$"
                        Else
                            Dim Cnt As String = CStr(CInt(dg_search.Rows(i).Cells(8).Text) + 1)
                            Counter += 1
                            Q &= "(" & (Counter) & ")~~" & dg_search.Rows(i).Cells(9).Text & "~~" & dg_search.Rows(i).Cells(1).Text & "~~" & IssueDate & "~~" & ReturnDate & "~~" & dg_search.Rows(i).Cells(7).Text & "~~" & Cnt & "~~" & dg_search.Rows(i).Cells(10).Text & "^^"
                            TapeDetail &= (Counter) & "-+" & dg_search.Rows(i).Cells(1).Text & "-+" & dg_search.Rows(i).Cells(9).Text & "-+" & IssueDate & "-+" & ReturnDate & "-+" & dg_search.Rows(i).Cells(7).Text & "-+" & Cnt & "-+" & dg_search.Rows(i).Cells(10).Text & "$$"

                        End If
                        lblEmployeeName.Text = dg_search.Rows(i).Cells(2).Text
                    End If

                Next
                Q &= "^Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us."
                Q &= "^^Regards,^^"
                Q &= Request.Cookies("userinfo")("userfullname").ToUpper
             
                If CInt(Request.Cookies("userinfo")("BaseStationID")) = 3 Then
                    ''****** For Lahore *******''
                    Q &= "^Lahore Archive"
                    Q &= "^Ext: 334 - 374"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 4 Then
                    ''****** For Islamabad *****''
                    Q &= "^Islamabad Archive"
                    Q &= "^Ext: 218"
                ElseIf CInt(Request.Cookies("userinfo")("BaseStationID")) = 5 Then
                    ''****** For Peshawar *****''
                    Q &= "^Peshawar Archive"
                    Q &= "^Ext: N/A"
                Else
                    ''****** For Karachi *******''
                    Q &= "^Circulation Desk"
                    Q &= "^Ext:6132 (News Archive) , 6568 (Central Archive)"
                End If

                Dim objEmailSrvc As New BusinessFacade.ReminderService()
                Dim EmailId As Integer
                EmailId = objEmailSrvc.SaveEmailData(strFrom, txt_EmailAddress.Text, CC, subject, Q, TapeDetail)

                SaveReminderDetails(EmailId)


                Dim G As String
                G = ""
                If G = "" Or G = "&nbsp;" Then
                    G = txt_EmailAddress.Text
                    Dim script As String
                    script = "<script language='javascript' type='text/javascript'>"
                    script = script + "window.onload=function OpenReport() {"

                    TapeDetail = ""

                    script = script + "var mywindow = window.open('LocalReminders.aspx?EmailID=" & EmailId & "&From=" + strFrom + "&To=" + G + "&CC=" + CC + "&BCC=" + BCC + "&Subject=" + subject + "&EmployeeName=" + lblEmployeeName.Text + "&UserName=" + Request.Cookies("userinfo")("userfullname") + "&TapeDetail=" + TapeDetail + "', 'mywindow');"

                    script = script + "}</script>"
                    Page.RegisterClientScriptBlock("test", script)

                    lblErr.Text = "An email message has been sent!"

                    '*******************************************************************''
                    ''******************** Save Reminder History ***********************''
                    '*******************************************************************''

                    Dim C As Integer
                    For C = 0 To dg_search.Rows.Count - 1
                        Dim myCheckbox As CheckBox = CType(dg_search.Rows(C).Cells(5).Controls(1), CheckBox)
                        If myCheckbox.Checked = True Then
                            Dim ObjSave As New BusinessFacade.ReminderService()
                            ObjSave.SendToEmployee = dg_search.Rows(C).Cells(2).Text
                            ObjSave.TapeNumber = dg_search.Rows(C).Cells(1).Text
                            ObjSave.TapeIssuanceDate = Convert.ToDateTime(dg_search.Rows(C).Cells(3).Text).Date
                            ObjSave.TapeReturnDueDate = Convert.ToDateTime(dg_search.Rows(C).Cells(4).Text).Date
                            If dg_search.Rows(C).Cells(7).Text = "Blank" Then
                                ObjSave.isBlank = 1
                            Else
                                ObjSave.isBlank = 0
                            End If
                            ObjSave.SendByEmployeeID = UserID
                            ObjSave.SaveRecord_ReminderHistory()
                        End If
                    Next
                    '*******************************************************************''

                    Clrscr()

                End If
            End If
        End If

    End Sub

    Sub SaveReminderDetails(ByVal EmailId As Integer)
        For i As Integer = 0 To dg_search.Rows.Count - 1
            Dim myCheckbox As CheckBox = CType(dg_search.Rows(i).Cells(5).Controls(1), CheckBox)
            If myCheckbox.Checked = True Then
                Dim SNo As Integer = i + 1
                Dim TapeType As String = dg_search.Rows(i).Cells(9).Text
                Dim TapeNo As String = dg_search.Rows(i).Cells(1).Text
                Dim IssueDate As String = CDate(dg_search.Rows(i).Cells(3).Text).ToString("dd-MMM-yyyy")
                Dim ReturnDate As String = CDate(dg_search.Rows(i).Cells(4).Text).ToString("dd-MMM-yyyy")
                Dim Remarks As String = dg_search.Rows(i).Cells(7).Text
                Dim TotalReminders As Integer = dg_search.Rows(i).Cells(8).Text
                Dim ProgramSlug As String = dg_search.Rows(i).Cells(10).Text
                Dim objEmailSrvc As New BusinessFacade.ReminderService()
                objEmailSrvc.SaveReminderDetails(EmailId, SNo, TapeType, TapeNo, IssueDate, ReturnDate, Remarks, TotalReminders, ProgramSlug)
            End If
        Next
    End Sub

    Protected Sub bttnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles bttnClear.Click

        'Dim A As String = "Dear ABDUL WAHID - P - 3630, ^^The following tape is outstanding against you. ^^bbbccc S.No dddeee~~bbbccc Tape Type dddeee~~bbbccc Tape No. dddeee~~bbbccc Issued Date dddeee~~bbbccc Due Date dddeee~~bbbccc Remarks dddeee~~bbbccc Total Reminders dddeee^^(1)~~MINI DV~~191008~~07-Jun-2010~~14-Jun-2010~~Archive~~1^^^Your are requested to return the above tapes as soon as possible. If you have already returned the above tapes, so please ignore this reminder and send reply to us.^^Regards,^^AHMED HUSSAIN^Circulation Desk^Ext:6132 (News Archive) , 6568 (Central Archive)"
        'Dim Arr As Array = Split(A, "^")
        'Dim Ext As String = Arr(Arr.Length - 1).ToString()
        'Dim Type As String = Arr(Arr.Length - 2).ToString()

        dg_search.DataSource = Nothing
        dg_search.DataBind()
        ClearControls()
    End Sub

    Private Sub ClearControls()
        txtEmployee.Text = String.Empty
        txt_DueDate.Text = String.Empty
        Chk_Employee.Checked = True
        Chk_Date.Checked = True
        txt_CC.Text = String.Empty
        txt_BCC.Text = String.Empty
        txt_EmailAddress.Text = String.Empty
        lblErr.Text = String.Empty
        txtToDate.Text = String.Empty
        txt_EmailAddress.BackColor = Drawing.Color.White
    End Sub


    Protected Sub dg_search_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles dg_search.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            e.Row.Attributes.Add("onmouseover", "MouseEvents(this, event)")
            e.Row.Attributes.Add("onmouseout", "MouseEvents(this, event)")
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Dim EmpCount As Integer = CheckEmployeeCount()
        lblErr.Text = String.Empty

        If EmpCount <> 0 Then
            lblErr.Text = "You Can not send Email to More than One Emplyee at a Time !!"
        Else
            Dim I As Integer
            For I = 0 To dg_search.Rows.Count - 1

                Dim myCheckbox As CheckBox = CType(dg_search.Rows(I).Cells(5).Controls(1), CheckBox)
                If myCheckbox.Checked = True Then
                    Dim ObjReminder As New BusinessFacade.ReminderService()
                    ObjReminder.EmployeeName = dg_search.Rows(I).Cells(2).Text
                    Dim EmailAddress As String = ObjReminder.GetEmailAddress()
                    If EmailAddress <> "N/A" Then
                        Dim Arr As Array = Split(EmailAddress, "@")
                        If Arr.Length = 2 Then
                            txt_EmailAddress.Text = EmailAddress
                            txt_EmailAddress.BackColor = Drawing.Color.PaleGreen
                        Else
                            lblErr.Text = "Invalid Email Address, Please Correct Email Address in Employee Form!"
                            txt_EmailAddress.BackColor = Drawing.Color.Pink
                        End If
                    Else
                        lblErr.Text = "Email Address not Exists"
                        txt_EmailAddress.BackColor = Drawing.Color.Pink
                    End If
                    Exit Sub
                End If
            Next
        End If
    End Sub


End Class
